//#region Global Imports
import * as React from 'react';
import { Link, Redirect } from 'react-router-dom';
import { bindActionCreators, Dispatch } from 'redux';
import * as MaterialUI from '@material-ui/core';
import {
  Grid,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  Popper,
  Typography,
} from '@material-ui/core';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import { Loader } from 'Components/UI/LoadingIndicator';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import { Processmaker } from '../Processmaker';
import moment from 'moment';
import _ from 'lodash';
import { ModalComponent } from 'Components/UI/ModalComponent';
import { ClaimModalComponent } from './ClaimModalComponent';

import StopIcon from '@material-ui/icons/Stop';
///////CHART////////
import {
  Chart,
  ArgumentAxis,
  ValueAxis,
  BarSeries,
  // Legend,
} from '@devexpress/dx-react-chart-material-ui';
import { scaleBand } from '@devexpress/dx-chart-core';
import { ArgumentScale, Stack } from '@devexpress/dx-react-chart';
// import { sales as dashboardStatsData } from './Component/ChartData/index.const';

// import { TicketSnackbarNotification } from './TicketSnackbarNotification';

//#endregion Global Imports

//#region Local Imports
import './style.scss';
//#endregion Local Imports

//#region Interface Imports
import { Store } from 'Components/Stores/IStore';
import { IDashboard } from './IDashboard';
import { DashboardActions } from './Actions';

// import { APIService } from 'Services/APIService';
//#endregion Interface Imports

import '@hims/core/dist/index.css';
import { API } from 'Pages/API';
import { API as API_2 } from './API';
import { Utils } from '@hims/core';
import { GlobalFunction as MS_GlobalFunction } from 'Components/UI/GlobalFunction';

const membershipModuleManagerGroup = [
  'APD_MANAGER GROUP',
  'APD_SUPERVISOR GROUP',
  'APD_TL GROUP',
  'APD_MANAGER',
  'APD_SUPERVISOR',
  'APD_TL',
];

const membershipEncoderGroup = [
  'DEPT_APD_ENCODER GROUP',
  'APD_ENCODER GROUP',
  'APD_ENCODER',
];

const RENEW_MEMBER_WORKFLOW = 'Renew Memberlist'; 
const CLAIMED_RENEW_MEMBER_WORKFLOW = 'Claimed Renew Memberlist'; 
const UPLOAD_RENEW_MEMBER_WORKFLOW = "Upload Renewal";
const MANUAL_RENEW_MEMBER_WORKFLOW = 'Manual Renewal';
const AUTO_RENEW_MEMBER_WORKFLOW = 'Auto-renewal';
const EXCEPTION_MANUAL_RENEW_MEMBER_WORKFLOW = 'Exception Renewal Report';
const VERIFY_RENEWAL_WORKFLOW = "Verify Renewal";
const CLAIMED_VERIFY_RENEWAL_WORKFLOW = "Claimed Verify Renewal";
const VERIFY_RENEWAL_MEMBERLIST = "Verify Renewal Memberlist";
const ENROLL_MEMBER_WORKFLOW = 'Enroll Memberlist';
const CLAIMED_ENROLL_MEMBER_WORKFLOW = 'Claimed Enroll Memberlist';
const VERIFY_MEMBER_WORKFLOW = 'Verify Memberlist';
const CLAIMED_VERIFY_MEMBER_WORKFLOW = 'Claimed Verify Memberlist';
const ADD_MEMBER_WORKFLOW = 'Add Member';
const ADD_MEMBER_UNASSIGNED_WORKFLOW = 'Add Member Unassigned';
const MANUAL_ENCODE_WORKFLOW = 'Manual Encode';
const VERIFY_WORKFLOW = 'Verify Member';
const CLAIMED_VERIFY_WORKFLOW = 'Claimed Verify Member';
const PRINTING_ID_WORKFLOW = 'Print Card IDs';
const CLAIMED_PRINTING_ID_WORKFLOW = 'Claimed Print Card IDs';
const CLAIMED_VERIFY_TERMINATION = 'Claimed Verify Termination';
const VERIFY_TERMINATION = 'Verify Termination';
const UPLOAD_TERMINATION = 'Upload Termination';
const CLAIMED_VERIFY_SUSPENSION = 'Claimed Verify Suspension';
const VERIFY_SUSPENSION = 'Verify Suspension';
const CLAIMED_VERIFY_SUSPENSION_COLLECTION =
  'Claimed Suspend Members from Collection'; //~
const VERIFY_SUSPENSION_COLLECTION = 'Suspend Members from Collection'; //~
const UPLOAD_SUSPENSION = 'Upload Suspension';
const BATCH_EDIT = 'Batch Edit';
const CLAIMED_VERIFY_EDIT = 'Claimed Verify Edit';
const VERIFY_EDIT = 'Verify Edit';
const VOID_MASTERLIST = 'Void Masterlist';
const CLAIMED_VOID_MASTERLIST = 'Claimed Void Masterlist';
// const PROCESS_VOID_MASTERLIST = 'Process Void Masterlist';
const VERIFY_DEPENDENTS = 'Verify Dependents';
const CLAIMED_VERIFY_DEPENDENTS = 'Claimed Verify Dependents';
const BATCH_EDIT_OCP = 'Batch Edit OCP';
const MANUAL_RENEWAL = 'Manual Renewal';
const FINISHED_MANUAL_RENEWAL = 'Finished Manual Renew of Members';
const MATCH_PERSON_PROFILE = 'Match Person Profile';
const CLAIMED_MATCH_PERSON_PROFILE = 'Claimed Match Person Profile';
const CLAIMED_TRANSMITTAL_REQUEST = 'Claimed Generate Transmittal Request';
const TRANSMITTAL_REQUEST = 'Generate Transmittal Request';
const CLAIMED_APPROVE_MEMBER_CHANGE_OCP =
  'Claimed Approve Member Changes from OCP';
const APPROVE_MEMBER_CHANGE_OCP = 'Approve Member Changes from OCP';
const CLAIMED_REPRINT_CARD_OCP = 'Claimed Reprint Card IDs from OCP';
const REPRINT_CARD_OCP = 'Reprint Card IDs from OCP';
const UPLOAD_VOID_MASTERLIST = 'Upload Void Masterlist';
const UPLOAD_TERMINATION_OCP = 'Upload Termination OCP';
const CLAIMED_UPLOAD_TERMINATION_OCP = 'Claimed Upload Termination OCP';


const ticket_options = [
  {
    name: 'Created by Me',
    value: 'created_by_me',
  },
  {
    name: 'Created Tickets',
    value: 'created_tickets',
  },
];

const top_facilities_options = [
  {
    name: 'By Amount',
    value: 'amount',
  },
  {
    name: 'By Availees',
    value: 'availees',
  },
  {
    name: 'By Cases',
    value: 'cases',
  },
];

/** end of data */
const useStyles = makeStyles({
  popper: {
    top: '4px',
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: '-0.9em',
      width: '3em',
      height: '1em',
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: '-0.9em',
      width: '3em',
      height: '1em',
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: '-0.9em',
      height: '3em',
      width: '1em',
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: '-0.9em',
      height: '3em',
      width: '1em',
    },
  },
  paper: {
    padding: '20px',
    width: 230,
  },
  link: {
    textDecoration: 'none',
    color: '#272E4C',
  },
  gridpadding: {
    paddingBottom: '16px',
  },
});

// JULY 8, 2020: ALTERNATIVE CHART LEGEND FOR DASHBOARD STATS CHART
// const ChartLegend = ({ color }) => {
//   return (
//     <StopIcon style={{ backgroundColor: color, color: color }} />
//   );
// }

const processedMembersLabels = [
  'Enrolled Members',
  'Approved Members',
  'Printed ID Cards',
];
const toEnrollVerifyLabels = ['Endorsed Members', 'Verified Members'];

const barGraphColors = {
  gold: '#bebf2c',
  blue: '#304290',
  pink: '#ce0b5e',
};

const processedMembersLegend = [
  {
    label: processedMembersLabels[0],
    color: barGraphColors['gold'],
  },
  {
    label: processedMembersLabels[1],
    color: barGraphColors['blue'],
  },
  {
    label: processedMembersLabels[2],
    color: barGraphColors['pink'],
  },
];

const toEnrollVerifyLegend = [
  {
    label: toEnrollVerifyLabels[0],
    color: barGraphColors['gold'],
  },
  {
    label: toEnrollVerifyLabels[1],
    color: barGraphColors['blue'],
  },
];

function Popover(props: any) {
  const classes = useStyles();
  return (
    <Popper
      open={props.isPopoverOpen}
      anchorEl={props.anchor}
      placement={'bottom-end'}
      className={classes.popper}
      disablePortal={false}
      style={{ marginTop: '4px' }}
      modifiers={{
        flip: {
          enabled: false,
        },
        preventOverflow: {
          enabled: true,
          boundariesElement: 'undefined',
        },
        arrow: {
          enabled: false,
        },
      }}
    >
      <Paper className={classes.paper}>
        <MaterialUI.ClickAwayListener onClickAway={props.handleClose}>
          <Grid container>
            <Grid item xs={12} className={classes.gridpadding}>
              <Link
                id="dashboard_encode_batch"
                data-cy="dashboard_encode_batch"
                className={classes.link}
                to={{
                  pathname: `/membership/encode/${
                    props.item.page_type ? props.item.page_type : 'manual'
                  }/${props.item.case_id}/${props.item.proposal_id}/${
                    props.item.client_id
                  }/?encodeTicket=true&ticketId=${
                    props.item.ticket_id
                  }&isProcessing=${props.item.status !== ''}&memberUploadId=${
                    props.item.member_upload_id
                  }`,
                  state: {
                    case_id: props.item.case_id,
                    proposal_id: props.item.proposal_id,
                    client_id: props.item.client_id,
                    encode_ticket: true,
                    ticket_id: props.item.ticket_id,
                    isProcessing: props.item.status !== '',
                    member_upload_id: props.item.member_upload_id,
                  },
                }}
              >
                Encode Batch Details
              </Link>
            </Grid>
            <Grid item xs={12}>
              <Link
                id="dashboard_upload_batch"
                data-cy="dashboard_upload_batch"
                className={classes.link}
                to={{
                  pathname: `/membership/add-members/${props.item.case_id}/${
                    props.item.client_id
                  }/${props.item.proposal_id}/?encodeTicket=true&ticketId=${
                    props.item.ticket_id
                  }&isProcessing=${props.item.status !== ''}&memberUploadId=${
                    props.item.member_upload_id
                  }`,
                  state: {
                    case_id: props.item.case_id,
                    proposal_id: props.item.proposal_id,
                    client_id: props.item.client_id,
                    ticket_id: props.item.ticket_id,
                    isProcessing: props.item.status !== '',
                    member_upload_id: props.item.member_upload_id,
                  },
                }}
              >
                Upload batch
              </Link>
            </Grid>
          </Grid>
        </MaterialUI.ClickAwayListener>
      </Paper>
    </Popper>
  );
}

export class Dashboard extends React.Component<
  IDashboard.IProps,
  IDashboard.IState
> {
  constructor(props: IDashboard.IProps) {
    super(props);
    this.state = {
      role: 'apd_verifier',
      loading: false,
      ticket_type: ticket_options[0].value,
      top_facility_sort: top_facilities_options[0].value,
      open_tickets: [],
      unassigned_tickets: [],
      openTicketsCount: 0,
      unassignedTicketsCount: 0,
      redirect: false,
      redirect_info: {},
      persons_to_match: [],
      enroll_members: [],
      validate_members: [],
      suspend_members: [],
      cancel_members: [],
      terminate_members: [],
      activate_members: [],
      reprint_members: [],
      print_members: [],
      popperOpen: false,
      popperAnchor: null,
      popperItem: {},
      modalTitle: '',
      modalMessage: '',
      isOpenModal: false,
      isTicketSnackBarOpen: false,
      claimedTicketData: {},
      isClaimModalOpen: false,
      next: null,
      statsData: {},
      currentUserData: {},
      isMembershipModuleManager: false,
      tListOpenTickets: [],
      tListUnassignedTickets: [],
      openStatusFilter: {},
      vmticketcount: 0,
      member_upload_id: '',
      member_void_upload_id: '',
    };
  }

  reset = () => {
    this.setState({
      role: 'apd',
      loading: false,
      ticket_type: ticket_options[0].value,
      top_facility_sort: top_facilities_options[0].value,
    });
  };

  handleTicketTypeSelect = (
    event: React.ChangeEvent<{ name?: string | undefined; value: any }>,
  ) => {
    this.setState({
      ticket_type: event.target.value,
    });
  };

  handleTopFacilitySortSelect = (
    event: React.ChangeEvent<{ name?: string | undefined; value: any }>,
  ) => {
    this.setState({
      top_facility_sort: event.target.value,
    });
  };

  queryStringToJSON = queryString => {
    if (queryString.trim().length < 1) {
      return {};
    }

    var pairs = queryString.slice(1).split('&');

    var result = {};
    pairs.forEach(function(pair) {
      //(pair as string)
      pair = (pair as string).split('=');
      //  pair = pair.split('=');
      result[pair[0]] = decodeURIComponent(pair[1] || '');
    });

    return JSON.parse(JSON.stringify(result));
  };

  handleClickOpenTicketsRow = (ticket: any) => {


    switch (ticket.ticket_type) {
      case CLAIMED_ENROLL_MEMBER_WORKFLOW:
      // case VOID_MASTERLIST:
      //5685
      case ENROLL_MEMBER_WORKFLOW:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/encode-member/${ticket.case_id}/${
              ticket.proposal_id
            }/${ticket.client_id}/?ticketId=${
              ticket.ticket_id
            }&isProcessing=${ticket.status !== ''}&memberUploadId=${
              ticket.member_upload_id
            }`,
            state: {
              case_id: ticket.case_id,
              proposal_id: ticket.proposal_id,
              client_id: ticket.client_id,
              ticket_id: ticket.ticket_id,
              isProcessing: ticket.status !== '',
              member_upload_id: ticket.member_upload_id,
            },
          },
        });
        break;
      
      // RENEWAL
      // these tickets need to work save for now manual, upload, auto renew, if save for now is required
      case UPLOAD_RENEW_MEMBER_WORKFLOW:
      case AUTO_RENEW_MEMBER_WORKFLOW: 
      case EXCEPTION_MANUAL_RENEW_MEMBER_WORKFLOW: // need to work around by in manual renewal
      case MANUAL_RENEW_MEMBER_WORKFLOW: // need to work around by in manual renewal
      case CLAIMED_RENEW_MEMBER_WORKFLOW:
      case RENEW_MEMBER_WORKFLOW: // 5685
        // new method 5685

        let renewal = this.state.tListOpenTickets.find(
          item => item._id === ticket._id,
        );

        const isAutoRenew = renewal?.renew_members ?? ticket?.renew_members ?? false;
        const contractID =  renewal?.contract_id ?? ticket?.proposal_id ?? false;
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/encode-member/${ticket.case_id}/${
              ticket.proposal_id
            }/${ticket.client_id}/?ticketId=${
              ticket.ticket_id
            }&isProcessing=${ticket.status !== ''}&memberUploadId=${
              ticket.member_upload_id
            }&isAutoRenew=${isAutoRenew}&contractId=${
              contractID
            }&type=renew_members`,
            state: {
              case_id: ticket.case_id,
              proposal_id: ticket.proposal_id,
              client_id: ticket.client_id,
              ticket_id: ticket.ticket_id,
              isProcessing: ticket.status !== '',
              member_upload_id: ticket.member_upload_id,
              ticket_details: {}
            },
          },
        });
        break;
      case VERIFY_RENEWAL_MEMBERLIST:
      case VERIFY_MEMBER_WORKFLOW:
      case CLAIMED_VERIFY_MEMBER_WORKFLOW:
      case VERIFY_WORKFLOW:
      case CLAIMED_VERIFY_WORKFLOW:
        if (ticket !== undefined && ticket.validation_finished === true) {
          if (ticket.from_ocp) {
            this.setState({
              redirect: true,
              redirect_info: {
                pathname:
                  '/membership/verify-member/' +
                  'vm-ocp/' +
                  ticket.case_id +
                  '/' +
                  ticket.client_id +
                  '/' +
                  ticket.ticket_id,
                state: {
                  case_id: ticket.case_id,
                },
              },
            });
          } else {
            this.setState({
              redirect: true,
              redirect_info: {
                pathname:
                  '/membership/verify-member/' +
                  ticket.case_id +
                  '/' +
                  ticket.client_id +
                  '/' +
                  ticket.ticket_id,
                state: {
                  case_id: ticket.case_id,
                  from_dashboard: true,
                },
              },
            });
          }
        }
        break;
      case VOID_MASTERLIST:
      case CLAIMED_VOID_MASTERLIST:
 
        //&& ticket.validation_finished === true
        if (ticket !== undefined) {
          // if(ticket && ticket.)
          let memberVoidID =
            ticket && ticket.member_void_upload_id !== ''
              ? ticket.member_void_upload_id
              : ticket.member_upload_id;
          this.setState({
            redirect: true,
            redirect_info: {
              pathname:
                '/membership/void-masterlist/' +
                ticket.case_id +
                '/' +
                // ticket.member_void_upload_id +
                memberVoidID +
                '/' +
                // '/' +
                ticket.ticket_id,
              state: {
                case_id: ticket.case_id,
                from_dashboard: true,
              },
            },
          });
        }
        break;
      case UPLOAD_VOID_MASTERLIST:
        if (ticket !== undefined) {
          this.setState({
            redirect: true,
            // redirect_info: {
            //   pathname:
            //     '/membership/encode/void/' +
            //     ticket.case_id +
            //     '/' +
            //     ticket.client_id,
            //   state: {
            //     case_id: ticket.case_id,
            //     from_dashboard: true,
            //   },
            // },
            redirect_info: {
              pathname: `/membership/encode/void/${ticket.case_id}/${
                ticket.proposal_id
              }/${ticket.client_id}/?ticketId=${
                ticket.ticket_id
              }&isProcessing=${ticket.status !== ''}&memberUploadId=${
                ticket.member_void_upload_id
              }`,
              state: {
                // case_id: ticket.case_id,
                // from_dashboard: true,
                case_id: ticket.case_id,
                proposal_id: ticket.proposal_id,
                client_id: ticket.client_id,
                ticket_id: ticket.ticket_id,
                isProcessing: ticket.status !== '',
                member_upload_id: ticket.member_upload_id,
                member_void_upload_id: ticket.member_void_upload_id,
              },
            },
          });
       
        }
        break;
      case ADD_MEMBER_UNASSIGNED_WORKFLOW:
      case ADD_MEMBER_WORKFLOW:
        if (ticket.is_ocp) {
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/add-members/${ticket.case_id}/${
                ticket.client_id
              }/?ticketId=${ticket.ticket_id}&isProcessing=${ticket.status !==
                ''}&memberUploadId=${ticket.member_upload_id}`,
              state: {
                case_id: ticket.case_id,
                proposal_id: ticket.proposal_id,
                client_id: ticket.client_id,
                ticket_id: ticket.ticket_id,
                isProcessing: ticket.status !== '',
                member_upload_id: ticket.member_upload_id,
                is_ocp: ticket.is_ocp,
              },
            },
          });
        } else {
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/add-members/${ticket.case_id}/${
                ticket.client_id
              }/${ticket.proposal_id}/?ticketId=${
                ticket.ticket_id
              }&isProcessing=${ticket.status !== ''}&memberUploadId=${
                ticket.member_upload_id
              }`,
              state: {
                case_id: ticket.case_id,
                proposal_id: ticket.proposal_id,
                client_id: ticket.client_id,
                ticket_id: ticket.ticket_id,
                isProcessing: ticket.status !== '',
                member_upload_id: ticket.member_upload_id,
              },
            },
          });
        }

        break;
      case MANUAL_ENCODE_WORKFLOW:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/encode/${
              ticket.page_type ? ticket.page_type : 'manual'
            }/${ticket.case_id}/${ticket.proposal_id}/${
              ticket.client_id
            }/?ticketId=${ticket.ticket_id}&isProcessing=${ticket.status !==
              ''}&memberUploadId=${ticket.member_upload_id}`,
            state: {
              case_id: ticket.case_id,
              proposal_id: ticket.proposal_id,
              client_id: ticket.client_id,
              ticket_id: ticket.ticket_id,
              isProcessing: ticket.status !== '',
              member_upload_id: ticket.member_upload_id,
            },
          },
        });
        break;
      case CLAIMED_PRINTING_ID_WORKFLOW:
      case PRINTING_ID_WORKFLOW:
        if (ticket.old_case_id) {
          if (ticket.client_name.includes('/')) {
            let split = ticket.client_name.split('/');
            ticket.client_name = `${split[0]}-${split[1]}`;
          }

          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/process-batches/${ticket.case_id}/${ticket.client_id}/${ticket.client_name}/${ticket.old_case_id}/${ticket.ticket_id}`,
            },
          });
        } else {
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/process-batches/${ticket.case_id}/${ticket.client_id}/${ticket.client_name}/${ticket.ticket_id}`,
            },
          });
        }
        break;
      case CLAIMED_VERIFY_TERMINATION:
      case VERIFY_TERMINATION:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/verify-termination/${ticket.client_id}/${ticket.member_upload_id}/${ticket.case_id}/${ticket._id}`,
          },
        });
      
        break;
      case UPLOAD_TERMINATION:
        if (ticket !== undefined) {
      
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/clients-profile/batch-terminate/${ticket.client_id}/${ticket.case_id}`,
              state: {
                // case_id: ticket.case_id,
                // from_dashboard: true,
                case_id: ticket.case_id,
                proposal_id: ticket.proposal_id,
                client_id: ticket.client_id,
                ticket_id: ticket.ticket_id,
                isProcessing: ticket.status !== '',
                member_upload_id: ticket.member_upload_id,
                member_void_upload_id: ticket.member_void_upload_id,
              },
            },
          });
        
        }
        break;
      case UPLOAD_TERMINATION_OCP:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/clients-profile/batch-terminate/${ticket.client_id}/${ticket.case_id}`,
            state: {
              is_ocp: ticket.is_ocp,
              member_upload_id: ticket.member_upload_id,
            },
          },
        });
        break;
      case VERIFY_SUSPENSION:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/verify-suspension/${ticket.case_id}/${ticket.client_id}`,
          },
        });
        
        break;
      case VERIFY_SUSPENSION_COLLECTION: //~
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/verify-suspension-collection/${ticket.case_id}/${ticket.client_id}`,
          },
        });
        break;
      case UPLOAD_SUSPENSION:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/clients-profile/batch-suspend/${ticket.client_id}/${ticket.case_id}`,
          },
        });
        break;
      case BATCH_EDIT_OCP:
      case BATCH_EDIT:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/clients-profile/batch-correction/${
              ticket.client_id
            }/${ticket.case_id}/${ticket.ticket_id}/${
              ticket.from_ocp ? 'ocp' : 'default'
            }`,
            state: {
              case_id: ticket.case_id,
              proposal_id: ticket.proposal_id,
              client_id: ticket.client_id,
              ticket_id: ticket.ticket_id,
              isProcessing: ticket.status !== '',
              member_upload_id: ticket.member_upload_id,
            },
          },
        });
        break;
      case VERIFY_EDIT:
        //@marc redirect to verify edit with ticket case id in the path use ticket.member_civil_status_group_id
        //@marc redirect to verify edit with ticket case id in the path
        if (ticket.from_ocp) {
          localStorage.setItem('TICKET_ID', ticket['ticket_id']);
          this.setState({
            redirect: true,
            redirect_info: {
              pathname:
                '/membership/verify/edit/' +
                've-ocp/' +
                ticket.case_id +
                '/' +
                ticket.member_upload_id +
                '/' +
                ticket.client_id,
              state: {
                case_id: ticket.case_id,
              },
            },
          });
        } else {
          localStorage.setItem('TICKET_ID', ticket['ticket_id']);
          this.setState({
            redirect: true,
            redirect_info: {
              pathname:
                '/membership/verify/edit/' +
                ticket.case_id +
                '/' +
                ticket.member_upload_id +
                '/' +
                ticket.client_id,
              state: {
                case_id: ticket.case_id,
              },
            },
          });
        }
        break;
      case VERIFY_DEPENDENTS:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname:
              '/membership/verify-dependent/vd/' +
              ticket.case_id +
              '/' +
              ticket.member_civil_status_group_id +
              '/' +
              ticket.client_id,
            state: {
              case_id: ticket.case_id,
            },
          },
        });

        break;
      case MANUAL_RENEWAL:
        const userId = 'encode-member';
        API.getSaveForNowData(userId, ticket.case_id).then(resp => {
          if (resp && resp[0] && resp[0].data && resp[0].data.page_type) {
            let page_type = resp[0].data.page_type;
            if (page_type === 'selection') {
              this.setState({
                redirect: true,
                redirect_info: {
                  pathname: `/membership/clients-profile/manual-renewal/${page_type}/${ticket.client_id}/${ticket.case_id}`,
                },
              });
            } else {
              this.setState({
                redirect: true,
                redirect_info: {
                  pathname: `/membership/clients-profile/batch-renewal/${ticket.client_id}/${ticket.case_id}`,
                },
              });
            }
          }
        });
        break;
      case MATCH_PERSON_PROFILE:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/match-person-profile/${ticket.case_id}`,
          },
        });
        break;
      case TRANSMITTAL_REQUEST:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/process-batches-transmittal-request/${ticket.client_id}/${ticket.case_id}/${ticket.ticket_id}`,
          },
        });
        break;
      case APPROVE_MEMBER_CHANGE_OCP:
        this.setState({
          redirect: true,
          redirect_info: {
            pathname: `/membership/modify-member-changes/${ticket.case_id}/${ticket.ticket_id}`,
          },
        });
        break;
      case REPRINT_CARD_OCP:
   
        if (ticket.old_case_id) {
          if (ticket.client_name.includes('/')) {
            let split = ticket.client_name.split('/');
            ticket.client_name = `${split[0]}-${split[1]}`;
          }
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/process-batches-ocp/${ticket.case_id}/${ticket.client_id}/${ticket.client_name}/${ticket.old_case_id}/${ticket.ticket_id}`,
            },
          });
        } else {
          this.setState({
            redirect: true,
            redirect_info: {
              pathname: `/membership/process-batches-ocp/${ticket.case_id}/${ticket.client_id}/${ticket.client_name}/${ticket.ticket_id}`,
            },
          });
        }
        break;
    }
  };

  handlePopoverOpen = (event: React.MouseEvent<HTMLSpanElement>, item: any) => {
    this.setState({
      popperOpen: true,
      popperAnchor: event.target,
      popperItem: item,
    });
  };

  handlePopoverClose = () => {
    this.setState({
      popperOpen: false,
      popperAnchor: null,
      popperItem: {},
    });
  };

  closeModalMessage = () => {
    const { next } = this.state;
    this.setState(
      {
        isOpenModal: false,
        isClaimModalOpen: false,
        modalTitle: '',
        modalMessage: '',
        next: null,
      },
      () => {
        if (next !== undefined && next !== null) {
          next();
        }
      },
    );
  };

  setTicketDueDate = (ticket: any, formattedDueDate: any) => {
    if (ticket.case_id) {
      this.setState(
        {
          loading: true,
        },
        () => {
          Processmaker.put('cases/' + ticket.case_id + '/variable', {
            ticket_due_date: formattedDueDate,
          })
            .then(() => {
              this.setState(
                {
                  loading: false,
                  isTicketSnackBarOpen: false,
                  claimedTicketData: ticket,
                  isClaimModalOpen: true,
                  modalTitle: 'Ticket Claimed',
                  modalMessage: `Ticket No. ${ticket.ticket_id} for ${ticket.ticket_type} was claimed successfully.`,
                },
                () => {
                  this.fetchTickets('open');
                  this.fetchTickets('unassigned');
                },
              );
            })
            .catch(e => {
              if (e.message === 'USER_DATA_NOT_FOUND') {
                this.setState({
                  loading: false,
                  modalTitle: 'User Data Error',
                  modalMessage:
                    'User data not found. Please try logging in again.',
                  isOpenModal: true,
                  next: () => {
                    // window.location.replace('../index.html#/');
                  },
                });
              } else {
                this.setState({
                  loading: false,
                });
              }
            });
        },
      );
    }
  };

  handleClaim = (ticket: any) => {
    if (ticket.case_id) {
      Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
   
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
          API.activityClaimedTicket(ticket.ticket_id, ticket, user_name)
            .then(response => {
              if (response == undefined) {
                console.log('response', response);
              }
            })
            .catch(e => {
              console.log('claiming error', e);
            });
        }
      });

      this.setState(
        {
          loading: true,
        },
        () => {
          Processmaker.postClaim('light/case/' + ticket.case_id + '/claim')
            .then(response => {
              if (
                response !== undefined &&
                response.status !== undefined &&
                response.status.toLowerCase() === 'ok'
              ) {
                const dueDate = moment();

                const workflow = ticket.ticket_type;


                

                if (
                  workflow === RENEW_MEMBER_WORKFLOW || // 5685
                  workflow === ENROLL_MEMBER_WORKFLOW ||
                  workflow === ADD_MEMBER_WORKFLOW ||
                  workflow === CLAIMED_ENROLL_MEMBER_WORKFLOW ||
                  workflow === MANUAL_ENCODE_WORKFLOW
                ) {
                  dueDate.add(3, 'days');
                } else if (
                  workflow === VERIFY_MEMBER_WORKFLOW ||
                  workflow === VERIFY_WORKFLOW ||
                  workflow === CLAIMED_VERIFY_MEMBER_WORKFLOW ||
                  workflow === CLAIMED_VERIFY_WORKFLOW ||
                  workflow === VERIFY_RENEWAL_MEMBERLIST
                ) {
                  dueDate.add(10, 'days');
                } else if (
                  workflow === PRINTING_ID_WORKFLOW ||
                  workflow === CLAIMED_PRINTING_ID_WORKFLOW
                ) {
                  dueDate.add(15, 'days');
                } else {
                  dueDate.add(7, 'days');
                }

                const dateFormat = 'YYYY-MM-DD HH:mm:ss';
                const formattedDueDate = dueDate.format(dateFormat);
                let pmakerVariable: any = {
                  from_membership: true,
                  assigned_to_self: true,
                };
                if (
                  [
                    VERIFY_TERMINATION,
                    CLAIMED_VERIFY_TERMINATION,
                    UPLOAD_TERMINATION,
                    UPLOAD_TERMINATION_OCP,
                    CLAIMED_UPLOAD_TERMINATION_OCP,
                     
                  ].includes(workflow)
                ) {
                  pmakerVariable['pressed_reassign'] = false;
                }
                if(workflow === RENEW_MEMBER_WORKFLOW){
                   pmakerVariable['pressed_reassign'] = 0;
                }
                Processmaker.put(
                  'cases/' + ticket.case_id + '/variable',
                  pmakerVariable,
                  true,
                )
                  .then(() => {
                    Processmaker.put(
                      'cases/' + ticket.case_id + '/route-case',
                      {},
                    )
                      .then(() => {
                        Processmaker.get('cases/' + ticket.case_id).then(
                          res => {
                            let app_index = '0';
                            if (
                              res.current_task &&
                              res.current_task.length > 0
                            ) {
                              app_index = res.current_task[0].del_index;
                            }
                            Processmaker.get(
                              'cases/' +
                                ticket.case_id +
                                '/variables?app_index=' +
                                app_index,
                            ).then(res2 => {
                              const processedTicket: any = {
                                ticket_type: ticket.ticket_type,
                              };
                              if (
                                workflow === VERIFY_MEMBER_WORKFLOW ||
                                workflow === VERIFY_WORKFLOW ||
                                workflow === CLAIMED_VERIFY_MEMBER_WORKFLOW ||
                                workflow === CLAIMED_VERIFY_WORKFLOW
                              ) {
                                if (res2.member_upload_id) {
                                  processedTicket.item_id =
                                    res2.member_upload_id;
                                }
                              } else if (
                                workflow === CLAIMED_VERIFY_EDIT ||
                                workflow === VERIFY_EDIT
                              ) {
                                if (res2.member_edit_upload_id) {
                                  processedTicket.item_id =
                                    res2.member_edit_upload_id;
                                }
                              }
                              if (processedTicket.item_id) {
                                API.postMemberProcessedClaim(processedTicket);
                              }
                            });
                          },
                        );
                        let payload = {
                          status: 'OPEN',
                          due_date: formattedDueDate,
                        };
                        API.patchTicket(payload, ticket.case_id, true)
                          .then(() => {
                            this.setTicketDueDate(ticket, formattedDueDate);
                          })
                          .catch(() => {
                            this.setState({
                              loading: false,
                              isOpenModal: true,
                              modalTitle: 'Problem claiming ticket',
                              modalMessage:
                                'There may have been problems with your internet connection or the ticket has been claimed by another user. Please try claiming it again or try another ticket.',
                              next: () => {
                                this.fetchTickets('open');
                                this.fetchTickets('unassigned');
                              },
                            });
                          });
                      })
                      .catch(() => {
                        this.setState({
                          loading: false,
                          isOpenModal: true,
                          modalTitle: 'Problem claiming ticket',
                          modalMessage:
                            'There may have been problems with your internet connection or the ticket has been claimed by another user. Please try claiming it again or try another ticket.',
                          next: () => {
                            this.fetchTickets('open');
                            this.fetchTickets('unassigned');
                          },
                        });
                      });
                  })
                  .catch(() => {
                    this.setState({
                      loading: false,
                      isOpenModal: true,
                      modalTitle: 'Problem claiming ticket',
                      modalMessage:
                        'There may have been problems with your internet connection or the ticket has been claimed by another user. Please try claiming it again or try another ticket.',
                      next: () => {
                        this.fetchTickets('open');
                        this.fetchTickets('unassigned');
                      },
                    });
                  });
              } else {
                this.setState({
                  loading: false,
                  isOpenModal: true,
                  modalTitle: 'Problem claiming ticket',
                  modalMessage:
                    'There may have been problems with your internet connection or the ticket has been claimed by another user. Please try claiming it again or try another ticket.',
                  next: () => {
                    this.fetchTickets('open');
                    this.fetchTickets('unassigned');
                  },
                });
              }
            })
            .catch(e => {
              if (e.message === 'USER_DATA_NOT_FOUND') {
                this.setState({
                  loading: false,
                  modalTitle: 'User Data Error',
                  modalMessage:
                    'User data not found. Please try logging in again.',
                  isOpenModal: true,
                  next: () => {
                    // window.location.replace('../index.html#/');
                  },
                });
              } else {
                this.setState({
                  loading: false,
                });
              }
            });
        },
      );
    }
  };

  getUnassigned = () => {
    let { tListUnassignedTickets } = this.state;
    let user_location;
    Utils.StorageService('user_data').then(result => {
      if (result && result !== undefined) {
        for (const i in result) {
          if (result[i]['key'] === 'location') {
            user_location = result[i]['value'];
          }
        }
      }
 
    });

    Processmaker.get('cases/unassigned')
      .then(async response => {

        if (response !== undefined && response.error === undefined) {
          const temp_unassigned: any = [];
          const filtered_response = response.filter(item => {
            return (
              item.app_status === 'TO_DO' &&
              (item.app_tas_title === ENROLL_MEMBER_WORKFLOW ||
              item.app_tas_title === RENEW_MEMBER_WORKFLOW || // 5685
              item.app_tas_title === VERIFY_RENEWAL_MEMBERLIST ||
              item.app_tas_title === VERIFY_MEMBER_WORKFLOW ||
              item.app_tas_title === ADD_MEMBER_WORKFLOW ||
              item.app_tas_title === ADD_MEMBER_UNASSIGNED_WORKFLOW ||
              item.app_tas_title === VERIFY_WORKFLOW ||
              item.app_tas_title === PRINTING_ID_WORKFLOW ||
              item.app_tas_title === VERIFY_TERMINATION ||
              item.app_tas_title === UPLOAD_TERMINATION ||
              item.app_tas_title === VERIFY_SUSPENSION ||
              item.app_tas_title === VERIFY_SUSPENSION_COLLECTION || //~
                item.app_tas_title === UPLOAD_SUSPENSION ||
                item.app_tas_title === BATCH_EDIT ||
                item.app_tas_title === VERIFY_EDIT ||
                item.app_tas_title === VERIFY_DEPENDENTS ||
                // item.app_tas_title === CLAIMED_VERIFY_DEPENDENTS ||
                item.app_tas_title === BATCH_EDIT_OCP ||
                item.app_tas_title === MANUAL_RENEWAL ||
                item.app_tas_title === MATCH_PERSON_PROFILE ||
                item.app_tas_title === APPROVE_MEMBER_CHANGE_OCP ||
                item.app_tas_title === REPRINT_CARD_OCP ||
                item.app_tas_title === VOID_MASTERLIST ||
                // item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                // item.app_tas_title === UPLOAD_VOID_MASTERLIST ||
                item.app_tas_title === TRANSMITTAL_REQUEST ||
                item.app_tas_title === UPLOAD_TERMINATION_OCP)
            );
          });

          if (filtered_response.length > 0) {
            const getDateUpdated = ticketInfo => {
              const dateUpdated = _.get(ticketInfo, 'app_update_date', null);
              if (_.isNil(dateUpdated)) {
                return '';
              }

              const dateUpdatedMoment: moment.Moment = moment(
                dateUpdated,
                'YYYY-MM-DD HH:mm:ss',
                true,
              );
              if (_.isNil(dateUpdated) || !dateUpdatedMoment.isValid()) {
                return '';
              }

              return dateUpdatedMoment.format('MMM DD, YYYY');
            };
            const currUser = await API.getUserDataFromDb().then(
              res => res.loginUsername,
            );
            const currUserFullName = `${localStorage.getItem(
              'last_name',
            )} ${localStorage.getItem('first_name')}`;

         
            for (
              let index = 0;
              index < filtered_response.length && temp_unassigned.length < 30;
              index++
            ) {

              let tListTicketId =
                tListUnassignedTickets[index] &&
                tListUnassignedTickets[index]['ticket_id']
                  ? tListUnassignedTickets[index]['ticket_id']
                  : '';
              let itemIdx = filtered_response
                .map(fData => fData['app_number'])
                .indexOf(tListTicketId);

              if (itemIdx !== -1) {
                const item = filtered_response[itemIdx];
     
                if (
                  item['app_number'] &&
                  temp_unassigned
                    .map(data => data['ticket_id'])
                    .indexOf(item['app_number']) === -1
                ) {
                  let due_date = moment(item.del_task_due_date).format(
                    'MMM DD, YYYY',
                  );
                  if (item.app_tas_title == ADD_MEMBER_UNASSIGNED_WORKFLOW) {
                    item.app_tas_title = ADD_MEMBER_WORKFLOW;
                  }

                  if (item.app_tas_title == BATCH_EDIT_OCP) {
                    item.app_tas_title = BATCH_EDIT;
                  }

                  let temp_opens_ticket: any = {
                    ticket_id: item.app_number,
                    due: due_date,
                    ticket_type: item.app_tas_title, // 5685
                    client_name: item.app_title ? item.app_title : '',
                    client_id: '',
                    ticket_item: item,
                    case_id: item.app_uid,
                    proposal_id: '',
                    app_index: item.del_index,
                    // Replaced by task MS770, used previous user name
                    // updated_by: item.app_del_previous_user,
                    updated_by:
                      item.previous_usr_username &&
                      item.previous_usr_username !== '' &&
                      item.previous_usr_username != currUser
                        ? item.previous_usr_username
                        : item.app_current_user
                        ? item.app_current_user
                        : currUserFullName,
                    isOcp: item.is_ocp,
                    date_updated: getDateUpdated(item),
                  };

                  if (
                    item.app_tas_title === RENEW_MEMBER_WORKFLOW || // 5685
                    item.app_tas_title === VERIFY_RENEWAL_MEMBERLIST ||
                    item.app_tas_title === ADD_MEMBER_WORKFLOW ||
                    item.app_tas_title === ADD_MEMBER_UNASSIGNED_WORKFLOW ||
                    item.app_tas_title === BATCH_EDIT_OCP ||
                    item.app_tas_title === BATCH_EDIT ||
                    item.app_tas_title === PRINTING_ID_WORKFLOW ||
                    item.app_tas_title === MANUAL_RENEWAL ||
                    item.app_tas_title === MATCH_PERSON_PROFILE ||
                    item.app_tas_title === REPRINT_CARD_OCP ||
                    item.app_tas_title === VOID_MASTERLIST ||
                    item.app_tas_title === TRANSMITTAL_REQUEST ||
                    item.app_tas_title === VERIFY_WORKFLOW ||
                    item.app_tas_title === ENROLL_MEMBER_WORKFLOW ||
                    item.app_tas_title === VERIFY_TERMINATION ||
                    // item.app_tas_title === VOID_MASTERLIST
                    // ||
                    // item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                    item.app_tas_title === UPLOAD_VOID_MASTERLIST ||
                    item.app_tas_title === UPLOAD_TERMINATION_OCP
                  ) {
                    let parsed_app_title;
                    try {
                      if (item.app_tas_title === VOID_MASTERLIST) {
                        item.app_title = tListUnassignedTickets[index].client;
                      } else if (
                        [
                          CLAIMED_UPLOAD_TERMINATION_OCP,
                          UPLOAD_TERMINATION_OCP,
                        ].includes(item.app_tas_title)
                      ) {
                        temp_opens_ticket.ticket_type = UPLOAD_TERMINATION;
                      } else {
                        if (
                          item.app_title &&
                          typeof item.app_title === 'string'
                        ) {
                          item.app_title = item.app_title.replace('\\', '');
                        }
                      }

                      parsed_app_title = JSON.parse(item.app_title);
            
                      temp_opens_ticket.client_name =
                        parsed_app_title.client_name;
                      temp_opens_ticket.client_id = parsed_app_title.client_id;
                    } catch (error) {
             
                      if (tListUnassignedTickets[index].client) {
                        temp_opens_ticket.client_name =
                          tListUnassignedTickets[index].client;
                      } else if (
                        [
                          CLAIMED_UPLOAD_TERMINATION_OCP,
                          UPLOAD_TERMINATION_OCP,
                        ].includes(item.app_tas_title)
                      ) {
                        temp_opens_ticket.ticket_type = UPLOAD_TERMINATION;
                      } else {
                        temp_opens_ticket.client_name = item.app_title;
                      }

                      if (item.client_id) {
                        temp_opens_ticket.client_id = item.client_id;
                      } else if (tListUnassignedTickets[index].client_id) {
                        temp_opens_ticket.client_id =
                          tListUnassignedTickets[index].client_id;
                      }
                    }
                  }

                  temp_unassigned.push(temp_opens_ticket);

                  /** NEW IMPLEMENTATION */
                  // this.setState({
                  //   unassigned_tickets: temp_unassigned
                  // })
                  /** */
                }
              }
            }

            //FILTER TICKETS DEPENDING ON USER ACCOUNT LOCATION
            let servicingBranch;
            let servicingBranches;
            let temp_unassigned_final: any = [];

            //GET ALL CLIENTS
            Promise.all([API.getClientsData()]).then(([resp]) => {
          
              //MAP FILTERED TICKETS
              temp_unassigned.map(unassigned => {
                if (unassigned.client_id !== '') {
                  let responseFindID = resp.find(obj => {
                    return obj._id === unassigned.client_id;
                  });
                  if (responseFindID) {
                    servicingBranch = responseFindID.servicing_branch;
                    servicingBranches = responseFindID.servicing_branches;
                    if (servicingBranch !== null) {
               
                      if (servicingBranch === user_location) {
                      
                        if (temp_unassigned_final.length < 10) {
                          temp_unassigned_final.push(unassigned);
                        }
                      } else if (servicingBranches) {
                 
                        if (
                          servicingBranches.account_management ===
                            user_location ||
                          servicingBranches.billing === user_location ||
                          servicingBranches.collection === user_location
                        ) {
                   
                          if (temp_unassigned_final.length < 10) {
                            temp_unassigned_final.push(unassigned);
                          }
                        } else {
                          if (servicingBranches.member_processing) {
                   
                            if (
                              servicingBranches.member_processing.some(
                                loc => loc === user_location,
                              )
                            ) {
                              // console.log('checkcondition1B');
                              if (temp_unassigned_final.length < 10) {
                                temp_unassigned_final.push(unassigned);
                              }
                            }
                          }
                          if (servicingBranches.reimbursement) {
                            //condition 1C (clientId): check if reimbursement === user_location
                            if (
                              servicingBranches.reimbursement.some(
                                loc => loc === user_location,
                              )
                            ) {
                              // console.log('checkcondition1C');
                              if (temp_unassigned_final.length < 10) {
                                temp_unassigned_final.push(unassigned);
                              }
                            }
                          }
                        }
                      }
                    } else if (servicingBranches) {
                      //condition 2 (clientId) (servicingBranch is null) :
                      //check if each servicingBranches == user location
                      if (
                        servicingBranches.account_management ===
                          user_location ||
                        servicingBranches.billing === user_location ||
                        servicingBranches.collection === user_location
                      ) {
  
                        if (temp_unassigned_final.length < 10) {
                          temp_unassigned_final.push(unassigned);
                        }
                      } else {
                        if (servicingBranches.member_processing) {
                          //condition 2B (clientId): check if member_processing === user_location
                          if (
                            servicingBranches.member_processing.some(
                              loc => loc === user_location,
                            )
                          ) {
                            // console.log(
                            //   'checkcondition2A',
                            //   temp_unassigned_final.length,
                            // );
                            if (temp_unassigned_final.length < 10) {
                              temp_unassigned_final.push(unassigned);
                            }
                          }
                        }
                        if (servicingBranches.reimbursement) {
                          //condition 2C (clientId): check if reimbursement === user_location
                          if (
                            servicingBranches.reimbursement.some(
                              loc => loc === user_location,
                            )
                          ) {
                            // console.log(
                            //   'checkcondition2B',
                            //   temp_unassigned_final.length,
                            // );
                            if (temp_unassigned_final.length < 10) {
                              temp_unassigned_final.push(unassigned);
                            }
                          }
                        }
                      }
                    }
                    this.setState({
                      unassigned_tickets: temp_unassigned_final,
                    });
                  }
                } else {
                  //If ticket has no client id -> check by client name
                  let responseFindName = resp.find(obj => {
                    return obj.registered_name === unassigned.client_name;
                  });
                  if (responseFindName) {
                    servicingBranch = responseFindName.servicing_branch;
                    servicingBranches = responseFindName.servicing_branches;
                    if (servicingBranch !== null) {
                      // condition 3 (clientname) :
                      // check if servicingBranch == user location
                      if (servicingBranch === user_location) {
                        // console.log('checkcondition3');
                        if (temp_unassigned_final.length < 10) {
                          temp_unassigned_final.push(unassigned);
                        }
                      } else if (servicingBranches) {
                        //condition 3A (clientname): check if servicingBranches === user_location
                        if (
                          servicingBranches.account_management ===
                            user_location ||
                          servicingBranches.billing === user_location ||
                          servicingBranches.collection === user_location
                        ) {
                          // console.log('checkcondition3A');
                          if (temp_unassigned_final.length < 10) {
                            temp_unassigned_final.push(unassigned);
                          }
                        } else {
                          if (servicingBranches.member_processing) {
                            //condition 3B (clientname): check if member_processing === user_location
                            if (
                              servicingBranches.member_processing.some(
                                loc => loc === user_location,
                              )
                            ) {
                              // console.log(
                              //   'checkcondition3B',
                              //   temp_unassigned_final.length,
                              // );
                              if (temp_unassigned_final.length < 10) {
                                temp_unassigned_final.push(unassigned);
                              }
                            }
                          }
                          if (servicingBranches.reimbursement) {
                            //condition 3C (clientname): check if reimbursement === user_location
                            if (
                              servicingBranches.reimbursement.some(
                                loc => loc === user_location,
                              )
                            ) {
                              // console.log(
                              //   'checkcondition3C',
                              //   temp_unassigned_final.length,
                              // );
                              if (temp_unassigned_final.length < 10) {
                                temp_unassigned_final.push(unassigned);
                              }
                            }
                          }
                        }
                      }
                    } else if (servicingBranches) {
                      //  condition 4 (clientName) (servicingBranch is null):
                      //  check if each servicingBranches == user location
                      if (
                        servicingBranches.account_management ===
                          user_location ||
                        servicingBranches.billing === user_location ||
                        servicingBranches.collection === user_location
                      ) {
                        // console.log('checkcondition4');
                        if (temp_unassigned_final.length < 10) {
                          temp_unassigned_final.push(unassigned);
                        }
                      } else {
                        if (servicingBranches.member_processing) {
                          //condition 4A (clientname): check if member_processing === user_location
                          if (
                            servicingBranches.member_processing.some(
                              loc => loc === user_location,
                            )
                          ) {
                            // console.log('checkcondition4A');
                            if (temp_unassigned_final.length < 10) {
                              temp_unassigned_final.push(unassigned);
                            }
                          }
                        }
                        if (servicingBranches.reimbursement) {
                          //condition 4B (clientname): check if reimbursement === user_location
                          if (
                            servicingBranches.reimbursement.some(
                              loc => loc === user_location,
                            )
                          ) {
                            if (temp_unassigned_final.length < 10) {
                              temp_unassigned_final.push(unassigned);
                            }
                          }
                        }
                      }
                    }

                    this.setState({
                      unassigned_tickets: temp_unassigned_final,
                    });
                  }
                }
              }); // End of tickets map
            });

          } else {
            this.setState({
              unassigned_tickets: [],
       
            });
          }
        } else if (response !== undefined && response.error !== undefined) {
          this.setState({
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        }
      })
      .catch(e => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.setState({
            loading: false,
          });

        }
      });
  };

  getOpenTickets = () => {
 
    let { tListOpenTickets } = this.state;

    let url: string = `cases?sort=APP_CACHE_VIEW.APP_UPDATE_DATE`;
    // 5685
    Processmaker.get(url)
      .then(async (response, index) => {

        if (response !== undefined && response.error === undefined) {
          // //****start of verify member tickets counter handling****//
          let filteredvmt: any[] = [];
        
          tListOpenTickets.map(data => {

            if (
              [
                'claimed verify member',
                'claimed verify memberlist',
                'verify member',
                'verify memberlist',
              ].includes(data.ticket_type.toLowerCase()) &&
              data.status === 'OPEN'
            )
              filteredvmt.push(data);
          });
          // this.setState({ vmticketcount: filteredvmt.length });
          //****end****//
          let temp_tickets_array: any = [];
          let temp_enroll_tickets: any = [];
          let temp_validate_tickets: any = [];
          let temp_persons_to_match_tickets: any = [];
          let temp_print_id_card_tickets: any = [];
          let temp_terminate_member_tickets: any = [];
          let temp_suspend_member_tickets: any = [];
          const filtered_response: any[] = response.filter(item => {
            // 5685
            return (
              item.app_status === 'TO_DO' &&
              (item.app_tas_title === ENROLL_MEMBER_WORKFLOW ||
              // 5685
              item.app_tas_title === CLAIMED_RENEW_MEMBER_WORKFLOW || 
              item.app_tas_title === UPLOAD_RENEW_MEMBER_WORKFLOW || 
              item.app_tas_title === AUTO_RENEW_MEMBER_WORKFLOW || 
              item.app_tas_title === EXCEPTION_MANUAL_RENEW_MEMBER_WORKFLOW || 
              item.app_tas_title === MANUAL_RENEW_MEMBER_WORKFLOW || 
              item.app_tas_title === VERIFY_RENEWAL_MEMBERLIST ||
              // 5685
              item.app_tas_title === CLAIMED_ENROLL_MEMBER_WORKFLOW ||        
              item.app_tas_title === VERIFY_MEMBER_WORKFLOW ||
              item.app_tas_title === CLAIMED_VERIFY_MEMBER_WORKFLOW ||
              item.app_tas_title === ADD_MEMBER_WORKFLOW ||
              item.app_tas_title === MANUAL_ENCODE_WORKFLOW ||
              item.app_tas_title === VERIFY_WORKFLOW ||
              item.app_tas_title === CLAIMED_VERIFY_WORKFLOW ||
              item.app_tas_title === PRINTING_ID_WORKFLOW ||
              item.app_tas_title === CLAIMED_PRINTING_ID_WORKFLOW ||
              item.app_tas_title === CLAIMED_VERIFY_TERMINATION ||
              item.app_tas_title === VERIFY_TERMINATION ||
              item.app_tas_title === UPLOAD_TERMINATION ||
              item.app_tas_title === CLAIMED_VERIFY_SUSPENSION ||
              item.app_tas_title === VERIFY_SUSPENSION ||
              item.app_tas_title === CLAIMED_VERIFY_SUSPENSION_COLLECTION || //~
              item.app_tas_title === VERIFY_SUSPENSION_COLLECTION || //~
                item.app_tas_title === UPLOAD_SUSPENSION ||
                item.app_tas_title === BATCH_EDIT ||
                item.app_tas_title === VERIFY_EDIT ||
                item.app_tas_title === CLAIMED_VERIFY_EDIT ||
                item.app_tas_title === VERIFY_DEPENDENTS ||
                item.app_tas_title === CLAIMED_VERIFY_DEPENDENTS ||
                item.app_tas_title === MANUAL_RENEWAL ||
                item.app_tas_title === FINISHED_MANUAL_RENEWAL ||
                item.app_tas_title === MATCH_PERSON_PROFILE ||
                item.app_tas_title === CLAIMED_MATCH_PERSON_PROFILE ||
                item.app_tas_title === TRANSMITTAL_REQUEST ||
                item.app_tas_title === CLAIMED_TRANSMITTAL_REQUEST ||
                item.app_tas_title === APPROVE_MEMBER_CHANGE_OCP ||
                item.app_tas_title === CLAIMED_APPROVE_MEMBER_CHANGE_OCP ||
                item.app_tas_title === CLAIMED_REPRINT_CARD_OCP ||
                item.app_tas_title === REPRINT_CARD_OCP ||
                item.app_tas_title === VOID_MASTERLIST ||
                item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                item.app_tas_title === UPLOAD_VOID_MASTERLIST ||
                item.app_tas_title === UPLOAD_TERMINATION_OCP ||
                item.app_tas_title === CLAIMED_UPLOAD_TERMINATION_OCP)
            );
          });

       
          this.setState({
            loading: true,
            // openTicketsCount: filtered_response.length,
          });

          const getDateUpdated = ticketInfo => {
            const dateUpdated = _.get(ticketInfo, 'app_update_date', null);
            if (_.isNil(dateUpdated)) {
              return '';
            }

            const dateUpdatedMoment: moment.Moment = moment(
              dateUpdated,
              'YYYY-MM-DD HH:mm:ss',
              true,
            );
            if (_.isNil(dateUpdated) || !dateUpdatedMoment.isValid()) {
              return '';
            }

            return dateUpdatedMoment.format('MMM DD, YYYY');
          };

          const currUser = await API.getUserDataFromDb().then(
            res => res.loginUsername,
          );
          const currUserFullName = `${localStorage.getItem(
            'last_name',
          )} ${localStorage.getItem('first_name')}`;
          for (
            let index = 0;
            index < filtered_response.length && temp_tickets_array.length < 10;
            index++
          ) {
            let tListTicketId =
              tListOpenTickets[index] && tListOpenTickets[index]['ticket_id']
                ? tListOpenTickets[index]['ticket_id'].trim()
                : '';
         

            let itemIdx = filtered_response
              .map(fData => fData['app_number'])
              .indexOf(tListTicketId);
        
      

            if (itemIdx !== -1) {
              const item = filtered_response[itemIdx];
              
              const due_date =
                tListOpenTickets[index].hasOwnProperty('due_date') &&
                tListOpenTickets[index].due_date !== ''
                  ? moment(tListOpenTickets[index].due_date).format(
                      'MMM DD, YYYY',
                    )
                  : moment(item.del_task_due_date).format('MMM DD, YYYY');
              //ANN - void : display tickets processing dashboard debug
              if (item.app_tas_title === CLAIMED_PRINTING_ID_WORKFLOW) {
                item.app_tas_title = PRINTING_ID_WORKFLOW;
              } else if (
                item.app_tas_title === CLAIMED_RENEW_MEMBER_WORKFLOW ||
                item.app_tas_title === MANUAL_RENEW_MEMBER_WORKFLOW ||
                item.app_tas_title === UPLOAD_RENEW_MEMBER_WORKFLOW ||
                item.app_tas_title === AUTO_RENEW_MEMBER_WORKFLOW ||
                item.app_tas_title === EXCEPTION_MANUAL_RENEW_MEMBER_WORKFLOW
              ) {
                // 5685
                  item.app_tas_title = RENEW_MEMBER_WORKFLOW;
                }else if (item.app_tas_title === CLAIMED_VERIFY_WORKFLOW) {
                item.app_tas_title = VERIFY_WORKFLOW;
              } else if (
                item.app_tas_title === CLAIMED_VERIFY_MEMBER_WORKFLOW
              ) {
                item.app_tas_title = VERIFY_MEMBER_WORKFLOW;
              } else if (
                item.app_tas_title === CLAIMED_ENROLL_MEMBER_WORKFLOW
              ) {
                item.app_tas_title = ENROLL_MEMBER_WORKFLOW;
              } else if (item.app_tas_title === CLAIMED_VERIFY_TERMINATION) {
                item.app_tas_title = VERIFY_TERMINATION;
              } else if (item.app_tas_title === CLAIMED_VERIFY_SUSPENSION) {
                item.app_tas_title = VERIFY_SUSPENSION;
              } else if (
                item.app_tas_title === CLAIMED_VERIFY_SUSPENSION_COLLECTION
              ) {
                //~
                item.app_tas_title = VERIFY_SUSPENSION_COLLECTION;
              } else if (item.app_tas_title === CLAIMED_VERIFY_EDIT) {
                item.app_tas_title = VERIFY_EDIT;
              } else if (item.app_tas_title === CLAIMED_VERIFY_DEPENDENTS) {
                item.app_tas_title = VERIFY_DEPENDENTS;
              } else if (item.app_tas_title === BATCH_EDIT_OCP) {
                item.app_tas_title = BATCH_EDIT;
              } else if (item.app_tas_title === CLAIMED_MATCH_PERSON_PROFILE) {
                item.app_tas_title = MATCH_PERSON_PROFILE;
              } else if (item.app_tas_title === CLAIMED_TRANSMITTAL_REQUEST) {
                item.app_tas_title = TRANSMITTAL_REQUEST;
              } else if (
                item.app_tas_title === CLAIMED_APPROVE_MEMBER_CHANGE_OCP
              ) {
                item.app_tas_title = APPROVE_MEMBER_CHANGE_OCP;
              } else if (item.app_tas_title === CLAIMED_REPRINT_CARD_OCP) {
                item.app_tas_title = REPRINT_CARD_OCP;
              } else if (item.app_tas_title === CLAIMED_VOID_MASTERLIST) {
                item.app_tas_title = VOID_MASTERLIST;
                // item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                // item.app_tas_title === UPLOAD_VOID_MASTERLIST
                // } else if(item.app_tas_title === CLAIMED_VOID_MASTERLIST) {
                //   item.app_tas_title = VOID_MASTERLIST;
              } else if (item.app_tas_title === UPLOAD_VOID_MASTERLIST) {
                item.app_tas_title = UPLOAD_VOID_MASTERLIST;
              } else if (
                [
                  CLAIMED_UPLOAD_TERMINATION_OCP,
                  UPLOAD_TERMINATION_OCP,
                ].includes(item.app_tas_title)
              ) {
                item.app_tas_title = UPLOAD_TERMINATION;
              }
              let tListIdx = tListOpenTickets
                .map(tData => tData['ticket_id'])
                .indexOf(item.app_number);
              const temp_opens_ticket: any = {
                _id:
                  itemIdx !== -1 &&
                  tListOpenTickets[tListIdx] &&
                  tListOpenTickets[tListIdx]['_id']
                    ? tListOpenTickets[tListIdx]['_id']
                    : '',
                ticket_id: item.app_number,
                due: due_date,
                ticket_type: item.app_tas_title,
                client_name: '',
                client_id: '',
                ticket_item: item,
                case_id: item.app_uid,
                proposal_id: '',
                status: '',
                member_upload_id: null,
                member_void_upload_id: '',
                // Replaced by task MS770, used previous user name
                // updated_by: item.app_del_previous_user && item.app_del_previous_user == "" ? item.app_del_previous_user : item.app_current_user,
                updated_by:
                  item.previous_usr_username &&
                  item.previous_usr_username !== '' &&
                  item.previous_usr_username != currUser
                    ? item.previous_usr_username
                    : item.app_current_user
                    ? item.app_current_user
                    : currUserFullName,
                date_updated: getDateUpdated(item),
                last_updated:
                  itemIdx !== -1 &&
                  tListOpenTickets[tListIdx] &&
                  tListOpenTickets[tListIdx]['last_updated'] &&
                  moment(tListOpenTickets[tListIdx]['last_updated']).isValid()
                    ? moment(
                        moment.parseZone(
                          tListOpenTickets[tListIdx]['last_updated'],
                        ),
                      ).format('hh:mma')
                    : '',
                batch_name: '',
              };
              const app_index = item.del_index;
    
              try {
                const response3 =
                  itemIdx !== -1 &&
                  (item['app_number'] &&
                    temp_tickets_array
                      .map(data => data['ticket_id'])
                      .indexOf(item['app_number']) === -1)
                    ? await Processmaker.get(
                        'cases/' +
                          item.app_uid +
                          '/variables?app_index=' +
                          app_index,
                      )
                    : undefined;
               
                if (response3 !== undefined) {
                  if (
                    (item.app_tas_title === CLAIMED_MATCH_PERSON_PROFILE ||
                      item.app_tas_title === MATCH_PERSON_PROFILE) &&
                    response3.batch_name !== undefined
                  ) {
                    temp_opens_ticket.batch_name = response3.batch_name;
                  }

                  if (response3.ticket_due_date !== undefined) {
                    const dueDate = moment(
                      response3.ticket_due_date,
                      'YYYY-MM-DD HH:mm:ss',
                      true,
                    );
                    if (dueDate.isValid()) {
                      temp_opens_ticket.due = dueDate.format('MMM DD, YYYY');
                    }
                  }

                  if (response3.member_upload_id !== undefined) {
                    temp_opens_ticket.member_upload_id =
                      response3.member_upload_id;
                    temp_opens_ticket.member_void_upload_id =
                      temp_opens_ticket.member_void_upload_id;
                    item['member_upload_id'] =
                      temp_opens_ticket.member_upload_id; 
                  } 

                  if (response3.client_name !== undefined) {
                    temp_opens_ticket.client_name = response3.client_name;
                  }
              
                  if (
                    (item.app_tas_title === ENROLL_MEMBER_WORKFLOW ||
                      item.app_tas_title  === RENEW_MEMBER_WORKFLOW || // 5685
                      item.app_tas_title  === ENROLL_MEMBER_WORKFLOW ||
                      item.app_tas_title  === ADD_MEMBER_WORKFLOW ||
                      item.app_tas_title  === CLAIMED_ENROLL_MEMBER_WORKFLOW ||
                      item.app_tas_title  === MANUAL_ENCODE_WORKFLOW ||
                      item.app_tas_title === CLAIMED_ENROLL_MEMBER_WORKFLOW ||
                      item.app_tas_title === UPLOAD_VOID_MASTERLIST) && //UPLOAD_VOID_MASTERLIST VOID_MASTERLIST
                    response3.client_id !== undefined
                  ) {
                    temp_opens_ticket.client_id = response3.client_id;
                    item.ticket_details = response3
                    if (response3.page_type !== undefined) {
                      temp_opens_ticket.page_type = response3.page_type;
                    }
                    if (!_.isNil(response3.upload_status)) {
                      temp_opens_ticket.status = response3.upload_status;
                      // response3.member_void_upload_id =  temp_opens_ticket.member_upload_id
                      temp_opens_ticket.member_void_upload_id =
                        response3.member_void_upload_id;
                      // response3.member_upload_id =  temp_opens_ticket.member_upload_id
                    }
                    if (response3.mother_contract_id !== undefined) {
                      temp_opens_ticket.proposal_id =
                        response3.mother_contract_id;
                      temp_tickets_array.push(temp_opens_ticket);
                      temp_enroll_tickets.push(temp_opens_ticket);
                    }
                

                  
                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                    });
                  } else if (
                    (item.app_tas_title === VERIFY_MEMBER_WORKFLOW ||
                      item.app_tas_title === CLAIMED_VERIFY_MEMBER_WORKFLOW ||
                      item.app_tas_title === VERIFY_WORKFLOW ||
                      item.app_tas_title === CLAIMED_VERIFY_WORKFLOW ||
                      item.app_tas_title === VOID_MASTERLIST) &&
                    response3.member_upload_id !== undefined
                  ) {
                   
                    temp_opens_ticket.client_id = response3.member_upload_id;
                    temp_opens_ticket['validation_finished'] = false;
                    response3.member_upload_id =
                      temp_opens_ticket.member_upload_id;

              
                    this.setState({
                      member_upload_id: response3.member_upload_id,
                    });

                    if (response3.validation_finished !== undefined) {
                      temp_opens_ticket['validation_finished'] =
                        response3.validation_finished;
                  
                    }
                    if (!_.isNil(response3.from_ocp)) {
                      temp_opens_ticket.from_ocp = response3.from_ocp;
                    }
                    temp_tickets_array.push(temp_opens_ticket);
                    temp_validate_tickets.push(temp_opens_ticket);
                    temp_enroll_tickets.push(temp_opens_ticket);
                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                    });
                  } else if (
                    item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                    item.app_tas_title === VOID_MASTERLIST ||
                    item.app_tas_title === UPLOAD_VOID_MASTERLIST
                  ) {
                    temp_opens_ticket.client_id = response3.client_id;
                    temp_opens_ticket['member_void_upload_id'] =
                      response3.member_void_upload_id;
                    temp_opens_ticket['validation_finished'] = false;

                    if (response3.validation_finished !== undefined) {
                      temp_opens_ticket['validation_finished'] =
                        response3.validation_finished;
                    }
                    if (
                      this.state.role === 'APD_VERIFIER GROUP' ||
                      this.state.role === 'APD_MANAGER GROUP'
                    ) {
                      temp_tickets_array.push(temp_opens_ticket);
                      temp_validate_tickets.push(temp_opens_ticket);

                      this.setState({
                        open_tickets: temp_tickets_array,
                        validate_members: temp_validate_tickets,
                      });

                    } else {
                      //ANN - dev should double check roles ticket view per users
                  
                      temp_tickets_array.push(temp_opens_ticket);
                      temp_validate_tickets.push(temp_opens_ticket);

                      this.setState({
                        open_tickets: temp_tickets_array,
                        validate_members: temp_validate_tickets,
                      });

                    
                    }
                  } else if (
                    (item.app_tas_title === ADD_MEMBER_WORKFLOW ||
                    item.app_tas_title === MANUAL_ENCODE_WORKFLOW ||
                    item.app_tas_title === ADD_MEMBER_UNASSIGNED_WORKFLOW ||
                    item.app_tas_title === UPLOAD_TERMINATION ||
                    item.app_tas_title === VERIFY_TERMINATION ||
                    item.app_tas_title === CLAIMED_VERIFY_TERMINATION ||
                    item.app_tas_title === UPLOAD_SUSPENSION ||
                    item.app_tas_title === VERIFY_SUSPENSION ||
                    item.app_tas_title === CLAIMED_VERIFY_SUSPENSION ||
                    item.app_tas_title === VERIFY_SUSPENSION_COLLECTION || //~
                    item.app_tas_title ===
                      CLAIMED_VERIFY_SUSPENSION_COLLECTION || //~
                      item.app_tas_title === BATCH_EDIT ||
                      item.app_tas_title === VERIFY_EDIT ||
                      item.app_tas_title === CLAIMED_VERIFY_EDIT ||
                      item.app_tas_title === MANUAL_RENEWAL ||
                      item.app_tas_title === TRANSMITTAL_REQUEST ||
                      item.app_tas_title === APPROVE_MEMBER_CHANGE_OCP ||
                      item.app_tas_title ===
                        CLAIMED_APPROVE_MEMBER_CHANGE_OCP ||
                      item.app_tas_title === CLAIMED_REPRINT_CARD_OCP ||
                      item.app_tas_title === REPRINT_CARD_OCP ||
                      item.app_tas_title === VOID_MASTERLIST ||
                      item.app_tas_title === CLAIMED_VOID_MASTERLIST ||
                      item.app_tas_title === UPLOAD_VOID_MASTERLIST ||
                      item.app_tas_title === UPLOAD_TERMINATION_OCP ||
                      item.app_tas_title === CLAIMED_UPLOAD_TERMINATION_OCP) &&
                    // ||
                    // item.app_tas_title === VERIFY_DEPENDENTS ||
                    // item.app_tas_title === CLAIMED_VERIFY_DEPENDENTS
                    !_.isEmpty(response3.client_name) &&
                    !_.isEmpty(response3.client_id)
                  ) {
                    temp_opens_ticket.client_id = response3.client_id;
                    if (response3.page_type !== undefined) {
                      temp_opens_ticket.page_type = response3.page_type;
                    }
                    if (!_.isNil(response3.upload_status)) {
                      temp_opens_ticket.status = response3.upload_status;
                    }
                    if (response3.mother_contract_id !== undefined) {
                      temp_opens_ticket.proposal_id =
                        response3.mother_contract_id;
                    }
                    if (!_.isNil(response3.from_ocp)) {
                      temp_opens_ticket.from_ocp = response3.from_ocp;
                    }
                    if (!_.isNil(response3.is_ocp)) {
                      temp_opens_ticket.is_ocp = response3.is_ocp;
                    }
                    if (item.app_tas_title === UPLOAD_TERMINATION) {
                      if (
                        this.state.role === 'APD_ENCODER GROUP' ||
                        this.state.role === 'APD_MANAGER GROUP'
                      )
                        temp_terminate_member_tickets.push(temp_opens_ticket);
                    } else if (item.app_tas_title === UPLOAD_SUSPENSION) {
                      if (
                        this.state.role === 'APD_ENCODER GROUP' ||
                        this.state.role === 'APD_MANAGER GROUP'
                      )
                        temp_suspend_member_tickets.push(temp_opens_ticket);
                    } else if (item.app_tas_title === VERIFY_TERMINATION) {
                      if (
                        this.state.role === 'APD_VERIFIER GROUP' ||
                        this.state.role === 'APD_MANAGER GROUP'
                      )
                        temp_terminate_member_tickets.push(temp_opens_ticket);
                    } else if (item.app_tas_title === VERIFY_SUSPENSION) {
                      if (
                        this.state.role === 'APD_VERIFIER GROUP' ||
                        this.state.role === 'APD_MANAGER GROUP'
                      )
                        temp_suspend_member_tickets.push(temp_opens_ticket);
                    } else if (
                      item.app_tas_title === VERIFY_SUSPENSION_COLLECTION
                    ) {
                      //~
                      if (
                        this.state.role === 'APD_VERIFIER GROUP' ||
                        this.state.role === 'APD_MANAGER GROUP'
                      )
                        temp_suspend_member_tickets.push(temp_opens_ticket);
                    } else if (item.app_tas_title === ADD_MEMBER_WORKFLOW) {
                      // temp_enroll_tickets.push(temp_opens_ticket)
                    } else if (item.app_tas_title === MANUAL_ENCODE_WORKFLOW) {
                      // temp_enroll_tickets.push(temp_opens_ticket)
                    } else if (item.app_tas_title === VOID_MASTERLIST) {
                    } else if (item.app_tas_title === CLAIMED_VOID_MASTERLIST) {
                    } else if (item.app_tas_title === UPLOAD_VOID_MASTERLIST) {
                    }
                    temp_tickets_array.push(temp_opens_ticket);
                    if (
                      item.app_tas_title !== UPLOAD_TERMINATION &&
                      item.app_tas_title !== VERIFY_TERMINATION &&
                      item.app_tas_title !== UPLOAD_SUSPENSION &&
                      item.app_tas_title !== VERIFY_SUSPENSION &&
                      item.app_tas_title !== VERIFY_SUSPENSION_COLLECTION && //~
                      item.app_tas_title !== CLAIMED_VERIFY_TERMINATION &&
                      item.app_tas_title !== CLAIMED_VERIFY_SUSPENSION &&
                      item.app_tas_title !==
                        CLAIMED_VERIFY_SUSPENSION_COLLECTION && //~
                      item.app_tas_title !== BATCH_EDIT &&
                      item.app_tas_title !== VERIFY_EDIT &&
                      item.app_tas_title !== CLAIMED_VERIFY_EDIT &&
                      item.app_tas_title !== MANUAL_RENEWAL &&
                      item.app_tas_title !== TRANSMITTAL_REQUEST &&
                      item.app_tas_title !== APPROVE_MEMBER_CHANGE_OCP &&
                      item.app_tas_title !==
                        CLAIMED_APPROVE_MEMBER_CHANGE_OCP &&
                      item.app_tas_title !== CLAIMED_REPRINT_CARD_OCP &&
                      item.app_tas_title !== REPRINT_CARD_OCP // &&
                      // item.app_tas_title !== VERIFY_DEPENDENTS &&
                      // item.app_tas_title !== CLAIMED_VERIFY_DEPENDENTS
                    ) {
                      temp_validate_tickets.push(temp_opens_ticket);
                      // temp_persons_to_match_tickets.push(temp_opens_ticket);
                    }

                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                      terminate_members: temp_terminate_member_tickets,
                      suspend_members: temp_suspend_member_tickets,
                    });
                  } else if (
                    (item.app_tas_title === PRINTING_ID_WORKFLOW ||
                      item.app_tas_title === CLAIMED_PRINTING_ID_WORKFLOW ||
                      item.app_tas_title === TRANSMITTAL_REQUEST ||
                      item.app_tas_title === CLAIMED_TRANSMITTAL_REQUEST) &&
                    !_.isEmpty(response3.client_name) &&
                    !_.isEmpty(response3.client_id)
                  ) {
                    temp_opens_ticket.client_id = response3.client_id;
                    if (response3.old_case_id) {
                      temp_opens_ticket.old_case_id = response3.old_case_id;
                    }
                    temp_tickets_array.push(temp_opens_ticket);
                    // MS888 (re-opened): do not add Print ID Card ticket to 'Memberlist to Validate' task tray
                  
                    temp_print_id_card_tickets.push(temp_opens_ticket);
                  

                    this.setState({
                      open_tickets: temp_tickets_array,
                      // MS888 (re-opened): do not add Print ID Card ticket to 'Memberlist to Validate' task tray
                     
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                      print_members: temp_print_id_card_tickets,
                    });
                  } else if (
                    (item.app_tas_title === CLAIMED_VERIFY_DEPENDENTS ||
                      item.app_tas_title === VERIFY_DEPENDENTS) &&
                    !_.isEmpty(response3.member_civil_status_group_id)
                  ) {
               
                    temp_opens_ticket.member_civil_status_group_id =
                      response3.member_civil_status_group_id;
                    temp_opens_ticket.client_id = response3.client_id;
                    temp_opens_ticket.client_name = response3.client_name;
                    temp_tickets_array.push(temp_opens_ticket);
               

                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                    });
                  } else if (
                    item.app_tas_title === CLAIMED_MATCH_PERSON_PROFILE ||
                    item.app_tas_title === MATCH_PERSON_PROFILE
                  ) {
                    temp_tickets_array.push(temp_opens_ticket);
                    temp_persons_to_match_tickets.push(temp_opens_ticket);

                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                    });
                  } else if (index === filtered_response.length - 1) {
             

                    this.setState({
                      open_tickets: temp_tickets_array,
                      validate_members: temp_validate_tickets,
                      enroll_members: temp_enroll_tickets,
                      persons_to_match: temp_persons_to_match_tickets,
                    });
                  }
                }

              
              } catch (e) {
         
              }
            } 
          }

          this.setState({
            loading: false,
          });
        }
      })
      .catch(e => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.setState({
            loading: false,
          });
         
        }
      });
  };

  fetchTicketTypeChecker = () => {
    let inq: any[] = [];
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS57',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      inq.push('Generate Transmittal Request');
      inq.push('Claimed Generate Transmittal Request');
    }
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS81',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      inq.push('Enroll Memberlist');
      inq.push(RENEW_MEMBER_WORKFLOW)
      inq.push('Enroll Member');
      // inq.push('Void Masterlist');
      inq.push('Upload Void Masterlist');
      // inq.push('Claimed Void Masterlist');
    }
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS82',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Add Member');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS83',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Manual Encode');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS85',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      inq.push('Verify Memberlist');
      inq.push('Verify Member');
      inq.push('Verify Renewal Memberlist');
      inq.push('Verify Dependents');
      inq.push('Match Person Profile');
      inq.push('Approve Member Changes from OCP');
      inq.push('Void Masterlist');
      inq.push('Upload Void Masterlist');
    }
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS86',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      inq.push('Print Card IDs');
      inq.push('Claimed Print Card IDs');
      inq.push('Reprint Card IDs from OCP');
    }
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS87',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      inq.push('Batch Edit');
      inq.push('Verify Edit');
    }
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS88',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Upload Termination', 'Upload Termination OCP');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS89',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Upload Suspension');
    // if (MS_GlobalFunction.checkUserRbacByPolicy('MS90', this.state.currentUserData, this.state.loading)) inq.push('Batch Edit');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS91',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Verify Termination');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS92',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Verify Suspension');
    if (
      MS_GlobalFunction.checkUserRbacByPolicy(
        'MS93',
        this.state.currentUserData,
        this.state.loading,
      )
    )
      inq.push('Suspend Members from Collection');

    return inq;
  };

  fetchTickets = async (ticketStatus: string) => {
  
    const sort = {
      // urgency: '',
      status: '',
      last_updated: ['desc'],
      _id: ['desc'],
      ticket_id_no: ['desc'],
      due_date: ['desc'],
      ticket_type: '',
      batch_no: '',
      client: '',
      assignee: '',
    };
    const offset = 100 * 0;
    const limit = 100;

    // CONSTRUCT AND FILTER

    // status tab filter
    let filter: any = { and: [] };
    let filterStatus: any = { and: [{ status: undefined }] };
    let filterStatus2: any = { and: [{ status: undefined }] };
    let filterStatus3: any = { and: [{ status: undefined }] };
    let allFilter: any = { or: [] };
    let isViewUnassigned =
      ticketStatus.toUpperCase() === 'UNASSIGNED' ? true : false;

    if (ticketStatus.toUpperCase() !== 'ALL') {
      filterStatus['and'][0]['status'] = ticketStatus.toUpperCase();
      if (isViewUnassigned) {
        filterStatus2['and'][0]['status'] = 'OPEN';
      }
    }

    let filter_and = filter['and'];

    if (
      ticketStatus &&
      ticketStatus.toUpperCase() === 'OPEN' &&
      this.state.currentUserData &&
      this.state.currentUserData['pmakerUid']
    ) {
      // filter = { and: [{ assignee_pmaker_uid: this.state.currentUserData['pmakerUid'] }] };
      filterStatus['and'].push({
        assignee_pmaker_uid: this.state.currentUserData['pmakerUid'],
      });
    } else {
      filterStatus2['and'].push({
        assignee_pmaker_uid: this.state.currentUserData['pmakerUid'],
      });
    }

    // covers all conditions of non-Membership Module Manager for tabs:
    // OPEN, PENDING, RESOLVED, CLOSED, AND ALL...
    // the filtering for ALL here only includes ticket_types for specific users,
    // filtering for assignee pmaker id is accomplished on the OR filters

    if (!this.state.isMembershipModuleManager && ticketStatus) {
      if (
        ['OPEN', 'PENDING', 'RESOLVED', 'CLOSED'].includes(
          ticketStatus.toUpperCase(),
        )
      ) {
        if (
          this.state.currentUserData &&
          this.state.currentUserData.groupName
        ) {
         
          let filter = { ticket_type: {} };

          let inq = this.fetchTicketTypeChecker();

          filter.ticket_type = { inq };

          if (isViewUnassigned) {
            filterStatus2['and'].push(filter);
          } else {
            filterStatus['and'].push(filter);
          }

          // if (this.state.currentUserData.groupName === 'APD_ENCODER GROUP') {
          //   const encoder_filter = {
          //     ticket_type: {
          //       inq: ['Enroll Memberlist', 'Enroll Member',
          //         'Add Member', 'Upload Termination', 'Upload Suspension', 'Manual Encode', 'Batch Edit', 'Manual Renewal', 'Suspend Members from Collection'] //~
          //     }
          //   }
          //   filterStatus['and'].push(encoder_filter)
          //   // if (getTabsCount) count_filter.push(encoder_filter);
          // }
          // if (this.state.currentUserData.groupName === 'APD_VERIFIER GROUP') {
          //   const verifier_filter = {
          //     ticket_type: {
          //       inq: ['Verify Memberlist', 'Verify Member', 'Verify Termination',
          //         'Verify Dependents', 'Verify Edit', 'Upload Termination',
          //         'Batch Edit', 'Verify Suspension', 'Suspend Members from Collection', 'Upload Suspension', //~
          //         'Batch Edit', 'Manual Renewal', 'Match Person Profile', 'Approve Member Changes from OCP', 'Void Masterlist']
          //     }
          //   }
          //   filterStatus['and'].push(verifier_filter)
          //   // if (getTabsCount) count_filter.push(verifier_filter);
          // }
          // if (this.state.currentUserData.groupName === 'APD_PRINTING_STAFF') {
          //   const printing_filter = {
          //     ticket_type: {
          //       inq: ['Print Card IDs', 'Claimed Print Card IDs',
          //         'Generate Transmittal Request', 'Claimed Generate Transmittal Request',
          //         'Reprint Card IDs from OCP']
          //     }
          //   }
          //   filterStatus['and'].push(printing_filter)
          //   // if (getTabsCount) count_filter.push(printing_filter);
          // }
        }
        // if (this.state.currentUserData && this.state.currentUserData.pmakerUid) {
        //   const pmaker_filter = { assignee_pmaker_uid: this.state.currentUserData.pmakerUid }
        //   filterStatus['and'].push(pmaker_filter)
        //   // if (getTabsCount) count_filter.push(pmaker_filter);
        // }
      } else if (
        ticketStatus.toUpperCase() === 'ALL' ||
        ticketStatus.toUpperCase() === 'UNASSIGNED'
      ) {
        if (
          this.state.currentUserData &&
          this.state.currentUserData.groupName
        ) {
          let filter = { ticket_type: {} };
          let inq = await this.fetchTicketTypeChecker();
          filter.ticket_type = { inq };
          filterStatus['and'].push(filter);

          if (ticketStatus.toUpperCase() === 'ALL') {
            filterStatus3['and'] = [
              { status: 'UNASSIGNED' },
              {
                ticket_type:
                  filterStatus.and.length === 2
                    ? filterStatus.and[1].ticket_type
                    : filterStatus.and[2].ticket_type,
              },
            ];
            allFilter['or'].push(filterStatus);
            allFilter['or'].push(filterStatus2);
          }

          // if (this.state.currentUserData.groupName === 'APD_ENCODER GROUP') {
          //   filterStatus['and'].push({
          //     ticket_type: {
          //       inq: ['Enroll Memberlist', 'Enroll Member',
          //         'Add Member', 'Upload Termination', 'Batch Edit', 'Upload Suspension', 'Manual Encode', 'Batch Edit', 'Manual Renewal', 'Suspend Members from Collection'] //~
          //     }
          //   })
          // }
          // if (this.state.currentUserData.groupName === 'APD_VERIFIER GROUP') {
          //   filterStatus['and'].push({
          //     ticket_type: {
          //       inq: ['Verify Memberlist', 'Verify Member', 'Verify Termination',
          //         'Verify Dependents', 'Verify Edit', 'Upload Termination', 'Batch Edit', 'Verify Suspension', 'Suspend Members from Collection', //~
          //         'Upload Suspension', 'Batch Edit', 'Manual Renewal', 'Match Person Profile', 'Approve Member Changes from OCP', 'Void Masterlist']
          //     }
          //   })
          // }
          // if (this.state.currentUserData.groupName === 'APD_PRINTING_STAFF') {
          //   filterStatus['and'].push({
          //     ticket_type: {
          //       inq: ['Print Card IDs', 'Claimed Print Card IDs',
          //         'Generate Transmittal Request', 'Claimed Generate Transmittal Request',
          //         'Reprint Card IDs from OCP']
          //     }
          //   })
          // }
        }
      }
    }

    // CONSTRUCT OR FILTER INSIDE AND FILTER
    // covers conditions for non-MMM in tabs: ALL (only sets pmaker filter),
    // UNASSIGNED, sets condition pmaker_id = ticket.assignee_pmaker_id

    // if (ticketStatus && ['UNASSIGNED', 'ALL'].includes(ticketStatus.toUpperCase()) &&
    //   this.state.currentUserData && this.state.currentUserData.groupName) {
    //   console.log('filter_2 user groupname', this.state.currentUserData.groupName)
    //   if (['APD_ENCODER GROUP', 'APD_VERIFIER GROUP', 'APD_PRINTING_STAFF']
    //     .includes(this.state.currentUserData.groupName) || !this.state.isMembershipModuleManager) {
    //     let or_filter: any[] = [];
    //     if (this.state.currentUserData.groupName === 'APD_ENCODER GROUP') {
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Enroll Memberlist' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Enroll Member' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Add Member' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Batch Edit' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Upload Termination' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Upload Suspension' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Manual Encode' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Manual Renewal' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Suspend Members from Collection' }
    //           ]
    //         })
    //     } else if (this.state.currentUserData.groupName === 'APD_VERIFIER GROUP') {
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Memberlist' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Member' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Suspension' }
    //           ]
    //         })
    //       or_filter.push( //~
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Suspend Members from Collection' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Termination' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Dependents' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Verify Edit' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Batch Edit' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Manual Renewal' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Match Person Profile' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Approve Member Changes from OCP' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Void Masterlist' }
    //           ]
    //         })
    //     } else if (this.state.currentUserData.groupName === 'APD_PRINTING_STAFF') {
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Print Card IDs' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Claimed Print Card IDs' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Claimed Generate Transmittal Request' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Generate Transmittal Request' }
    //           ]
    //         })
    //       or_filter.push(
    //         {
    //           and: [
    //             { status: 'UNASSIGNED' },
    //             { ticket_type: 'Reprint Card IDs from OCP' }
    //           ]
    //         })
    //     }
    //     // let count_or_filter = or_filter
    //     if (ticketStatus.toUpperCase() === 'ALL') {
    //       or_filter.push(
    //         {
    //           and: [
    //             { assignee_pmaker_uid: this.state.currentUserData.pmakerUid },
    //             { status: { nin: ['UNASSIGNED'] } }
    //           ]
    //         })
    //     }
    //     if (or_filter.length > 0) {
    //       filterStatus['and'].push({ or: or_filter })
    //     }
    //     // if (getTabsCount) count_filter.push({ or: count_or_filter });
    //   }

    // }

    // let filter_for_count: any = {};
    // if (getTabsCount) {
    //   filter_for_count = this.constructFilterForCount(count_filter)
    // }

    // console.log('FILTER FOR COUNT', filter_for_count)

    // SORTING
    let _order: string[] = [];
    //need to fix search and filtering ticketlist
    Object.keys(sort)
      .filter(key => sort[key])
      .forEach(item => {
        _order.push(`${item} ${sort[item]} `);
      });

    // API CALL
    filter['and'] = filter_and;


    let order_str; // = ``
    if (_order) {
      order_str = `,"order": "${[_order]}"`;
    }
 
    let officeQuery: string = '';
    if (this.state.currentUserData && this.state.currentUserData['office']) {
      officeQuery = `&office=${this.state.currentUserData['office']}&`;
    }

    let tableFilter: string = '';
    if (filter['and'] && filter['and'].length > 0) {
      tableFilter = `&table_filter=${JSON.stringify(filter)}`;
    }

    if (ticketStatus.toUpperCase() === 'OPEN') {
      this.setState({ openStatusFilter: filterStatus });
    }

    let additional_query = '&';
    if (
      ticketStatus.toUpperCase() === 'UNASSIGNED' ||
      ticketStatus.toUpperCase() === 'ALL'
    ) {
     
      let finalOpenStatusFilter: any = isViewUnassigned
        ? filterStatus2
        : this.state.openStatusFilter;
      additional_query = `&open_status_filter={"where":${JSON.stringify(
        finalOpenStatusFilter,
      )}`;
    }
   

    const GET_TICKETS_URL =
      `members/ticket?` +
      'tab_counts=true' +
      officeQuery +
      `filter={"skip":${offset},
      "limit":${limit},
      "where":${JSON.stringify(
        ticketStatus.toUpperCase() === 'ALL' ? allFilter : filterStatus,
      )}, 
      "order":${JSON.stringify(_order)} }
      ${tableFilter}${additional_query}}
      `;
  
    await API_2.get(
      GET_TICKETS_URL,
      response => {
        // SUCCESS
        if (response && response.error === undefined) {
          if (response['verify_member_count']) {
            this.setState({
              vmticketcount: response['verify_member_count'].count,
            });
           
          }
          if (response['enroll_member']) {
            this.setState({
              enrollMember: response['enroll_member'],
            });
          }

          if (response['print_id_member']) {
            this.setState({
              printIdMember: response['print_id_member'],
            });
          }

          /** COMMENTED SHOWING COUNT FROM TICKETLIST ENDPOINT */
          let ticketCount: number = 0;
   
          if (response['count']) {
            ticketCount = response['count'];
          }
          /** */
          let modifiedTickets =
            response.tickets && response.tickets.length > 0
              ? response.tickets.map(tData => {
                  // put modifications to the data here
                  return tData;
                })
              : [];


          if (ticketStatus === 'open') {
            this.setState(
              {
                tListOpenTickets: modifiedTickets,
                /** COMMENTED SHOWING COUNT FROM TICKETLIST ENDPOINT */
                openTicketsCount: ticketCount,
              },
              () => {
                this.getOpenTickets();
              },
            );
          } else if (ticketStatus === 'unassigned') {
            this.setState(
              {
                tListUnassignedTickets: modifiedTickets,
                /** COMMENTED SHOWING COUNT FROM TICKETLIST ENDPOINT */
                unassignedTicketsCount: ticketCount,
              },
              () => {
                this.getUnassigned();
              },
            );
          }
        } else {
          this.setState({
            loading: false,
          });
        
          if (response && response.errror) {
            this.showUnauthorizedError(response.error);
          }
        }
      },
      error => {
        // ERROR
        this.setState({
          loading: false,
        });
     
      },
    );
  };

  componentDidMount = () => {
    this.getUser();
    this.getDashboardStats();
  };

  componentDidUpdate = (_, prevState) => {
    if (prevState.statsData !== this.state.statsData) {

    }
  };

  getDashboardStats = async () => {
    let processedMembersArr: any[] = [];
    let toEnrollVerifyArr: any[] = [];

    await API.getProcessedMembers().then(response => {
      if (response && response.error) {
     
        this.showUnauthorizedError(response.error);
      } else {
    
        if (response && response[moment().year()]) {
       
          processedMembersArr = response[moment().year()];
        } 
      }
    });

    await API.getMembersToEnrollOrVerify().then(response => {
      if (response && response.error) {
   
        this.showUnauthorizedError(response.error);
      } else {
 
        if (response && response[moment().year()]) {
       
          toEnrollVerifyArr = response[moment().year()];
        } 
      }
    });

    this.setState({
      statsData: {
        processed_members: processedMembersArr,
        to_enroll_verify: toEnrollVerifyArr,
      },
    });
  };

  getUser = async () => {
    await API.getUserDataFromDb().then(userData => {


      // SEPT 09, 2020: RBAC Implementation (MVP-MS968 & MVP-MS969)
      /**
       * check the current user's username if it has the word 'supervisor'
       * then store the rbac value
       */
      if (
        userData['rbac'] &&
        userData['loginUsername'] &&
        // breakdown the string per underscore and convert the strings to lowercase in the new array
        userData['loginUsername']
          .split('_')
          .map(a => a.toLowerCase())
          .includes('supervisor')
      ) {
   
        localStorage.setItem(
          'all_rbac_policies',
          JSON.stringify(userData['rbac']),
        );
      }

      let rbacPolicies: any[] = JSON.parse(
        localStorage.getItem('all_rbac_policies') || '[]',
      );

      // sort by Policy ID in ASC order
      rbacPolicies = rbacPolicies.sort((a, b) =>
        a.policy_id > b.policy_id ? 1 : -1,
      );

      // log the rbac policies not included for the current user based from the overall rbac policies
      if (
        rbacPolicies &&
        rbacPolicies.length > 0 &&
        userData['rbac'] &&
        userData['rbac'].length > 0
      ) {
        let unincludedPolicies: any[] = [];

        rbacPolicies.forEach(rbacPolicy => {
          if (rbacPolicy['policy_id']) {
            let userRbacPolicies: any[] = userData['rbac'].map(
              userRbac => userRbac['policy_id'],
            );
            if (!userRbacPolicies.includes(rbacPolicy['policy_id'])) {
              unincludedPolicies.push(rbacPolicy['policy_id']);
            }
          }
        });

     
      }
      //

      this.setState(
        {
          currentUserData: userData,
          role: userData.groupName,
        },
        async () => {
  
          if (userData.groupName !== undefined) {
            await API.getUserGroup()
              .then(group => {
                let isMembershipModuleManager: boolean = false;
                if (
                  (group.name &&
                    membershipModuleManagerGroup.includes(group.name)) ||
                  (!membershipEncoderGroup.includes(group.name) &&
                    GlobalFunction.checkUserRbacByPolicy(
                      'MS79',
                      this.state.currentUserData,
                      this.state.loading,
                    ))
                ) {
      
                  isMembershipModuleManager = true;
                }

                this.setState(
                  {
                    isMembershipModuleManager: isMembershipModuleManager,
                  },
                  () => {
                    this.fetchTickets('open');
                    this.fetchTickets('unassigned');
                  },
                );
              })
              .catch(err => {
                this.setState({
                  loading: false,
                });
            
              });
          } else {
            let isMembershipModuleManager: boolean = false;
            if (
              GlobalFunction.checkUserRbacByPolicy(
                'MS79',
                this.state.currentUserData,
                this.state.loading,
              )
            ) {
           
              isMembershipModuleManager = true;
            }

            this.setState(
              {
                isMembershipModuleManager: isMembershipModuleManager,
              },
              () => {
                this.fetchTickets('open');
                this.fetchTickets('unassigned');
              },
            );
          }
        },
      );
    });
  };

  getTicketInformation = (claimedTicket: any) => {
    this.setState({
      loading: true,
    });

    const {
      ticket_id,
      due,
      ticket_type,
      client_name,
      client_id,
      ticket_item,
      case_id,
      proposal_id,
      app_index,
    } = claimedTicket;

    const ticket: any = {
      ticket_id: ticket_id,
      due: due,
      ticket_type: ticket_type,
      client_name: client_name,
      client_id: client_id,
      ticket_item: ticket_item,
      case_id: case_id,
      proposal_id: proposal_id,
      app_index: app_index,
      renew_members: null
    };

    let parsedAppIndex = 0;
    try {
      parsedAppIndex = parseInt(app_index, 10) + 1;
    } catch (e) {
      console.log('Error parsing app index: ', e);
    }

    Processmaker.get(
      'cases/' + case_id + '/variables?app_index=' + parsedAppIndex,
    )
      .then(ticketInfo => {
   
        if (
          ticketInfo !== undefined &&
          (ticketInfo.error === undefined || ticketInfo.error === null)
        ) {
          if (ticketInfo.client_name !== undefined) {
            ticket.client_name = ticketInfo.client_name;
          }
          if (ticketInfo.member_upload_id !== undefined) {
            // console.log('666AAA', ticketInfo);
            ticket.member_upload_id = ticketInfo.member_upload_id;
            // console.log('777AAA', ticketInfo);
          }
          if (
            (ticket_type === ENROLL_MEMBER_WORKFLOW ||
              ticket_type === RENEW_MEMBER_WORKFLOW || //renew member 5685
              ticket_type === CLAIMED_ENROLL_MEMBER_WORKFLOW) &&
            ticketInfo.client_id !== undefined
          ) {
            ticket.client_id = ticketInfo.client_id;
            if (ticketInfo.mother_contract_id !== undefined) {
              ticket.proposal_id = ticketInfo.mother_contract_id;
            }
          // 5685
         if (ticketInfo.hasOwnProperty('renew_members')){
              if(ticketInfo.renew_members === 0){
                ticket.renew_members = "false"
              }else{
                ticket.renew_members = "true"
              }
            }
          } else if (
            (ticket_type === VERIFY_MEMBER_WORKFLOW ||
              ticket_type === VERIFY_RENEWAL_MEMBERLIST ||
              ticket_type === CLAIMED_VERIFY_MEMBER_WORKFLOW ||
              ticket_type === VERIFY_WORKFLOW ||
              ticket_type === CLAIMED_VERIFY_WORKFLOW ||
              ticket_type === VERIFY_EDIT ||
              ticket_type === CLAIMED_VERIFY_EDIT) &&
            ticketInfo.member_upload_id !== undefined
          ) {
            ticket.client_id = ticketInfo.member_upload_id;

            // fix for bug id MS513 //
            if (
              ticket_type === VERIFY_EDIT ||
              ticket_type === CLAIMED_VERIFY_EDIT
            ) {
              ticket.client_id = ticketInfo.client_id;
              ticket['member_upload_id'] = ticketInfo.member_upload_id;
            }

            ticket['validation_finished'] = false;
            if (ticketInfo.validation_finished !== undefined) {
              ticket['validation_finished'] = ticketInfo.validation_finished;
            }

            // SEPT 16, 2020: To enable 'vm-ocp' flag if ticket is from OCP
      
            if (!_.isNil(ticketInfo.from_ocp)) {
              ticket.from_ocp = ticketInfo.from_ocp;
            }
          } else if (
            (ticket_type === ADD_MEMBER_WORKFLOW ||
            ticket_type === MANUAL_ENCODE_WORKFLOW ||
            ticket_type === ADD_MEMBER_UNASSIGNED_WORKFLOW ||
            ticket_type === VERIFY_TERMINATION ||
            ticket_type === UPLOAD_TERMINATION ||
            ticket_type === CLAIMED_VERIFY_TERMINATION ||
            ticket_type === VERIFY_SUSPENSION ||
            ticket_type === VERIFY_SUSPENSION_COLLECTION || //~
              ticket_type === UPLOAD_SUSPENSION ||
              ticket_type === CLAIMED_VERIFY_SUSPENSION ||
              ticket_type === BATCH_EDIT ||
              ticket_type === BATCH_EDIT_OCP ||
              ticket_type === APPROVE_MEMBER_CHANGE_OCP ||
              ticket_type === REPRINT_CARD_OCP ||
              ticket_type === MANUAL_RENEWAL ||
              ticket_type === VOID_MASTERLIST ||
              // ||
              ticket_type === CLAIMED_VOID_MASTERLIST ||
              ticket_type === UPLOAD_VOID_MASTERLIST) && //||
            // ticket_type === VERIFY_DEPENDENTS ||
            // ticket_type === CLAIMED_VERIFY_DEPENDENTS
            !_.isEmpty(ticketInfo.client_name) &&
            !_.isEmpty(ticketInfo.client_id)
          ) {
            ticket.member_void_upload_id = ticketInfo.member_void_upload_id;
            ticket.client_id = ticketInfo.client_id;
            if (ticketInfo.mother_contract_id !== undefined) {
              ticket.proposal_id = ticketInfo.mother_contract_id;
            }
            if (!_.isNil(ticketInfo.from_ocp)) {
              ticket.from_ocp = ticketInfo.from_ocp;
            }
            if (!_.isNil(ticketInfo.is_ocp)) {
              ticket.is_ocp = ticketInfo.is_ocp;
            }
          } else if (
            (ticket_type === PRINTING_ID_WORKFLOW ||
              ticket_type === CLAIMED_PRINTING_ID_WORKFLOW) &&
            !_.isEmpty(ticketInfo.client_name) &&
            !_.isEmpty(ticketInfo.client_id)
          ) {
            ticket.client_name = ticketInfo.client_name;
            ticket.client_id = ticketInfo.client_id;
            if (ticketInfo.old_case_id) {
              ticket.old_case_id = ticketInfo.old_case_id;
            }
          } else if (
            (ticket_type === CLAIMED_VERIFY_DEPENDENTS ||
              ticket_type === VERIFY_DEPENDENTS) &&
            !_.isEmpty(ticketInfo.member_civil_status_group_id)
          ) {
            ticket.member_civil_status_group_id =
              ticketInfo.member_civil_status_group_id;
            ticket.client_id = ticketInfo.client_id;
          } else if (
            ticket_type === CLAIMED_VOID_MASTERLIST ||
            ticket_type === VOID_MASTERLIST ||
            ticket_type === UPLOAD_VOID_MASTERLIST
          ) {
            ticket.member_void_upload_id = ticketInfo.member_void_upload_id;
            ticket.validation_finished = false;
            if (ticketInfo.validation_finished !== undefined) {
              ticket.validation_finished = ticketInfo.validation_finished;
              ticket.member_void_upload_id = ticketInfo.member_void_upload_id;
            }
          } else if (ticket_type === UPLOAD_VOID_MASTERLIST) {
            ticket['member_void_upload_id'] = ticketInfo.member_upload_id;
          }

          this.setState(
            {
              loading: false,
            },
            () => {
              this.handleClickOpenTicketsRow(ticket);
            },
          );
        } else {
          this.setState({
            loading: false,
          });
        }
      })
      .catch(e => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            loading: false,
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.setState({
            loading: true,
          });
        }
      });
  };

  onViewTicket = (ticket: any) => {
    this.setState(
      {
        isClaimModalOpen: false,
        isTicketSnackBarOpen: false,
      },
      () => {
        this.getTicketInformation(ticket);
      },
    );
  };

  closeTicketSnackBar = () => {
    this.setState({
      isTicketSnackBarOpen: false,
      claimedTicketData: {},
    });
  };

  showUnauthorizedError = (errorData: any) => {
  
    if (
      errorData &&
      errorData['message'] &&
      errorData['statusCode'] &&
      errorData['statusCode'] === 401 &&
      errorData['name'] &&
      errorData['name'] === 'UnauthorizedError'
    ) {
      let errorTitle: string = errorData['name']
        .match(/[A-Z][a-z]+/g)
        .join(' ');

    }
  };



  renderCardsToPrintTray = (printIdMember, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#3AB77D' }}
              >
                {printIdMember ? printIdMember.count : 0}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Batches of cards to print
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {printIdMember
                  ? printIdMember.data.map((item, index) => {
                      return (
                        <ListItem key={index}>
                          <ListItemText
                            data-cy={'card_batches_to_print_' + index}
                            id={'card_batches_to_print_' + index}
                            className={clsx('dashboard-list-item-text')}
                            primary={item.client}
                          />
                          <ListItemSecondaryAction>
                            <span
                              id={'dashboard_view_print_' + item.index}
                              data-cy={'dashboard_view_print_' + item.index}
                              style={{
                                textDecoration: 'underline',
                                color: '#0D5E40',
                                fontSize: '14px',
                                fontWeight: 700,
                              }}
                              onClick={() => {
                                this.setState({
                                  loading: true,
                                });
                                this.handleClickOpenTicketsRow(item);
                              }}
                            >
                              View
                            </span>
                          </ListItemSecondaryAction>
                        </ListItem>
                      );
                    })
                  : null}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
          </Paper>
        </Grid>
      </>
    );
  };

  renderCardsToReprintTray = (reprint_members, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#3AB77D' }}
              >
                {reprint_members.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Batches of cards to reprint
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {reprint_members.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        data-cy={'card_batches_to_reprint_' + index}
                        id={'card_batches_to_reprint_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={item.name}
                      />
                      <ListItemSecondaryAction>
                        <Link
                          id={'dashboard_view_reprint_' + item.index}
                          data-cy={'dashboard_view_reprint_' + item.index}
                          className={clsx('dashboard-list-item-link')}
                          to={item.link}
                        >
                          View
                        </Link>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
              <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
                <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
              </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  renderMembersToTerminateTray = (terminate_members, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#FD5451' }}
              >
                {terminate_members.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Members to Terminate
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {terminate_members.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        id={'members_to_terminate_' + index}
                        data-cy={'members_to_terminate_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={item.client_name}
                      />
                      <ListItemSecondaryAction>
                        {/* <Link
                          id={'dashboard_terminate_member_' + index}
                          data-cy={'dashboard_terminate_member_' + index}
                          className={clsx('dashboard-list-item-link')}
                          to={item.link}
                        >
                          Terminate
                        </Link> */}
                        <span
                          id={'dashboard_view_terminate_' + index}
                          data-cy={'dashboard_view_terminate_' + index}
                          style={{
                            textDecoration: 'underline',
                            color: '#0D5E40',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                          onClick={() => {
                            this.setState({
                              loading: true,
                            });
                            this.handleClickOpenTicketsRow(item);
                          }}
                        >
                          Terminate
                        </span>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
              <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
                <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
              </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  renderMembersToCancelTray = (cancel_members, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#3AB77D' }}
              >
                {cancel_members.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Members to Cancel
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {cancel_members.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        id={'members_to_cancel_' + index}
                        data-cy={'members_to_cancel_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={item.name}
                      />
                      <ListItemSecondaryAction>
                        <Link
                          id={'dashboard_cancel_member_' + index}
                          data-cy={'dashboard_cancel_member_' + index}
                          className={clsx('dashboard-list-item-link')}
                          to={item.link}
                        >
                          Cancel
                        </Link>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
              <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
                <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
              </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  renderMembersToSuspendTray = (suspend_members, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#3AB77D' }}
              >
                {suspend_members.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Members to Suspend
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {suspend_members.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        id={'members_to_suspend_' + index}
                        data-cy={'members_to_suspend_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={item.client_name}
                      />
                      <ListItemSecondaryAction>
                        {/* <Link
                          id={'dashboard_suspend_member_' + index}
                          data-cy={'dashboard_suspend_member_' + index}
                          className={clsx('dashboard-list-item-link')}
                          to={item.link}
                        >
                          Suspend
                        </Link> */}
                        <span
                          id={'dashboard_view_suspend_' + index}
                          data-cy={'dashboard_view_suspend_' + index}
                          style={{
                            textDecoration: 'underline',
                            color: '#0D5E40',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                          onClick={() => {
                            this.setState({
                              loading: true,
                            });
                            this.handleClickOpenTicketsRow(item);
                          }}
                        >
                          Suspend
                        </span>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
              <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
                <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
              </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  renderMembersToActivateTray = (activate_members, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                className={clsx('dashboard-count')}
                style={{ color: '#FD5451' }}
              >
                {activate_members.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Members to Activate
              </Typography>
            </Box>
            <Box className={clsx('card-content')}>
              <List dense>
                {activate_members.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        id={'members_to_activate_' + index}
                        data-cy={'members_to_activate_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={item.name}
                      />
                      <ListItemSecondaryAction>
                        <Link
                          id={'dashboard_activate_members_' + index}
                          data-cy={'dashboard_activate_members_' + index}
                          className={clsx('dashboard-list-item-link')}
                          to={item.link}
                        >
                          Activate
                        </Link>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
              <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
                <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
              </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  renderPersonsToMatchTray = (persons_to_match, size) => {
    return (
      <>
        <Grid item xs={12} md={size}>
          <Paper className={clsx('content')}>
            <Box className={clsx('card-header')}>
              <Typography
                id="dashboard_persons_to_match_count"
                data-cy="dashboard_persons_to_match_count"
                className={clsx('dashboard-count')}
                style={{ color: '#FD5451' }}
              >
                {persons_to_match.length}
              </Typography>
              <Typography className={clsx('dashboard-count-description')}>
                Persons to Match
              </Typography>
            </Box>
            <Box className={clsx('card-content')} alignItems="flex-start">
              <List dense>
                {persons_to_match.map((item, index) => {
                  return (
                    <ListItem key={index}>
                      <ListItemText
                        id={'persons_to_match_' + index}
                        data-cy={'persons_to_match_' + index}
                        className={clsx('dashboard-list-item-text')}
                        primary={`Batch ${
                          item.batch_name ? item.batch_name : item.ticket_id
                        }`}
                      />
                      <ListItemSecondaryAction>
                        <span
                          id={'dashboard_view_match_' + index}
                          data-cy={'dashboard_view_match_' + index}
                          style={{
                            textDecoration: 'underline',
                            color: '#0D5E40',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                          onClick={() => {
                            this.setState({
                              loading: true,
                            });
                            this.handleClickOpenTicketsRow(item);
                          }}
                        >
                          View
                        </span>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
            <Box
              className={clsx('card-footer')}
              display="flex"
              justifyContent="flex-end"
            >
              <Link
                id="dashboard_view_match"
                data-cy="dashboard_view_match"
                className={clsx('dashboard-view-more-link')}
                to={'/membership/ticketlist'}
              >
                View All
              </Link>
            </Box>
            {/*true ?
            <Box className={clsx('card-footer')} display="flex" justifyContent="flex-end">
              <Link className={clsx('dashboard-view-more-link')} to={"#"}>View All</Link>
            </Box> : null */}
          </Paper>
        </Grid>
      </>
    );
  };

  public render(): JSX.Element {
    const { statsData } = this.state;
    const {
      loading,
      open_tickets,
      redirect,
      redirect_info,
      persons_to_match,
      enroll_members,
      validate_members,
      suspend_members,
      // cancel_members,
      terminate_members,
      // activate_members,
      reprint_members,
      print_members,
      popperOpen,
      popperAnchor,
      popperItem,
      unassigned_tickets,
      modalTitle,
      modalMessage,
      isOpenModal,
      isClaimModalOpen,
      claimedTicketData,
      vmticketcount,
      enrollMember,
      printIdMember,
      // isTicketSnackBarOpen,
    } = this.state;

    // const sortedOpenTickets = _.sortBy(open_tickets, ticket => {
    //   const { due } = ticket;
    //   const dueDate = moment(due, 'MMM DD, YYYY', true);
    //   if (dueDate.isValid()) {
    //     const date = dueDate.toDate().getTime();

    //     return date * -1;
    //   }

    //   return 0;
    // });

    // BACKEND-SORTED TICKETS
    const sortedOpenTickets = open_tickets;
    if (redirect === true) {
      return <Redirect to={redirect_info} />;
    } else if (
      !GlobalFunction.checkUserRbacByPolicy(
        'MS64',
        this.state.currentUserData,
        this.state.loading,
      )
    ) {
      return <div></div>;
    } else {
      return (
        <div className={clsx('Dashboard')}>
          {loading ? <Loader /> : null}
          <Grid
            container
            spacing={2}
            justify="space-between"
            alignItems="flex-start"
          >
            <Grid item xs={12}>
              <Grid container justify="flex-start" alignItems="flex-start">
                <Grid item xs={12}>
                  <Typography className={clsx('title')}>Dashboard</Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={12}>
              <Grid
                container
                justify="space-between"
                spacing={4}
                alignItems="flex-start"
              >
                <Grid
                  item
                  xs={12}
                  md={this.state.role !== 'APD_PRINTING_STAFF' ? 8 : 12}
                >
                  <Typography className={clsx('sub-title')}>
                    Your Ticket List
                  </Typography>
                  <Paper className={clsx('content')}>
                    <Box
                      className={clsx('card-header')}
                      display="flex"
                      flexDirection="row"
                      justifyContent="space-between"
                    >
                      <Box>
                        <Typography
                          id="open_tickets_count"
                          data-cy="open_tickets_count"
                          className={clsx('dashboard-count')}
                          style={{ color: '#FD5451' }}
                        >
                          {this.state.openTicketsCount}
                        </Typography>
                        <Typography
                          className={clsx('dashboard-count-description')}
                        >
                          Open Tickets
                        </Typography>
                      </Box>
                      {/* <Box>
												<Select items={ticket_options} value={this.state.ticket_type} onChange={this.handleTicketTypeSelect} />
											</Box> */}
                    </Box>
                    <Box className={clsx('card-content')}>
                      <Table size="small" className={clsx('dashboard-table')}>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              ID
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Due
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Ticket Type
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Corporate Account
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Updated By
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Date Updated
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Last Update Time
                            </TableCell>
                            {/* <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Last Updated By
                            </TableCell> */}
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            />
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {/* {console.log(
                            'sortedOpenTickets33',
                            sortedOpenTickets,
                          )} */}
                          {sortedOpenTickets.map((row, index) => {
                            // console.log('what ticket?', row)
                            if (
                              (row.ticket_type === VERIFY_MEMBER_WORKFLOW ||
                                row.ticket_type ===
                                  CLAIMED_VERIFY_MEMBER_WORKFLOW) &&
                              row.validation_finished !== true
                            ) {
                              return (
                                <TableRow
                                  key={index}
                                  className={clsx(
                                    index % 2 === 0
                                      ? row.is_urgent
                                        ? 'dashboard-table-hg-even-row'
                                        : 'dashboard-table-even-row'
                                      : row.is_urgent
                                      ? 'dashboard-table-hg-odd-row'
                                      : 'dashboard-table-odd-row',
                                  )}
                                >
                                  <TableCell
                                    style={{ minWidth: '80px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-disabled',
                                    )}
                                    component="th"
                                    scope="row"
                                  >
                                    {row.ticket_id}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-disabled',
                                    )}
                                  >
                                    {row.due}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '165px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-disabled',
                                    )}
                                  >
                                    {row.ticket_type}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '165px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-disabled',
                                    )}
                                  >
                                    {row.client_name}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '175px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.updated_by}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.date_updated}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '200px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated}
                                  </TableCell>
                                  {/* <TableCell
                                    style={{ minWidth: '180px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated_by}
                                  </TableCell> */}
                                  <TableCell
                                    style={{ minWidth: '125px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-status',
                                      {
                                        'dashboard-table-data-cell-status-done': [
                                          'Done Processing',
                                          'Done Uploading',
                                        ].includes(row.status),
                                        'dashboard-table-data-cell-status-ongoing': [
                                          'Processing',
                                          'Uploading',
                                        ].includes(row.status),
                                        'dashboard-table-data-cell-status-cancelled':
                                          row.status === 'Cancelled',
                                        'dashboard-table-data-cell-status-error':
                                          row.status === 'Error',
                                      },
                                    )}
                                  >
                                    {row.status}
                                  </TableCell>
                                </TableRow>
                              );
                            } else if (
                              row.ticket_type === UPLOAD_VOID_MASTERLIST
                            ) {
                              // console.log('ticket is void upload', row)
                              return (
                                <TableRow
                                  id="dashboard_open_tickets"
                                  data-cy="dashboard_open_tickets"
                                  style={{
                                    cursor: 'pointer',
                                  }}
                                  key={index}
                                  className={clsx(
                                    index % 2 === 0
                                      ? row.is_urgent
                                        ? 'dashboard-table-hg-even-row'
                                        : 'dashboard-table-even-row'
                                      : row.is_urgent
                                      ? 'dashboard-table-hg-odd-row'
                                      : 'dashboard-table-odd-row',
                                  )}
                                  onClick={() => {
                                    if (
                                      GlobalFunction.checkUserRbacByPolicy(
                                        'MS78',
                                        this.state.currentUserData,
                                        this.state.loading,
                                      )
                                    ) {
                                      this.setState({
                                        loading: true,
                                      });
                                     
                                      this.setState({
                                        member_upload_id:
                                          row && row.member_void_upload_id,
                                      });
                                 
                                      this.handleClickOpenTicketsRow(row);
                                    }
                                  }}
                                >
                                  <TableCell
                                    style={{ minWidth: '80px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                    component="th"
                                    scope="row"
                                  >
                                    {row.ticket_id}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.due}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '165px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.ticket_type}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '200px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.client_name}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '175px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.updated_by}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.date_updated}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '200px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated}
                                  </TableCell>
                                  {/* <TableCell
                                    style={{ minWidth: '180px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated_by}
                                  </TableCell> */}
                                  <TableCell
                                    style={{ minWidth: '125px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-status',
                                      {
                                        'dashboard-table-data-cell-status-done':
                                          row.status === 'Done Processing',
                                        'dashboard-table-data-cell-status-ongoing':
                                          row.status === 'Processing',
                                        'dashboard-table-data-cell-status-cancelled':
                                          row.status === 'Cancelled',
                                        'dashboard-table-data-cell-status-error':
                                          row.status === 'Error',
                                      },
                                    )}
                                  >
                                    {/* {console.log('TICKET DETAILS ANNN', row)} */}
                                    {row.status}
                                    {/* {row.ticket_type === 'Verify Edit' || (row.ticket_type === 'Add Member' && row.status === 'Done Processing') ? '' : row.status} 1222 */}
                                  </TableCell>
                                </TableRow>
                              );
                            } else {
                              return (
                                <TableRow
                                  id="dashboard_open_tickets"
                                  data-cy="dashboard_open_tickets"
                                  style={{
                                    cursor: 'pointer',
                                  }}
                                  key={index}
                                  className={clsx(
                                    index % 2 === 0
                                      ? row.is_urgent
                                        ? 'dashboard-table-hg-even-row'
                                        : 'dashboard-table-even-row'
                                      : row.is_urgent
                                      ? 'dashboard-table-hg-odd-row'
                                      : 'dashboard-table-odd-row',
                                  )}
                                  onClick={() => {
                                    if (
                                      GlobalFunction.checkUserRbacByPolicy(
                                        'MS78',
                                        this.state.currentUserData,
                                        this.state.loading,
                                      )
                                    ) {
                                      this.setState({
                                        loading: true,
                                      });
                                    
                                      this.handleClickOpenTicketsRow(row);
                                    } 
                                  }}
                                >
                                  <TableCell
                                    style={{ minWidth: '80px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                    component="th"
                                    scope="row"
                                  >
                                    {row.ticket_id}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.due}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '165px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.ticket_type}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '200px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.client_name}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '175px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.updated_by}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '150px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.date_updated}
                                  </TableCell>
                                  <TableCell
                                    style={{ minWidth: '200px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated}
                                  </TableCell>
                                  {/* <TableCell
                                    style={{ minWidth: '180px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell',
                                    )}
                                  >
                                    {row.last_updated_by}
                                  </TableCell> */}
                                  <TableCell
                                    style={{ minWidth: '125px' }}
                                    className={clsx(
                                      'dashboard-table-data-cell-status',
                                      {
                                        'dashboard-table-data-cell-status-done': [
                                          'Done Processing',
                                          'Done Uploading',
                                        ].includes(row.status),
                                        'dashboard-table-data-cell-status-ongoing': [
                                          'Processing',
                                          'Uploading',
                                        ].includes(row.status),
                                        'dashboard-table-data-cell-status-cancelled':
                                          row.status === 'Cancelled',
                                        'dashboard-table-data-cell-status-error':
                                          row.status === 'Error',
                                      },
                                    )}
                                  >
                                    {/* {console.log('TICKET DETAILS ANNN', row)} */}
                                    {row.status}
                                    {/* {row.ticket_type === 'Verify Edit' || (row.ticket_type === 'Add Member' && row.status === 'Done Processing') ? '' : row.status} 1222 */}
                                  </TableCell>
                                </TableRow>
                              );
                            }
                          })}
                        </TableBody>
                      </Table>
                    </Box>
                    <Box
                      className={clsx('card-footer')}
                      display="flex"
                      justifyContent="flex-end"
                    >
                      <Link
                        id="dashboard_view_all_tickets"
                        data-cy="dashboard_view_all_tickets"
                        className={clsx('dashboard-view-more-link')}
                        to={'/membership/ticketlist/open'}
                      >
                        View All
                      </Link>
                    </Box>
                  </Paper>
                  <Paper className={clsx('content')}>
                    <Box
                      className={clsx('card-header')}
                      display="flex"
                      flexDirection="row"
                      justifyContent="space-between"
                    >
                      <Box>
                        <Typography
                          id="unassigned_tickets_count"
                          data-cy="unassigned_tickets_count"
                          className={clsx('dashboard-count')}
                          style={{ color: '#FD5451' }}
                        >
                          {this.state.unassignedTicketsCount}
                        </Typography>
                        <Typography
                          className={clsx('dashboard-count-description')}
                        >
                          Unassigned Tickets
                        </Typography>
                      </Box>
                    </Box>
                    <Box className={clsx('card-content')}>
                      <Table size="small" className={clsx('dashboard-table')}>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              ID
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Due
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Ticket Type
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Corporate Account
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Updated By
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Date Updated
                            </TableCell>
                            <TableCell
                              className={clsx('dashboard-table-header-cell')}
                            >
                              Action
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {unassigned_tickets.map((row, index) => {
                            return (
                              <TableRow
                                key={index}
                                className={clsx(
                                  index % 2 === 0
                                    ? row.is_urgent
                                      ? 'dashboard-table-hg-even-row'
                                      : 'dashboard-table-even-row'
                                    : row.is_urgent
                                    ? 'dashboard-table-hg-odd-row'
                                    : 'dashboard-table-odd-row',
                                )}
                              >
                                <TableCell
                                  style={{ minWidth: '80px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                  component="th"
                                  scope="row"
                                >
                                  {row.ticket_id}
                                </TableCell>
                                <TableCell
                                  style={{ minWidth: '150px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                >
                                  {row.due}
                                </TableCell>
                                <TableCell
                                  style={{ minWidth: '175px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                >
                                  {row.ticket_type}
                                </TableCell>
                                <TableCell
                                  style={{ minWidth: '200px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                >
                                  {row.client_name}
                                </TableCell>
                                <TableCell
                                  style={{ minWidth: '175px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                >
                                  {row.updated_by}
                                </TableCell>
                                <TableCell
                                  style={{ minWidth: '150px' }}
                                  className={clsx('dashboard-table-data-cell')}
                                >
                                  {row.date_updated}
                                </TableCell>
                                <TableCell style={{ minWidth: '80px' }}>
                                  <span
                                    id="dashboard_claim_tickets"
                                    data-cy="dashboard_claim_tickets"
                                    style={{ textDecoration: 'underline' }}
                                    className={clsx('dashboard-list-item-link')}
                                    onClick={() => {
                                      this.handleClaim(row);
                                    }}
                                  >
                                    Claim
                                  </span>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </Box>
                    <Box
                      className={clsx('card-footer')}
                      display="flex"
                      justifyContent="flex-end"
                    >
                      <Link
                        id="dashboard_view_unassigned"
                        data-cy="dashboard_view_unassigned"
                        className={clsx('dashboard-view-more-link')}
                        to={'/membership/ticketlist/unassigned'}
                      >
                        View All
                      </Link>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  {this.state.role !== 'APD_PRINTING_STAFF' ? (
                    <Typography className={clsx('sub-title')}>
                      Your Task Tray
                    </Typography>
                  ) : null}
                  {/* {localStorage.getItem('role_name') !== 'APD Verifier' ?
										( */}

                  {this.state.role !== 'APD_PRINTING_STAFF' &&
                  this.state.role !== 'APD_VERIFIER GROUP' ? (
                    <Paper className={clsx('content')}>
                      <Box className={clsx('card-header')}>
                        <Typography
                          id="dashboard_members_count"
                          data-cy="dashboard_members_count"
                          className={clsx('dashboard-count')}
                          style={{ color: '#3AB77D' }}
                        >
                          {enrollMember ? enrollMember.count : 0}
                        </Typography>
                        <Typography
                          className={clsx('dashboard-count-description')}
                        >
                          Memberlist to Enroll
                        </Typography>
                      </Box>
                      <Box
                        className={clsx('card-content')}
                        alignItems="flex-start"
                      >
                   
                        <List dense>
                          {enrollMember
                            ? enrollMember.data.map((item, index) => {
                             
                                return (
                                  <ListItem key={index}>
                                    <ListItemText
                                      id={'memberlist_to_enroll_' + index}
                                      className={clsx(
                                        'dashboard-list-item-text',
                                      )}
                                      primary={item.client}
                                    />
                                    <ListItemSecondaryAction>
                                      <span
                                        id="dashboard_enroll_member"
                                        data-cy="dashboard_enroll_member"
                                        style={{
                                          textDecoration: 'underline',
                                          color: '#0D5E40',
                                          fontSize: '14px',
                                          fontWeight: 700,
                                        }}
                                        onClick={e => {
                                          if (
                                            item.ticket_type === 'Add Member'
                                          ) {
                                       
                                            this.setState({
                                              loading: true,
                                            });
                                            this.handleClickOpenTicketsRow(
                                              item,
                                            );
                                          } else {
                                            this.handlePopoverOpen(e, item);
                                          }

                                          if (
                                            item.ticket_type ===
                                            'Upload Void Masterlist'
                                          ) {
                                            console.log('CLICKED!2', item);
                                            this.setState({
                                              loading: true,
                                            });
                                            this.handleClickOpenTicketsRow(
                                              item,
                                            );
                                          } else {
                                            this.handlePopoverOpen(e, item);
                                          }
                                        }}
                                      >
                                        Enroll
                                      </span>
                                    </ListItemSecondaryAction>
                                  </ListItem>
                                );
                              })
                            : null}
                        </List>
                      </Box>
                      <Box
                        className={clsx('card-footer')}
                        display="flex"
                        justifyContent="flex-end"
                      >
                        <Link
                          id="dashboard_view_memberslist"
                          data-cy="dashboard_view_memberslist"
                          className={clsx('dashboard-view-more-link')}
                          to={'/membership/ticketlist'}
                        >
                          View List
                        </Link>
                      </Box>
                    </Paper>
                  ) : null}
                  {/* ) : null
									} */}
                  {/* { localStorage.getItem('role_name') !== 'APD Encoder' ?
										( */}
                  {this.state.role !== 'APD_PRINTING_STAFF' &&
                  this.state.role !== 'APD_ENCODER GROUP' ? (
                    <Paper className={clsx('content')}>
                      <Box className={clsx('card-header')}>
                        <Typography
                          id="validate_members_count"
                          data-cy="validate_members_count"
                          className={clsx('dashboard-count')}
                          style={{ color: '#3AB77D' }}
                        >
                          {/* {validate_members.length} */}
                          {vmticketcount}
                        </Typography>
                        <Typography
                          className={clsx('dashboard-count-description')}
                        >
                          Memberlist to Validate
                        </Typography>
                      </Box>
                      <Box
                        className={clsx('card-content')}
                        alignItems="flex-start"
                      >
                        <List dense>
                          {validate_members.map((item, index) => {
                            if (item.is_pending_upload) {
                              return (
                                <ListItem key={index}>
                                  <ListItemText
                                    id={'members_to_verify_' + index}
                                    className={clsx(
                                      'dashboard-list-item-text2',
                                    )}
                                    primary={
                                      item.ticket_id + ' ' + item.client_name
                                    }
                                  />
                                  <ListItemSecondaryAction>
                                    <Typography
                                      className={clsx(
                                        'dashboard-list-item-link2',
                                      )}
                                    >
                                      Pending Upload
                                    </Typography>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              );
                            } else {
                              if (item.validation_finished === true) {
                                return (
                                  <ListItem key={index}>
                                    <ListItemText
                                      id={'members_to_verify_' + index}
                                      className={clsx(
                                        'dashboard-list-item-text',
                                      )}
                                      primary={item.client_name}
                                    />
                                    <ListItemSecondaryAction>
                                      <Link
                                        id={
                                          'dashboard_verify_' + item.client_id
                                        }
                                        data-cy={
                                          'dashboard_verify_' + item.client_id
                                        }
                                        className={clsx(
                                          'dashboard-list-item-link',
                                        )}
                                        to={
                                          '/membership/verify-member/' +
                                          item.case_id +
                                          '/' +
                                          item.client_id +
                                          '/' +
                                          item.ticket_id
                                        }
                                      >
                                        Verify
                                      </Link>
                                    </ListItemSecondaryAction>
                                  </ListItem>
                                );
                              } else {
                                return (
                                  <ListItem key={index}>
                                    <ListItemText
                                      id={'members_to_verify_' + index}
                                      className={clsx(
                                        'dashboard-list-item-text',
                                      )}
                                      primary={item.client_name}
                                    />
                                    <ListItemSecondaryAction>
                                      <span
                                        id={'dashboard_verify_' + index}
                                        data-cy={'dashboard_verify_' + index}
                                        className={clsx(
                                          'dashboard-list-item-link-disabled',
                                        )}
                                      >
                                        Verify
                                      </span>
                                    </ListItemSecondaryAction>
                                  </ListItem>
                                );
                              }
                            }
                          })}
                        </List>
                      </Box>
                      <Box
                        className={clsx('card-footer')}
                        display="flex"
                        justifyContent="flex-end"
                      >
                        <Link
                          id="dashboard_view_unvalidated"
                          data-cy="dashboard_view_unvalidated"
                          className={clsx('dashboard-view-more-link')}
                          to={'/membership/ticketlist'}
                        >
                          View List
                        </Link>
                      </Box>
                    </Paper>
                  ) : null}
                  {/* ) : (null)
									} */}
                </Grid>
              </Grid>
            </Grid>

            {this.state.role === 'APD_ENCODER GROUP' ? (
              <Grid item xs={12} md={12}>
                <Grid
                  container
                  justify="space-between"
                  spacing={4}
                  alignItems="flex-start"
                >
                  <>
                    {this.renderMembersToTerminateTray(terminate_members, 6)}
                    {/* {this.renderMembersToCancelTray(cancel_members, 4)} */}
                    {this.renderMembersToSuspendTray(suspend_members, 6)}
                  </>
                </Grid>
              </Grid>
            ) : null}

            {this.state.role === 'APD_VERIFIER GROUP' ? (
              <>
                <Grid item xs={12} md={12}>
                  <Grid
                    container
                    justify="space-between"
                    spacing={4}
                    alignItems="flex-start"
                  >
                    <>
                      {/* {this.renderMembersToActivateTray(activate_members, 6)} */}
                      {this.renderMembersToTerminateTray(terminate_members, 4)}
                      {/* {this.renderMembersToCancelTray(cancel_members, 4)} */}
                      {this.renderMembersToSuspendTray(suspend_members, 4)}
                      {this.renderPersonsToMatchTray(persons_to_match, 4)}
                    </>
                  </Grid>
                </Grid>
                {/* <Grid item xs={12} md={12}>
                  <Grid
                    container
                    justify="space-between"
                    spacing={4}
                    alignItems="flex-start"
                  >
                    <>
                      {this.renderMembersToSuspendTray(suspend_members, 6)}
                      {this.renderPersonsToMatchTray(persons_to_match, 6)}
                    </>
                  </Grid>
                </Grid> */}
              </>
            ) : null}

            {this.state.role === 'APD_PRINTING_STAFF' ? (
              <Grid item xs={12} md={12}>
                <Grid
                  container
                  justify="space-between"
                  spacing={4}
                  alignItems="flex-start"
                >
                  <>
                    {this.renderCardsToPrintTray(printIdMember, 6)}
                    {this.renderCardsToReprintTray(reprint_members, 6)}
                  </>
                </Grid>
              </Grid>
            ) : null}

            {this.state.role !== 'APD_PRINTING_STAFF' &&
            this.state.role !== 'APD_VERIFIER GROUP' &&
            this.state.role !== 'APD_ENCODER GROUP' ? (
              <>
                <Grid item xs={12} md={12}>
                  <Grid
                    container
                    justify="space-between"
                    spacing={4}
                    alignItems="flex-start"
                  >
                    <>
                      {this.renderCardsToPrintTray(printIdMember, 4)}
                      {this.renderCardsToReprintTray(reprint_members, 4)}
                      {/* {this.renderMembersToActivateTray(activate_members, 4)} */}
                      {this.renderMembersToTerminateTray(terminate_members, 4)}
                    </>
                  </Grid>
                </Grid>
                <Grid item xs={12} md={12}>
                  <Grid
                    container
                    justify="space-between"
                    spacing={4}
                    alignItems="flex-start"
                  >
                    <>
                      {/* {this.renderMembersToTerminateTray(terminate_members, 4)} */}
                      {/* {this.renderMembersToCancelTray(cancel_members, 4)} */}
                      {this.renderMembersToSuspendTray(suspend_members, 6)}
                      {this.renderPersonsToMatchTray(persons_to_match, 6)}
                    </>
                  </Grid>
                </Grid>
                {/* <Grid item xs={12} md={12}>
                  <Grid
                    container
                    justify="space-between"
                    spacing={4}
                    alignItems="flex-start"
                  >
                    <>
                      {this.renderPersonsToMatchTray(persons_to_match, 4)}
                    </>
                  </Grid>
                </Grid> */}
              </>
            ) : null}

            <Grid item xs={12} md={12} style={{ display: 'none' }}>
              <Typography
                className={clsx('sub-title')}
                style={{ marginTop: 48 }}
              >
                Reports
              </Typography>
            </Grid>
            <Grid item xs={12} md={12} style={{ display: 'none' }}>
              <Grid
                container
                justify="space-between"
                spacing={4}
                alignItems="flex-start"
              >
                <Grid item xs={12} md={6}>
                  <Paper className={clsx('content')}>
                    <Box className={clsx('card-graph-header')}>
                      <Typography className={clsx('dashboard-graph-title')}>
                        Processed Members
                      </Typography>
                    </Box>
                    <Box className={clsx('card-content')}>
                      <img
                        src={'temp_dashboard_graph_03.png'}
                        className={clsx('dashboard-temp-graph-img-report')}
                        alt="Top 5 Clients in Utilization"
                      />
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Paper className={clsx('content')}>
                    <Box className={clsx('card-graph-header')}>
                      <Typography className={clsx('dashboard-graph-title')}>
                        Processed Members
                      </Typography>
                    </Box>
                    <Box className={clsx('card-content')}>
                      <img
                        src={'temp_dashboard_graph_03.png'}
                        className={clsx('dashboard-temp-graph-img-report')}
                        alt="Top 5 Clients in Utilization"
                      />
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={12} style={{ display: 'none' }}>
              <Grid
                container
                justify="space-between"
                spacing={4}
                alignItems="flex-start"
              >
                <Grid item xs={12} md={12}>
                  <Paper
                    className={clsx('content')}
                    style={{ visibility: 'visible' }}
                  >
                    <Box className={clsx('card-graph-header')}>
                      <Typography className={clsx('dashboard-graph-title')}>
                        Members to Enroll/Verify
                      </Typography>
                    </Box>
                    <Box className={clsx('card-content')}>
                      <img
                        src={'temp_dashboard_graph_04.png'}
                        className={clsx('dashboard-temp-graph-img')}
                        alt="Top Facilities"
                      />
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>

            {GlobalFunction.checkUserRbacByPolicy(
              'MS74',
              this.state.currentUserData,
              this.state.loading,
            ) ? (
              <Grid item xs={12} md={12}>
                <Paper style={{ padding: 20 }}>
                  <Grid container>
                    <Grid item xs={12} style={{ paddingBottom: 25 }}>
                      <Typography
                        variant="h1"
                        component="h2"
                        style={{ fontSize: 22, fontWeight: 'bold' }}
                      >
                        Processed Members
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Chart
                        data={
                          statsData['processed_members']
                            ? statsData['processed_members']
                            : []
                        }
                        height={300}
                      >
                        <ArgumentScale factory={scaleBand} />
                        <ArgumentAxis />
                        <ValueAxis />

                        <BarSeries
                          valueField="enrolled"
                          argumentField="month"
                          name={processedMembersLabels[0]}
                          color={barGraphColors['gold']}
                        />
                        <BarSeries
                          valueField="approved"
                          argumentField="month"
                          name={processedMembersLabels[1]}
                          color={barGraphColors['blue']}
                        />
                        <BarSeries
                          valueField="printed"
                          argumentField="month"
                          name={processedMembersLabels[2]}
                          color={barGraphColors['pink']}
                        />

                        <Stack />
                        {/* JULY 8, 2020: ALTERNATIVE CHART LEGEND FOR DASHBOARD STATS CHART */}
                        {/* <Legend
                    markerComponent={ChartLegend}
                  /> */}
                      </Chart>
                    </Grid>
                    {/* CUSTOM CHART LEGEND */}
                    <Grid
                      item
                      container
                      spacing={1}
                      xs={12}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingTop: 20,
                      }}
                    >
                      {processedMembersLegend.map(item => {
                        return (
                          <Grid
                            item
                            xs={2}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <StopIcon
                              style={{
                                backgroundColor: item['color'],
                                color: item['color'],
                              }}
                            />
                            <span style={{ marginLeft: 5 }}>
                              {item['label']}
                            </span>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            ) : null}

            {GlobalFunction.checkUserRbacByPolicy(
              'MS73',
              this.state.currentUserData,
              this.state.loading,
            ) ? (
              <Grid item xs={12} md={12}>
                <Paper style={{ padding: 20 }}>
                  <Grid container>
                    <Grid item xs={12} style={{ paddingBottom: 25 }}>
                      <Typography
                        variant="h1"
                        component="h2"
                        style={{ fontSize: 22, fontWeight: 'bold' }}
                      >
                        Members to Enroll/Verify
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Chart
                        data={
                          statsData['to_enroll_verify']
                            ? statsData['to_enroll_verify']
                            : []
                        }
                        height={300}
                      >
                        <ArgumentScale factory={scaleBand} />
                        <ArgumentAxis />
                        <ValueAxis />

                        <BarSeries
                          valueField="to_enroll"
                          argumentField="month"
                          name={toEnrollVerifyLabels[0]}
                          color={barGraphColors['gold']}
                        />
                        <BarSeries
                          valueField="to_verify"
                          argumentField="month"
                          name={toEnrollVerifyLabels[1]}
                          color={barGraphColors['blue']}
                        />

                        <Stack />
                        {/* JULY 8, 2020: ALTERNATIVE CHART LEGEND FOR DASHBOARD STATS CHART */}
                        {/* <Legend
                    markerComponent={ChartLegend}
                  /> */}
                      </Chart>
                    </Grid>
                    {/* CUSTOM CHART LEGEND */}
                    <Grid
                      item
                      container
                      spacing={1}
                      xs={12}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingTop: 20,
                      }}
                    >
                      {toEnrollVerifyLegend.map(item => {
                        return (
                          <Grid
                            item
                            xs={2}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <StopIcon
                              style={{
                                backgroundColor: item['color'],
                                color: item['color'],
                              }}
                            />
                            <span style={{ marginLeft: 5 }}>
                              {item['label']}
                            </span>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            ) : null}
          </Grid>

          <Popover
            isPopoverOpen={popperOpen}
            anchor={popperAnchor}
            item={popperItem}
            handleClose={this.handlePopoverClose}
          />
          <ModalComponent
            id="dashboard_error_modal"
            isModalOpen={isOpenModal}
            title={modalTitle}
            message={modalMessage}
            onClose={this.closeModalMessage}
          />
          <ClaimModalComponent
            id="dashboard_claim_modal"
            isModalOpen={isClaimModalOpen}
            ticket={claimedTicketData}
            isPrimary={true}
            onViewTicket={this.onViewTicket}
            onClose={this.closeModalMessage}
          />
          {/* <TicketSnackbarNotification
            isOpen={isTicketSnackBarOpen}
            ticket={claimedTicketData}
            onViewTicket={this.onViewTicket}
            onSnackbarClose={() => {
              this.closeTicketSnackBar();
            }}
          /> */}
        </div>
      );
    }
  }
}

const mapStateToProps = (state: Store) => state.dashboard;

const mapDispatchToProps = (dispatch: Dispatch) => ({
  Map: bindActionCreators(DashboardActions.Map, dispatch),
});

export { mapStateToProps, mapDispatchToProps };

/**
 * Notes:
 * Limit Types(Hard coded): ABL, MBL, PEC (Pre-existing condition)
 */
