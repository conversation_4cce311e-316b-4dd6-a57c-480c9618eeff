import * as React from 'react';
import {
  Grid,
  TextField,
  InputLabel,
  FormHelperText,
} from '@material-ui/core/';
import { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';

import clsx from 'clsx';
import { cloneDeep } from 'lodash'; 
import { Components } from '@hims/core';
import { DatePicker } from 'Components/UI/DatePicker';
import { AddSelect } from 'Components/UI/AddSelect';
import { AddNewBatchModal } from '../AddNewBatch';
import { CustomAutocomplete } from 'Components/UI/CustomAutocomplete';
// import Link from '@material-ui/core/Link';

const LinkStyles = makeStyles({
  link: {
    top: '343px',
    left: '1156px',
    width: '60px',
    height: '14px',
    textDecoration: 'underline',
    font: 'Medium 12px/14px Usual',
    letterSpacing: '0',
    color: '#0D5E40',
    fontSize: '12px'
  },
});

interface AddDetailstProps {
  isAddMembersTicket: boolean;
  state_data: any;
  responseData: any;
  details_data: any;
  contract_options: any[];
  batchnames: any;
  updateDetails: (value: Object, attr: String) => void;
  updateState: (data: any, attr: string) => void;
  encodePageType: string;
  hasTicketId: boolean;
  handleUpdateDetailsChannelAndName: (fieldName: string,  value: any) => void; // 5685
  ticketVariables: any;
  // setDataMapping: any;
}

const useStyles = makeStyles({
  label: {
    fontSize: '1rem',
  },
  errorText: {
    marginTop: '5px',
    color: '#E53935',
  },
  errorText2: {
    color: '#E53935',
    marginTop: '0px',
  },
});

type ISelectItem = Components.UI.InputSelect.ISelectItem;

export const AddDetails: React.FC<AddDetailstProps> = (
  props: AddDetailstProps,
): JSX.Element => {

  const renew_members = props.ticketVariables ? props.ticketVariables.renew_members : false
  const [accounts] = useState<any>(function () {
    const accounts_data: any[] = props.responseData.map(item => {
      return {
        id:item['_id'], // item['contract_id'], //item['_id'],
        value: item['_id'], // item['contract_id'], //item['_id'],
        label: item.registered_name,
        client_id: item.client_id,
        client_contract_id: item['contract_id'],
        contract_no: item['contract_no']
      };
    });
    return accounts_data;
  });
  const [mediums, setMediums] = useState([
    {
      id: 1,
      value: 'Email',
      label: 'Email',
    },
    {
      id: 2,
      value: 'Fax',
      label: 'Fax',
    },
    {
      id: 3,
      value: 'Client Portal',
      label: 'Client Portal',
    },
  ]);

  useEffect(() => {
    const newSelectItem = {
      id: 4,
      value: 'Auto-renew',
      label: 'Auto-renew',
    }

    if(props.ticketVariables){ 

      if(props.ticketVariables.renew_members){
      setMediums(prev=>[...prev,newSelectItem ])
      setSelectedChannel(newSelectItem)
      //5685
      props.handleUpdateDetailsChannelAndName("sent_through_channel", newSelectItem)
      }else{
          setMediums(prev => prev.filter(item => item.value !== 'Client Portal'))
      }
    }
    
  },[props.ticketVariables])

  const [selectedAccount, setSelectedAccount] = useState<any>({
    id:'',
    value:'',
    label:''
  });
  const [selectedContract, setSelectedContract] = useState<any>({
    id:props && props.details_data.contract['client_contract_id'] ? props.details_data.contract['client_contract_id'] : '',
    value:props && props.details_data.contract['client_contract_id'] ? props.details_data.contract['client_contract_id'] : '',
    label: props && props.details_data.contract['label'] ?  props.details_data.contract['label'] : '',
  });
  const [selectedChannel, setSelectedChannel] = useState<any>({
    id:'',
    value:'',
    label:''
  });
  const [selectedBatchName, setSelectedBatchName] = useState<any>({
    id:'',
    value:'',
    label:''
  });
  const [senderName, setSenderName] = useState('');
  const [senderEmail, setSenderEmail] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  // = useState(
  //   props.save_for_now_system_name,
  // );
  // const [internet_flag, setInternetFlag] = useState(false);
  const [selectedNumberMember, setNumberMember] = useState('');
  const [ticketId, setTicketId] = useState('');
  const [selectedFileUrl, setFileUrl] = useState('');
  const [openBatchModal, setOpenBatchModal] = useState(false);
  const classes = useStyles();

  useEffect(() => {


    if(props.details_data){
      setSelectedContract({
        id:   props.details_data.contract.client_contract_id,
        value:   props.details_data.contract.client_contract_id,
        label:  props.details_data.contract.label,
      })

      if(props.details_data.account && selectedAccount.id!==props.details_data.account.id){
        setSelectedAccount(props.details_data.account);
      }
      if(selectedContract && selectedContract.id){
        //  console.log("contract333",  props.details_data.contract, props.details_data.account, selectedAccount.id, props.details_data, selectedAccount, selectedContract)
        if(props.details_data.contract && selectedContract.id!==props.details_data.contract.id){
          // console.log("contract444", props.details_data.contract, selectedContract, )

          setSelectedContract(props.details_data.contract);
          // console.log("contract555", selectedContract)
        }
        // console.log("contract777", selectedContract)
      } else if(props.details_data.contract && props.details_data.contract.id){
        // console.log("contract888", props.details_data.contract,  props.details_data)
        setSelectedContract(props.details_data.contract);
        // console.log("contract999", selectedContract)
      
      } else if(props && props.details_data.contract['client_contract_id'] !== undefined) {
        setSelectedContract(props.details_data.contract['client_contract_id']);
      } else {
        // console.log("contract1010", selectedContract)
        setSelectedContract({id:'',value:'',label:''})
      }
      
      if(props.details_data.sender_name && senderName!==props.details_data.sender_name){
        setSenderName(props.details_data.sender_name);
      }

      if(props.details_data.sender_email && senderEmail!==props.details_data.sender_email){
        setSenderEmail(props.details_data.sender_email);
      }

      if(props.details_data.date_sent && selectedDate!==props.details_data.date_sent){
        setSelectedDate(props.details_data.date_sent);
      }

      if(props.details_data.number_of_members && selectedNumberMember!==props.details_data.number_of_members){
        setNumberMember(props.details_data.number_of_members);
      }
      
      if(props.details_data.file_url && selectedFileUrl!==props.details_data.file_url){
        setFileUrl(props.details_data.file_url);
      }

      if(props.details_data.sent_through_channel && selectedChannel.id!==props.details_data.sent_through_channel.id && !renew_members){
        setSelectedChannel(props.details_data.sent_through_channel);
      }

      if(props.details_data.batch_name && selectedBatchName.id!==props.details_data.batch_name.id){
        setSelectedBatchName(props.details_data.batch_name);
      }
    
      if(props.details_data.ticket_id && ticketId !== props.details_data.ticket_id ){
        setTicketId(props.details_data.ticket_id)
      }

      if(renew_members  &&  props.details_data.sender_name.length === 0 && props.details_data.sender_name!== !props.contract_options?.[0]?.created_by?.full_name){
        setSenderName(props.contract_options[0]?.created_by?.username)
        setSelectedDate(props.contract_options[0]?.date_created)
 
        // 5685 put a value on details_data
 
        props.handleUpdateDetailsChannelAndName("sender_name", props.contract_options[0]?.created_by?.username ?? "")

      }

      
    }
  }, [props]); //details_data

  function handleSelectChange(item:ISelectItem,param:string){
    let updateDetails = true;
    switch(param){
      case 'account':
        setSelectedAccount(item);
        break;
      case 'contract':
        setSelectedContract(item);
        break;
      case 'sent_through_channel':
        setSelectedChannel(item);
        break;
      case 'batch_name':
        if (item.value !== 'add') {
          setSelectedBatchName(item)
          const details = props.details_data;
          details['batch_name'] = item;
          props.updateDetails(details, 'batch_name');
        } else {
          updateDetails=false;
          setSelectedBatchName('');
          setOpenBatchModal(true);
        }
        break;
      default:
        break;
    }
    if(updateDetails){
      const details = cloneDeep(props.details_data);
      details[param] = item;
      props.updateDetails(details,param);
    }
  }

  function handleTextFieldChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,param:string) {
    const { value } = e.target;
    switch(param){
      case 'sender_email':
        setSenderEmail(value);
        break;
      case 'sender_name':
        setSenderName(value);
        break;
      case 'number_of_members':
        setNumberMember(value);
        break;
      case 'ticket_id':
        setTicketId(value);
        break;
      default:
        break;
    }
    const details = props.details_data;
    details[param] = value;
    props.updateDetails(details, param);
  }

  function handleNumberMemberValue(evt: React.KeyboardEvent<HTMLInputElement>) {
    if (
      evt.key === '-' ||
      evt.key === '.' ||
      evt.key === '+' ||
      evt.key === 'e' ||
      evt.key === 'E'
    ) {
      evt.preventDefault();
    }
  }

  function handlePasteEvt(evt: React.ClipboardEvent<HTMLDivElement>) {
    const { clipboardData } = evt;

    if (clipboardData && clipboardData.getData('text')) {
      const pastedText = clipboardData.getData('text');
      try {
        const parsedData = parseFloat(pastedText);

        if (isNaN(parsedData) || parsedData < 0 || pastedText.includes('.')) {
          evt.preventDefault();
        }
      } catch (e) {
        evt.preventDefault();
      }
    }
  }

  function handleDateSent(date: Date) {
    setSelectedDate(date);
    const details = props.details_data;
    details['date_sent'] = date;
    props.updateDetails(details, 'date_sent');
  }

  function closeBatchNameModal() {
    setOpenBatchModal(false);
  }

  function handleAddBatchName(name: String) {
    const batches = props.batchnames;

    const new_batch = {
      id: batches.length + 1,
      value: name,
      label: name,
    };

    batches.push(new_batch);
    props.updateState('batchnames', batches);

    const details = props.details_data;
    details['batch_name'] = new_batch;
    props.updateDetails(details, 'batch_name');

    setOpenBatchModal(false);
  }

  const linkClasses = LinkStyles();
  return (
    <>

    <Grid container alignItems="stretch" className={clsx('member-body')}>
      <Grid item xs={12}>
        <Grid item container spacing={4}>
          <Grid item xs={4}>
            <CustomAutocomplete
              data-cy={'add_details_account_select'}
              id={'add_details_account_select'}
              options={accounts.sort(function (a, b) {
                return a.label.toLowerCase().localeCompare(b.label.toLowerCase());})}
              disabled={!props.isAddMembersTicket || props.details_data['isOcp'] || ['void','client-manual'].includes(props.encodePageType)} 
              label={'Corporate Account *'}
              value={selectedAccount}
              error={props.state_data.account_error}
              error_message={props.state_data.account_error_message}
              addButton={false}
              // variant={'outlined'}
              addButtonLabel={''}
              onChange={(e)=>{
                handleSelectChange(e,'account')
              }}
            />
            {/* <AddSelect
              id={'add_details_account_select'}
              options={accounts}
              disabled={!props.isAddMembersTicket}
              label={'Corporate Account *'}
              value={selectedAccount}
              error={props.state_data.account_error}
              error_message={props.state_data.account_error_message}
              addButton={false}
              addButtonLabel={''}
              onChange={handleChangeAccount}
            /> */}
          </Grid>
          <Grid item xs={4}>
            <CustomAutocomplete
              data-cy={'add_details_contract_select'}
              id={'add_details_contract_select'}
              options={props.contract_options}
              // label={'Contract *123'}
              label={'Contract *'}
              disabled={props.details_data['isOcp'] || ['void','client-manual'].includes(props.encodePageType)}
              // value={selectedContract && selectedContract.id && selectedContract.id.length>0 ? selectedContract : props.details_data.contract['client_contract_id']}
              value={
                selectedContract && selectedContract.id && selectedContract.id.length>0 ? selectedContract : selectedAccount.client_contract_id
              }
              error={selectedAccount.client_contract_id === undefined ? props.state_data.contract_error : ''}
              error_message={selectedAccount.client_contract_id === undefined ? props.state_data.contract_error_message : ''}
              // error_border={selectedAccount.client_contract_id === undefined ? "text-field-with-error" : " "}
              addButton={false}
              // variant={'outlined'}
              addButtonLabel={''}
              onChange={(e)=>{
              handleSelectChange(e,'contract')
              }}
            />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} className={clsx('member-body')}>
        <Grid item container spacing={4}>
          <Grid item xs={4}>
            <AddSelect
              data-cy={'add_details_sent_channel_select'}
              id={'add_details_sent_channel_select'}
              options={mediums}
              label={'Sent through channel *'}
              value={selectedChannel}
              error={props.state_data.sent_through_channel_error}
              error_message={
                props.state_data.sent_through_channel_error_message
              }
              disabled={props.details_data['isOcp'] || renew_members}
              addButton={false}
              addButtonLabel={''}
              onChange={(e)=>{
                handleSelectChange(e,'sent_through_channel')
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <DatePicker
              data-cy={'add_details_date_sent_picker'}
              id={'add_details_date_sent_picker'}
              placeholder="Date Sent"
              value={selectedDate ? selectedDate : null}
              maxDate={new Date()}
              error={props.state_data.date_sent_error}
              error_message={props.state_data.date_sent_error_message}
              onChange={handleDateSent}
              disabled={props.details_data['isOcp'] || renew_members}
            //   onKeyDown={(e) => {
            //     e.preventDefault();
            // }}
              // readonly={true}
              // templateOptions= {{
              //   readonly: true
              //   }}
            //  style={{ background:'white'}}
            // onChangeRaw={handleDateChangeRaw}
            />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} className={clsx('member-body')}>
        <Grid item container spacing={4}>
          <Grid item xs={4}>
            <InputLabel
              className={classes.label}
              htmlFor="add_details_sender_name_field"
            >
              Sender Name *
            </InputLabel>
            <TextField
              data-cy={'add_details_sender_name_field'}
              id={'add_details_sender_name_field'}
              style={{ marginTop: '8px', width: '100%' }}
              margin="normal"
              variant="outlined"
              value={senderName}
              onChange={(e)=>{
                handleTextFieldChange(e,'sender_name')
              }}
              error={props.state_data.sender_name_error}
              inputProps={{ 'aria-label': 'bare' }}
              disabled={props.details_data['isOcp'] || renew_members}
            />
            {props.state_data.sender_name_error === true ? (
              <FormHelperText
                id="add_details_sender_name_field_error"
                className={classes.errorText}
              >
                {props.state_data.sender_name_error_message}
              </FormHelperText>
            ) : null}
          </Grid>
          <Grid item xs={4}>
            <InputLabel
              className={classes.label}
              htmlFor="add_details_sender_email_field"
            >
           
         
              {
                selectedChannel
                  ? selectedChannel.value.toLowerCase() === "email"
                    ? "Sender Email *"
                    : "Sender Email"
                  : "Sender Email *"
              }
            </InputLabel>
            <TextField
              data-cy={'add_details_sender_email_field'}
              id={'add_details_sender_email_field'}
              style={{ marginTop: '8px', width: '100%' }}
              type="email"
              margin="normal"
              variant="outlined"
              value={senderEmail}
              // error={props.details_data['sent_through_channel'] &&
              // props.details_data['sent_through_channel'].value === 'Email'
              //   ? props.state_data.sender_email_error
              //   : null}
              error={props.state_data.sender_email_error}
              onChange={(e)=>{
                handleTextFieldChange(e,'sender_email')
              }}
              disabled={props.details_data['isOcp'] || renew_members}
              inputProps={{ 'aria-label': 'bare' }}
              
            />
            {props.state_data.sender_email_error === true ? (
              <FormHelperText
                id="add_details_sender_email_error"
                className={classes.errorText}
              >
              {props.details_data['sent_through_channel'] //&&
              // props.details_data['sent_through_channel'].value === 'Email'
              ? props.state_data.sender_email_error_message : null}
              </FormHelperText>
            ) : null}
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} className={clsx('member-body')}>
        <Grid item container spacing={4}>
          {['manual','client-manual'].includes(props.encodePageType) ?
            <Grid item xs={2}>
              <InputLabel
                className={classes.label}
                htmlFor="add_details_ticket_id_field"
              >
                Ticket ID
            </InputLabel>
              <TextField
                data-cy={'add_details_ticket_id_field'}
                id={'add_details_ticket_id_field'}
                disabled={props.hasTicketId}
                style={{ marginTop: '8px', width: '100%' }}
                margin="normal"
                variant="outlined"
                value={ticketId}
                onChange={(e)=>{
                  handleTextFieldChange(e,'ticket_id')
                }}
                error={props.state_data.ticket_id_error}
                inputProps={{ 'aria-label': 'bare' }}
              />
              {props.state_data.ticket_id_error === true ? (
                <FormHelperText
                  id="add_details_ticket_id_field_error"
                  className={classes.errorText}
                >
                  {props.state_data.ticket_id_error_message}
                </FormHelperText>
              ) : null}
            </Grid>
            : props.encodePageType !== 'manual'
              && props.details_data['isOcp'] ?
              <Grid item xs={1} >
                <InputLabel
                  className={classes.label}
                  htmlFor="add_details_ticket_id"
                >
                  Ticket ID
            </InputLabel>
                <TextField
                  data-cy={'add_details_ticket_id'}
                  id={'add_details_ticket_id'}
                  style={{ marginTop: '8px', width: '100%' }}
                  margin="normal"
                  variant="outlined"
                  disabled
                  value={props.details_data['batch_name']['value']}
                  error={props.state_data.number_of_members_error}
                  inputProps={{ 'aria-label': 'barnamee', min: 0 }}
                  InputProps={{
                    onKeyDown: handleNumberMemberValue,
                    onPaste: handlePasteEvt,
                  }}
                />
              </Grid>
              : null
          }
          <Grid item xs={props.encodePageType === 'default' ? 3 : 2}>
            <AddSelect
              data-cy={'add_details_batch_name_select'}
              id={'add_details_batch_name_select'}
              options={props.batchnames}
              label={'Batch Name'}
              value={selectedBatchName}
              addButton={true}
              addButtonLabel={'New Batch'}
              onChange={(e)=>{
                handleSelectChange(e,'batch_name')
              }}
              disabled={props.details_data['isOcp'] || renew_members}
              error={props.state_data.batch_name_error}
              error_message={props.state_data.batch_name_error_message}
            />
          </Grid>
          <Grid item xs={props.encodePageType === 'default' ? 3 : 2}>
            <InputLabel
              className={classes.label}
              htmlFor="add_details_number_members_field"
            >
              No. of Members
            </InputLabel>
            <TextField
              data-cy={'add_details_number_members_field'}
              id={'add_details_number_members_field'}
              style={{ marginTop: '8px' }}
              type="number"
              margin="normal"
              variant="outlined"
              value={selectedNumberMember}
              onChange={(e)=>{
                handleTextFieldChange(e,'number_of_members')
              }}
              disabled={props.details_data['isOcp'] || renew_members}
              error={props.state_data.number_of_members_error}
              inputProps={{ 'aria-label': 'bare', min: 0 }}
              InputProps={{
                onKeyDown: handleNumberMemberValue,
                onPaste: handlePasteEvt,
              }}
            />
            {props.state_data.number_of_members_error === true ? (
              <FormHelperText
                id="add_details_number_of_members_error"
                className={classes.errorText2}
              >
                {props.state_data.number_of_members_error_message}
              </FormHelperText>
            ) : null}
          </Grid>
          {props.details_data['file_url'] ? <Grid item xs={3} >
            <InputLabel
              className={classes.label}
              htmlFor="add_details_ticket_id"
            >
              Link Download
            </InputLabel>
            <div >
              <a className={linkClasses.link} href={selectedFileUrl} download>{selectedFileUrl.substr(selectedFileUrl.lastIndexOf("/") + 1)}</a>
            </div>
            {/* <Link
                key={'download'}
                id='link_download'
                data-cy='link_download'
                className={linkClasses.link}
                onClick={() => {
                  //downloadFiles = true;
                  //globalSelectedFiles = row.filearray;
                }}
              >
                Download
              </Link> */}
          </Grid>
            : ''}
        </Grid>
      </Grid>
      <Grid item xs={12}>
        <AddNewBatchModal
          id={'add_new_batch_modal'}
          batchnames={props.batchnames}
          isModalOpen={openBatchModal}
          onClose={closeBatchNameModal}
          onAdd={handleAddBatchName}
        />
      </Grid>

    </Grid>
    </>
  );
};
