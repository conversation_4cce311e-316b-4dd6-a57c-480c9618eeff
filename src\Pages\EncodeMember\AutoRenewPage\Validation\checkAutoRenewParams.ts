  export type MemberForRenewalPayload = {
    client_id: string;
    contractId: string;
    case_id: string;
    memberObjIds: string[];
    batch_name: string;
    type: string;
  };

  export function isValidAutoRenewalPayload(payload: any): payload is MemberForRenewalPayload {
  return (
    typeof payload.client_id === "string" &&
    typeof payload.contractId === "string" &&
    typeof payload.case_id === "string" &&
    Array.isArray(payload.memberObjIds) &&
    payload.memberObjIds.every(id => typeof id === "string") &&
    typeof payload.batch_name === "string" &&
    typeof payload.type === "string"
  );
}