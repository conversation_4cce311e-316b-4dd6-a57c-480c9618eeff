//#region Global Imports
import { Props } from 'prop-types';
//#endregion Global Imports

declare namespace IEncodeMember {
  export type IProps = IOwnProps & IStateProps & IDispatchProps;
  export interface IOwnProps extends Props<{}> {
    match: any;
    history: any;
    location: any;
  }
  export interface IState {
    check_internet_flag: boolean;
    isSaveForNow: boolean;
    isSaveForNowMemberList: boolean;
    pmaker_task: string;
    breadcrumbs: any;
    steps: any;
    activeStep: number;
    open: boolean;
    uploaded_file_flag: boolean;
    disabledNext: boolean;
    pageHeader: string;
    details_data: any;
    clients_data: any;
    corporate_account: string;
    initial_load: boolean;
    loading_state: boolean;
    sender_email_error: boolean;
    sender_email_error_message: string;
    account_error: boolean;
    account_error_message: string;
    contract_error: boolean;
    contract_error_message: string;
    sender_name_error: boolean;
    sender_name_error_message: string;
    date_sent_error: boolean;
    date_sent_error_message: string;
    sent_through_channel_error: boolean;
    sent_through_channel_error_message: string;
    batch_name_error: boolean;
    batch_name_error_message: string;
    number_of_members_error: boolean;
    number_of_members_error_message: string;
    uploaded_data: any;
    raw_uploaded_data: any;
    columns_data: any;
    system_names: any;
    clients_data_maps: any;
    selected_data_map: any;
    contracts_data: any;
    gender_data: any;
    civil_status_data: any;
    plan_type_data: any;
    type_data: any;
    relationship_data: any;
    vip_data: any;
    ph_rider_data: any;
    member_consent_data: any;
    site_data: any;
    //branch_options?: any;
    add_new_data_map: any;
    rows_start_at: string;
    filtered_raw_data: any;
    client_maps: any;
    isOpenModal: boolean;
    isConfirmModalOpen: boolean;
    modalTitle: string;
    modalMessage: string;
    customModalMessage: any;
    modalCloseText: string;
    modalConfirmText: string;
    next: any;
    isOpenErrorWithListModal: boolean;
    error_list: any;
    submit_flag: boolean;
    batchnames: any;
    save_for_now_id: string;
    uploadMemberData: any;
    ticketId: string;
    isProcessing: boolean;
    memberUploadId: any;
    memberVoidUploadID: any;
    processingStatus: any;
    isAddMembersTicket: boolean;
    isVoidMembersTicket: boolean;
    isFromEncodeMembersTicket: boolean;
    redirectFlag: boolean;
    redirectInfo: any;
    trueTicketId: any;
    fetchContactsErr: boolean;
    encodePageType: string;
    clientStatus: string;
    ticket_details: any;
    handleNextCounter: number;
    isSuffixError: boolean;
    isShowSuffixErrorModal: boolean;
    disable_next: boolean;
    isExpiredClientModalOpen: boolean;
    isExpiredClient: boolean;
    void_ticket_id: string;
    suffix_data:any;
    data_mapping_values:any;
    is_ocp: boolean;
    ocp_file: any[];
    data_mapping_save_for_now: any;
    default_data_mapping_save_for_now: any;
    save_for_now_system_name:any;
    selected_client_details:any;
    refresh_data_map:boolean;
    // retain_selected_data_map:boolean;
    reload_table:boolean;

    //Dropdown Options
    plan_type_options: any;
    civil_status_options: any;
    relationship_options: any;
    principals_options: any;
    site_assigned_options: any;
    contract_options:any;
    assignTicketType:string;
    isHowToRenewModalOpen: boolean;
    ticketVariables: any | null;
    renewalType?: string;
    manualRenewalDetails?: any;
  }

export interface AppName {
  client_id: string;
  mother_contract_id: string;
  client_name: string;
  renew_members: string;
  prev_pmaker_case_uid: string;
}

export interface CurrentTask {
  delStatus: string;
  delInitDate: string | null;
  delTaskDueDate: string;
  usr_uid: string;
  usr_name: string;
  tas_uid: string;
  tas_title: string;
  del_index: string;
  del_thread: string;
  del_thread_status: string;
  del_init_date: string;
  del_task_due_date: string;
}

export interface TicketVariables {
  app_uid: string;
  app_number: number;
  app_name: string | AppName;
  app_status: string;
  app_init_usr_uid: string;
  app_init_usr_username: string;
  pro_uid: string;
  pro_name: string;
  app_create_date: string;
  app_update_date: string;
  current_task: CurrentTask[];
}
  export interface IStateProps { }

  export interface IDispatchProps {
    Map(payload: Actions.IMapPayload): Actions.IMapResponse;
  }

  namespace Actions {
    export interface IMapPayload { }
    export interface IMapResponse { }
  }
}
