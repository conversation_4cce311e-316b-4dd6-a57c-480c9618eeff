//#region Global Imports
import * as React from 'react';
import { bindActionCreators, Dispatch } from 'redux';
import { Grid, Typography, Tabs, Tab } from '@material-ui/core/';
import { withStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import { get } from 'lodash';

//#region Local Imports

import { API } from '../API';
import './style.scss';

//#endregion Local Imports
import { Contract } from './Contract';

//#region Interface Imports
import { Store } from 'Components/Stores/IStore';
import { IMemberContractPage } from './IMemberContractPage';
import { MemberContractPageActions } from './Actions';
import { Loader } from 'Components/UI/LoadingIndicator';
import { Components } from '@hims/core';
import { MemberBenefits } from 'Pages/MemberContract/Benefits';
// import { ExclusionsSection } from './Exclusions'
import { LostInternetModal } from 'Components/UI'
import { ExclusionsSection } from './Exclusions';
//#endregion Interface Imports

import '@hims/core/dist/index.css';
import { PartnerNetworks } from './PartnerNetworks';

/*
const page_tab_titles: any = [
    "Benefits",
    "Exclusions",
    "Partner Network",
    "Contract",
]
*/
const page_tab_titles: any = [
  'Benefits',
  'Exclusions',
  'Contract',
  'Partner Network',
];

const MemberContractTabs = withStyles({
  root: {},
  indicator: {
    display: 'box',
    marginTop: 4,
    height: 3,
    backgroundColor: '#3AB77D',
  },
})(Tabs);

const MemberContractTab = withStyles({
  root: {
    color: 'rgba(39, 46, 76, 0.45)',
    paddingLeft: 32,
    paddingRight: 32,
    fontSize: '1rem',
    '&:hover': {
      color: 'rgba(39, 46, 76, 1)',
    },
    '&$selected': {
      textDecoration: 'none',
      color: 'rgba(39, 46, 76, 1)',
      fontWeight: 600,
    },
  },
  selected: {},
})(Tab);

export class MemberContractPage extends React.Component<
  IMemberContractPage.IProps,
  IMemberContractPage.IState
  > {
  constructor(props: IMemberContractPage.IProps) {
    super(props);

    this.state = {
      check_internet_flag: false,
      loading: false,
      selectedTab: this.props.match.params.contract_check ? page_tab_titles.indexOf('Contract') : 0,
      memberData: null,
      contractData: null,
      clientName: '',
      memberName: '',
      memberId: '',
      contractName: '-',
      planType: '',
      client_id: '',
    };

    this.setLoading = this.setLoading.bind(this);
    this.setSelectedTab = this.setSelectedTab.bind(this);
  }

  reset = () => {
    this.setState({
      loading: false,
      selectedTab: 0,
      clientName: '',
      memberName: '',
      memberId: '',
      contractName: '-',
    });
  };

  setSelectedTab = tab => {
    this.setState({
      selectedTab: tab,
    });
  };

  handleChange = (event: any, newValue: number) => {
    console.log('event >>>', event);
    this.setSelectedTab(newValue);
  };

  setLoading = isLoading => {
    this.setState({
      loading: isLoading,
    });
  };

  componentDidMount = () => {
    this.getMemberData();
  };

  getMemberData = () => {
    this.setState({
      loading: true,
      check_internet_flag: true,
    });
    API.getMemberData(this.props.match.params.member_id).then(response => {
      if (response && response.client_id) {
        const {
          member_id,
          first_name,
          middle_name,
          last_name,
          client_id,
        } = response;
        const firstName = first_name || '';
        /** MVP-MS998 */
        // const mi = middle_name ? middle_name.charAt(0) + '.' : '';
        const mi = middle_name ? middle_name : '';
        const lastName = last_name || '';
        const suffixName = response['suffix'] && response['suffix'] !== null && response['suffix'].trim() !== ''
          ? ', ' + response['suffix'] : '';
        const memberId = member_id || '';
        const planType = get(response, 'plan_type', '');
        this.setState(
          {
            check_internet_flag: false,
            memberData: response,
            memberName: firstName + ' ' + mi + ' ' + lastName + suffixName,
            memberId: memberId,
            planType: planType,
            client_id: client_id
          },
          () => {
            this.getClientContracts(client_id);
          },
        );
      } else {
        this.setState({
          loading: false,
        });
      }
    });
  };

  handleCloseLostInternetConnectionModal = () => {
    this.setState({
      check_internet_flag: false,
    })
  }

  getClientContracts = (clientId: string) => {
    const contractId = this.props.match.params.contract_id;
    this.setState({
      check_internet_flag: true,
    })
    // console.log('contract id update8',this.props.match.params)  
    // console.log('contract id update9',clientId,)  
    
    API.getClientContracts(clientId,true).then(response2 => {
      // console.log('contract id update10',response2,)  
      if (response2 && response2.length > 0) {
        const clientDtls = response2[0];
        const clientName = clientDtls.registered_name || '';
        let contractData = null;
        try {
          contractData = clientDtls.contract_objects.find(
            ({ _id }) => _id === contractId,
          );
        } catch (err) { }
        if (contractData) {
          console.log('memberprofile>contract page', contractData)
          let contractName = contractData['client_contract_id'] ? contractData['client_contract_id'] : '-'
          // contractData['version'] ?
            // 'Contract ' + contractData['version'] : '-';
          setTimeout(() => {
            this.setState({
              check_internet_flag: false,
              loading: false,
              clientName: clientName,
              contractData: contractData,
              contractName: contractName,
            });
          }, 3000)
        } else {
          this.setState({
            loading: false,
          });
        }
      } else {
        this.setState({
          loading: false,
        });
      }
    });
  };

  // public 
  public render(): JSX.Element {
    const {
      loading,
      selectedTab,
      memberName,
      memberId,
      contractName,
      client_id
      // clientName,
    } = this.state;

    let member_id = this.props.match.params.member_id;
    let contract_id = this.props.match.params.contract_id;
    let clientMembersPath = '#/membership/client-member-contract/' + member_id + '/' + contract_id;
    let membersPath = '#/membership/member-contract/' + member_id + '/' + contract_id;
    let currentPath = this.props.history.location.hash;
    let page = currentPath === clientMembersPath ? 'client_members' : currentPath === membersPath ? 'members' : ''

    let newUrl;
    if (page === 'client_members') newUrl = '#/membership/clients-profile/' + client_id + '/member'
    else if (page === 'members') newUrl = '#/membership/members'


    let breadcrumbs_items = [
      {
        label: 'Members',
        url: newUrl,
      },
      {
        label: memberName,
        url: '#/membership/view-member/' + this.props.match.params.member_id,
      },
      {
        label: memberId,
        url: '#/membership/view-member/' + this.props.match.params.member_id,
      },
      {
        // label: contractName + ' - ' + (clientName ? clientName : 'N.A.'),
        label: contractName,
        url: '',
      },
    ];

    // const BlankTemplate = () => {
    //   return (
    //     <Grid container>
    //       <Grid item xs={12}>
    //         <Typography>Test</Typography>
    //       </Grid>
    //     </Grid>
    //   );
    // };



    return (
      <div className={clsx('MemberContractPage')}>
        {loading ? <Loader /> : null}
        <Grid container>
          <Grid item xs={12}>
            <Grid container className={clsx('page-header')}>
              <Grid item xs={12}>
                <Components.UI.BreadcrumbsComponent items={breadcrumbs_items} />
              </Grid>
              <Grid item xs={12}>
                <Typography className={clsx('member-contract-header')}>
                  {contractName}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} className={clsx('page-tab-title')}>
            <MemberContractTabs
              id={'member_contract_tabs'}
              data-cy={'member_contract_tabs'}
              value={selectedTab}
              onChange={this.handleChange}
              aria-label="page tab"
            >
              {page_tab_titles.map((title: string, index: number) => (
                <MemberContractTab
                  label={title}
                  id={'member_contract_tab_' + index}
                  data-cy={'member_contract_tab_' + index}
                  aria-controls={'member-contract-tabpanel-' + index}
                  key={index}
                />
              ))}
            </MemberContractTabs>
            <div className={clsx('tab-title-shadow')} />
          </Grid>
          <Grid item xs={12} className={clsx('page-tab-content')}>
            {selectedTab === page_tab_titles.indexOf('Benefits') ? (
              <MemberBenefits
                memberId={this.props.match.params.member_id}
                contractId={this.props.match.params.contract_id}
                setIsLoading={this.setLoading}
              />
            ) : selectedTab === page_tab_titles.indexOf('Exclusions') ? (
              <ExclusionsSection
                contractID={this.props.match.params.contract_id}
                planType={this.state.planType}
              />
            ) : selectedTab === page_tab_titles.indexOf('Partner Network') ? (
              <PartnerNetworks
                memberId={this.props.match.params.member_id}
                contractId={this.props.match.params.contract_id}
                planType={this.state.planType}
                setIsLoading={this.setLoading}
              />
            ) : // <BlankTemplate />
                  selectedTab === page_tab_titles.indexOf('Contract') ? (
                    <Contract
                      contract_name={'2018 Contract'}
                      contract_id={this.props.match.params.contract_id}
                      setIsLoading={this.setLoading}
                      memberId={this.props.match.params.member_id}
                    />
                  ) : null}
          </Grid>
        </Grid>
        <LostInternetModal
          id="Lost Internet Modal"
          isModalOpen={this.state.check_internet_flag}
          onClose={this.handleCloseLostInternetConnectionModal}
        />
      </div>
    );
  }
}

const mapStateToProps = (state: Store) => state.home;

const mapDispatchToProps = (dispatch: Dispatch) => ({
  Map: bindActionCreators(MemberContractPageActions.Map, dispatch),
});

export { mapStateToProps, mapDispatchToProps };
