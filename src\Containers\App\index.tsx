/**
 *
 * App
 *
 * This component is the skeleton around the actual pages, and should only
 * contain code that should be seen on all pages. (e.g. navigation bar)
 */

import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import { renderRoutes } from 'react-router-config';

import { Switch, Route } from 'react-router-dom';
// import { makeStyles } from '@mui/styles';
import { Containers } from '@hims/core';
import { createBrowserHistory } from 'history';

// import HomePage from 'Pages/Home/Loadable';
import { MemberContractPage } from 'Pages/MemberContract/MemberContractPage';
import { EncodeMember } from 'Pages/EncodeMember/EncodeMemberPage';
import { VerifyMember } from 'Pages/VerifyMember/VerifyMemberPage';
import { ViewMemberPage } from 'Pages/ViewMember/ViewMemberPage';
import { VoidMasterlist } from 'Pages/VoidMasterlist/VoidMasterlistPage';
import { PersonProfile } from 'Pages/PersonProfile';
import { MemberPage } from 'Pages/Member/MemberPage';
import { Dashboard } from 'Pages/Dashboard/Dashboard';
import { Clients } from 'Pages/Clients/Clients';
import { Components } from '@hims/core';
import { ProcessBatchesPage } from 'Pages/ProcessBatches/ProcessBatchesPage';

import { createTheme } from '@material-ui/core/styles';
import { ThemeProvider } from '@material-ui/styles';
import createPalette from '@material-ui/core/styles/createPalette';
import createTypography from '@material-ui/core/styles/createTypography';
import { ClientsProfilePage } from 'Pages/ClientsProfile/ClientsProfilePage';
import { TicketListPage } from 'Pages/ViewMember/TicketList/TicketListPage';
import TestLoginPage from 'Pages/Login/TestLoginPage';
import { API } from 'Pages/API';

import { UploadAFile } from 'Pages/ClientsProfile/Component/UploadAFile/UploadAFilePage';
import { ManualRenewPage } from 'Pages/ClientsProfile/Component/Renew/ManualRenewPage';
import { ModifyMemberChangesPage } from '../../Pages/ModifyMemberChanges/ModifyMemberChangesPage';
import { ActiveSuspendMemberCount } from 'Pages/Reports/ActiveSuspendMemberCount'; 
import { MembersProcessedPerUser } from 'Pages/Reports/MembersProcessedPerUser';
import { MatchPersonProfile } from 'Pages/MatchPersonProfile'

import HMOInformationBenefitsPage from 'Pages/ClientsProfile/Component/HMOInformationBenefits/Loadable';

import './custom-style.scss';

import { Settings } from 'Pages/Settings/Settings';
import { AddEmailTemplates } from 'Pages/Settings/EmailTemplates/AddEmailTemplates/AddEmailTemplates';
import { ViewEmailTemplates } from 'Pages/Settings/EmailTemplates/AddEmailTemplates/ViewEmailTemplates';
import { EditEmailTemplates } from 'Pages/Settings/EmailTemplates/AddEmailTemplates/EditEmailTemplates';
import { VerifyTermination } from 'Pages/VerifyTermination/VerifyTerminationPage'
import GenerateTransmittalRequest from 'Pages/GenerateTransmittalRequest/GenerateTransmittalRequest';
import * as crypto from 'crypto';


const history = createBrowserHistory();
// const history = require("history").createBrowserHistory()
let backendUrl =
  localStorage.getItem('CLIENT_URL') ?
    localStorage.getItem('CLIENT_URL') :
    localStorage.getItem('XDEV_CLIENT_URL');

export const Profile = (): JSX.Element => {
  return (
    <Components.UI.ProfilePage
      url={backendUrl || ''}
    />
  )
}

const theme = createTheme({
  typography: createTypography(createPalette({}), {
    fontFamily: 'usual',
  }),
  palette: {
    text: {
      primary: '#272E4C',
      secondary: '#272E4C',
      hint: '#272E4C',
    },
    primary: {
      main: '#3AB77D',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: 'rgba(58, 183, 125, 0.38)',
      contrastText: '#FFFFFF',
    },
  },
  overrides: {
    MuiLink: {
      underlineAlways: {
        fontWeight: 'bold',
        color: '#272E4C',
      },
      underlineHover: {
        textDecoration: 'underline',
        '&:hover': {
          fontWeight: 'bold',
          color: '#272E4C',
        },
      },
    },
    MuiTab: {
      root: {
        textTransform: 'none',
        textAlign: 'left',
        color: 'rgba(39, 46, 76, 0.5)',
        '&.Mui-selected': {
          textDecoration: 'underline',
          fontSize: '14px',
          fontWeight: 'bold',
          color: 'rgba(58, 183, 125, 1)',
        },
        '@media (min-width:960px)': {
          minWidth: '120px',
        },
      },
    },
    MuiTabs: {
      indicator: {
        backgroundColor: '#3AB77D',
        display: 'none',
      },
    },
    MuiButton: {
      containedPrimary: {
        textTransform: 'inherit',
        boxShadow: '0px 3px 6px #1e207129',
        '&:hover': {
          backgroundColor: '#008650',
        },
      },
      containedSecondary: {
        textTransform: 'inherit',
        border: '1px solid #0000001A',
        boxShadow: '0px 1px 3px #00000033',
        '&:hover': {
          backgroundColor: '#008650',
        },
      },
    },
    MuiListItem: {
      root: {
        paddingTop: '2.5px',
        paddingBottom: '2.5px',
      },
    },
    MuiListSubheader: {
      root: {
        color: '#B7BAC7',
        fontSize: '0.813em',
        fontWeight: 'bold',
        textTransform: 'uppercase',
      },
    },
    MuiListItemText: {
      root: {
        color: '#272E4C',
        fontSize: '0.875em',
      },
    },
    MuiFormHelperText: {
      root: {
        color: '272E4C',
      },
      contained: {
        marginLeft: '0px',
      },
    },
    MuiOutlinedInput: {
      input: {
        padding: '12px 16px',
      },
    },
    MuiInputBase: {
      root: {
        backgroundColor: '#FFFFFF',
        fontSize: '0.813em',
        '.PageSizeSelector.inputRoot > .MuiSelect-selectMenu': {
          border: '1px solid rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiTable: {
      root: {
        fontWeight: 600,
        '& thead': {
          backgroundColor: '#DDDFE3',
        },
        '& tbody tr:nth-of-type(even)': {
          backgroundColor: '#F6F7FA',
          '&.same-field': {
            backgroundColor: '#FEDCDB',
            '& :first-child': {
              borderLeft: '3px solid #FD5451',
            },
          },
        },
        '& tbody tr:nth-of-type(odd)': {
          backgroundColor: '#FFFFFF',
          '&.same-field': {
            backgroundColor: '#FEDCDB',
            '& :first-child': {
              borderLeft: '3px solid #FD5451',
            },
          },
        },
      },
    },
    MuiTableCell: {
      root: {
        fontSize: '0.8125rem',
        borderBottom: '1px solid rgba(224, 224, 224, 0.3);',
      },
      head: {
        color: '#272E4C',
        fontSize: '0.8125rem',
        fontWeight: 600,
      },
      body: {
        color: '#272E4C',
        fontWeight: 600,
      },
      stickyHeader: {
        backgroundColor: '#DDDFE3',
      },
    },
    MuiStepper: {
      root: {
        padding: '0',
        backgroundColor: '#F5F7FB',
      },
    },
    MuiBreadcrumbs: {
      root: {
        fontSize: '0.75rem',
        textTransform: 'uppercase',
      },
    },
    MuiTypography: {
      root: {
        fontSize: '0.75rem',
      },
      body1: {
        fontSize: '0.75rem',
      },
    },
    MuiStepLabel: {
      label: {
        fontWeight: 600,
        '&.MuiStepLabel-active': {
          fontWeight: 600,
        },
        '&.MuiStepLabel-completed': {
          fontWeight: 600,
        },
      },
    },
    MuiStepIcon: {
      root: {
        color: '#E7E9EF',
      },
      text: {
        opacity: 0,
      },
    },
    MuiSnackbar: {
      root: {
        bottom: '10px',
      },
    },
    MuiSnackbarContent: {
      root: {
        width: '100%',
        backgroundColor: '#363636',
        opacity: 0.64,
        borderRadius: '0px',
        lineHeight: 4.25,
        border: '1px solid #707070',
      },
    },
    MuiMenuItem: {
      root: {
        fontSize: '0.9375rem',
        '&:first-child': {
          paddingTop: '10px',
        },
        '&:last-child': {
          paddingTop: '10px',
        },
      },
    },
    MuiFormControl: {
      marginNormal: {
        marginTop: '10px',
      },
    },
    MuiInput: {
      underline: {
        '&:before': {
          borderBottom: '1px solid rgba(0, 0, 0, 0)',
        },
      },
    },
    MuiButtonBase: {
      root: {
        '&.textColorInherit': {
          '&.selected': {
            textDecoration: 'underline',
          },
        },
        '& .MuiIconButton-label': {
          '& input[type="radio"]': {
            border: '1px solid #707070',
          },
        },
        '&.MuiTab-root': {
          alignItems: 'left',
        },
        '&.PrivateSwitchBase-root': {
          padding: '0px 5px',
        },
        '&.MuiPickersDay-dayDisabled': {
          color: '#ff0000',
        },
      },
    },
    MuiFormControlLabel: {
      root: {
        '& .MuiFormControlLabel-label': {
          fontWeight: 600,
        },
      },
    },
    MuiRadio: {
      root: {
        padding: '0px 5px',
      },
      colorPrimary: {
        '&.Mui-checked': {
          color: '#1E2071',
        },
      },
    },
    MuiPaper: {
      elevation1: {
        boxShadow: '0px 3px 6px rgb(0, 0, 0, 0.29)',
        '@media (max-width:0px)': {
          maxWidth: '300px',
        },
        '@media (max-width:600px)': {
          maxWidth: '580px',
        },
        '@media (max-width:960px)': {
          maxWidth: '940px',
        },
        '@media (max-width:1280px)': {
          maxWidth: '1260px',
        },
        '@media (max-width:1920px)': {
          maxWidth: '1900px',
        },
      },
    },
    MuiCheckbox: {
      colorPrimary: {
        '&.Mui-checked': {
          color: '#1E2071',
        },
      },
    },
    MuiSelect: {
      select: {
        '&.Mui-disabled': {
          backgroundColor: '#E4E4E4',
        },
      },
    },
    MuiDialog: {
      paperScrollPaper: {
        maxHeight: 'calc(100% - 50px)',
      },
    },
    // MuiPickersDay: {
    //   dayDisabled: {
    //     color: '#00000060',
    //   },
    // },
  },
});
const NotFoundPage = Containers.NotFoundPage;
let listening = false;

export default function App() {
  const [selectedMenu, setSelectedMenu] = useState('');
  const [username, setUsername] = useState('');
  const [fullname, setFullname] = useState('');
  const [profilePic, setProfilePic] = useState('');
  const [unauthorizedModalOpen, setUnauthorizedModalOpen] = useState(false);
  const onLogout = () => {
    window.location.replace('../index.html#/');
  };
  console.log(React)
  if (!listening) {
    history.listen(location => {
      listening = true;
      if (
        location['hash'] === '#/membership/dashboard' ||
        location['hash'] === '#/membership/' ||
        location['hash'] === '#/membership' //||
        // window.location.hash === '#/membership/dashboard' ||
        // window.location.hash === '#/membership/' ||
        // window.location.hash === '#/membership'
        // location['hash'] === '#/membership/dashboard' ||
        // location['hash'] === '#/membership/' ||
        // location['hash'] === '#/membership'
      ) {
        setSelectedMenu('dashboard');
      } else {
        console.log('history.listen bug', location)
      }
    });
  }

  // const RenderComponent = Component => {
  //   return class App extends React.Component {
  //     componentDidMount() { }

  //     render() {
  //       return <Component {...this.props} />;
  //     }
  //   };
  // };
//_selected: string,  
  const RenderComponent = (_selected: string, Component ) => {
    return class App extends React.Component {
      componentDidMount() { }

      render() {
        return <Component {...this.props} />;
      }
    };
  };
  let routes = [
    {
      path: '/',
      component: RenderComponent('dashboard', Dashboard),
      exact: true,
    },
    {
      path: '/match-person-profile/:ticket_id',
      component: RenderComponent('dashboard', MatchPersonProfile)
    },
    {
      path: '/member-contract/:member_id/:contract_id/:contract_check',
      component: RenderComponent('dashboard', MemberContractPage),
    },
    {
      path: '/client-member-contract/:member_id/:contract_id',
      component: RenderComponent('dashboard', MemberContractPage),
    },
    {
      path: '/client-benefits/:client_id/:contract_id',
      component: RenderComponent('dashboard', HMOInformationBenefitsPage),
    },
    {
      path: '/member-contract/:member_id/:contract_id',
      component: RenderComponent('dashboard', MemberContractPage),
    },
    {
      path: '/encode/:type/:ticket_id/:proposal_id/:client_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/encode/:type/:ticket_id/:client_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/encode/:type/:client_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/encode/:type/',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/encode-member/:ticket_id/:proposal_id/:client_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/add-members/:ticket_id/:client_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/add-members/:ticket_id/:client_id/:proposal_id',
      component: RenderComponent('dashboard', EncodeMember),
    },
    
    {
      path: '/add-members',
      component: RenderComponent('dashboard', EncodeMember),
    },
    {
      path: '/verify/:type/:ocp/:ticket_id/:member_upload_id/:client_id',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/verify/:type/:ticket_id/:member_upload_id/:client_id',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/verify-dependent/:type/:ticket_id/:member_upload_id/:client_id',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/verify-member/:ocp/:ticket_id/:member_upload_id/:ticket_number',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/verify-member/:ticket_id/:member_upload_id/:ticket_number',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/verify-member/:ticket_id/:member_upload_id',
      component: RenderComponent('dashboard', VerifyMember),
    },
    {
      path: '/view-member/:member_id/:source_id',
      component: RenderComponent('dashboard', ViewMemberPage),
    },
    {
      path: '/view-client-member/:member_id',
      // path: '/view-member/:member_id',
      component: RenderComponent('dashboard', ViewMemberPage),
    },
    {
      path: '/view-member/:member_id',
      // path: '/view-member/:member_id',
      component: RenderComponent('dashboard', ViewMemberPage),
    },
    {
      path: '/void-masterlist/:ticket_id/:member_void_upload_id/:ticket_number',
      component: RenderComponent('dashboard', VoidMasterlist),
    },
    {
      path: '/person-profile/:member_id/:page/:client_id/:person_memberId',
      component: RenderComponent('dashboard', PersonProfile),
    },
    {
      path: '/members',
      component: RenderComponent('dashboard', MemberPage),
    },
    {
      path: '/process-batches-ocp/:case_id/:client_id/:client_name/:old_case_id/:ticket_id',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/process-batches/:case_id/:client_id/:client_name/:old_case_id/:ticket_id',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/process-batches-ocp/:case_id/:client_id/:client_name/:ticket_id',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/process-batches/:case_id/:client_id/:client_name/:ticket_id',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/process-batches/:client_name/:client_id',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/process-batches-transmittal-request/:client_id/:case_id/:tracking_no',
      component: RenderComponent('dashboard', GenerateTransmittalRequest),
    },
    {
      path: '/process-batches',
      component: RenderComponent('dashboard', ProcessBatchesPage),
    },
    {
      path: '/clients',
      component: RenderComponent('dashboard', Clients),
    },
    {
      path: '/dashboard',
      component: RenderComponent('dashboard', Dashboard),
    },
    {
      path: '/clients-profile/batch-terminate/:client_id/:ticket_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-terminate/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    // {
    //   path: '/verify-termination/:ticket_id/:client_id/:index',
    //   component: RenderComponent(UploadAFile),
    // },
    {
      path: '/clients-profile/batch-suspend/:client_id/:ticket_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-suspend/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/verify-suspension-collection/:ticket_id/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/verify-suspension/:ticket_id/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-correction/:client_id/:ticket_id/:dashboard_id/:page_flag',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-correction/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-renewal/:client_id/:ticket_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/clients-profile/batch-renewal/:client_id',
      component: RenderComponent('dashboard', UploadAFile),
    },
    {
      path: '/verify-termination/:client_id/:member_upload_id/:case_id/:ticket_id',
      component: RenderComponent('termination', VerifyTermination),
    },
    // {
    //   path: '/clients-profile/manual-renewal/:type/:client_id',
    //   component: RenderComponent('client', ManualRenewPage),
    // }, manual renewal should be with ticket id
    {
      path: '/clients-profile/manual-renewal/:type/:client_id/:ticket_id',
      component: RenderComponent('client', ManualRenewPage),
    },
    {
      path: '/clients-profile/:client_id',
      component: RenderComponent('client', ClientsProfilePage),
    },
    {
      path: '/ticketlist/:tab',
      component: RenderComponent('ticket', TicketListPage),
    },
    {
      path: '/ticketlist',
      component: RenderComponent('ticket', TicketListPage),
    },
    {
      path: '/settings',
      component: RenderComponent('setiings', Settings),
    },
    {
      path: '/email-templates/add',
      component: RenderComponent('email', AddEmailTemplates),
      exact: true,
    },
    {
      path: '/email-templates/:id',
      component: RenderComponent('email', ViewEmailTemplates),
      exact: true,
    },
    {
      path: '/email-templates/edit/:id',
      component: RenderComponent('email', EditEmailTemplates),
      exact: true,
    },
    {
      path: '/',
      component: RenderComponent('dashboard', Dashboard),
      exact: true,
    },
    {
      path: '/profile',
      component: RenderComponent('profile', Profile),
    },
    {
      path: '/login',
      component: RenderComponent('login', TestLoginPage),
    },
    {
      path: '/modify-member-changes/:case_id/:ticket_id',
      component: RenderComponent('ticket', ModifyMemberChangesPage),
    },
    {
      path: '/reports/activesuspendmembercount',
       component: RenderComponent('reports', ActiveSuspendMemberCount),
    },
    {
      path: '/reports/membersprocessedperuser',
       component: RenderComponent('reports',  MembersProcessedPerUser),
    },
  ];
  // let routes = [
  //   {
  //     path: '/',
  //     component: RenderComponent(Dashboard),
  //     exact: true,
  //   },
  //   {
  //     path: '/match-person-profile/:ticket_id',
  //     component: RenderComponent(MatchPersonProfile)
  //   },
  //   {
  //     path: '/member-contract/:member_id/:contract_id/:contract_check',
  //     component: RenderComponent(MemberContractPage),
  //   },
  //   {
  //     path: '/client-member-contract/:member_id/:contract_id',
  //     component: RenderComponent(MemberContractPage),
  //   },
  //   {
  //     path: '/client-benefits/:client_id/:contract_id',
  //     component: RenderComponent(HMOInformationBenefitsPage),
  //   },
  //   {
  //     path: '/member-contract/:member_id/:contract_id',
  //     component: RenderComponent(MemberContractPage),
  //   },
  //   {
  //     path: '/encode/:type/:ticket_id/:proposal_id/:client_id',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/encode/:type/:ticket_id/:client_id',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/encode/:type/:client_id',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/encode/:type/',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/encode-member/:ticket_id/:proposal_id/:client_id',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/add-members/:ticket_id/:proposal_id/:client_id',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/add-members',
  //     component: RenderComponent(EncodeMember),
  //   },
  //   {
  //     path: '/verify/:type/:ocp/:ticket_id/:member_upload_id/:client_id',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/verify/:type/:ticket_id/:member_upload_id/:client_id',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/verify-dependent/:type/:ticket_id/:member_upload_id/:client_id',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/verify-member/:ocp/:ticket_id/:member_upload_id/:ticket_number',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/verify-member/:ticket_id/:member_upload_id/:ticket_number',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/verify-member/:ticket_id/:member_upload_id',
  //     component: RenderComponent(VerifyMember),
  //   },
  //   {
  //     path: '/view-member/:member_id/:source_id',
  //     component: RenderComponent(ViewMemberPage),
  //   },
  //   {
  //     path: '/view-client-member/:member_id',
  //     // path: '/view-member/:member_id',
  //     component: RenderComponent(ViewMemberPage),
  //   },
  //   {
  //     path: '/view-member/:member_id',
  //     // path: '/view-member/:member_id',
  //     component: RenderComponent(ViewMemberPage),
  //   },
  //   {
  //     path: '/void-masterlist/:ticket_id/:member_void_upload_id/:ticket_number',
  //     component: RenderComponent(VoidMasterlist),
  //   },
  //   {
  //     path: '/person-profile/:member_id/:page/:client_id/:person_memberId',
  //     component: RenderComponent(PersonProfile),
  //   },
  //   {
  //     path: '/members',
  //     component: RenderComponent(MemberPage),
  //   },
  //   {
  //     path: '/process-batches-ocp/:case_id/:client_id/:client_name/:old_case_id/:ticket_id',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/process-batches/:case_id/:client_id/:client_name/:old_case_id/:ticket_id',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/process-batches-ocp/:case_id/:client_id/:client_name/:ticket_id',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/process-batches/:case_id/:client_id/:client_name/:ticket_id',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/process-batches/:client_name/:client_id',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/process-batches-transmittal-request/:client_id/:case_id/:tracking_no',
  //     component: RenderComponent(GenerateTransmittalRequest),
  //   },
  //   {
  //     path: '/process-batches',
  //     component: RenderComponent(ProcessBatchesPage),
  //   },
  //   {
  //     path: '/clients',
  //     component: RenderComponent(Clients),
  //   },
  //   {
  //     path: '/dashboard',
  //     component: RenderComponent(Dashboard),
  //   },
  //   {
  //     path: '/clients-profile/batch-terminate/:client_id/:ticket_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-terminate/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   // {
  //   //   path: '/verify-termination/:ticket_id/:client_id/:index',
  //   //   component: RenderComponent(UploadAFile),
  //   // },
  //   {
  //     path: '/clients-profile/batch-suspend/:client_id/:ticket_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-suspend/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/verify-suspension-collection/:ticket_id/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/verify-suspension/:ticket_id/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-correction/:client_id/:ticket_id/:dashboard_id/:page_flag',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-correction/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-renewal/:client_id/:ticket_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/clients-profile/batch-renewal/:client_id',
  //     component: RenderComponent(UploadAFile),
  //   },
  //   {
  //     path: '/verify-termination/:client_id/:member_upload_id/:ticket_id',
  //     component: RenderComponent(VerifyTermination),
  //   },
  //   {
  //     path: '/clients-profile/manual-renewal/:type/:client_id/:ticket_id',
  //     component: RenderComponent(ManualRenewPage),
  //   },
  //   {
  //     path: '/clients-profile/manual-renewal/:type/:client_id',
  //     component: RenderComponent(ManualRenewPage),
  //   },
  //   {
  //     path: '/clients-profile/:client_id',
  //     component: RenderComponent(ClientsProfilePage),
  //   },
  //   {
  //     path: '/ticketlist/:tab',
  //     component: RenderComponent(TicketListPage),
  //   },
  //   {
  //     path: '/ticketlist',
  //     component: RenderComponent(TicketListPage),
  //   },
  //   {
  //     path: '/settings',
  //     component: RenderComponent(Settings),
  //   },
  //   {
  //     path: '/email-templates/add',
  //     component: RenderComponent(AddEmailTemplates),
  //     exact: true,
  //   },
  //   {
  //     path: '/email-templates/:id',
  //     component: RenderComponent(ViewEmailTemplates),
  //     exact: true,
  //   },
  //   {
  //     path: '/email-templates/edit/:id',
  //     component: RenderComponent(EditEmailTemplates),
  //     exact: true,
  //   },
  //   {
  //     path: '/',
  //     component: RenderComponent(Dashboard),
  //     exact: true,
  //   },
  //   {
  //     path: '/profile',
  //     component: RenderComponent(Profile),
  //   },
  //   {
  //     path: '/login',
  //     component: RenderComponent(TestLoginPage),
  //   },
  //   {
  //     path: '/modify-member-changes/:case_id/:ticket_id',
  //     component: RenderComponent(ModifyMemberChangesPage),
  //   },
  //   {
  //     path: '/reports/activesuspendmembercount',
  //      component: RenderComponent(ActiveSuspendMemberCount),
  //   },
  //   {
  //     path: '/reports/membersprocessedperuser',
  //      component: RenderComponent( MembersProcessedPerUser),
  //   },
  // ];
  const prefix = '/membership';
  routes.map(route => {
    if (route.path !== '/profile') {
      route.path = `${prefix}${route.path}`
    }
  });

  React.useEffect(() => {
    getUserData()
  }, [])

  // let checkTokenInterval:any = {};

  React.useEffect(()=>{
    // const urlArray = window.location.href.split('/');1
    isTokenValid();
    // clearInterval(checkTokenInterval);
    // checkTokenInterval = setInterval(()=>{
    //   isTokenValid();
    //   if(window.location.href.split('/').includes('invalidSession')){
    //     clearInterval(checkTokenInterval);
    //     setUnauthorizedModalOpen(true);
    //   }
    // },1000);
    setTimeout(() => {
      if(window.location.href.split('/').includes('invalidSession')){
        console.log("Check Invalid Session Detected.");
        setUnauthorizedModalOpen(true);
      }
    }, 1000);
  },[window.location.href]);

  React.useEffect(()=>{
    console.log("Check Unauthorized Modal Value Changed: ",unauthorizedModalOpen);;
  },[unauthorizedModalOpen])

    const fieldsToEncrypt = [
      'first_name',
      'last_name',
      'middle_name',
      'username',
      'full_name',
      'email',
    ]
    const key = crypto
      .createHash('sha256')
      .update(String(process.env.REACT_APP_HASHING_PASSWORD))
      .digest('base64')
      .substring(0, 32);
    const cipherAlgorithm = 'aes-256-gcm';

    const encryptCredentials = (text: any) => {
      const initVector = crypto.randomBytes(16);
      const initVectorHex = initVector.toString('hex');
      const cipher = crypto.createCipheriv(cipherAlgorithm, key, initVector);
      const encoded = cipher.update(text, 'utf8', 'hex') + cipher.final('hex');
      const authTag = cipher.getAuthTag().toString('hex');
      const metaAndEncoded = [authTag, initVectorHex, encoded].join('|');
      return metaAndEncoded;
    };

    const decryptCredentials = (text: string): string => {
      const [authTag, initVectorHex, encrypted] = text.split('|');
      const initVector = Buffer.from(initVectorHex, 'hex');
      const decipher = crypto.createDecipheriv(cipherAlgorithm, key, initVector);
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));

      let decrypted = decipher
        .update(Buffer.from(encrypted, 'hex'))
        .toString('utf-8');
      decrypted += decipher.final().toString('utf-8');

      return decrypted;
    };

    const encryptUsers = (user: any) => {
      const encryptedUsers = { ...user };
      fieldsToEncrypt.forEach(field => {
        if (encryptedUsers[field]) {
          encryptedUsers[field] = encryptCredentials(encryptedUsers[field]);
        }
      });
      return encryptedUsers;
    };

    console.log(encryptUsers)

  const getUserData = async () => {
    let user = await API.getUserDataFromDb();

    console.log('user body', user)
    if (user !== null) {
            fieldsToEncrypt.forEach((field: string) => {
              if (user !== null && field in user) { // (user[field] as string)
                user[field] = decryptCredentials(user[field]);
              }
            });

          }
    // const decryptedData = data.body.map(user => decryptUsers(user))   

    setUsername(user.loginUsername);
    setFullname(user.userName);
    setProfilePic(user.profilePic);

  }

  const isTokenValid = async () => {
    let url_array = window.location.href.split('/');
    url_array = url_array.slice(url_array.findIndex(arr=>arr==='membership'));
    //Jerico ====> Added this part to allow login in development side. 
    //No risk since this will force the page to login page
    // console.log("Check End URL: ",url_array);
    let isValid = true,
      isPmValid = true,
      isApiValid = true;

    if (
      !localStorage.getItem('api_token') ||
      !localStorage.getItem('pm_token')
    ) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await API.checkToken(localStorage.getItem('api_token'));
        let pm_token = await API.checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }
    // if(url_array.pop()==='login') {
    //   if(url_array.findIndex(arr=>!['membership','login'].includes(arr))>-1){
    //     var a = '../#/membership/login';
    //     window.location.href = a;
    //   } else {
    //     return;
    //   }
    // }
    if (!isValid || !isPmValid || !isApiValid) {
      var a = '../membership/index.html#/membership/invalidSession';
      window.location.href = a;
      //onLogout();
    }
  }

  const title = (localStorage.getItem('HIMS_TITLE') || '').replace(/-/g, ' ') + 'Membership';
  const icon = process.env.PUBLIC_URL + '/' + localStorage.getItem('HIMS_ICON') || '';

  return (
    <ThemeProvider theme={theme}>
      <Components.UI.Idle url={backendUrl || ''} />
      <Components.UI.UnauthorizedSessionModal
        isModalOpen={unauthorizedModalOpen}
        onClose={()=>{
          setUnauthorizedModalOpen(false);
          window.location.replace('../index.html#/');
        }}
      />
      <Helmet
        titleTemplate={`${title} - %s`}
        defaultTitle={title || ''}>
        <meta name="description" content="NEO application" />
        <link rel="shortcut icon" href={icon} />
      </Helmet>
      <Components.UI.PageLayout
        selectedMenu={selectedMenu}
        setSelectedMenu={setSelectedMenu}
        history={history}
        page={'membership'}
        onLogout={onLogout}
        currentModule={'Membership'}
        username={username}
        fullname={fullname}
        profilePic={profilePic}
      >
        <Switch>
          {renderRoutes(routes)}
          <Route path="" component={NotFoundPage} />
        </Switch>
      </Components.UI.PageLayout>
    </ThemeProvider>
  );
}
