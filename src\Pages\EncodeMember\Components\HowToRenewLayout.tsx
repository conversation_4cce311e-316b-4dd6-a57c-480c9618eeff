import { Box, Button, FormControl, FormControlLabel, makeStyles, Radio, RadioGroup, Typography } from "@material-ui/core";
import { useState } from "react";

interface IHowToRenewLayout {
  onCancel: () => void;
  onNext?: (renewal_type: string | null) => void;
}

const HowToRenewLayout: React.FC<IHowToRenewLayout> = (props) => {
  const [value, setValue] = useState<string | null>(null); // Explicitly type value as string | null

  // Handler for radio button changes
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value);
  };

  const RadioStyle = makeStyles({
    radioList: {
      fontSize: "16px",
      color: "black",
      "&.Mui-checked": {
        color: "black",
      },
    },
  });

  const radioStyle = RadioStyle();
  return (
    <Box style={{ width: "27vw" }}>
      <Typography style={{fontSize: "16px"}} gutterBottom>
        How do you want to renew the members?
      </Typography>
      <FormControl>
        <Box style={{ paddingLeft: 40, marginTop: 20, fontSize: "16px" }}>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            name="radio-buttons-group"
            value={value}
            onChange={handleChange}
            
          >
            <FormControlLabel
              value="uploadRenewal"
              control={<Radio className={radioStyle.radioList} />}
              label={<Typography style={{fontSize: "16px"}}>Upload Renewal</Typography>}
              style={{ marginBottom: 20}}
            />
            <FormControlLabel
              value="manualRenewal"
              control={<Radio className={radioStyle.radioList} />}
              label={<Typography style={{fontSize: "16px"}}>Manual Renewal</Typography>}
              style={{ fontSize: "16px"}}
            />
          </RadioGroup>
        </Box>
      </FormControl>
      <Box style={{ marginTop: 30, display: "flex", justifyContent: "flex-end", gap: 10 }}>
        <Button
          data-cy="upload_member_cancel_btn"
          id="upload_member_cancel_btn"
          variant="contained"
          style={{
            fontSize: '16px',
            fontWeight: 600,
            color: '#6B6B6B',
            backgroundColor: '#FFFFFF',
            border: '1px solid #D9D9D9',
            padding: '8px 24px',
            borderRadius: '4px',
            cursor: 'pointer',
            textTransform: 'none',
          }}  
          size="small"
          onClick={props.onCancel}
        >
          Cancel
        </Button>
        <Button
          data-cy="upload_member_next_btn"
          id="upload_member_next_btn"
          variant="contained"
          color="primary"
          size="small"
            style={{
            fontSize: '16px',
            fontWeight: 600,
            padding: '8px 24px',
            borderRadius: '4px',
            cursor: 'pointer',
          }}  
          onClick={() => props.onNext?.(value)} // Use optional 
          disabled={!value}
        >
          Next
        </Button>
      </Box>
    </Box>
  );
};

export default HowToRenewLayout;