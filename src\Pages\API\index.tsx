// import moment from 'moment';
import { get, isNil, trim } from 'lodash';
import { Utils } from '@hims/core';

let API_URL = localStorage.getItem('CLIENT_URL')
  ? localStorage.getItem('CLIENT_URL')
  : localStorage.getItem('XDEV_CLIENT_URL');

let OCP_URL = localStorage.getItem('OCP_URL')
  ? localStorage.getItem('OCP_URL')
  : localStorage.getItem('XDEV_OCP_URL');

let DDS_URL = localStorage.getItem('DDS_URL')
  ? localStorage.getItem('DDS_URL')
  : localStorage.getItem('XDEV_DDS_URL');

// let DDS_USERNAME = localStorage.getItem('DDS_USERNAME');
// let DDS_PASSWORD = localStorage.getItem('DDS_PASSWORD');

//let DDS_USERNAME = 'dds_qa_admin';
let DDS_PASSWORD = 'Passw0rdo!';

// JUNE 15, 2020: DDS_BASE_URL for xdev.env & xdevssl.env temporarily kept/commented
// http://api-ddstr.veridata-stg.com/

// console.log('API_URL', API_URL)
// console.log('CLIENT_URL',localStorage.getItem('CLIENT_URL'))
// console.log('XDEV_CLIENT_URL',localStorage.getItem('XDEV_CLIENT_URL'))
// console.log('OCP_URL',localStorage.getItem('OCP_URL'))
// console.log('XDEV_OCP_URL',localStorage.getItem('XDEV_OCP_URL'))
// console.log('DDS_URL',localStorage.getItem('DDS_URL'))
// console.log('XDEV_DDS_URL',localStorage.getItem('XDEV_DDS_URL'))
// console.log('CLIENT_URL', localStorage.getItem('CLIENT_URL'))

export const BASE_API_URL = API_URL;

export class API {
  public static async getUserDataFromDb() {
    const userIdFromDb = await Utils.StorageService('user_data', 'user_id');
    const firstNameFromDb = await Utils.StorageService(
      'user_data',
      'first_name',
    );
    const lastNameFromDb = await Utils.StorageService('user_data', 'last_name');
    const pmakerUidFromDb = await Utils.StorageService(
      'user_data',
      'pmaker_uid',
    );
    const usernameFromDb = await Utils.StorageService('user_data', 'username');
    const roleFromDb = await Utils.StorageService('user_data', 'role');
    const groupNameDb = await Utils.StorageService('user_data', 'group').catch(
      err => {
        console.log(err);
      },
    );
    const profilePicDb = await Utils.StorageService('user_data', 'profile_pic');
    const rbacFromDb = await Utils.StorageService('user_data', 'rbac');
    const officeFromDb = await Utils.StorageService('user_data', 'location');
    const groupNameDbV2 = await Utils.StorageService('user_data', 'group');

    const userId = get(userIdFromDb, 'result', '');
    const firstName = get(firstNameFromDb, 'result', '');
    const lastName = get(lastNameFromDb, 'result', '');
    const pmakerUid = get(pmakerUidFromDb, 'result', '');
    const loginUsername = get(usernameFromDb, 'result', '');
    const loginRole = get(roleFromDb, 'result', '');
    const groupName =
      groupNameDb === undefined
        ? { name: 'N/A' }
        : get(groupNameDb, 'result', '');
    const profilePic = get(profilePicDb, 'result', '');
    const rbac = get(rbacFromDb, 'result', '');
    const office = get(officeFromDb, 'result', '');
    const groupNameV2 = get(groupNameDbV2, 'result', '');

    return {
      userId: userId,
      userName: `${firstName} ${lastName}`,
      pmakerUid: pmakerUid,
      loginUsername: loginUsername,
      loginRole: loginRole,
      groupName: groupName.name,
      groupNameV2: groupNameV2.name,
      profilePic: profilePic,
      rbac: rbac,
      office: office,
    };
  }

  public static async getUserPmakerUId() {
    const pmaker_uid = await Utils.StorageService('user_data', 'pmaker_uid');
    return get(pmaker_uid, 'result', '');
  }

  public static async get(url: string, success: Function, fail: Function) {
    fetch(API_URL + url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('pm_token'),
      },
    })
      .then(response => response.json())
      .then(data => success(data))
      .catch(error => fail(error));
  }
  public static async getUserGroup() {
    const group = await Utils.StorageService('user_data', 'group').catch(
      err => {
        console.log(err);
      },
    );
    const groupName =
      group === undefined ? { name: 'N/A' } : get(group, 'result', '');
    return groupName;
  }

  // // public static async getUsersByGroup() {
  //   const group = await Utils.StorageService('user_data', 'group').catch((err)=>{console.log(err)});
  //   const groupName = group === undefined? {name:'N/A'} : get(group, 'result', '')
  //   let usersByGroup = group
  //   return groupName
  // }

  public static async getUsername() {
    const pmaker_uid = await Utils.StorageService('user_data', 'username');
    return get(pmaker_uid, 'result', '');
  }

  static generateClientAuditLogsReport(
    clientId: string,
    format: string,
    query: string | null,
    limit: number,
    skip: number,
    where: any,
    order: string[] = [],
  ) {
    let filters = {
      limit: limit,
      skip: skip,
    };

    filters['where'] = {
      metadata: {
        client_id: clientId,
      },
    };

    if (where) {
      where.map(w => {
        if (w.columnName === 'activity_id') {
          filters['where'][w.columnName] = w.value;
        } else if (w.type === 'string') {
          filters['where'][w.columnName] = { like: w.value, options: 'i' };
        } else {
          filters['where'][w.columnName] = w.value;
        }
      });
    }

    filters['order'] = order;
    let AUDIT_LOGS_URL = '';
    if (query && query.length > 0) {
      AUDIT_LOGS_URL = `${API_URL}client/auditlogsreport?format=${format}&search=${query}?&filter=${JSON.stringify(
        filters,
      )}`;
    } else {
      AUDIT_LOGS_URL = `${API_URL}client/auditlogsreport?format=${format}&filter=${JSON.stringify(
        filters,
      )}`;
    }

    return fetch(AUDIT_LOGS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('Audit Logs Error: ', error, skip, limit);
      });
  }
  public static async getUserFullName() {
    const firstNameFromDb = await Utils.StorageService(
      'user_data',
      'first_name',
    );
    const lastNameFromDb = await Utils.StorageService('user_data', 'last_name');
    const firstName = get(firstNameFromDb, 'result', '');
    const lastName = get(lastNameFromDb, 'result', '');
    return `${firstName} ${lastName}`;
  }

  /**
   * Get access token from indexedDb
   */

  public static async getAccessTokenFromDb(): Promise<string | null> {
    const accessTokenInDb = await Utils.StorageService(
      'user_data',
      'access_token',
    );

    return get(accessTokenInDb, 'result', null);
  }

  private static async getProcessMakerToken(): Promise<string> {
    try {
      // Try getting token from db
      const pmTokenFromDb = await this.getAccessTokenFromDb();
      if (!isNil(pmTokenFromDb) && trim(pmTokenFromDb) !== '') {
        return pmTokenFromDb;
      }
    } catch (e) {
      console.log('getProcessMakerToken Error', e);
    }

    // Try getting token from localStorage
    const pmTokenFromStorage = localStorage.getItem('pm_token');
    if (!isNil(pmTokenFromStorage) && trim(pmTokenFromStorage) !== '') {
      return pmTokenFromStorage;
    }

    throw new Error('USER_DATA_NOT_FOUND');
  }

  static getDownloadLink(public_path: string) {
    if (!API_URL) {
      return '';
    }
    return public_path.replace('public/', API_URL);
  }

  // // public static
  public static async getUserData() {
    let token: string = '';
    try {
      // token = await this.getProcessMakerToken();
      token = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const LOGIN_URL = API_URL + 'login/' + token;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(LOGIN_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getTicketData(ticket_id: any, isPmakerUid?: boolean) {
    let TICKET_URL = API_URL + 'members/ticket/' + ticket_id;
    if (isPmakerUid) {
      TICKET_URL = TICKET_URL + `?isPmakerUid=${isPmakerUid}`;
    }
    let getToken = await this.getAccessTokenFromDb();
    return fetch(TICKET_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getTicketAssignees(ticket_type: any, pmaker_id?: string) {
    let extension = ``;
    if (pmaker_id) {
      extension = pmaker_id;
    } else {
      extension = await this.getUsername();
    }
    // const username = await this.getUsername();
    const request_str =
      '?ticket_type=' + ticket_type + '&requesting_username=' + extension;
    const TICKET_URL = API_URL + 'user-groups/membership' + request_str;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(TICKET_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientsData() {
    const CLIENT_URL = API_URL + 'clients/';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getProvinceData() {
    const PROVINCE_URL = API_URL + 'province-list';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PROVINCE_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getCityData(props: string) {
    const CITY_URL = API_URL + `city-list?filter[where][province]=${props}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CITY_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberByTempId(tempId: string) {
    let filter = {
      where: {
        temp_id: tempId,
      },
    };
    const MEMBER_URL = API_URL + `members?filter=${JSON.stringify(filter)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVerifyMemberInfo(id) {
    console.log('getVerifyMemberInfo1', id);
    const MEMBER_URL = API_URL + `members/basic-info/` + id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getCityByName(props: string) {
    const CITY_URL = API_URL + `city-by-name?name=${props}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CITY_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getBarangayData(props: string) {
    const BARANGAY_URL = API_URL + `barangay-list?filter[where][city]=${props}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(BARANGAY_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putRenewMembers(payload: any, client_id: string) {
    let PUT_URL = API_URL + `members/renewal?client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Renew Members error: ', error);
      });
  }

  public static async getClient(client_id: string) {
    let CLIENT_URL = API_URL + 'clients/id'; // will trigger 404 on blank client ids
    if (client_id && client_id.trim().length > 0) {
      CLIENT_URL = API_URL + 'clients/' + client_id;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientPrincipals(client_id: string) {
    let CLIENT_URL = API_URL + 'members/principal/client/' + client_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getTransmittalListHistory(client_id: string) {
    let CLIENT_URL = API_URL + 'client/transmittal-history/' + client_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('transmittal history error: ', error);
      });
  }

  public static async cancelTransmittal(transmittal_number: any) {
    let PUT_URL =
      API_URL + `client/transmittal-history/${transmittal_number}/cancel`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Cancel Transmittal error: ', error);
      });
  }

  // // publicpublic static async getTransmittalList(client_id: string) {
  //   let CLIENT_URL = API_URL + '/members/print/generate-transmittal/' + client_id;
  //   let getToken = await this.getAccessTokenFromDb();
  //   return fetch(CLIENT_URL, {
  //     method: 'GET',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + getToken,
  //     },
  //   })
  //     .then(response => response.json())
  //     .catch(error => {
  //       console.log('client error: ', error);
  //     });
  // }

  // "https://api-dev.hims.veridata.com.ph/api/client/transmittal-history/"
  // // publicpublic static async cancelTransmittal(clientId: string) { //_id: any
  //       // let DELETE_TRANSMITTAL = API_URL + 'client/transmittal-history/'  + clientId;
  //         let DELETE_TRANSMITTAL = "https://api-dev.hims.veridata.com.ph/api/client/transmittal-history/" + clientId;
  //         let getToken = await this.getAccessTokenFromDb();

  //       console.log('URL', DELETE_TRANSMITTAL)
  //       return fetch(DELETE_TRANSMITTAL, {
  //           method: 'PUT', //DELETE
  //           headers: {
  //               'Content-Type': 'application/json',
  //               Authorization: 'Bearer ' + getToken,
  //           },
  //           body: JSON.stringify(clientId)
  //       })
  //       // .then(response =>  response.json())
  //       .then(response => {
  //         this.getTransmittalListHistory(clientId)
  //           console.log('response 1', response)
  //       })
  //       // .then(response => {
  //       //   console.log('response 1', response)
  //       //   console.log('clientId', clientId)
  //       //   console.log(' this.getTransmittalListHistory(clientId)',  this.getTransmittalListHistory(clientId))
  //       //  // console.log('_id', _id)
  //       //   response.json()
  //       //   console.log('response 2', response)
  //       // })
  //       .catch(error => {
  //           console.log('transmittal delete error', error)
  //           //return error;
  //       });
  //   }

  // // publicpublic static async deleteMemberCancelTermination(payload: any) {
  //   let DELETE_MEMBER_URL = API_URL + 'members/termination';
  //   let getToken = await this.getAccessTokenFromDb();
  //   return fetch(DELETE_MEMBER_URL, {
  //     method: 'DELETE',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + getToken,
  //     },
  //     body: JSON.stringify(payload),
  //   })
  //     .then(response => response.json())
  //     .catch(error => {
  //       console.log('Member Cancel Termination error: ', error);
  //     });
  // }

  public static async getDocuments(selectednav: string, clientId: string) {
    const DOCUMENTS_URL =
      API_URL +
      'documents?filter[where][section]=' +
      selectednav +
      '&filter[where][client_id]=' +
      clientId;
    return fetch(DOCUMENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async searchAuditLogs(
    clientId: string,
    limit: number,
    skip: number,
    where: any,
    order: string = '',
    query?: string,
  ) {
    let filters = {
      limit: limit,
      skip: skip,
    };

    if (query && query.length > 0) {
      filters['search'] = query;
    }

    //console.log(query);

    filters['where'] = {
      metadata: {
        client_id: clientId,
      },
    };

    if (where) {
      console.log('where filter', where);
      where.map(w => {
        if (w.type === 'string') {
          filters['where'][w.columnName] = { like: w.value, options: 'i' };
        } else {
          filters['where'][w.columnName] = w.value;
        }
      });
    }

    // if (order.length === 1) {
    filters['order'] = order;
    // } else {
    //   filters['order'] = order;
    // }
    console.log('fetch filter audit logs', filters, order);
    const AUDIT_LOGS_URL = `${API_URL}audit-logs?filter=${JSON.stringify(
      filters,
    )}`;

    return fetch(AUDIT_LOGS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async getAuditLogs(
    clientId: string,
    limit: number,
    skip: number,
    order: string[] = [],
  ) {
    let filters = {
      limit: limit,
      skip: skip,
    };

    filters['where'] = {
      metadata: {
        client_id: clientId,
      },
    };

    console.log('getAuditLogs1', filters);
    if (order.length === 1) {
      filters['order'] = order[0];
      console.log('getAuditLogs2', filters, order);
    } else {
      filters['order'] = order;
      console.log('getAuditLogs3', filters, order);
    }

    const AUDIT_LOGS_URL = `${API_URL}audit-logs?filter=${JSON.stringify(
      filters,
    )}`;

    console.log('AUDIT_LOGS_URL', AUDIT_LOGS_URL);
    return fetch(AUDIT_LOGS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async getTransmittalList(
    clientId: string | undefined,
    limit: number,
    offset: number,
    tab: string,
    filter?: any,
    order?: any,
    query?: string,
    isSelectAll?: boolean,
    isResCount?: boolean,
  ) {
    if (filter !== undefined && filter.length > 0) {
      filter = filter.filter(item => {
        return !(item.columnName === 'member_status' && item.value === 'All');
      });
    }
    // console.log('FILTER', filter);
    let CLIENTS_URL =
      API_URL +
      `members/print/generate-transmittal?client_id=${clientId}&limit=${limit}&offset=${offset}&tab=${tab}`;

    if (isSelectAll) {
      CLIENTS_URL =
        API_URL +
        `members/print/generate-transmittal?client_id=${clientId}&tab=${tab}`;
    }

    if (limit === 0 && offset === 0) {
      CLIENTS_URL =
        API_URL +
        `members/print/generate-transmittal?client_id=${clientId}&tab='all'`;
    }

    if (query !== null && query !== '') {
      CLIENTS_URL = `${CLIENTS_URL}&query=${query}`;
    }

    if (filter.length > 0) {
      console.log('filters', filter);
      CLIENTS_URL = `${CLIENTS_URL}&filters=${JSON.stringify(filter)}`;
    }

    if (order.length !== 0) {
      CLIENTS_URL = `${CLIENTS_URL}&order=${JSON.stringify(order)}`;
    }

    if (isResCount) {
      CLIENTS_URL = `${CLIENTS_URL}&table=true`;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async getExportIdPictureLink(client_id: string) {
    let LINK_URL =
      API_URL + `members/download/id-picture?client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(LINK_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientsInfo(client_id: string) {
    const CLIENT_URL =
      API_URL +
      '/clients/account/hmo-info/' +
      client_id +
      '?fields=' +
      JSON.stringify({ canCover: false, canAvail: false });
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMembersForRenewals(client_id: string) {
    const CLIENT_URL =
      API_URL + '/members/for-renewal/' + client_id 
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientsWithCorporateNo() {
    const sorting = {
      order: ['registered_name ASC'],
      fields: {
        _id: true,
        registered_name: true,
        member_data_fields: true,
        main_office_address: true,
        branches: true,
        status: true,
        client_id: true,
        contract_id: true,
        contract_no: true,
      },
    };

    // console.log('getClientsWithCorporateNo', sorting)
    // console.log('getClientsWithCorporateNo2', JSON.stringify(sorting))

    const CLIENT_URL = `${API_URL}clients/corporate-account-no?filter=${JSON.stringify(
      sorting,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientContracts(
    client_id: string,
    inlcude_expired?: boolean,
  ) {
    let CONTRACT_URL = API_URL + 'clients/contracts';
    if (client_id) {
      const filter = {
        where: {
          _id: client_id,
        },
      };
      if (inlcude_expired) {
        CONTRACT_URL =
          CONTRACT_URL +
          '?filter=' +
          JSON.stringify(filter) +
          '&include_expired=true';
      } else {
        CONTRACT_URL = CONTRACT_URL + '?filter=' + JSON.stringify(filter);
      }
    }
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CONTRACT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }


  public static async getClientRenewalContract(
    contract_id: string,
    client_id: string
  ) {

    let getToken = await this.getAccessTokenFromDb();
    return fetch(`${API_URL}contract-versions/contract_id/${contract_id}/client_id/${client_id}`, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientDataMap(client_id: string) {
    let DATAMAP_URL = API_URL + 'members/data-mapping';
    if (client_id) {
      const filter = {
        where: {
          client_id: client_id,
        },
      };
      DATAMAP_URL = DATAMAP_URL + '?filter=' + JSON.stringify(filter);
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(DATAMAP_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postNewDataMap(payload: any) {
    const DATAMAP_URL = API_URL + 'members/data-mapping';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DATAMAP_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postUploadedDataForRenewal(payload: any) {
    // const DATAMAP_URL = API_URL + 'members/search-by-member-id';
    const DATAMAP_URL = API_URL + 'members/search-by-member';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DATAMAP_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchDataMap(id: string, payload: any) {
    const DATAMAP_URL = API_URL + 'members/data-mapping/' + id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DATAMAP_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putTransferPrincipal(payload: any, principalId: any) {
    try {
      const userData = await this.getUserDataFromDb();
      payload['user_id'] = userData.userId;
      payload['user_name'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const TRANSFER_PRINCIPAL_URL =
      API_URL + `members/${principalId}/transfer-principal`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(TRANSFER_PRINCIPAL_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async cloneMemberUtilizationLOA(
    userPayload: any,
    old_member_id: string,
    new_member_id: string,
    loaPayload: any,
    claimedUtils?: any,
  ) {
    try {
      const userData = await this.getUserDataFromDb();
      userPayload['user_id'] = userData.userId;
      userPayload['user_name'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const CLONE_LOA_URL =
      API_URL +
      `customer-care/clone-member-utilization-loa/old_member/${old_member_id}/new_member/${new_member_id}` +
      `?new_member_id_display=${loaPayload.new_member_id_display}` +
      `&contract_id=${loaPayload.contract_id}` +
      `&client_id=${loaPayload.client_id}` +
      `&new_contract_id=${loaPayload.new_contract_id}` +
      `&new_client_id=${loaPayload.new_client_id}` +
      `&client_name=${loaPayload.client_name}` +
      `&type=${loaPayload.type}` +
      `&new=${loaPayload.isNew}`;

    let payload = {
      claimedUtils: claimedUtils && claimedUtils.length ? claimedUtils : [],
    };

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLONE_LOA_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchMembersTicket(
    id: string,
    payload: any,
    is_reqssigned?: boolean,
  ) {
    try {
      const userData = await this.getUserDataFromDb();
      let TICKET_URL =
        API_URL + `members/ticket/${id}?username=${userData.userName}`;
      if (is_reqssigned)
        TICKET_URL = TICKET_URL + `&is_reassign=${is_reqssigned}`;
      let getToken = await this.getAccessTokenFromDb();
      return fetch(TICKET_URL, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(payload),
      })
        .then(response => {
          return response;
        })
        .catch(error => {
          console.log('client error: ', error);
          return error;
        });
    } catch (err) {
      console.log('getUserDataFromDb catch err', err);
      return err;
    }
  }

  public static async postUploadMember(payload: any, case_id: string) {
    try {
      const userData = await this.getUserDataFromDb();
      payload['user_id'] = userData.userId;
      payload['user_name'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
      payload['pmaker_token'] = processMakerToken;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const postUploadMemberUrl = `${API_URL}clients/member-upload/contract/background`;
    const pmaker = {
      token: processMakerToken,
      case_id: case_id,
    };

    const MEMBER_UPLOAD_URL = `${postUploadMemberUrl}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async retrieveMemberUploadMembers(
    member_upload_id: string,
    skip: number,
    limit: number,
  ) {
    const MEMBER_UPLOAD_URL = `${API_URL}member-upload/retrieve/${member_upload_id}`;
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify({
        skip: skip,
        limit: limit,
      }),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postUploadVoidMasterlist(
    payload: string,
    case_id: string,
    client_id: string,
    endorsement_details: any,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
      payload['pmaker_token'] = processMakerToken;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const postUploadMemberUrl = `${API_URL}member-void/upload`;
    const pmaker = {
      token: processMakerToken,
      case_id: case_id,
    };

    const MEMBER_UPLOAD_URL = `${postUploadMemberUrl}/${client_id}?pmaker=${JSON.stringify(
      pmaker,
    )}&endorsement_details=${JSON.stringify(endorsement_details)}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getUploadMember(member_upload_id: string) {
    const MEMBER_UPLOAD_URL = `${API_URL}member-upload/${member_upload_id}`; //correct url
    //let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      // method: 'get',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Authorization: 'Bearer ' + getToken,
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        setTimeout(() => {
          console.log('no response catch()', error);
          console.error();
        }, 1000);
      });
  }

  public static async getUploadMemberDetails(member_upload_id: string) {
    // const MEMBER_UPLOAD_URL = `${API_URL}member-upload/details/${member_upload_id}`; //correct url
    const MEMBER_UPLOAD_URL = `${API_URL}member-upload/details/${member_upload_id}`;
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        setTimeout(() => {
          console.log('no response catch()', error);
          console.error();
        }, 1000);
      });
  }

  public static async getVoidMasterlist(member_void_upload_id: string) {
    const MEMBER_UPLOAD_URL = `${API_URL}member-void/${member_void_upload_id}`; //correct url
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        setTimeout(() => {
          console.log('no response catch()', error);
          console.error();
        }, 1000);
      });
  }

  // // public static async getUploadMember2(id: string) { //id
  //    const MEMBER_UPLOAD_URL =  API_URL + 'member-upload/' + id;
  //   // const MEMBER_UPLOAD_URL = 'https://api-dev.hims.veridata.com.ph/api/' + 'member-upload/' + member_upload_id;
  //   let getToken = await this.getAccessTokenFromDb();
  //   console.log('LINK GET MEMBER UPLOAD', MEMBER_UPLOAD_URL)
  //   console.log('member_upload_id', id)
  //   console.log('API_URL', API_URL)
  //   return fetch(MEMBER_UPLOAD_URL, {
  //     method: 'GET',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + getToken,
  //     },
  //   })
  //     .then(response => response.json())
  //     .catch(error => {
  //       console.log('client error: ', error);
  //       setTimeout(() => {
  //         console.log('no response catch()', error)
  //         console.error()
  //       }, 10000)
  //     });
  // }

  // // public static async getUploadMember(id: string) {
  //   const MEMBER_UPLOAD_URL = API_URL + 'member-upload/' + id;
  //   let getToken = await this.getAccessTokenFromDb();
  //   return fetch(MEMBER_UPLOAD_URL, {
  //     method: 'get',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + getToken,
  //     },
  //   })
  //     .then(response => {
  //       // setTimeout(() => {
  //       //   console.log('no response in then()')
  //       // }, 30000)
  //       response.json()
  //     })
  //     .catch(error => {
  //       // setTimeout(() => {
  //       //   console.log('no response in catch()')
  //       // }, 20000)
  //       console.log('client error: ', error);
  //     });
  // }

  // public static
  public static async getProcessedMembers() {
    const GET_URL = API_URL + 'member-stats/processed-members/';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMembersToEnrollOrVerify() {
    const GET_URL = API_URL + 'member-stats/members-to-enroll-verify/';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postOcpLogin(payload: any) {
    const POST_OCP_URL = OCP_URL + 'auth/login/member/';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(POST_OCP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  // @Blueming

  public static async postOcpExceptionReport(payload: any, token: string) {
    const POST_OCP_URL = OCP_URL + 'exception-report/';
    // let getToken = await this.getAccessTokenFromDb();
    return fetch(POST_OCP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVerifyEditMemberUpload(upload_id: string) {
    const GET_VERIFY_EDIT_URL = API_URL + 'member-edit/upload/' + upload_id;
    let getToken = await this.getAccessTokenFromDb();
    console.log('GET VERIFY EDU+IT URL', GET_VERIFY_EDIT_URL);
    return fetch(GET_VERIFY_EDIT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVerifyDependentMemberUpload(upload_id: string) {
    const GET_VERIFY_EDIT_URL =
      API_URL + 'member-edit-civil-status/group/' + upload_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_VERIFY_EDIT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putVerifyEditUpdateList(upload_id: string, payload: any) {
    const PUT_VERIFY_EDIT_URL =
      API_URL + 'member-edit/validate-process/update-list/' + upload_id;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putVerifyDependentUpdateList(
    upload_id: string,
    payload: any,
  ) {
    const PUT_VERIFY_EDIT_URL =
      API_URL + 'member-edit-civil-status/update-list/' + upload_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => (response.status != 204 ? response.json() : response))
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putSaveDependentMemberEdit(
    id: string,
    isReplace: boolean,
    payload: any,
  ) {
    const PUT_VERIFY_EDIT_URL =
      API_URL + '/member-edit-civil-status/' + id + '?replace=' + isReplace;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putSaveMemberEdit(
    id: string,
    isReplace: boolean,
    payload: any,
    upload_id?: string,
    case_id?: string,
  ) {
    let PUT_VERIFY_EDIT_URL = `${API_URL}member-edit/${id}?replace=${isReplace}`;

    // Append query parameters conditionally
    if (upload_id) {
      PUT_VERIFY_EDIT_URL += `&uploadId=${upload_id}`;
    }
    if (case_id) {
      PUT_VERIFY_EDIT_URL += `&pmakerCaseId=${case_id}`;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchUploadMember(
    id: string,
    data: any,
    caseId?: string,
  ) {
    // const MEMBER_UPLOAD_URL = 'https://api-dev.hims.veridata.com.ph/api/' + 'member-upload/' + member_upload_id;
    // const MEMBER_UPLOAD_URL = `${API_URL}member-upload/${member_upload_id}`;
    let MEMBER_UPLOAD_URL = API_URL + 'member-upload/' + id; //correct url

    try {
      const userData = await this.getUserDataFromDb();
      data['userId'] = userData.userId;
      data['userName'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    if (caseId) {
      let processMakerToken: string = '';
      try {
        processMakerToken = await this.getProcessMakerToken();
      } catch (e) {
        throw new Error('USER_DATA_NOT_FOUND');
      }

      const pmaker = {
        token: processMakerToken,
        case_id: caseId,
      };
      console.log('API patchUploadMember', MEMBER_UPLOAD_URL);
      MEMBER_UPLOAD_URL = `${API_URL}member-upload/${id}?pmaker=${JSON.stringify(
        pmaker,
      )}`;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchVoidMasterlist(
    id: string,
    data: any,
    caseId?: string,
  ) {
    let MEMBER_UPLOAD_URL = API_URL + 'member-void/' + id;
    if (caseId) {
      let processMakerToken: string = '';
      try {
        processMakerToken = await this.getProcessMakerToken();
      } catch (e) {
        throw new Error('USER_DATA_NOT_FOUND');
      }

      const pmaker = {
        token: processMakerToken,
        case_id: caseId,
      };
      console.log('API patchVoidMasterlist', MEMBER_UPLOAD_URL);
      MEMBER_UPLOAD_URL = `${API_URL}member-void/${id}?pmaker=${JSON.stringify(
        pmaker,
      )}`;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchValidateResolve(
    id: string,
    caseId: string,
    newlyApprovedMemberIds?: string[],
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const VALIDATE_RESOLVE_URL = `${API_URL}member-upload/validate-resolve/${id}?pmaker=${JSON.stringify(
      pmaker,
    )}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(VALIDATE_RESOLVE_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify({
        newlyApprovedMemberIds: newlyApprovedMemberIds,
      }),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchVoidValidateResolve(
    id: string,
    caseId: string,
    newlyApprovedMemberIds?: string[],
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const VALIDATE_RESOLVE_URL = `${API_URL}member-void/validate-resolve/${id}?pmaker=${JSON.stringify(
      pmaker,
    )}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(VALIDATE_RESOLVE_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify({
        newlyApprovedMemberIds: newlyApprovedMemberIds,
      }),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static sendEmailReport(email: string[]) {
    // let EMAIL_REPORT_URL = API_URL+'/';
    console.log('sendEmailReport emails: ', email);

    return 'response';
  }

  public static async downloadExceptionReport(id: string, data: any) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    const DOWNLOAD_EXCEPTION_REPORT_URL =
      API_URL +
      'member-upload/download/' +
      id +
      '?userData=' +
      JSON.stringify(userData);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DOWNLOAD_EXCEPTION_REPORT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('downloadExceptionReport error: ', error);
      });
  }

  public static async downloadVoidExceptionReport(id: string, data: any) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    const DOWNLOAD_EXCEPTION_REPORT_URL =
      API_URL +
      'member-void/download/' +
      id +
      '?userData=' +
      JSON.stringify(userData);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DOWNLOAD_EXCEPTION_REPORT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('downloadExceptionReport error: ', error);
      });
  }

  public static async downloadExceptionReportVerifyEdit(id: string, data: any) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    const DOWNLOAD_EXCEPTION_REPORT_URL =
      API_URL +
      'member-edit/download/' +
      id +
      '?userData=' +
      JSON.stringify(userData);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DOWNLOAD_EXCEPTION_REPORT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReportVerifyEdit error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('downloadExceptionReportVerifyEdit error: ', error);
      });
  }

  public static async getUploadMemberList() {
    const MEMBER_UPLOAD_URL = API_URL + 'member-upload';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_UPLOAD_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberData(member_id: string) {
    const GET_MEMBER_URL = API_URL + 'members/' + member_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
    .then(response => response.json())
    .catch(error => {
      console.log('client error: ', error);
    });
  }

  public static async getUploadMemberPrincipal(principal_id: string) {
    const GET_MEMBER_URL = `${API_URL}member-upload-members/${principal_id}`;
    const token = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(response => response.json())
      .catch(error => console.log('Client Error: ', error));
  }

  public static async getSelectedPrincipal(principal_id: string) {
    // const GET_MEMBER_URL = `${API_URL}member-validated/${principal_id}`;
    // const token = await this.getAccessTokenFromDb();
    // return fetch(GET_MEMBER_URL, {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     Authorization: `Bearer ${token}`
    //   }
    // })
    // .then(response => response.json())
    // .catch(error => console.log('Client Error: ', error));

    const GET_MEMBER_URL = `${API_URL}members/${principal_id}`;
    const token = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(response => response.json())
      .catch(error => console.log('Client Error: ', error));
  }

  public static async getMemberUploadMemberData(member_id: string) {
    const GET_MEMBER_UPLOAD_URL =
      API_URL + 'member-upload-members/by-temp-id/' + member_id;
    const getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_UPLOAD_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => console.log('client-error: ', error));
  }

  public static async getPersonData(person_id: string) {
    const GET_PERSON_URL = API_URL + 'members/' + person_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_PERSON_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  // Edit Person Profile

  public static async patchPersonData(id: string, payload: any) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }

    let PATCH_PERSON_URL =
      API_URL + `member-person/${id}?userData=${JSON.stringify(userData)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PATCH_PERSON_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getDependentData(member_id: string) {
    const GET_MEMBER_URL =
      API_URL + 'member-edit-civil-status/group/' + member_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberStatusActionDependents(
    memberObject_id: string,
    is_member_id?: boolean,
  ) {
    if (is_member_id) {
      let CLIENT_URL =
        API_URL +
        'members/' +
        memberObject_id +
        '/dependents?is_member_id=' +
        is_member_id;
      let getToken = await this.getAccessTokenFromDb();
      //  console.log('THIS URL getdependentdata', CLIENT_URL)
      return fetch(CLIENT_URL, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
      })
        .then(response => response.json())
        .catch(error => {
          console.log('Member Action Dependents error: ', error);
        });
    } else {
      let CLIENT_URL = API_URL + 'members/' + memberObject_id + '/dependents';
      let getToken = await this.getAccessTokenFromDb();
      // console.log('THIS URL ELSE getdependentdata', CLIENT_URL)
      return fetch(CLIENT_URL, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
      })
        .then(response => response.json())
        .catch(error => {
          console.log('Member Action Dependents error: ', error);
        });
    }
  }

  public static async putMemberSuspension(payload: any) {
    let PUT_MEMBER_URL = API_URL + 'members/suspension';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Suspension error: ', error);
      });
  }

  public static async putMemberSuspensionUpload(
    payload: any,
    ticketId?,
    clientId?,
  ) {
    let PUT_MEMBER_URL =
      API_URL +
      `members/suspension/upload?ticket_id=${ticketId}&client_id=${clientId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Suspension error: ', error);
      });
  }

  public static async putMemberProfilePicture(payload: any) {
    try {
      const userData = await this.getUserDataFromDb();
      payload['user_id'] = userData.userId;
      payload['user_name'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    let PUT_MEMBER_PICTURE_URL = API_URL + `members/profile-picture`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_PICTURE_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Profile Picture error: ', error);
      });
  }

  public static async patchMemberLiftSuspension(payload: any) {
    let PATCH_MEMBER_URL = API_URL + 'members/suspension';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PATCH_MEMBER_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Lift Suspension error: ', error);
      });
  }

  public static async deleteMemberCancelSuspension(payload: any) {
    let DELETE_MEMBER_URL = API_URL + 'members/suspension';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(DELETE_MEMBER_URL, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Cancel Suspension error: ', error);
      });
  }

  public static async deleteMemberCancelTermination(payload: any) {
    let DELETE_MEMBER_URL = API_URL + 'members/termination';
    let getToken = await this.getAccessTokenFromDb();

    const userData = await this.getUserDataFromDb();
    let userObj: any = {
      user_id: userData['userId'] ? userData['userId'] : '',
      user_fullname: userData['loginUsername'] ? userData['loginUsername'] : '', // userData['userName'] ? userData['userName'] : '',
      terminated_by:
        payload && payload.terminated_by !== undefined
          ? payload.terminated_by
          : '',
      endorsed_by:
        payload && payload.endorsed_by !== undefined ? payload.endorsed_by : '',
      username:
        payload && payload.terminated_by !== undefined
          ? payload.terminated_by
          : '',
      // user_name:  payload && payload.terminated_by !== undefined ? payload.terminated_by : '',
    };
    return fetch(DELETE_MEMBER_URL, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Cancel Termination error: ', error);
      });
  }

  public static async getMemberCorrectionOCP(caseId: any) {
    let GET_MEMBER_URL = API_URL + `member-edit/ocp/correction/${caseId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Cancel Termination error: ', error);
      });
  }

  public static async putMemberReactivation(payload: any) {
    let PUT_MEMBER_URL = API_URL + 'members/reactivation';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Reactivation error', error);
      });
  }

  public static async putSingleMemberReactivation(
    memberObject_id: string,
    payload: any,
  ) {
    let PUT_MEMBER_URL =
      API_URL + 'members/' + memberObject_id + '/reactivation';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Single Member Reactivation error', error);
      });
  }

  public static async putMemberTerminationByFileUpload(payload: any) {
    let PUT_MEMBER_URL = API_URL + 'members/termination/upload';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Termination by File Upload error: ', error);
      });
  }

  //ACTIVITY LOGS FOR UM
  public static async getReceiverData(payload: any) {
    let GET_USERS_URL =
      API_URL + `users/list?filter=${JSON.stringify(payload)}`;
    let getToken = await this.getAccessTokenFromDb();

    console.log('getReceiverData URL', GET_USERS_URL);
    console.log('getReceiverData URL payload', payload);
    return fetch(GET_USERS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      // body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('get Receiver Data error', error);
      });
  }

  public static async getMSUserReport(payload: any) {
    // let GET_USERS_URL = API_URL + `users/ms-list?filter=${JSON.stringify(payload)}`;

    let GET_USERS_URL =
      API_URL +
      `users/ms-list?main_module=Membership${JSON.stringify(payload)}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(GET_USERS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      // body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('get Receiver Data error', error);
      });
  }

  public static async getAllMSUsers(payload: any) {
    // let GET_USERS_URL = API_URL + `users/ms-list?filter=${JSON.stringify(payload)}`;
    console.log('getReceiverData PAYLOAD', JSON.stringify(payload));
    let GET_USERS_URL =
      API_URL + `all-ms-users/list?${JSON.stringify(payload)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_USERS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      // body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('get Receiver Data error', error);
      });
  }

  public static async activityClaimedTicket(ticket_id, ticket, username) {
    let CLAIM_TICKET_URL =
      API_URL + `members/${ticket_id}/claimed-ticket/${username}`;
    console.log('CLAIM URL', CLAIM_TICKET_URL);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(CLAIM_TICKET_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(ticket),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }
  //activity enroll memberlist
  // const MEMBER_UPLOAD_URL = API_URL + 'member-upload';

  public static async activityCreateAddMemberTicket(
    // ticket_id,
    // ticket,
    // username
    ticketId,
    payload,
    username,
  ) {
    let ENROLL_MEMBERLIST_URL =
      API_URL +
      `members/${ticketId}/activity-create-add-member-ticket/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ENROLL_MEMBERLIST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async activityEnrollMemberList(
    // ticket_id,
    // ticket,
    // username
    ticketId,
    payload,
    username,
  ) {
    let ENROLL_MEMBERLIST_URL =
      API_URL + `members/${ticketId}/enrollmemberlist/${username}`;
    console.log('CLAIM URL11', ENROLL_MEMBERLIST_URL);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ENROLL_MEMBERLIST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async activityAddMembers(ticketId, payload, username) {
    let ENROLL_MEMBERLIST_URL =
      API_URL + `members/${ticketId}/activity-add-members/${username}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(ENROLL_MEMBERLIST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async activityEnrollManualEncode(ticketId, payload, username) {
    let ENROLL_MEMBERLIST_URL =
      API_URL + `members/${ticketId}/activity-manual-encode/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ENROLL_MEMBERLIST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  //upload  members save for now
  public static async activityUploadSaveForNow(ticketId, data, username) {
    let ACTIVITY_UPLOAD_SAVE =
      API_URL + `members/${ticketId}/activity-upload-save-for-now/${username}`;
    console.log('CLAIM URL', ACTIVITY_UPLOAD_SAVE, ticketId);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_SAVE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  //upload  members save for now
  public static async activityUploadEnrollMemberlistSaveForNow(
    ticketId,
    data,
    username,
  ) {
    let ACTIVITY_UPLOAD_SAVE =
      API_URL +
      `members/${ticketId}/activity-upload-enroll-memberlist-save-for-now/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_SAVE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async activityEnrollManualSaveForNow(ticketId, data, username) {
    let ACTIVITY_UPLOAD_SAVE =
      API_URL +
      `members/${ticketId}/activity-manual-encode-save-for-now/${username}`;
    console.log('CLAIM URL', ACTIVITY_UPLOAD_SAVE, ticketId);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_SAVE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async activityCreateManualEncodeTicket(
    ticketId,
    payload,
    username,
  ) {
    let ENROLL_MEMBERLIST_URL =
      API_URL +
      `members/${ticketId}/activity-create-manual-encode-ticket/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ENROLL_MEMBERLIST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  //activity edit member

  public static async activityEditMember(memberId, data, username) {
    let EDIT_MEMBER_URL =
      API_URL + `members/${memberId}/editmember/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(EDIT_MEMBER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Edit member error', error);
      });
  }

  public static async assignClaimedTicket(ticket_id, ticket, username) {
    let ASSIGN_TICKET_URL =
      API_URL + `members/${ticket_id}/assign-ticket/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ASSIGN_TICKET_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(ticket),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityTerminate(memberId, data, username) {
    let CLAIM_TICKET_URL =
      API_URL + `members/${memberId}/activity-terminate/${username}`;
    console.log('CLAIM URL', CLAIM_TICKET_URL);
    let getToken = await this.getAccessTokenFromDb();
    // console.log('activity termination', data)

    return (
      fetch(CLAIM_TICKET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityCancelTermination(memberId, data, username) {
    let CLAIM_TICKET_URL =
      API_URL + `members/${memberId}/activity-cancel-termination/${username}`;
    let getToken = await this.getAccessTokenFromDb();
    return (
      fetch(CLAIM_TICKET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityVerifyMember(memberId, data, username) {
    let CLAIM_TICKET_URL =
      API_URL + `members/${memberId}/activity-verify-member/${username}`;

    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(CLAIM_TICKET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activitySuspend(memberId, data, username) {
    let CLAIM_TICKET_URL =
      API_URL + `members/${memberId}/activity-suspend/${username}`;
    console.log('CLAIM URL', CLAIM_TICKET_URL);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLAIM_TICKET_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityDownloadVerifyExceptionReport(
    id,
    data,
    username,
  ) {
    let ACTIVITY_DOWNLOAD_EXCEPTION_URL =
      API_URL +
      `member-upload/download/${id}/activity-download-verify-exception/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_DOWNLOAD_EXCEPTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityEmailVerifyExceptionReport(id, data, username) {
    let ACTIVITY_DOWNLOAD_EXCEPTION_URL =
      API_URL +
      `members/notification/exception/report/${id}/activity-email-verify-exception/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_DOWNLOAD_EXCEPTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityDownloadVerifyEditReport(id, data, username) {
    let ACTIVITY_DOWNLOAD_EXCEPTION_URL =
      API_URL +
      `members/notification/exception/report/${id}/activity-download-verify-edit-report/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_DOWNLOAD_EXCEPTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityEmailVerifyEditReport(id, data, username) {
    let ACTIVITY_DOWNLOAD_EXCEPTION_URL =
      API_URL +
      `members/notification/exception/report/${id}/activity-email-verify-edit-report/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_DOWNLOAD_EXCEPTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityDownloadVerifyTerminationExceptionReport(
    id,
    data,
    username,
  ) {
    let ACTIVITY_DOWNLOAD_EXCEPTION_URL =
      API_URL +
      `member-upload/download/${id}/activity-download-termination-exception/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_DOWNLOAD_EXCEPTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityEditMemberVerifyEditTicket(
    memberId,
    data,
    username,
  ) {
    let PUT_VERIFY_EDIT_URL =
      API_URL +
      `member-edit/${memberId}/activity-edit-member-verify-edit/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVerifyEditUploadDocument(
    memberId,
    data,
    username,
  ) {
    let PUT_VERIFY_EDIT_URL =
      API_URL +
      `member-edit/${memberId}/activity-upload-file-verify-edit/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityRemarksMemberProfile(id, data, username) {
    let ACTIVITY_PROFILE_REMARKS =
      API_URL +
      `member-remark/${id}/activity-remarks-member-profile/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return (
      fetch(ACTIVITY_PROFILE_REMARKS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(data),
      })
        .then(response => response.json())
        // .then(response => response.json())
        .catch(error => {
          console.log('Claimed Ticket error', error);
        })
    );
  }

  public static async activityBatchUploaEditTicketStart(
    memberUploadId,
    data,
    username,
  ) {
    let ACTIVITY_UPLOAD_BATCH_EDIT =
      API_URL +
      `member-edit/upload/${memberUploadId}/activity-batch-upload-start/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_BATCH_EDIT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityBatchUploaEditTicket(
    memberUploadId,
    data,
    username,
  ) {
    let ACTIVITY_UPLOAD_BATCH_EDIT =
      API_URL +
      `member-edit/upload/${memberUploadId}/activity-batch-upload/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_BATCH_EDIT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activitySaveForNowBatchUploaEditTicket(
    ticketId,
    data,
    username,
  ) {
    let PUT_VERIFY_EDIT_URL =
      API_URL +
      `member-edit/upload/${ticketId}/activity-save-for-now-batch-upload/${username}`;
    console.log('CLAIM URL', PUT_VERIFY_EDIT_URL, ticketId);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(PUT_VERIFY_EDIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityBatchUploadSuspension(ticketId, data, username) {
    let ACTIVITY_BATCH_SUSPENSION_URL =
      API_URL +
      `members/suspension/upload/${ticketId}/activity-batch-upload-suspension/${username}`;
    console.log('CLAIM URL', ACTIVITY_BATCH_SUSPENSION_URL, ticketId);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_BATCH_SUSPENSION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidUploadStart(ticketId, data, username) {
    let ACTIVITY_VOID_UPLOAD_START =
      API_URL +
      `members/ticket/${ticketId}/activity-void-upload-start/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_VOID_UPLOAD_START, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidUpload(ticketId, data, username) {
    let ACTIVITY_VOID_UPLOAD =
      API_URL +
      `member-void/upload/${ticketId}/activity-void-upload/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_VOID_UPLOAD, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidUploadSaveForNow(ticketId, data, username) {
    let ACTIVITY_VOID_UPLOAD_SAVE =
      API_URL +
      `members/ticket/${ticketId}/activity-void-upload-save-for-now/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_VOID_UPLOAD_SAVE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidEdit(id, data, username) {
    let ACTIVITY_VERIFY_VOID_EDIT =
      API_URL + `member-void/${id}/activity-void-edit/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_VERIFY_VOID_EDIT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidUploadDocument(id, data, username) {
    let VOID_UPLOAD_URL =
      API_URL + `member-void/${id}/activity-void-upload-document/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(VOID_UPLOAD_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidDownloadExceptionReport(id, data, username) {
    let DOWNLOAD_EXCEPTION_REPORT_URL =
      API_URL +
      `member-void/download/${id}/activity-void-download-exception/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(DOWNLOAD_EXCEPTION_REPORT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVoidEmailExceptionReport(id, data, username) {
    let MEMBER_VOID_EMAIL_EXCEPTION =
      API_URL + `member-void/${id}/activity-void-email-exception/${username}`;
    console.log('CLAIM URL', MEMBER_VOID_EMAIL_EXCEPTION, id);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(MEMBER_VOID_EMAIL_EXCEPTION, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityReactivateMember(member_id, data, username) {
    let POST_ACTIVITY_REACTIVATE_PROFILE =
      API_URL +
      `members/reactivation/${member_id}/activity-reactivate-member/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(POST_ACTIVITY_REACTIVATE_PROFILE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityBatchUploadTermination(
    memberUploadId: string,
    data,
    username,
  ) {
    let ACTIVITY_UPLOAD_BATCH_TERMINATION =
      API_URL +
      `members/${memberUploadId}/activity-batch-upload-termination/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_UPLOAD_BATCH_TERMINATION, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async activityVerifyEditUploadSaveForNow(
    ticketId,
    data,
    username,
  ) {
    let ACTIVITY_VOID_UPLOAD_SAVE =
      API_URL +
      `members/ticket/${ticketId}/activity-verify-edit-save-for-now/${username}`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTIVITY_VOID_UPLOAD_SAVE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async cancelMember(username) {
    let CANCEL_URL = API_URL + `members/cancel-member/${username}`;
    console.log('CLAIM URL', CANCEL_URL);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(CANCEL_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      // body: JSON.stringify(data),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Claimed Ticket error', error);
      });
  }

  public static async putTransferDependent(
    memberObject_id: string,
    payload: any,
  ) {
    let PUT_MEMBER_URL =
      API_URL + `members/${memberObject_id}/transfer-dependent`;
    let getToken = await this.getAccessTokenFromDb();

    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Transfer Dependent error', error);
      });
  }

  public static async putCivilStatusChangeSubmit(
    memberObject_id: string,
    payload: any,
  ) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }

    let PUT_MEMBER_URL =
      API_URL +
      'member-edit-civil-status/submit/' +
      memberObject_id +
      `?userData=${JSON.stringify(userData)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Civil Status Change Submit error: ', error);
      });
  }

  public static async putPersonCivilStatusChangeSubmit(
    memberObject_id: string,
    payload: any,
  ) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    let PUT_MEMBER_URL =
      API_URL +
      'member-person/' +
      memberObject_id +
      `/change-civil-status?userData=${JSON.stringify(userData)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Civil Status Change Submit error: ', error);
      });
  }

  // 5716 client id added as param
  public static async putMemberCorrectionByFileUpload(
    payload: any,
    ticketId: string,
    clientID: string,
    fromOcp?: boolean,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    let additonalQuery = '';
    if (fromOcp) {
      additonalQuery = '&from_ocp=true';
    }

    let PUT_MEMBER_URL =
      API_URL +
      'member-edit/upload?pmaker[token]=' +
      processMakerToken +
      '&pmaker[case_id]=' +
      ticketId +
      '&clientID=' +
      clientID +
      additonalQuery;

    //Handle types
    for (let typeKey of payload) {
      if (typeKey['is_member_consent']) {
        if (typeKey['is_member_consent'] === 'false') {
          typeKey['is_member_consent'] = false;
        } else if (typeKey['is_member_consent'] === 'true') {
          typeKey['is_member_consent'] = true;
        } else if (typeKey['is_member_consent'] === '') {
          typeKey['is_member_consent'] = false;
        }
      }

      if (typeKey['is_philhealth_rider']) {
        if (typeKey['is_philhealth_rider'] === 'false') {
          typeKey['is_philhealth_rider'] = false;
        } else if (typeKey['is_philhealth_rider'] === 'true') {
          typeKey['is_philhealth_rider'] = true;
        } else if (typeKey['is_philhealth_rider'] === '') {
          typeKey['is_philhealth_rider'] = false;
        }
      }

      if (typeKey['is_vip']) {
        if (typeKey['is_vip'] === 'false') {
          typeKey['is_vip'] = false;
        } else if (typeKey['is_vip'] === 'true') {
          typeKey['is_vip'] = true;
        } else if (typeKey['is_vip'] === '') {
          typeKey['is_vip'] = false;
        }
      }
    }

    console.log('member edit upload: ', JSON.stringify(payload));

    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Member Correction by File Upload error: ', error);
      });
  }

  public static async validateMembers(payload: any, clientID: string) {
    const CHECK_MEMBER_URL = `${API_URL}member-edit/validate-members?clientID=${clientID}`;

    console.log('member edit upload: ', JSON.stringify(payload));

    try {
      const token = await this.getAccessTokenFromDb();
      const response = await fetch(CHECK_MEMBER_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      return await response.json();
    } catch (error) {
      console.error('Member Correction by File Upload error: ', error);
      return { error }; // optional: return a standardized error structure
    }
  }

  public static async getDdsRequestStatus(
    transmittalNumber: any,
    token?: number,
  ) {
    // let POST_DDS_URL = DDS_URL + 'dds/requestor/requests';
    let baseUrl;
    if (DDS_URL !== null) {
      const splittedUrl = DDS_URL.split('/');
      baseUrl = splittedUrl[0] + '//' + splittedUrl[2];
      if (baseUrl.indexOf('dds') === -1) {
        baseUrl = baseUrl + '/dds';
      }
    }
    let GET_DDS_URL = baseUrl + '/requests/' + transmittalNumber + '/status';
    let getToken = await this.getAccessTokenFromDb();
    console.log('DDS token: ', token);
    // const headers:any = {
    //   'Content-Type': 'application/json'
    // }
    // if(token){
    //   headers['Authorization']= 'Bearer ' + getToken // token;
    // }
    // headers['Authorization'] = 'Bearer ' + getToken // token;
    return fetch(GET_DDS_URL, {
      method: 'GET',
      // headers: headers,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken, //token,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Get Request Status error: ', error);
      });
  }

  public static async getDdsRequest(request_id: any, token?: number) {
    // let POST_DDS_URL = DDS_URL + 'dds/requestor/requests';
    console.log('DDS Get Request token ', token);
    let baseUrl;
    let getToken = await this.getAccessTokenFromDb();
    if (DDS_URL !== null) {
      const splittedUrl = DDS_URL.split('/');
      baseUrl = splittedUrl[0] + '//' + splittedUrl[2];
    }
    // let GET_DDS_URL = baseUrl + '/dds/requestor/requests/' + request_id;
    let GET_DDS_URL = baseUrl + '/dds/requestor/requests/' + request_id;

    return fetch(GET_DDS_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken, // token,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Get Request error: ', error);
      });
  }
  //token: number

  public static async putDdsCancelRequest(request_id: any, reason: string) {
    // let POST_DDS_URL = DDS_URL + 'dds/requestor/requests';
    let baseUrl;
    if (DDS_URL !== null) {
      const splittedUrl = DDS_URL.split('/');
      baseUrl = splittedUrl[0] + '//' + splittedUrl[2];
    }
    // let GET_DDS_URL = baseUrl + '/dds/requestor/requests/' + request_id + '/cancel';
    let GET_DDS_URL =
      baseUrl + '/dds/requestor/requests/' + request_id + '/cancel';
    let getToken = await this.getAccessTokenFromDb();
    let payload = {
      reason: reason,
    };

    return fetch(GET_DDS_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken, //token,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Get Request error: ', error);
      });
  }

  public static async postDds(payload: any) {
    //, token: number
    // let POST_DDS_URL = DDS_URL + 'dds/requestor/requests';
    // let baseUrl;
    // if (DDS_URL !== null) {
    //   const splittedUrl = DDS_URL.split('/');
    //   baseUrl = splittedUrl[0] + "//" + splittedUrl[2]
    // }
    // let POST_DDS_URL = baseUrl + '/dds/requestor/requests';
    let getToken = await this.getAccessTokenFromDb();
    let POST_DDS_URL = DDS_URL + 'requestor/requests';

    return fetch(POST_DDS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken, //token,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Post: ', error);
      });
  }

  public static async ddsLogin() {
    const userData = await this.getUserDataFromDb();

    let payload = {
      username: userData.loginUsername,
      password: DDS_PASSWORD,
      // username: 'dds_qa_admin',
      // password: 'Passw0rdo!'
    };

    // let baseUrl;
    // if (DDS_URL !== null) {
    //   const splittedUrl = DDS_URL.split('/');
    //   baseUrl = splittedUrl[0] + "//" + splittedUrl[2]
    // }
    // let DDS_LOGIN_URL = baseUrl + '/dds/login';

    if (DDS_URL === null) {
      // DDS_URL = 'https://api-dev.hims.veridata.com.ph/dds/'
      console.log('dds url @api');
    }

    let DDS_LOGIN_URL = DDS_URL + 'login';

    let getToken = await this.getAccessTokenFromDb();
    console.log('DDS_URL', DDS_LOGIN_URL);
    console.log('DDS_TOKEN', getToken);
    return fetch(DDS_LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Login: ', error);
      });
  }

  public static async ddsRequestorLogin() {
    const userData = await this.getUserDataFromDb();
    let payload = {
      username: userData.loginUsername,
      password: DDS_PASSWORD,
      module: 'REQUESTOR',
      himsUser: {
        first_name: localStorage.getItem('first_name'),
        last_name: localStorage.getItem('last_name'),
        username: userData.loginUsername,
        employee_id: localStorage.getItem('employee_id'),
        org_id: '3C8302B42731CDB13074AED9CE56EAE5',
        department: userData.groupName,
        location: userData.office,
      },
    };
    console.log('getting userData', userData, payload, DDS_URL);

    // if(DDS_URL === null) {
    //   DDS_URL = 'https://api-dev.hims.veridata.com.ph/dds/'
    // }

    // let DDS_LOGIN_URL = DDS_URL + 'login';
    let DDS_LOGIN_URL = DDS_URL + 'login';

    let getToken = await this.getAccessTokenFromDb();
    console.log('DDS_URL', DDS_LOGIN_URL);
    console.log('DDS_TOKEN', getToken);
    return fetch(DDS_LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('DDS Login: ', error);
      });
  }

  public static async getPersonsToMatch(ticket_id: string) {
    let GET_MEMBER_URL = API_URL + 'member-person/match/' + ticket_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('GET Persons to Match error: ', error);
      });
  }

  public static async putPersonsToMatch(ticket_id: string, payload: any) {
    let PUT_MEMBER_URL = API_URL + 'member-person/match/' + ticket_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('PUT Persons to Match error: ', error);
      });
  }

  public static async ddsPrintPreview(object_id: string, token?: number) {
    // let GET_DDS_URL = DDS_URL + `dds/requests/${object_id}/print-preview?by=id`;
    //let GET_DDS_URL = DDS_URL + `dds/requests/${object_id}/transmittal-slip-pdf`;
    // let baseUrl;
    // if (DDS_URL !== null) {
    //   const splittedUrl = DDS_URL.split('/');
    //   baseUrl = splittedUrl[0] + "//" + splittedUrl[2]
    // }
    // let GET_DDS_URL = baseUrl + `/dds/requests/${object_id}/transmittal-slip-pdf`;
    let baseUrl;
    if (DDS_URL !== null) {
      const splittedUrl = DDS_URL.split('/');
      baseUrl = splittedUrl[0] + '//' + splittedUrl[2];
    }
    let GET_DDS_URL =
      baseUrl + `/dds/requests/${object_id}/transmittal-slip-pdf`;
    const headers = {
      'Content-Type': 'application/json',
    };
    let getToken = await this.getAccessTokenFromDb();
    if (token) {
      headers['Authorization'] = 'Bearer ' + getToken; //token
    }
    return (
      fetch(GET_DDS_URL, {
        method: 'GET',
        headers: headers,
      })
        // .then(response => response.text())
        .then(response => {
          console.log(response, 'DDS PRINT REVIEW');
          console.log(baseUrl); // I add this because this variable should be read to remove error on build
          return response.blob();
        })
        .catch(error => {
          console.log('DDS Print Preview: ', error);
        })
    );
  }

  public static async suspendMember(member_id: string, payload: any) {
    let PUT_MEMBER_URL = API_URL + 'members/' + member_id + '/suspension';

    let getToken = await this.getAccessTokenFromDb();
    return fetch(PUT_MEMBER_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async cancelMemberTermination(member_id: string) {
    let CANCEL_MEMBER_TERMINATION =
      API_URL + 'members/' + member_id + '/termination';

    let payload = {};
    try {
      const userData = await this.getUserDataFromDb();
      payload['user_id'] = userData.userId;
      payload['user_name'] = userData.loginUsername;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CANCEL_MEMBER_TERMINATION, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async cancelSuspension(member_id: string) {
    let username: string = '';
    let payload = {};
    try {
      const userData = await this.getUserDataFromDb();
      payload['user_id'] = userData.userId;
      payload['username'] = userData.userName;
      username = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const CANCEL_SUSPENSION = `${API_URL}members/${member_id}/suspension?username=${username}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CANCEL_SUSPENSION, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async viewMemberDependents(principalId: string) {
    const GET_DEPENDENTS_URL = `${API_URL}members/${principalId}/dependents`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_DEPENDENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getPlantype(clientId: string) {
    const GET_PLANTYPE_URL = `${API_URL}client/${clientId}/plantype`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_PLANTYPE_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('plan type error: ', error);
      });
  }

  public static async memberTerminateBatch(
    payload: any,
    client_id?: string,
    ticket_id?: string,
  ) {
    let BATCH_URL = `${API_URL}members/termination`;
    if (client_id && ticket_id) {
      BATCH_URL = BATCH_URL.concat(
        '?',
        `client_id=${client_id}`,
        `&ticket_id=${ticket_id}`,
      );
    }
    let getToken = await this.getAccessTokenFromDb();
    return fetch(BATCH_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberDependents(principalTempId: string) {
    const filter = JSON.stringify({
      where: {
        principal_temp_id: principalTempId,
        // member_status: 'Active',
      },
    });

    const GET_DEPENDENTS_URL = `${API_URL}members?filter=${filter}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_DEPENDENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  //getmember dependent total
  // async countMemberDependentsTotal(principalTempId: string, clientId: string) {
  //     const filter = JSON.stringify({
  //       where: {
  //         principal_temp_id: principalTempId,
  //         clientId: clientId,
  //         member_type: 'Dependent'
  //       },
  //     });

  //     const GET_DEPENDENTS_URL = `${API_URL}client_id=${clientId}/members?filter=${filter}`;
  //     console.log('URL FOR GETDEPENDENTS!!!', GET_DEPENDENTS_URL)
  //     let getToken = await this.getAccessTokenFromDb();
  //     return fetch(GET_DEPENDENTS_URL, {
  //       method: 'get',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         Authorization: 'Bearer ' + getToken,
  //       },
  //     })
  //       .then(response => response.json())
  //       .catch(error => {
  //         console.log('client error: ', error);
  //       });

  //   }

  //getmember dependent total

  public static async getMemberDependentsTotal(principalTempId: string) {
    const filter = JSON.stringify({
      where: {
        principal_temp_id: principalTempId,
        member_type: 'Dependent',
      },
    });

    const GET_DEPENDENTS_URL = `${API_URL}members?filter=${filter}`;
    console.log('URL FOR GETDEPENDENTS!!!', GET_DEPENDENTS_URL);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_DEPENDENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberDependentSuspended(principalTempId: string) {
    const filter = JSON.stringify({
      where: {
        principal_temp_id: principalTempId,
        member_type: 'Dependent',
        member_status: 'Suspended', // 'Expired',
      },
    });

    const GET_DEPENDENTS_URL = `${API_URL}members?filter=${filter}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_DEPENDENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberPrincipal(principalTempId: string) {
    const filter = JSON.stringify({
      where: {
        temp_id: principalTempId,
      },
    });

    const GET_PRINCIPAL_URL = `${API_URL}members?filter=${filter}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_PRINCIPAL_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static patchMemberDependents(keys: string[], payloads: any) {
    const ROOT_MEMBER_EDIT_URL = API_URL + 'members/';
    // let getToken = await this.getAccessTokenFromDb();
    return keys.map(key => {
      return new Promise(resolve => {
        fetch(ROOT_MEMBER_EDIT_URL + key, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer ' + getToken
          },
          body: JSON.stringify(payloads[key]),
        })
          .then(() => {
            resolve('');
          })
          .catch(error => {
            resolve(error.message);
          });
      });
    });
  }

  public static async liftSuspensionMember(member_id: string, payload: any) {
    let MEMBER_URL = API_URL + 'members/' + member_id + '/suspension';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchMemberData(
    member_id: string,
    data: any,
    isDocument?: boolean,
  ) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    const MEMBER_EDIT_URL =
      isDocument && isDocument === true
        ? `${API_URL}members/${member_id}?isDocument=true`
        : `${API_URL}members/${member_id}?isDocument=false`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_EDIT_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify({
        userData: userData,
        member: data,
      }),
    })
      .then(response => {
        return response;
      })
      .catch(error => {
        return error;
      });
  }

  //get memberlogs
  public static async getMemberLogs(member_id: string, person_id?: string) {
    let filterLogs: any[] = [
      `member-logs?filter[order][0]=activity_id%20DESC&filter[where][member_id]=']`,
    ];
    let _order: any[] = [];
    _order = ['filter[order]=[activity_id%20ASC&]'];
    console.log('verifymemberpage getmemberlogs', filterLogs, member_id);
    if (API_URL) {
      const GET_MEMBER_LOGS_URL = person_id
        ? API_URL + filterLogs + person_id
        : API_URL +
          //api/member-logs?filter[order][0]=activity_id%20DESC&filter[where][member_id]=66dea7450b064d6036c3598a
          `member-logs?filter[order][0]=activity_id%20DESC&filter[where][member_id]=` +
          member_id;

      let getToken = await this.getAccessTokenFromDb();
      return fetch(GET_MEMBER_LOGS_URL, {
        method: 'get',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
      })
        .then(response => response.json())
        .catch(error => {
          console.log('client error: ', error);
        });
    }
  }

  public static generateContractFileURL(contract_id: string) {
    return API_URL + 'base-benefit-plans/contracts?id=' + contract_id;
  }

  public static async getMembers(limit: number, skip: number) {
    const filters = {
      limit: limit,
      skip: skip,
    };

    const CLIENT_URL =
      API_URL + `members/?filter=${JSON.stringify(filters)}&count=${true}`;
    let getToken = await this.getAccessTokenFromDb();

    console.log('membership endpoint getMembers', CLIENT_URL);
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        console.log(response, 'Get Members Success');
        return response.json();
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMembersByClientId(
    limit: number,
    skip: number,
    clientId?: any,
  ) {
    console.log(clientId);
    const filters = {
      limit: limit,
      skip: skip,
    };

    const CLIENT_URL =
      API_URL + `members/?filter=${JSON.stringify(filters)}&count=${true}`;
    let getToken = await this.getAccessTokenFromDb();

    console.log('getMembersByClientId()', CLIENT_URL);

    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        console.log(response, 'Get Members Success');
        return response.json();
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async searchMembers(
    limit: number,
    offset: number,
    query: string,
    filters: any,
    sorting: any,
    clientId?: any,
  ) {
    let CLIENT_URL =
      API_URL +
      `members/search?query=${query}&filters=${JSON.stringify(
        filters,
      )}&sorting=${JSON.stringify(sorting)}`;

    if (limit !== 0) {
      CLIENT_URL += `&limit=${limit}&offset=${offset}`;
    }

    if (!isNil(clientId)) {
      CLIENT_URL += `&clientId=${clientId}`;
    }
    console.log('search members url', CLIENT_URL);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postAutoRenewalMembersForVerification(
    params: {
      client_id: string;
      contractId: string;
      case_id: string;
      memberObjIds: string[];
      batch_name: string;
      type: string;
    }
  ): Promise<any> {
    const userData = await this.getUserDataFromDb();

    const payload = {
      client_id: params.client_id,
      contractId: params.contractId,
      user_id: userData.userId,
      memberObjIds: params.memberObjIds,
      batch_name: params.batch_name,
      type: params.type || 'Auto Renewal',
      case_id: params.case_id,
    };

    const POST_URL = `${API_URL}/save-members-for-renewal`;
    const getToken = await this.getAccessTokenFromDb();
    
    return fetch(POST_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Post Manual Renewal Members for Verification error: ', error);
        throw error;
      });
  }

  public static async postManualRenewalMembersForVerification(
    clientId: string,          
    contractId: string,        
    caseId: string,           
    includedMembers: any[], // Changed to accept full member objects instead of just IDs
    batchName: string,
    type: string,           
    excludedMemberIds?: string[],
    channel?: string,
    senderEmail?: string, 
    senderName?: string,
  ): Promise<any> {
    try {
      // Get user data for audit trail
      const userData = await this.getUserDataFromDb();
      
      // Format the member data for the existing postUploadMember API
      // Use the same structure as the existing member upload functionality
      const memberUploadPayload = {
        client_id: clientId,
        contract_id: contractId,
        batch_name: batchName.toString(),
        sender_name: senderName || userData.userName || userData.loginUsername || "",
        sender_email: senderEmail || "", 
        channel: channel || "Fax" || "Email",
        date_sent: new Date().toISOString(),
        no_of_members: includedMembers.length,
        ticket_id: caseId,
        file_url: "",
        default_data_map_save_for_now: [],
        renewalType: "manualRenewal",
        members: includedMembers.map((member, index) => {
          return {
            first_name: member.first_name || '',
            middle_name: member.middle_name || '',
            last_name: member.last_name || '',
            suffix: member.suffix || '',
            date_of_birth: member.date_of_birth || '',
            gender: member.gender || '',
            civil_status: member.civil_status || '',
            email_address: member.email_address || '',
            member_card_no: member.member_card_no || member.member_id || '',
            employee_id: member.employee_id || '',
            member_type: member.member_type || '',
            plan_type: member.plan_type || '',
            effectivity_date: member.effectivity_date || '',
            hire_date: member.hire_date || '',
            regularization_date: member.regularization_date || '',
            member_account_no: member.member_account_no || '',
            member_status: member.member_status || '',
            address: member.address || '',
            contact_no: member.contact_no || '',
            principal_temp_id: member.principal_temp_id || '',
            row_number: index + 1,
            isExisting: true,
            original_member_id: member._id,
            contract_id: contractId,
            client_id: clientId,
          };
        })
      };
      
      // Step 1: Use the existing postUploadMember API 
      // This will create both member_upload and member_upload_members records
      const uploadResponse = await this.postUploadMember(memberUploadPayload, caseId);
      
      if (uploadResponse && uploadResponse.error) {
        throw new Error(uploadResponse.error);
      }
            
      return uploadResponse
      
    } catch (error) {
      throw error;
    }
  }


  public static async searchManualRenewalEligibleMembers(
    clientId: string
  ): Promise<any> { 

    if (!clientId) {
      return { data: [], error: { message: "Client ID is required." } };
    }

    const url = `${API_URL}members/for-renewal/${clientId}`;

    const token = await this.getAccessTokenFromDb();
    if (!token) {
      return { data: [], error: { message: "Authentication token is missing." } };
    }

    return fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
      .then(response => {
        if (!response.ok) {
          return response.text().then(text => { 
              throw new Error(`Backend error: ${response.status} ${response.statusText} - ${text}`);
          }).catch(() => {
              throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
          });
        }
        return response.json();
      })
      .then(data => {
        return {
          data: data,
          totalCount: data.length,
          error: undefined
        };
      })
      .catch(error => {
        return {
          data: [],
          totalCount: 0,
          error: { message: error.message || "An unknown network error occurred." }
        };
      });
  }

  public static async getMemberListReport(
    query: string,
    filters: any,
    sorting: any,
    memberId: any,
    client_id?: any,
    // contract_id?:any,
  ) {
    let CLIENT_URL =
      API_URL +
      `members/generate-csv?query=${query}&filters=${JSON.stringify(
        filters,
        // contract_id,
        memberId,
      )}&sorting=${JSON.stringify(sorting)}`;
    // console.log('contract_id for list report', contract_id)
    // console.log('memberId api', contract_id)
    console.log('client api memberlist report', client_id);
    console.log('memberId api memberlist report', memberId);

    // if (!isNil(memberId)) {
    //   console.log('is nil', CLIENT_URL)
    //   CLIENT_URL += `&memberId=${memberId}`;
    // }
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('downloadExceptionReport error: ', error);
      });
  }

  public static async getMemberDocuments(member_id: string) {
    const MEMBER_DOCUMENTS = API_URL + 'members/documents/' + member_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_DOCUMENTS, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putMemberDocuments(payload: any, member_id: string) {
    const MEMBER_DOCUMENTS = API_URL + 'members/documents/' + member_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_DOCUMENTS, {
      method: 'put',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postChangeMemberStatus(payload: any, member_id: string) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    // const changeStatusUrl = `${API_URL}members/${member_id}/status?userData=${JSON.stringify(
    //   userData,
    // )}`;
    //userData=${JSON.stringify(userData)}
    const changeStatusUrl = `${API_URL}members/${member_id}/status?userData=${userData}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(changeStatusUrl, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  // NO LONGER USED AS OF MAY 06, 2020 - MARC > MS477

  public static async putReactivateMemberStatus(
    member_id: string,
    dependents: string[],
    ticket_no: string,
  ) {
    let userData = await this.getUserDataFromDb();
    if (userData['rbac']) {
      delete userData['rbac'];
    }
    const changeStatusUrl = `${API_URL}members/${member_id}/reactivation?userData=${JSON.stringify(
      userData,
    )}`;
    let payload = {
      dependents: dependents,
      ticket_no: ticket_no,
    };

    let getToken = await this.getAccessTokenFromDb();
    return fetch(changeStatusUrl, {
      method: 'put',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        //console.log('putReactivateMemberStatus error: ', error);
        return error;
      });
  }

  public static async putTerminateMember(payload: any, member_id: string) {
    const changeStatusUrl = `${API_URL}members/${member_id}/termination`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(changeStatusUrl, {
      method: 'put',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getClientHMOInfo(clientId: string) {
    const CLIENT_INFO_URL =
      API_URL +
      'clients/account/hmo-info/' +
      clientId +
      '?fields=' +
      JSON.stringify({ canCover: false, canAvail: false });

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_INFO_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    }).then(response => response.json());
  }

  public static async getContractDocuments(
    clientId: string,
    contractId: string,
    type: string,
  ) {
    const CLIENT_INFO_URL =
      API_URL +
      'clients/' +
      clientId +
      '/contracts/' +
      contractId +
      '/uploads' +
      '?type=' +
      type;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_INFO_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    }).then(response => response.json());
  }

  public static getClientHMOData(client_id: string) {
    let CLIENT_HMO_URL = API_URL + 'clients/account/hmo-info/' + client_id;
    return fetch(CLIENT_HMO_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    }).then(response => {
      if (response.ok) {
        return response.json();
      } else {
        return {
          error: {
            status: response.status,
            message: response.statusText,
          },
        };
      }
    });
  }

  public static getSupervisors(
    department: string,
    amount_limit: number,
    user_id: string,
  ) {
    const GET_SUPERVISORS = `${API_URL}user-groups/underwriting/create?department=${department}&type=users&amount_limit=${amount_limit}&user-id=${user_id}`;

    return fetch(GET_SUPERVISORS, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static getClientUtilization(contractId: string, clientId: string) {
    const UTILIZATION_URL = `${API_URL}customer-care/member-utilization-loa/contract/${contractId}?type=client&id=${clientId}`;
    return fetch(UTILIZATION_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('utilization error', error);
        return error;
      });
  }

  public static getCommissionDetails(clientId: string) {
    const COMMRIDERS_URL = API_URL + 'clients/commission/' + clientId;
    return fetch(COMMRIDERS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static getCommissionRiders(clientId: string) {
    const COMMRIDERS_URL = API_URL + 'clients/commission-riders/' + clientId;
    return fetch(COMMRIDERS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static getCommissionFiles(clientId: string) {
    const COMMFILES_URL =
      API_URL +
      'documents?filter[where][section]=' +
      'commissions' +
      '&filter[where][client_id]=' +
      clientId;
    return fetch(COMMFILES_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static getCommissionRemarks(clientId: string) {
    const COMMRIDERS_URL = API_URL + 'clients/commission-remarks/' + clientId;
    return fetch(COMMRIDERS_URL, {
      method: 'get',
      headers: { 'Content-Type': 'application/json' },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static getHistory(client_id: string, type: string, filter?: string) {
    const addfilter = filter ? '?' + filter : '';
    const HISTORY_URL =
      API_URL + 'clients/' + client_id + '/type/' + type + addfilter;
    return fetch(HISTORY_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getPersonAccountHistory(personId: string) {
    const PERSON_INFO_URL = API_URL + `members/${personId}/accounts/history`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(PERSON_INFO_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    }).then(response => response.json());
  }

  public static async downloadTransmittalWithHeader(
    clientId: any,
    query: string,
    filters: any,
    sorting: any,
    payload: any,
  ) {
    const downloadPrintedMembersUrl =
      API_URL +
      `members/print/generate-report-json?clientId=${clientId}&query=${query}&filters=${JSON.stringify(
        filters,
      )}&sorting=${JSON.stringify(sorting)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(downloadPrintedMembersUrl, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    }).then(response => response.json());
    // .then(response => {
    //   console.log('generate-report', response);
    //   const responseBlob = response.clone().blob();
    //   const responseJson = response.json();
    //   return responseJson
    //     .then(res => {
    //       if (res.error !== undefined) {
    //         return responseJson;
    //       } else {
    //         return responseBlob;
    //       }
    //     })
    //     .catch(res => {
    //       console.log('generateMemberTicketsReport error: ', res);
    //       return responseBlob;
    //     });
    // })
    // .catch(error => {
    //   console.log('generateMemberTicketsReport Error: ', error);
    // });
  }

  public static async getContractTree(contractId: string) {
    const PLANTYPE_URL = `${API_URL}benefit-plans/tree/${contractId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(PLANTYPE_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberBenefits(memberCardNo: string) {
    const memberBenefitsUrl = `${API_URL}members/${memberCardNo}/benefits`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(memberBenefitsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberTickets(
    memberId?: string,
    query?: string,
    filters?: any[],
  ) {
    const baseMemberTicketsUrl = `${API_URL}customer-care/tickets/members/${memberId}`;
    const memberTicketsUrl = `${baseMemberTicketsUrl}?query=${query}&filters=${JSON.stringify(
      filters,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(memberTicketsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    }).then(response => response.json());
  }

  // public static async getMemberCCTickets(
  //   memberId: string,

  // ) {
  //   let MEMBER_TICKET_URL = API_URL + 'customer-care/tickets/members/' + memberId;
  //   return fetch(MEMBER_TICKET_URL, {
  //     method: 'get',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + localStorage.getItem('api_token'),
  //     },
  //   }).then(response => {
  //     if (response.ok) {
  //       return response.json();
  //     } else {
  //       return {
  //         error: {
  //           status: response.status,
  //           message: response.statusText,
  //         },
  //       };
  //     }
  //   });
  // }

  public static async generateMemberTicketsReport(
    memberId: string,
    query: string,
    filters: any[],
  ) {
    const baseTicketsReportUrl = `${API_URL}customer-care/tickets/members/${memberId}/generate-report`;
    const ticketsReportUrl = `${baseTicketsReportUrl}?query=${query}&filters=${JSON.stringify(
      filters,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(ticketsReportUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        const responseBlob = response.clone().blob();
        const responseJson = response.json();
        return responseJson
          .then(res => {
            if (res.error !== undefined) {
              return responseJson;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('generateMemberTicketsReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('generateMemberTicketsReport Error: ', error);
      });
  }

  //client pages/benefits/plantype
  public static getBaseBenefits() {
    const BASE_BENEFITS_URL = API_URL + 'base-benefit-plans/';
    return fetch(BASE_BENEFITS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static getContractUpdate(clientId: string) {
    const COMMRIDERS_URL = API_URL + 'clients/contract-update/' + clientId;
    return fetch(COMMRIDERS_URL, {
      method: 'get',
      headers: { 'Content-Type': 'application/json' },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  //benefit-member plan type
  public static async getBenefitsByContractAndPlanType(
    contractId: string,
    planType: string,
  ) {
    const benefitsUrl = `${API_URL}benefit-plans/${contractId}/benefits/${planType}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(benefitsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getOfficeList() {
    const benefitsUrl = `${API_URL}offices/list`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(benefitsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async findMemberBenefitsByContact(
    memberId: string,
    contractId: string,
  ) {
    const benefitsUrl = `${API_URL}members/${memberId}/contracts/${contractId}/benefits`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(benefitsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async findBaseBenefits() {
    const baseBenefitsUrl = `${API_URL}base-benefit-plans`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(baseBenefitsUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postEmailReport(
    receivers: any,
    attachments: File[],
    batch?: any,
    filename?: any,
    sender?: any,
  ) {
    let POST_MEMBER_URL = API_URL + 'members/exception/report';

    if (receivers) {
      POST_MEMBER_URL =
        POST_MEMBER_URL + `?recipient=${JSON.stringify(receivers)}`;
    }
    if (batch) {
      POST_MEMBER_URL = POST_MEMBER_URL + `&batch=${batch}`;
    }
    if (filename) {
      POST_MEMBER_URL = POST_MEMBER_URL + `&filename=${filename}`;
    }
    if (sender) {
      POST_MEMBER_URL = POST_MEMBER_URL + `&verifier=${sender}`;
    }

    const formData = new FormData();
    formData.append('attachment', attachments[0]); // currently can only handle single file upload

    let getToken = await this.getAccessTokenFromDb();
    return fetch(POST_MEMBER_URL, {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + getToken,
      },
      body: formData,
    })
      .then(response => response.json())
      .catch(error => {
        console.log('POST Email Report error: ', error);
      });
  }

  public static async uploadMemberDocument(
    memberId: string,
    type: string,
    attachment: File,
    id_type?: string,
    from?: string,
    ticket_id?: string,
    endorsement_date?: string,
  ) {
    let user = await this.getUserDataFromDb();
    let uploadMemberDocumentUrl = `${API_URL}members/${memberId}/uploads?type=${type}&user_id=${user.userId}&user_name=${user.loginUsername}`;
    if (id_type)
      uploadMemberDocumentUrl = uploadMemberDocumentUrl + `&id_type=${id_type}`;
    if (from)
      uploadMemberDocumentUrl = uploadMemberDocumentUrl + `&from=${from}`;
    if (ticket_id)
      uploadMemberDocumentUrl =
        uploadMemberDocumentUrl + `&ticket_id=${ticket_id}`;
    if (endorsement_date)
      uploadMemberDocumentUrl =
        uploadMemberDocumentUrl + `&endorsement_date=${endorsement_date}`;

    const formData = new FormData();
    formData.append('attachment', attachment);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(uploadMemberDocumentUrl, {
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + getToken,
      },
      body: formData,
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async uploadPersonDocument(
    memberId: string,
    type: string,
    attachment: File,
  ) {
    let user = await this.getUserDataFromDb();
    const uploadMemberDocumentUrl = `${API_URL}members/${memberId}/uploads?type=${type}&user_id=${user.userId}&user_name=${user.loginUsername}&is_person=true`;
    const formData = new FormData();
    formData.append('attachment', attachment);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(uploadMemberDocumentUrl, {
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + getToken,
      },
      body: formData,
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async uploadMemberSingleDocument(
    memberId: string,
    type: string,
    attachment: File,
    idType: string,
    from?: string,
    ticket_id?: string,
    endorsement_date?: string,
  ) {
    let user = await this.getUserDataFromDb();
    let uploadMemberDocumentUrl = `${API_URL}members/${memberId}/uploads?type=${type}&id_type=${idType}&user_id=${user.userId}&user_name=${user.loginUsername}`;
    if (from)
      uploadMemberDocumentUrl = uploadMemberDocumentUrl + `&from=${from}`;
    if (ticket_id)
      uploadMemberDocumentUrl =
        uploadMemberDocumentUrl + `&ticket_id=${ticket_id}`;
    if (endorsement_date)
      uploadMemberDocumentUrl =
        uploadMemberDocumentUrl + `&endorsement_date=${endorsement_date}`;

    const formData = new FormData();
    formData.append('attachment', attachment);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(uploadMemberDocumentUrl, {
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + getToken,
      },
      body: formData,
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getSaveForNowData(userId: string, caseId: string) {
    const filter = JSON.stringify({
      where: {
        user_id: userId,
        ticket_no: caseId,
      },
      order: ['timestamp DESC'],
      limit: 1,
    });

    const SAVE_FOR_NOW_URL = API_URL + `tmp?filter=${filter}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(SAVE_FOR_NOW_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async saveForNow(
    userId: string,
    caseId: string,
    processName: string,
    data: any,
  ) {
    const saveForNowUrl = `${API_URL}tmp`;
    const payload = {
      user_id: userId,
      ticket_no: caseId,
      module: 'Membership',
      process: processName,
      data: data,
    };

    let getToken = await this.getAccessTokenFromDb();
    return fetch(saveForNowUrl, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async patchSaveForNow(saveForNowId: string, data: any) {
    const saveForNowUrl = `${API_URL}tmp/${saveForNowId}`;
    const payload = {
      _id: saveForNowId,
      data: data,
    };

    let getToken = await this.getAccessTokenFromDb();
    return fetch(saveForNowUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async deleteSaveForNow(saveForNowId: string) {
    const saveForNowUrl = `${API_URL}tmp/${saveForNowId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(saveForNowUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async verifyMemberSaveToTmp(payload: object) {
    const saveTmpUrl = `${API_URL}tmp/`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(saveTmpUrl, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async verifyMemberPatchTmp(tmpId: string, payload: any) {
    const saveForNowUrl = `${API_URL}tmp/${tmpId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(saveForNowUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async verifyDeleteFromTmp(id) {
    const deleteTmpUrl = `${API_URL}tmp/${id}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(deleteTmpUrl, {
      method: 'delete',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async loadVerifyDataFromTmp(ticket_id: string) {
    const loadTmpUrl = `${API_URL}tmp/?filter[where][ticket_no]=${ticket_id}&filter[where][user_id]=verify-member`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(loadTmpUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async startMemberUpload(
    memberUploadId: string,
    caseId: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const startMemberUpload = `${API_URL}member-upload/validate-process/start/${memberUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(startMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async startVoidUpload(
    memberVoidUploadId: string,
    caseId: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const startMemberUpload = `${API_URL}member-void/validate-process/start/${memberVoidUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(startMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async startMemberUploadCorrection(
    memberUploadId: string,
    ticketId: string,
    clientID: string,
    batchEditTicketNumber?: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    // const pmaker = {
    //   token: processMakerToken,
    //   // case_id: caseId,
    // };

    const startMemberUpload = `${API_URL}member-edit/validate-process/start/${memberUploadId}?pmaker[token]=${processMakerToken}&pmaker[case_id]=${ticketId}&clientID=${clientID}&ticketNumber=${batchEditTicketNumber}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(startMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberUploadStatus(memberUploadId: string) {
    const getMemberUploadStatus = `${API_URL}member-upload/validate-process/status/${memberUploadId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(getMemberUploadStatus, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVoidUploadStatus(memberVoidUploadId: string) {
    const getMemberUploadStatus = `${API_URL}member-void/validate-process/status/${memberVoidUploadId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(getMemberUploadStatus, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberUploadStatusCorrection(memberUploadId: string) {
    const getMemberUploadStatus = `${API_URL}member-edit/validate-process/status/${memberUploadId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(getMemberUploadStatus, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async cancelMemberUpload(
    memberUploadId: string,
    caseId: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const cancelMemberUpload = `${API_URL}member-upload/validate-process/cancel/${memberUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(cancelMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async deleteCancelEnrollment(
    memberUploadId: string,
    caseId: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    let DELETE_MEMBER_URL =
      API_URL +
      '/member-upload/cancel/' +
      memberUploadId +
      '?pmaker=' +
      JSON.stringify(pmaker);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(DELETE_MEMBER_URL, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        return response;
      })
      .catch(error => {
        console.log('Cancel Enrollment error: ', error);
      });
  }

  public static async cancelVoidUpload(memberUploadId: string, caseId: string) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    const cancelMemberUpload = `${API_URL}member-void/validate-process/cancel/${memberUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(cancelMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async deleteCancelVoid(memberUploadId: string, caseId: string) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: caseId,
    };

    let DELETE_MEMBER_URL =
      API_URL +
      '/member-void/cancel/' +
      memberUploadId +
      '?pmaker=' +
      JSON.stringify(pmaker);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(DELETE_MEMBER_URL, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        return response;
      })
      .catch(error => {
        console.log('Cancel Enrollment error: ', error);
      });
  }

  public static async confirmMemberUpload(
    memberUploadId: string,
    case_id: string,
    isManualEncode?: boolean,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: case_id,
    };

    let confirmMemberUpload = `${API_URL}member-upload/validate-process/confirm/${memberUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    if (isManualEncode) {
      confirmMemberUpload = confirmMemberUpload + '&encode_method=manual';
    }
    console.log('confirm enrollment URL', confirmMemberUpload);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(confirmMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async confirmVoidUpload(
    memberVoidUploadId: string,
    case_id: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const pmaker = {
      token: processMakerToken,
      case_id: case_id,
    };

    let confirmMemberUpload = `${API_URL}member-void/validate-process/confirm/${memberVoidUploadId}?pmaker=${JSON.stringify(
      pmaker,
    )}`;

    console.log('confirm void enrollment URL', confirmMemberUpload);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(confirmMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async confirmMemberUploadCorrection(
    memberUploadId: string,
    ticketId: string,
    from?: boolean,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    const userData = await this.getUserDataFromDb();
    let userObj: any = {
      user_id: userData['userId'] ? userData['userId'] : '',
      user_fullname: userData['userName'] ? userData['userName'] : '',
    };

    let confirmMemberUpload = `${API_URL}member-edit/validate-process/confirm/${memberUploadId}?pmaker[token]=${processMakerToken}&pmaker[case_id]=${ticketId}&user_id=${userObj['user_id']}&user_name=${userObj['user_fullname']}`;
    if (from) {
      confirmMemberUpload = confirmMemberUpload + `&from_ocp=${from}`;
    }

    let getToken = await this.getAccessTokenFromDb();
    return fetch(confirmMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async cancelMemberUploadCorrection(
    memberUploadId: string,
    ticketId: string,
  ) {
    let processMakerToken: string = '';
    try {
      processMakerToken = await this.getProcessMakerToken();
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    // const pmaker = {
    //   token: processMakerToken,
    //   case_id: caseId,
    // };

    const cancelMemberUpload = `${API_URL}member-edit/validate-process/cancel/${memberUploadId}?pmaker[token]=${processMakerToken}&pmaker[case_id]=${ticketId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(cancelMemberUpload, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async deleteMemberUpload(memberUploadId: string) {
    const deleteMemberUpload = `${API_URL}member-upload/${memberUploadId}`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(deleteMemberUpload, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('deleteMemberUpload error: ', error);
      });
  }

  public static async deleteMemberUploadCorrection(memberUploadId: string) {
    const deleteMemberUpload = `${API_URL}member-edit/upload/${memberUploadId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(deleteMemberUpload, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('deleteMemberUpload error: ', error);
      });
  }

  public static async deleteMemberUploadData(memberUploadId: string) {
    const deleteMemberUploadData = `${API_URL}member-upload-members/member-validated/${memberUploadId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(deleteMemberUploadData, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('deleteMemberUploadData error: ', error);
      });
  }

  public static async getBenefitTree(contractId: string) {
    const getBenefits = `${API_URL}benefit-plans/tree/${contractId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(getBenefits, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getContractsHistory(memberId: string) {
    const getContractsHistoryUrl = `${API_URL}members/${memberId}/contracts/history`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(getContractsHistoryUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVerifyTermination(case_id, client_id) {
    const getVerifyTerminationUrl = `${API_URL}members/termination/upload?ticket_id=${case_id}&client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(getVerifyTerminationUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getVerifySuspensionCollection(case_id, client_id) {
    const getVerifySuspensionCollectionUrl = `${API_URL}members/suspension/collection?ticket_id=${case_id}&client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(getVerifySuspensionCollectionUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('getVerifySuspensionCollection error: ', error);
      });
  }

  public static async getVerifySuspension(case_id, client_id) {
    const getVerifySuspensionUrl = `${API_URL}members/suspension/upload?ticket_id=${case_id}&client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(getVerifySuspensionUrl, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putVerifyTermination(payload, case_id, client_id) {
    const putVerifyTerminationUrl = `${API_URL}members/termination?ticket_id=${case_id}&client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(putVerifyTerminationUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putVerifySuspension(payload, case_id, client_id) {
    const putVerifySuspensionUrl = `${API_URL}members/suspension?ticket_id=${case_id}&client_id=${client_id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(putVerifySuspensionUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async putVerifySuspensionCollection(
    payload,
    case_id,
    client_id,
  ) {
    try {
      const userData = await this.getUserDataFromDb();
      let userObj: any = {
        user_id: userData['userId'] ? userData['userId'] : '',
        user_fullname: userData['userName'] ? userData['userName'] : '',
      };

      const putVerifySuspensionCollectionUrl = `${API_URL}members/suspension/collection?ticket_id=${case_id}&client_id=${client_id}&user_id=${userObj['user_id']}&user_name=${userObj['user_fullname']}`;
      let getToken = await this.getAccessTokenFromDb();
      return fetch(putVerifySuspensionCollectionUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(payload),
      })
        .then(response => response.json())
        .catch(error => {
          console.log('putVerifySuspensionCollection error: ', error);
        });
    } catch (err) {
      console.log('getUserDataFromDb catch err', err);
      return err;
    }
  }

  // Clients

  //Client Active Suspended Member Count
  public static async getClientCount(
    limit: number,
    skip: number,
    filter: object = {},
    order: string[] = [],
  ) {
    let CLIENTS_URL = API_URL + `clients/reports-count?`;
    if (limit !== 0) {
      CLIENTS_URL += `limit=${limit}&`;
    }
    CLIENTS_URL += `offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;

    console.log('getClientCount', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  // Clients List
  public static async getClients(
    limit: number,
    skip: number,
    filter: object = {},
    order: string[] = [],
  ) {
    let CLIENTS_URL = API_URL + `clients/listing/search?`;
    if (limit !== 0) {
      CLIENTS_URL += `limit=${limit}&`;
    }
    CLIENTS_URL += `offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;

    console.log('getClients', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async getActiveSuspendedClient(
    limit: number,
    skip: number,
    filter: object = {},
    order: string[] = [],
  ) {
    let CLIENTS_URL = API_URL + `clients/listing/active-suspended?`;
    if (limit !== 0) {
      CLIENTS_URL += `limit=${limit}&`;
    }
    CLIENTS_URL += `offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;

    console.log('getClients', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async searchClients(
    query: string,
    limit: number,
    skip: number,
    filter: object = {},
    order: any,
  ) {
    let CLIENTS_URL = API_URL + `clients/listing/search?query=${query}`;
    if (limit !== 0) {
      CLIENTS_URL += `&limit=${limit}`;
    }
    CLIENTS_URL += `&offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;

    console.log('searchClients', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async searchActiveSuspendedClient(
    query: any,
    limit: number,
    skip: number,
    filter: object = {},
    order: any,
    range: any,
  ) {
    let CLIENTS_URL =
      API_URL + `clients/listing/active-suspended?query=${query}`;
    if (limit !== 0) {
      CLIENTS_URL += `&limit=${limit}`;
    }
    CLIENTS_URL += `&offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;
    if (range !== undefined) {
      CLIENTS_URL += `&range=${JSON.stringify(range)}`;
    }

    console.log('searchClients', CLIENTS_URL);
    console.log('range', range);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async listClients(
    query: string | null,
    limit: number,
    skip: number,
    filter: object = {},
    order: string[] = [],
  ) {
    console.log(query);
    let CLIENTS_URL = API_URL + `clients/listing/search?`;
    if (limit !== 0) {
      CLIENTS_URL += `&limit=${limit}`;
    }
    CLIENTS_URL += `&offset=${skip}&filters=${JSON.stringify(
      filter,
    )}&sorting=${JSON.stringify(order)}`;

    console.log('listClients', CLIENTS_URL);
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  //Reports Membership
  public static async getSuspendMember(
    client_id: string,
    limit: number,
    skip: number,
    filter: object = {},
    order: string[] = [],
  ) {
    console.log(client_id);
    console.log(limit);
    console.log(skip);
    console.log(filter);
    console.log(order);
    let GET_MEMBER_URL = API_URL + '/members/suspension/collection';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GET_MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async generateMemberCountReport(
    format: string,
    query: string | null,
    // filter: object = {},
    filter: any[],
    order: any[],
  ) {
    // const filters = {};
    // if (Object.entries(filter).length !== 0) {
    //   const where = {};
    //   for (const x in filter) {
    //     const fil = filter[x];
    //     if (fil['columnName'] && fil['value']) {
    //       if (
    //         ['registered_name', 'corporate_account_no'].includes(
    //           fil['columnName'],
    //         )
    //       ) {
    //         where[fil['columnName']] = {
    //           like: fil['value'],
    //           options: 'i',
    //         };
    //       } else if (
    //         ['prev_hmo_effectivity_date', 'prev_hmo_expiry_date'].includes(
    //           fil['columnName'],
    //         )
    //       ) {
    //         let date = fil['value'];

    //         if (moment.isMoment(fil['value'])) {
    //           date = fil['value'].format('YYYY-MM-DD');
    //         }

    //         where[fil['columnName']] = {
    //           like: date,
    //           options: 'i',
    //         };
    //       } else {
    //         where[fil['columnName']] = fil['value'];
    //       }
    //     }
    //   }
    //   filters['where'] = where;
    // }

    let columnsNotAllowedForSorting: any[] = [
      //'active_members_count',   // Active Members
      // 'for_printing_count',     // For Printing
      // 'suspended_members_count' //suspended
    ];
    let hasRestrictedColumnSorting: boolean = false;

    if (order && order.length > 0) {
      order.forEach(item => {
        if (
          item['columnName'] &&
          columnsNotAllowedForSorting.includes(item['columnName'])
        ) {
          hasRestrictedColumnSorting = true;
        }
      });
    }

    const REPORT_URL =
      API_URL +
      'clients/generate-print-reports?&format=' +
      format +
      (query ? '&query=' + query : '') +
      // (filters['where'] ? '&filter=' + JSON.stringify(filters) : '');
      (filter ? '&filter=' + JSON.stringify(filter) : '') +
      (order && order.length > 0 && hasRestrictedColumnSorting === false
        ? '&sorting=' + JSON.stringify(order)
        : '');

    // console.log('Report: ', REPORT_URL, filters);
    console.log(
      'Clients generateMemberCountReport > Generate Report URL 1',
      REPORT_URL,
    );

    let getToken = await this.getAccessTokenFromDb();
    return fetch(REPORT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        console.log('generateMemberCountReport BLOB', responseBlob);
        console.log('generateMemberCountReport 2 JSON', responseJSON);
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async generateProcessedMembersReport(
    // format: string,
    filter: object = {},
    order?: any,
    alltypes?: boolean,
  ) {
    // let columnsNotAllowedForSorting: any[] = [
    //   //'active_members_count',   // Active Members
    //  // 'for_printing_count',     // For Printing
    //  // 'suspended_members_count' //suspended
    // ];
    // let hasRestrictedColumnSorting: boolean = false;

    // if (order && order.length > 0) {
    //   order.forEach(item => {
    //     if (
    //       item['columnName'] &&
    //       columnsNotAllowedForSorting.includes(item['columnName'])
    //     ) {
    //       hasRestrictedColumnSorting = true;
    //     }
    //   })
    // }

    const REPORT_URL = API_URL + 'members/ticket/report';

    // console.log('Report: ', REPORT_URL, filters);
    console.log(
      'Clients generateProcessedMembersReport > Generate Report URL 1',
      REPORT_URL,
    );
    const payload: any = {
      filter: filter,
      alltypes: alltypes !== undefined ? alltypes : false,
      sorting: order,
    };
    let getToken = await this.getAccessTokenFromDb();
    return fetch(REPORT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getTicketTypes() {
    const TICKET_API_URL = `${API_URL}members/ticket/types`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(TICKET_API_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async generateActiveSuspendedCountReport(
    format: string,
    query: string | null,
    // filter: object = {},
    filter: any[],
    order: any[],
    range: any[],
    // username: string,
  ) {
    // console.log('GET username for activesuspend', username)
    let columnsNotAllowedForSorting: any[] = [
      //'active_members_count',   // Active Members
      // 'for_printing_count',     // For Printing
      // 'suspended_members_count' //suspended
    ];
    let hasRestrictedColumnSorting: boolean = false;

    if (order && order.length > 0) {
      order.forEach(item => {
        if (
          item['columnName'] &&
          columnsNotAllowedForSorting.includes(item['columnName'])
        ) {
          hasRestrictedColumnSorting = true;
        }
      });
    }

    const REPORT_URL =
      API_URL +
      'clients/generate-active-suspended-reports?&format=' +
      format +
      (query ? '&query=' + query : '') +
      // (filters['where'] ? '&filter=' + JSON.stringify(filters) : '');
      (filter ? '&filter=' + JSON.stringify(filter) : '') +
      (order && order.length > 0 && hasRestrictedColumnSorting === false
        ? '&sorting=' + JSON.stringify(order)
        : '') +
      (range && range.length > 0 ? '&range=' + JSON.stringify(range) : '');

    // console.log('Report: ', REPORT_URL, filters);
    console.log(
      'Clients generateMemberCountReport > Generate Report URL 1',
      REPORT_URL,
    );

    let getToken = await this.getAccessTokenFromDb();
    return fetch(REPORT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        if (format === 'XLSX') {
          return response.json();
        }
        const responseBlob = response.blob();
        const responseJSON = response.json();
        console.log('generateMemberCountReport BLOB', responseBlob);
        console.log('generateMemberCountReport 2 JSON', responseJSON);
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async generateClientReport(
    format: string,
    query: string | null,
    // filter: object = {},
    filter: any[],
    order: any[],
    username: any,
  ) {
    console.log('GET USERDATA111', username);
    // const filters = {};
    // if (Object.entries(filter).length !== 0) {
    //   const where = {};
    //   for (const x in filter) {
    //     const fil = filter[x];
    //     if (fil['columnName'] && fil['value']) {
    //       if (
    //         ['registered_name', 'corporate_account_no'].includes(
    //           fil['columnName'],
    //         )
    //       ) {
    //         where[fil['columnName']] = {
    //           like: fil['value'],
    //           options: 'i',
    //         };
    //       } else if (
    //         ['prev_hmo_effectivity_date', 'prev_hmo_expiry_date'].includes(
    //           fil['columnName'],
    //         )
    //       ) {
    //         let date = fil['value'];

    //         if (moment.isMoment(fil['value'])) {
    //           date = fil['value'].format('YYYY-MM-DD');
    //         }

    //         where[fil['columnName']] = {
    //           like: date,
    //           options: 'i',
    //         };
    //       } else {
    //         where[fil['columnName']] = fil['value'];
    //       }
    //     }
    //   }
    //   filters['where'] = where;
    // }

    let columnsNotAllowedForSorting: any[] = [
      'active_members_count', //'active_members_count',   // Active Members
      'for_printing_count', // For Printing
    ];
    let hasRestrictedColumnSorting: boolean = false;

    if (order && order.length > 0) {
      order.forEach(item => {
        if (
          item['columnName'] &&
          columnsNotAllowedForSorting.includes(item['columnName'])
        ) {
          hasRestrictedColumnSorting = true;
        }
      });
    }

    const REPORT_URL =
      API_URL +
      'clients/generate-reports?&format=' +
      format +
      (query ? '&query=' + query : '') +
      // (filters['where'] ? '&filter=' + JSON.stringify(filters) : '');
      (filter ? '&filter=' + JSON.stringify(filter) : '') +
      (order && order.length > 0 && hasRestrictedColumnSorting === false
        ? '&sorting=' + JSON.stringify(order)
        : '');

    // console.log('Report: ', REPORT_URL, filters);
    console.log('Clients > Generate Report URL 1', REPORT_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(REPORT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        console.log('XLS response', response);
        const responseBlob = response.blob();
        const responseJSON = response.json();
        console.log('RESPONSE', responseBlob);
        console.log('RESPONSE 2', responseJSON);
        return responseJSON
          .then(res => {
            console.log('333', res);
            if (res.error !== undefined) {
              console.log('333', res);
              return responseJSON;
            } else {
              console.log('333', responseBlob);
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async generateMembersProcessedReport(
    format: string,
    query: string | null,
    // filter: object = {},
    filter: any[],
    order: any[],
    range: any[],
  ) {
    // let columnsNotAllowedForSorting: any[] = [
    //   //'active_members_count',   // Active Members
    //  // 'for_printing_count',     // For Printing
    //  // 'suspended_members_count' //suspended
    // ];
    let hasRestrictedColumnSorting: boolean = false;

    const REPORT_URL =
      API_URL +
      'members/ticket/members-processed-report?&format=' +
      format +
      (query ? '&query=' + query : '') +
      // (filters['where'] ? '&filter=' + JSON.stringify(filters) : '');
      (filter ? '&filter=' + JSON.stringify(filter) : '') +
      (order && order.length > 0 && hasRestrictedColumnSorting === false
        ? '&sorting=' + JSON.stringify(order)
        : '') +
      (range && range.length > 0 ? '&range=' + JSON.stringify(range) : '');

    // console.log('Report: ', REPORT_URL, filters);
    console.log(
      'Clients generateMembersProcessedReport > Generate Report URL 1',
      REPORT_URL,
    );

    let getToken = await this.getAccessTokenFromDb();
    return fetch(REPORT_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        if (format === 'XLSX') {
          return response.json();
        }
        const responseBlob = response.blob();
        const responseJSON = response.json();
        console.log('generateMembersProcessedReport BLOB', responseBlob);
        console.log('generateMembersProcessedReport 2 JSON', responseJSON);
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async fetchPrinting(
    clientId: string | undefined,
    limit: number,
    offset: number,
    tab: string,
    filter?: any,
    order?: any,
    query?: string,
    caseId?: string,
    isSelectAll?: boolean,
    isResCount?: boolean,
    isOcp?: boolean,
  ) {
    if (filter !== undefined && filter.length > 0) {
      filter = filter.filter(item => {
        return !(item.columnName === 'member_status' && item.value === 'All');
      });
    }
    // console.log('FILTER clientId', clientId);
    // console.log('FILTER limit', limit);
    // console.log('FILTER tab', tab);
    // console.log('FILTER order', order);
    // console.log('FILTER query', query);
    // console.log('FILTER caseId', caseId);
    // console.log('FILTER isSelectAll', isSelectAll);
    // console.log('FILTER isResCount', isResCount);

    let CLIENTS_URL =
      API_URL +
      `members/list/printing?client_id=${clientId}&limit=${limit}&offset=${offset}&tab=${tab}${
        caseId ? `&case_id=${caseId}` : ''
      }`;

    if (isSelectAll) {
      CLIENTS_URL =
        API_URL +
        `members/list/printing?client_id=${clientId}&tab=${tab}${
          caseId ? `&case_id=${caseId}` : ''
        }`;
    }

    if (query !== null && query !== '') {
      CLIENTS_URL = `${CLIENTS_URL}&query=${query}`;
    }

    if (filter.length > 0) {
      console.log('filters', filter);
      CLIENTS_URL = `${CLIENTS_URL}&filters=${JSON.stringify(filter)}`;
    }

    if (order.length !== 0) {
      CLIENTS_URL = `${CLIENTS_URL}&order=${JSON.stringify(order)}`;
      console.log('fetchDefault fetchPrinting order', JSON.stringify(order));
    }

    if (isResCount) {
      CLIENTS_URL = `${CLIENTS_URL}&table=true`;
    }

    if (isOcp) {
      CLIENTS_URL = `${CLIENTS_URL}&from_ocp=true`;
    }

    console.log('print id cards URL', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async postPrint(
    clientId: any,
    query: string,
    filters: any,
    sorting: any,
    payload: any,
    isOcp?: boolean,
  ) {
    let CLIENTS_URL =
      API_URL +
      `members/print?clientId=${clientId}&query=${query}&filters=${JSON.stringify(
        filters,
      )}&sorting=${JSON.stringify(sorting)}`;

    if (isOcp) {
      CLIENTS_URL = CLIENTS_URL + '&from_ocp=true';
    }
    console.log('post print URL', CLIENTS_URL);

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENTS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
        return error;
      });
  }

  public static async patchClient(client_id: string, payload: any) {
    if (
      payload &&
      payload['sales_channel'] &&
      payload['sales_channel']['type']
    ) {
      if (payload['sales_channel']['type'] === 'agent') {
        payload['sales_channel']['display_name'] =
          payload['sales_channel']['agent_name'];
      } else if (payload['sales_channel']['type'] === 'bdo') {
        payload['sales_channel']['display_name'] =
          payload['sales_channel']['bdo_name'];
      } else if (payload['sales_channel']['type'] === 'broker') {
        payload['sales_channel']['display_name'] =
          payload['sales_channel']['contact_person'];
      }
    }
    let CLIENT_URL = API_URL + 'clients/' + client_id;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => {
        return response;
      })
      .catch(error => {
        return error;
      });
  }

  static async generatePrintIDCardReport(clientId: string, filters: any) {
    let sendfilter = {};
    if (filters !== undefined && filters.length > 0) {
      filters = filters.filter(item => {
        return !(item.columnName === 'member_status' && item.value === 'All');
      });
      filters.map(item => {
        sendfilter[item.columnName] = item.value;
      });
    }
    let GENERATE_URL = `${API_URL}members/generate-report?clientId=${clientId}${
      filters.length > 0
        ? '&filter={where:' + JSON.stringify(sendfilter) + '}'
        : ''
    }`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(GENERATE_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        return error;
      });
  }

  public static getRegionList() {
    const URL = API_URL + 'region-list';

    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
    })
      .then(response => response.json())
      .catch(error => console.log('api error: ', error));
  }

  public static async downloadPrintedMembers(
    clientId: any,
    query: string,
    filters: any,
    sorting: any,
    payload: any,
  ) {
    const downloadPrintedMembersUrl =
      API_URL +
      `members/print/generate-report?clientId=${clientId}&query=${query}&filters=${JSON.stringify(
        filters,
      )}&sorting=${JSON.stringify(sorting)}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(downloadPrintedMembersUrl, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => {
        console.log('generate-report', response);
        const responseBlob = response.clone().blob();
        const responseJson = response.json();
        return responseJson
          .then(res => {
            if (res.error !== undefined) {
              return responseJson;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('generateMemberTicketsReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('generateMemberTicketsReport Error: ', error);
      });
  }

  public static async batchPrinting(payload: any) {
    try {
      const userData = await this.getUserDataFromDb();
      let userObj: any = {
        user_id: userData['userId'] ? userData['userId'] : '',
        user_fullname: userData['userName'] ? userData['userName'] : '',
      };

      const downloadPrintedMembersUrl =
        API_URL + `batch-printing?username=${JSON.stringify(userObj)}`;
      let getToken = await this.getAccessTokenFromDb();
      return fetch(downloadPrintedMembersUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(payload),
      })
        .then(response => {
          return response.json();
        })
        .catch(error => {
          console.log('generateMemberTicketsReport Error: ', error);
        });
    } catch (err) {
      console.log('getUserDataFromDb catch err', err);
      return err;
    }
  }

  public static async generateByBatch(clientId: string, payload: any) {
    const downloadPrintedMembersUrl =
      API_URL + `members/print/generate-by-batch?clientId=${clientId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(downloadPrintedMembersUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => {
        console.log('generate-report', response);
        const responseBlob = response.clone().blob();
        const responseJson = response.json();
        return responseJson
          .then(res => {
            if (res.error !== undefined) {
              return responseJson;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('generateMemberTicketsReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('generateMemberTicketsReport Error: ', error);
      });
  }

  public static async downloadConfirmTransmittalList(
    clientId: string,
    payload: any,
  ) {
    const downloadPrintedMembersUrl =
      API_URL + `members/print/generate-by-batch-json?clientId=${clientId}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(downloadPrintedMembersUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    }).then(response => response.json());
    // .then(response => {
    //   console.log('generate-report', response);
    //   const responseBlob = response.clone().blob();
    //   const responseJson = response.json();
    //   return responseJson
    //     .then(res => {
    //       if (res.error !== undefined) {
    //         return responseJson;
    //       } else {
    //         return responseBlob;
    //       }
    //     })
    //     .catch(res => {
    //       console.log('generateMemberTicketsReport error: ', res);
    //       return responseBlob;
    //     });
    // })
    // .catch(error => {
    //   console.log('generateMemberTicketsReport Error: ', error);
    // });
  }

  public static async postTicket(payload: any, claimed?: boolean) {
    payload['assignee'] = '';
    payload['assignee_pmaker_uid'] = '';
    if (claimed) {
      const userData = await this.getUserDataFromDb();
      payload['assignee'] = userData.userName;
      payload['assignee_pmaker_uid'] = userData.pmakerUid;
    }
    const MEMBER_TICKET = API_URL + 'members/ticket/';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(MEMBER_TICKET, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => {
        return response;
      })
      .catch(error => {
        console.log('member ticket error: ', error);
        return error;
      });
  }

  public static async patchTicket(
    payload: any,
    case_id: string,
    claimed?: boolean,
  ) {
    try {
      payload['assignee'] = '';
      payload['assignee_pmaker_uid'] = '';
      const userData = await this.getUserDataFromDb();
      if (claimed) {
        payload['assignee'] = userData.userName;
        payload['assignee_pmaker_uid'] = userData.pmakerUid;
      }
      const MEMBER_TICKET =
        API_URL +
        `members/ticket?username=${userData.userName}&where[pmaker_case_uid]=${case_id}&where[status][nin][0]=CLOSED`;
      let getToken = await this.getAccessTokenFromDb();
      return fetch(MEMBER_TICKET, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + getToken,
        },
        body: JSON.stringify(payload),
      })
        .then(response => {
          return response;
        })
        .catch(error => {
          console.log('member ticket error: ', error);
          return error;
        });
    } catch (err) {
      console.log('getUserDataFromDb catch err', err);
      return err;
    }
  }

  public static async postMemberRemark(
    source: string,
    memberId: string,
    remark: string,
  ) {
    let payload = {};

    try {
      const userData = await this.getUserDataFromDb();
      console.log('profile>userremarks111', userData);
      payload['user_id'] = userData.userId;
      payload['user_fullname'] = userData.userName;
      payload['username'] = userData.userName;
      payload['user_name'] = userData.userName;
    } catch (e) {
      throw new Error('USER_DATA_NOT_FOUND');
    }

    payload['member_id'] = memberId;
    payload['source'] = source;
    payload['remarks'] = remark;
    console.log('PAYLOAD POST remark', payload);
    const ADD_REMARK_URL = API_URL + 'member-remark';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(ADD_REMARK_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.ok)
      .catch(() => {
        return false;
      });
  }

  public static async getPersonProfileData() {
    const USERHISTORY_URL = API_URL + '/members/{personId}/accounts/history';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(USERHISTORY_URL, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postNewEmailTemplate(payload: Object) {
    const CLIENT_URL = API_URL + 'system-settings/email-templates';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getEmailTemplates() {
    const CLIENT_URL = API_URL + 'system-settings/email-templates';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getEmailTemplatesWithPaging(
    filter?: object,
    count?: number,
  ) {
    const CLIENT_URL =
      API_URL +
      `system-settings/email-templates-filter?filter=${JSON.stringify(
        filter,
      )}&count=${count}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getEmailTemplateById(id: String) {
    const CLIENT_URL = API_URL + `system-settings/email-templates/${id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async updateEmailTemplate(payload: Object, id: string) {
    const CLIENT_URL = API_URL + `system-settings/email-templates/${id}`;
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'put',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async saveActionMemoRecipients(
    payload: object,
    client_id: String,
  ) {
    const CLIENT_URL = API_URL + `/clients/${client_id}/action-memo-recipients`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async sendEmailExceptionReport(payload: object) {
    const CLIENT_URL = API_URL + 'members/notification/exception/report';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async sendEmailVoidExceptionReport(payload: object) {
    const CLIENT_URL = API_URL + 'members/notification/exception/report/void';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async saveActionMemoHistory(
    payload: object,
    client_id: String,
  ) {
    const CLIENT_URL = API_URL + `/clients/${client_id}/action-memo-history`;

    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async uploadEmailBodyImage(payload: any) {
    console.log('payload:', payload);
    const CLIENT_URL = API_URL + 'members/email-body-uploader';
    let getToken = await this.getAccessTokenFromDb();
    return fetch(CLIENT_URL, {
      method: 'post',
      headers: {
        Authorization: 'Bearer ' + getToken,
      },
      body: payload,
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  // Member Card Transmittal API
  public static async getMembersForTransmittalRequest(
    client_id: string,
    case_id?: string,
  ) {
    const token = await this.getAccessTokenFromDb();
    let REQUEST_URL = `${API_URL}member-transmittal/${client_id}/request`;
    if (!isNil(case_id)) {
      REQUEST_URL += `?case_id=${case_id}`;
    }
    return fetch(REQUEST_URL, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async generateMemberCardTransmittalRequest(
    client_id: string,
    payload: any,
  ) {
    const token = await this.getAccessTokenFromDb();

    return fetch(`${API_URL}member-transmittal/${client_id}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMembersByCaseTracking(
    client_id: string,
    case_id: string,
    tracking_no: string,
  ) {
    const token = await this.getAccessTokenFromDb();

    return fetch(
      `${API_URL}member-transmittal/${client_id}/${case_id}/${tracking_no}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async submitMemberCardTransmittal(
    client_id: string,
    payload: any,
  ) {
    const token = await this.getAccessTokenFromDb();

    return fetch(`${API_URL}member-transmittal/${client_id}/submit`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async checkToken(token) {
    let URL = API_URL + 'underwriting/user/check';
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    }).then(async (res: any) => {
      if (!res.ok) {
        let respjson = await res.json();
        let errormessage = respjson.error.message;
        throw Error(errormessage);
      }
      return res ? res.json() : null;
    });
  }

  public static async getUsersALL(filters: any, limit?: number) {
    //http://localhost:4101/api/users?filter[offset]=0&filter[limit]=5&filter[skip]=0
    console.log('userfilter', filters);
    console.log('userlimit', limit);
    const token = await this.getAccessTokenFromDb();
    // let URL = `${API_URL}membership-users?filter=${JSON.stringify(
    //   filters,
    // )}&[offset]=0&filter[limit]=${limit}&filter[skip]=0`;
    let URL = `${API_URL}membership-users?filter=${JSON.stringify(
      filters,
    )}&[offset]=0&`;
    //filter[limit]=${limit}&filter[skip]=0
    console.log('URL USE THIS', URL);
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async response => {
        console.log('THE RESPONSE', response);
        const res = await response.json();
        console.log('THE RESPONSE RES', res);

        return res;
        // if(!response.ok){
        //   throw Error(res.error.message)
        // }
        // return true;
      })
      .catch(error => {
        console.log('check token error: ', error);
        return false;
      });
  }

  public static async getMSUsers() {
    const token = await this.getAccessTokenFromDb();
    let URL = API_URL + 'users/membership';
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async response => {
        console.log('GET USERS RESPONSE');
        const res = await response.json();
        console.log('FINAL FINAL FINAL', res);
        // if(!response.ok){
        //   throw Error(res.error.message)
        // }
        // return true;
      })
      .catch(error => {
        console.log('check token error: ', error);
        return false;
      });
  }

  /* try this to get user list not by account but by groupzsz
  @get('/awb/accounts/list', {
    responses: {
      '200': {
        description: 'Returns List of Users',
        content: {
          'application/json': {
            schema: { type: 'object', items: { 'x-ts-type': { body: Array<Login>(), count: Number } } },
          },
        },
      },
    },
  })
  async listAccounts(


  */

  public static async getMembershipUsersByGroup() {
    const token = await this.getAccessTokenFromDb();
    let URL = API_URL + 'users-by-group';
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async response => {
        console.log('GET USERS RESPONSE22', response);
        const res = await response.json();
        console.log('GET USERS RESPONSE33', res);
        if (!response.ok) {
          throw Error(res.error.message);
        }
        return true;
      })
      .catch(error => {
        console.log('check token error: ', error);
        return false;
      });
  }

  //users/ms-users
  public static async getMS() {
    const token = await this.getAccessTokenFromDb();
    let URL = API_URL + 'users/membership';
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async response => {
        console.log('GET USERS RESPONSE0', response);
        const res = await response.json();
        console.log('GET USERS RESPONSE00', res);
        if (!response.ok) {
          throw Error(res.error.message);
        }
        return true;
      })
      .catch(error => {
        console.log('check token error: ', error);
        return false;
      });
  }

  public static async getMembershipUsers() {
    const token = await this.getAccessTokenFromDb();
    let URL = API_URL + 'users';
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async response => {
        console.log('GET USERS RESPONSE0', response);
        const res = await response.json();
        console.log('GET USERS RESPONSE00', res);
        if (!response.ok) {
          throw Error(res.error.message);
        }
        return true;
      })
      .catch(error => {
        console.log('check token error: ', error);
        return false;
      });
  }

  public static async getTicketList(filter?: any) {
    const token = await this.getAccessTokenFromDb();
    let URL = API_URL + 'members/ticket';
    if (filter) {
      URL = URL + '?filter=' + JSON.stringify(filter);
    }
    return fetch(URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    })
      .then(response => response.json())
      .catch(error => {
        console.log('check token error: ', error);
      });
  }

  public static async activitySendActionMemo(ticket_id, ticket, username) {
    let ACTION__MEMO_URL =
      API_URL + `members/${ticket_id}/send-action-memo/${username}`;
    console.log('CLAIM URL', ACTION__MEMO_URL);
    let getToken = await this.getAccessTokenFromDb();

    return fetch(ACTION__MEMO_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + getToken,
      },
      body: JSON.stringify(ticket),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('Send Action Memo error', error);
      });
  }

  public static async updateVerifyTermination(
    member_upload_id: string,
    payload: any[],
  ) {
    const MEMBER_UPLOAD_URL = `${API_URL}member-upload/verify-terminate/${member_upload_id}`;
    console.log('API_URL', API_URL);
    return fetch(MEMBER_UPLOAD_URL, {
      // method: 'get',
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async downloadPdfProcessMemberReport(
    payload: any,
    username: string,
  ) {
    const MEMBER_TICKET_URL = `${API_URL}members/ticket/members-processed-report?username=${username}`;
    console.log('API_URL', API_URL);
    return fetch(MEMBER_TICKET_URL, {
      // method: 'get',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify(payload),
    })
      .then(response => {
        const responseBlob = response.blob();
        const responseJSON = response.json();
        return responseJSON
          .then(res => {
            if (res.error !== undefined) {
              return responseJSON;
            } else {
              return responseBlob;
            }
          })
          .catch(res => {
            console.log('downloadExceptionReport error: ', res);
            return responseBlob;
          });
      })
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async downloadOcpFile(url: string) {
    const response = await fetch(url);
    return response.arrayBuffer();
  }

  public static async createMemberProcessed(payload: any) {
    const MEMBER_URL = `${API_URL}member-processed`;
    return fetch(MEMBER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getMemberProcessedReports(payload: any) {
    const MEMBER_URL = `${API_URL}member-processed/reports`;
    return fetch(MEMBER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async postMemberProcessedClaim(payload: any) {
    const MEMBER_URL = `${API_URL}member-processed/claim/ticket`;
    return fetch(MEMBER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + localStorage.getItem('api_token'),
      },
      body: JSON.stringify(payload),
    })
      .then(response => response.json())
      .catch(error => {
        console.log('client error: ', error);
      });
  }

  public static async getCheckConnection() {
    const MEMBER_URL = `${API_URL}user/check-connection`;
    return fetch(MEMBER_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }).then(response => response.json());
  }
}
