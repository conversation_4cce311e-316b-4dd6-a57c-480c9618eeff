import React, { useState, useEffect } from 'react';
import { Grid, TextField } from '@material-ui/core/';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import {
  every,
  filter,
  forEach,
  includes,
  isArray,
  isEmpty,
  isNil,
  toLower,
} from 'lodash';
import { Redirect } from 'react-router-dom';
import { API } from 'Pages/API';
import { Loader } from 'Components/UI/LoadingIndicator';
import { ModalComponent } from 'Components/UI/ModalComponent';
import { TableComponent } from 'Components/UI/TableComponent';

interface HistoryProps {
  memberId: string;
  isSelected: boolean;
  handleOpenLostInternetConnectionModal: () => void; 
  handleCloseLostInternetConnectionModal: () => void;
}

interface ModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  next?: () => void;
}

export const HistorySection: React.FC<HistoryProps> = (
  props: HistoryProps,
): JSX.Element => {
  const [isLoading, setIsLoading] = useState(false);
  const [allData, setAllData] = useState<any[]>([]);
  const [tableRows, setTableRows] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [defaultFilters, setDefaultFilters] = useState<any[]>([]);
  const [sorting, setSorting] = useState<any[]>([]);
  const [typingTimeout, setTypingTimeout] = useState<any>(0);
  const [initialize, setInitialize] = useState(true);
  const [modalProps, setModalProps] = useState<ModalProps>({
    isOpen: false,
    title: '',
    message: '',
  });
  const [rowsContract, setRowsContract] = useState<any[]>([]);
  const [rowData, setRowData] = useState('');
  const [redirect, setRedirect] = useState(false);

  useEffect(() => {
    getMemberContractsHistory();
  }, [props.isSelected]);

  useEffect(() => {
    handleSearchAndFilter();
  }, [allData, sorting, defaultFilters]);

  useEffect(() => {
    clearTimeout(typingTimeout);

    if(!initialize){
      setTypingTimeout(() => {
        return setTimeout(() => {
          handleSearchAndFilter();
        }, 500);
      });
    }

    setInitialize(false)
    
  }, [searchQuery]);

  const getMemberContractsHistory = () => {
    if (isNil(props.memberId)) {
      return;
    }
    if (props.memberId === '') {
      return;
    }

    setIsLoading(true);

    props.handleOpenLostInternetConnectionModal() 
    API.getContractsHistory(props.memberId)
      .then(response => {
        if (!isNil(response) && isNil(response.error) && isArray(response)) {
          props.handleCloseLostInternetConnectionModal()
          console.log('check history',response,props)
          setRowsContract(response);
          generateTableData(response);
        } else {
          setModalProps({
            isOpen: true,
            title: 'Loading failed',
            message:
              'An error occured while loading the list of contract history.',
          });
        }
        setIsLoading(false);
      })
      .catch(e => {
        console.log('getMemberContractsHistory Error', e);
        setIsLoading(true);
      });
  };

  const generateTableData = (data: any[]) => {
    const rows: any[] = [];
    forEach(data, contractHistory => {
      rows.push({
        contract: <a href="javascript:void(0);">{`${contractHistory.contract_name}`}</a>,
        effectivity_date: contractHistory.contract_effectivity_date,
        plan_type: contractHistory.member_plan_type,
        type: contractHistory.member_type,
        status: contractHistory.status,
      });
    });

    setAllData(rows);
  };

  const columns: any = [
    {
      name: 'contract',
      title: 'Contract',
    },
    {
      name: 'effectivity_date',
      title: 'Contract Effectivity Date',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'type',
      title: 'Type',
    },
    {
      name: 'status',
      title: 'Status',
    },
  ];

  const columnExtensions = [
    {
      columnName: 'contract',
      wordWrapEnabled: true,
      textAlign: 'center',
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
      textAlign: 'center',
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
      textAlign: 'center',
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
      textAlign: 'center',
    },
    {
      columnName: 'status',
      wordWrapEnabled: true,
      textAlign: 'center',
    },
  ];

  const handleFilterByColumn = (filter: any) => {
    setDefaultFilters(filter);
  };

  const handleSearchAndFilter = () => {
    if (isNil(searchQuery) && isEmpty(defaultFilters)) {
      setTableRows(allData);

      return;
    }

    const includesQuery = (contractHistory: any) => {
      if (isNil(searchQuery)) {
        return true;
      }

      const query = toLower(searchQuery);
      const contract = toLower(contractHistory.contract);
      const effectivityDate = toLower(contractHistory.effectivity_date);
      const planType = toLower(contractHistory.plan_type);
      const type = toLower(contractHistory.type);
      const status = toLower(contractHistory.status);

      return (
        includes(contract, query) ||
        includes(effectivityDate, query) ||
        includes(planType, query) ||
        includes(type, query) ||
        includes(status, query)
      );
    };

    const includesFilters = (contractHistory: any) => {
      if (isEmpty(defaultFilters)) {
        return true;
      }

      const contract = toLower(contractHistory.contract);
      const effectivityDate = toLower(contractHistory.effectivity_date);
      const planType = toLower(contractHistory.plan_type);
      const type = toLower(contractHistory.type);
      const status = toLower(contractHistory.status);

      return every(defaultFilters, defaultFilter => {
        const query = toLower(defaultFilter.value);

        switch (defaultFilter.columnName) {
          case 'contract':
            return includes(contract, query);
          case 'effectivity_date':
            return includes(effectivityDate, query);
          case 'plan_type':
            return includes(planType, query);
          case 'type':
            return includes(type, query);
          case 'status':
            return includes(status, query);
          default:
            return true;
        }
      });
    };

    const rows = filter(allData, contractHistory => {
      return includesQuery(contractHistory) && includesFilters(contractHistory);
    });

    setTableRows(rows);
  };

  const handleSortChange = (sort: any) => {
    setSorting(sort);
  };

  const handleRowClick = (row: any) => {
    console.log('handleRowClick row', row);
        if (rowsContract[row]) {
           setRowData(rowsContract[row]);
           setRedirect(true);
        }
        return null;
  };
  
     if (redirect === true) {
        return (
        <Redirect to={{ pathname: '/membership/member-contract/' + rowData['_id'] + '/' + rowData['contract_id'] }} />
        )
      }


  const closeModal = () => {
    const next = modalProps.next;

    setModalProps({
      isOpen: false,
      title: '',
      message: '',
    });

    if (next) {
      next();
    }
  };

  return (
    <Grid container>
      <Grid item xs={12}>
        {isLoading ? <Loader /> : null}
      </Grid>
      <Grid item xs={12} style={{ paddingLeft: '24px' }}>
        <TextField
          // id="contract_search_tf"
          data-cy={'history_search'}
          id={'history_search'}
          placeholder="Search year, company, or contract"
          style={{ backgroundColor: 'white' }}
          margin="dense"
          variant="outlined"
          fullWidth
          value={searchQuery}
          onChange={(event: any) => {
            setSearchQuery(event.target.value);
          }}
          inputProps={{ 'aria-label': 'bare' }}
          InputProps={{
            startAdornment: (
              <FontAwesomeIcon
                icon={faSearch}
                style={{ color: '#00000061', marginRight: '8px' }}
              />
            ),
          }}
        />
      </Grid>
      <Grid
        item
        xs={12}
        style={{
          boxShadow: '0px 3px 6px #00000029',
          marginTop: '20px',
          marginLeft: '24px',
        }}
      >
        
        <TableComponent
          // id="history-table"
          data-cy={'history_table'}
          id={'history_table'}
          rows={tableRows}
          columns={columns}
          message="No data"
          columnExtensions={columnExtensions}
          formattedColumns={{}}
          defaultFilter={defaultFilters}
          onFilterChange={handleFilterByColumn}
          onSortingChange={handleSortChange}
          onClickRow={handleRowClick}
          disableSelect
          disableSearch
        />
      
      </Grid>
      <Grid item xs={12}>
        <ModalComponent
          id="contract-history-modal"
          isModalOpen={modalProps.isOpen}
          title={modalProps.title}
          message={modalProps.message}
          onClose={closeModal}
        />
      </Grid>
    </Grid>
  );
};
