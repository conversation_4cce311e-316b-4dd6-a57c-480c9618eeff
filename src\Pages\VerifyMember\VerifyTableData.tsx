import { map } from 'lodash';

const getIntgFltrColumnExtensions = (columnNames: string[]) => {
  const predicate = () => true;

  return map(columnNames, name => {
    return {
      columnName: name,
      predicate: predicate,
    };
  });
};

/** VERFIY EDIT TABLES */



const verifyEdit_conflictOnData: any = {
  formattedColumns: {
    verify_edit_action: ['conflict_data_action'],
    required_columns: [
      'no',
      'member_id',
      'first_name',
      'last_name',
      'date_of_birth',
      'civil_status',
      'gender',
      "member_type",
      'plan_type',
      'principal_name',
      'relationship_to_principal',
      
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_id',
    'first_name',
    'middle_name',
    'last_name',
    'date_of_birth',
    'member_type',
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'remarks',
    'user_remarks',
    'relationship_to_principal',
  ]),
  filterExtensions: [
    {
      columnName: 'conflict_data_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 150,
    },
  
    {
      columnName: 'conflict_data_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'Sytem Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
   
    {
      name: 'conflict_data_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const verifyEdit_forValidation: any = {
  formattedColumns: {
    verify_edit_action: ['for_validation_action'],
    required_columns: [
      "member_id",
      'first_name',
      'last_name',
      "date_of_birth",
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    "no",
    "member_id",
    'first_name',
    'middle_name',
    'last_name',
    "date_of_birth",
    'member_type',
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'remarks',
    'relationship_to_principal',
  ]),
  filterExtensions: [
    {
      columnName: 'for_validation_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 150,
    },
 
    {
      columnName: 'for_validation_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
 
    {
      name: 'for_validation_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const verifyEdit_other: any = {
  formattedColumns: {
    // verify_edit_action: ['other_members_action'],
    required_columns: [
     "no", 
      'first_name',
      'last_name',
      'civil_status',
      "date_of_birth",
      'gender',
      'plan_type',
      'member_status',

      'relationship_to_principal',
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    "no",
    'member_id',
    'first_name',
    'middle_name',
    'last_name',
    "date_of_birth",
    'civil_status',
    'gender',
    'plan_type',
    'member_type',
    'member_status',
    'principal_name',
    'remarks',
    'relationship_to_principal',
  ]),
  filterExtensions: [
    {
      columnName: 'other_members_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 150,
    },

    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
      width: 150,
    },
  
    {
      columnName: 'member_status',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
      
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
      width: 150,
    },

 
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 150,
    },
  
    {
      columnName: 'other_members_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },

    {
      name: 'member_status',
      title: 'Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },

    {
      name: 'remarks',
      title: 'Sytem Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
    
    // {
    //   name: 'other_members_action',
    //   title: 'Action',
    // },
  ],
  rows: [],
};

const verifyEdit_supporting: any = {
  formattedColumns: {
    verify_edit_action: ['supporting_action'],
    required_columns: [
      "member_id",
      'first_name',
      'last_name',
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
    ]
    // bubble_chat: ['similar_fields']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    "no",
    "member_id",
    'first_name',
    'middle_name',
    'last_name',
    'date_of_birth',
    'member_type',
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'remarks',
    'relationship_to_principal',

  ]),
  filterExtensions: [
    {
      columnName: 'suppoting_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
      
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
   
  
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 150,
    },
 
    {
      columnName: 'supporting_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
 
    {
      name: 'supporting_action',
      title: 'Action',
    },
  ],
  rows: [],
}


//remove
const verifyEdit_terminatedMembers: any = {
  formattedColumns: {
    verify_edit_action: ['terminated_members_action'],
    required_columns: [
      'first_name',
      'last_name',
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'first_name',
    'middle_name',
    'last_name',
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'remarks',
    'relationship_to_principal',
  ]),
  filterExtensions: [
    {
      columnName: 'terminated_members_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'terminated_members_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'terminated_members_action',
      title: 'Action',
    },
  ],
  rows: [],
};
//remove
const verifyEdit_activeMembers: any = {
  formattedColumns: {
    verify_edit_action: ['active_members_action'],
    required_columns: [
      'first_name',
      'last_name',
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'first_name',
    'middle_name',
    'last_name',
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'remarks',
    'relationship_to_principal',
  ]),
  filterExtensions: [
    {
      columnName: 'active_members_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      width: 200,
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'active_members_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'active_members_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const verifyEdit_approveMembers: any = {
  formattedColumns: {
    verify_edit_action: ['active_members_action'],
    required_columns: [
      'member_id',
      'first_name',
      'middle_name',
      'last_name',
      'effectivity_date',
      "member_type",
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
      
      
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_id',
    'first_name',
    'middle_name',
    'last_name',
    'effectivity_date',
    "member_type",
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'relationship_to_principal',

  ]),
  filterExtensions: [
    {
      columnName: 'active_members_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'full_name',
      wordWrapEnabled: true,
      width: 250,
    },

    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 400,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 400,
    },
  
   
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'full_name',
      title: 'Full Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
 
  ],
  rows: [],
};
const verifyEdit_disapproveMembers: any = {
  formattedColumns: {
    verify_edit_action: ['active_members_action'],
    required_columns: [
      'no',
      'member_id',
      'first_name',
      'middle_name',
      'last_name',
      "member_type",
      'civil_status',
      'gender',
      'plan_type',
      'principal_name',
      'relationship_to_principal',
      
    ]
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_id',
    'first_name',
    'middle_name',
    'last_name',
    'effectivity_date',
    "member_type",
    'civil_status',
    'gender',
    'plan_type',
    'principal_name',
    'relationship_to_principal',

  ]),
  filterExtensions: [
    {
      columnName: 'active_members_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      width: 150,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'full_name',
      wordWrapEnabled: true,
      width: 250,
    },

    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 400,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 400,
    },
  
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'full_name',
      title: 'Full Name',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
 
  ],
  rows: [],
};


/** VERFIY MEMBER TABLES */
const partial_member: any = {
  formattedColumns: {
    radio: ['action'],
    // bubble_chat: ['similar_fields']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_added',
    'existing_member',
    'member_type',
    'remarks',
    'user_remarks',
    'plan_type',
    'member_type',
    'similar_fields',
  ]),
  filterExtensions: [
    {
      columnName: 'action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_added',
      wordWrapEnabled: true,
    },
    {
      columnName: 'existing_member',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      width: 300,
      wordWrapEnabled: true,
    },
    {
      columnName: 'user_remarks',
      width: 300,
      wordWrapEnabled: true,
    },
    {
      columnName: 'similar_fields',
      wordWrapEnabled: true,
    },
    {
      columnName: 'action',
      width: 300,
    },
  ],
  columns: [
    {
      name: 'member_added',
      title: 'Member Added',
    },
    {
      name: 'existing_member',
      title: 'Existing Member Name',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
      {
        name: 'similar_fields',
        title: 'Similar Field/s',
      },
      {
        name: 'action',
        title: 'Action',
      },
  ],
  rows: [],
};

const partial_items: any = [
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
];

const partial_columns: any = {
  columnExtensions: [
    {
      columnName: 'system_name',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'existing_member',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'added_member',
      wordWrapEnabled: true,
      width: 200,
    },
 
  ],
  columns: [
    {
      name: 'system_name',
      title: 'System Name',
    },
    {
      name: 'existing_member',
      title: 'Existing Member (s)',
    },
    {
      name: 'added_member',
      title: 'Added Member',
    },

  ],
};

const incomplete_data: any = {
  formattedColumns: {
    incomplete_radio: ['incomplete_action'],
    // bubble_chat: ['missing_fields']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'first_name',
    'middle_name',
    'last_name',
    'suffix',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'remarks',
    'user_remarks',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'missing_fields',
  ]),
  filterExtensions: [
    {
      columnName: 'incomplete_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'suffix',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      // width: 200,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'missing_fields',
      wordWrapEnabled: true,
      // width: 180,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'incomplete_action',
      wordWrapEnabled: true,
     
    },
  ],
  columns: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',

    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',

    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'missing_fields',
      title: 'Missing Fields',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
    {
      name: 'incomplete_action',
      title: 'Action',
    },
  ],
  columns2: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'suffix',
      title: 'Suffix',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'missing_fields',
      title: 'Missing Fields',
    },
    {
      name: 'remarks',
      title: 'System Remarks',

    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
    {
      name: 'incomplete_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const conflict_data: any = {
  formattedColumns: {
    incomplete_radio: ['conflict_data_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'remarks',
    'user_remarks'
  ]),
  filterExtensions: [
    {
      columnName: 'conflict_data_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'conflict_data_action',
      width: 250,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
    {
      name: 'conflict_data_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const validation_data: any = {
  formattedColumns: {
    incomplete_radio: ['validation_action'],
    principal_select: ['principal_name']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'batch_name',
    'system_remarks',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'validation_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'batch_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'validation_action',
      width: 280,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'batch_name',
      title: 'Principal Batch No.',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
    {
      name: 'validation_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const other_validation_data: any = {
  formattedColumns: {
    incomplete_radio: ['other_validation_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'type',
    'principal_name',
    'relationship_to_principal',
    'plan_type',
    'remarks',
    'user_remarks'
  ]),
  filterExtensions: [
    {
      columnName: 'other_validation_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'user_remarks',   
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'other_validation_action',
      // width: 250,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },

    {
      name: 'plan_type',
      title: 'Plan Type',
    },
   
    {
      name: 'remarks',
      title: 'System Remarks',
   
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    
    },
    {
      name: 'other_validation_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const supporting_data: any = {
  formattedColumns: {
    incomplete_radio: ['supporting_data_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no.',
    'member_name',
    'gender',
    'date_of_birth',
    'civil_status',
    'type',
    'plan_type',
    'relationship_to_principal',
    'principal_name',
    'remarks',
    'user_remarks'
  ]),
  filterExtensions: [
    {
      columnName: 'suppoting_data_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
      // width: 250,
    },
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      // width: 250,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      // width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'supporting_data_action',
      // width: 450,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No.',
    },
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'date_of_birth',
      title: 'Date of Birth',
    },
    {
      name: 'type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'remarks',
      title: 'System Remarks',
    
    },
    {
      name: 'user_remarks',
      title: ' Remarks',
    },
    {
      name: 'supporting_data_action',
      title: 'Action',
    },
  ],
  rows: [], 
}

const disapprove_data: any = {
  formattedColumns: {
    disapprove_action: ['disapprove_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'system_remarks',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'disapprove_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
 
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 300,
    },
    {
      columnName: 'disapprove_action',
      // width: 200,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'remarks',
      title: 'System Remarks',

    },
    {
      name: 'user_remarks',
      title: 'Remarks',
 
    },
    {
      name: 'disapprove_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const approved_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_name',
    'plan_type',
    'member_id',
    'date_of_birth',
    'gender',
    'civil_status',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'effectivity_date',
    'remarks',
    'user_remarks',
  ]),
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_columnName',
      wordWrapEnabled: true,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true, 
    }, 
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'user_remarks',
      wordWrapEnabled: true,
      width: 200,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No.',
    },
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'date_of_birth',
      title: 'Date of Birth',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
    {
      name: 'remarks',
      title: 'System Remarks',  
    },
    {
      name: 'user_remarks',
      title: 'Remarks',
    },
  ],
  rows: [],
};

// const approved_data: any = {
//   formattedColumns: {},
//   intgFltrColumnExtensions: getIntgFltrColumnExtensions([
//     'member_name',
//     'plan_type',
//     'account_no',
//     'date_of_birth',
//     'member_type',
//     'civil_status',
//     'gender',
//     'effectivity_date',
//     'principal_name',
//     'relationship_to_principal',
//   ]),
//   columnExtensions: [
//     {
//       columnName: 'member_name',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'plan_type',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'account_no',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'date_of_birth',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'member_type',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'civil_status',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'gender',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'effectivity_date',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'principal_columnName',
//       wordWrapEnabled: true,
//     },
//     {
//       columnName: 'relationship_to_principal',
//       wordWrapEnabled: true,
//     },
//   ],
//   columns: [
//     {
//       name: 'member_name',
//       title: 'Member Name',
//     },
//     {
//       name: 'plan_type',
//       title: 'Plan Type',
//     },
//     {
//       name: 'account_no',
//       title: 'Account No',
//     },
//     {
//       name: 'date_of_birth',
//       title: 'Date of Birth',
//     },
//     {
//       name: 'member_type',
//       title: 'Member Type',
//     },
//     {
//       name: 'civil_status',
//       title: 'Civil Status',
//     },
//     {
//       name: 'gender',
//       title: 'Gender',
//     },
//     {
//       name: 'effectivity_date',
//       title: 'Effectivity Date',
//     },
//     {
//       name: 'principal_name',
//       title: 'Principal Name',
//     },
//     {
//       name: 'relationship_to_principal',
//       title: 'Relationship',
//     },
//   ],
//   rows: [],
// };

const all_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'member_issue',
  ]),
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_issue',
      wordWrapEnabled: true,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Name',
    },
    {
      name: 'member_issue',
      title: 'Issues',
    },
  ],
  rows: [],
};


const default_tab_list: any[] = [
  {
    label: 'Partial Matches',
    count: partial_member['rows'].length,
  },
  {
    label: 'Incomplete Requirements',
    count: incomplete_data['rows'].length,
  },
  {
    label: 'Conflict on Data',
    count: conflict_data['rows'].length,
  },
  {
    label: 'For Validation',
    count: validation_data['rows'].length,
  },
  {
    label: 'Other Validation Rules',
    count: other_validation_data['rows'].length,
  },
  {
    label: 'Requiring Supporting Documents',
    count: supporting_data['rows'].length,
  },
  {
    label: 'Disapproved',
    count: disapprove_data['rows'].length,
  },
  {
    label: 'Approved',
    count: approved_data['rows'].length,
  },
  {
    label: 'All',
    count: all_data['rows'].length,
  },
];

//~ TEMPORARILY SET FOR VERIFY EDIT TESTING. FINALIZE WHEN FILE FOR CORRECTION IS COMPLETE
const edit_tab_list: any[] = [
  {
    label: 'Conflict on Data',
    count: 0,
  },
  {
    label: 'For Validation',
    count: 0,
  },
  {
    label: 'Requiring Supporting Documents',
    count: verifyEdit_supporting['rows'].length,
  },
  {
    label: 'Terminated Members',
    count: 0,
  },
  {
    label: 'Active Members',
    count: 0,
  },
  {
    label: 'Others',
    count: 0,
  },
];



/** VOID MASTERLIST TABLES */
const void_partial_member: any = {
  formattedColumns: {
    radio: ['action'],
    // bubble_chat: ['similar_fields']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_added',
    'existing_member',
    'member_type',
    'similar_fields',
  ]),
  filterExtensions: [
    {
      columnName: 'action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_added',
      wordWrapEnabled: true,
    },
    {
      columnName: 'existing_member',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'similar_fields',
      wordWrapEnabled: true,
    },
    {
      columnName: 'action',
      width: 400,
    },
  ],
  columns: [
    {
      name: 'member_added',
      title: 'Member Added',
    },
    {
      name: 'existing_member',
      title: 'Existing Member Name',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'similar_fields',
      title: 'Similar Field/s',
    },
    {
      name: 'action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_partial_items: any = [
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
  {
    status: '',
    person_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member (s)',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
    member_info: {
      columnExtensions: [
        {
          columnName: 'system_name',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'existing_member',
          wordWrapEnabled: true,
          width: 200,
        },
        {
          columnName: 'added_member',
          wordWrapEnabled: true,
          width: 200,
        },
      ],
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'existing_member',
          title: 'Existing Member',
        },
        {
          name: 'added_member',
          title: 'Added Member',
        },
      ],
      rows: [],
    },
  },
];

const void_partial_columns: any = {
  columnExtensions: [
    {
      columnName: 'system_name',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'existing_member',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'added_member',
      wordWrapEnabled: true,
      width: 200,
    },
  ],
  columns: [
    {
      name: 'system_name',
      title: 'System Name',
    },
    {
      name: 'existing_member',
      title: 'Existing Member (s)',
    },
    {
      name: 'added_member',
      title: 'Added Member',
    },
  ],
};

const void_incomplete_data: any = {
  formattedColumns: {
    incomplete_radio: ['incomplete_action'],
    // bubble_chat: ['missing_fields']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'first_name',
    'middle_name',
    'last_name',
    'suffix',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'missing_fields',
  ]),
  filterExtensions: [
    {
      columnName: 'incomplete_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'first_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'middle_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'last_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'suffix',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 200,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'missing_fields',
      wordWrapEnabled: true,
      width: 180,
    },
    {
      columnName: 'incomplete_action',
      width: 300,
    },
  ],
  columns: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    // {
    //   name: 'suffix',
    //   title: 'Suffix',
    // },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'missing_fields',
      title: 'Missing Fields',
    },
    {
      name: 'incomplete_action',
      title: 'Action',
    },
  ],
  columns2: [
    {
      name: 'first_name',
      title: 'First Name',
    },
    {
      name: 'middle_name',
      title: 'Middle Name',
    },
    {
      name: 'last_name',
      title: 'Last Name',
    },
    {
      name: 'suffix',
      title: 'Suffix',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'missing_fields',
      title: 'Missing Fields',
    },
    {
      name: 'incomplete_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_conflict_data: any = {
  formattedColumns: {
    incomplete_radio: ['conflict_data_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'conflict_data_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'conflict_data_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'conflict_data_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_unmatched_data: any = {
  formattedColumns: {
    incomplete_radio: ['validation_action'],
    principal_select: ['principal_name']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'batch_name',
  ]),
  filterExtensions: [
    {
      columnName: 'validation_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'batch_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'validation_action',
      width: 280,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'batch_name',
      title: 'Principal Batch No.',
    },
    {
      name: 'validation_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_other_validation_data: any = {
  formattedColumns: {
    incomplete_radio: ['other_validation_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'type',
    'principal_name',
    'relationship_to_principal',
    'plan_type',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'other_validation_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'other_validation_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    // {
    //   name: 'civil_status',
    //   title: 'Civil Status',
    // },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    // {
    //   name: 'date_of_birth',
    //   title: 'Birthdate',
    // },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'other_validation_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_supporting_data: any = {
  formattedColumns: {
    incomplete_radio: ['supporting_data_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'suppoting_data_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
      width: 250,
    },
    {
      columnName: 'supporting_data_action',
      width: 450,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'supporting_data_action',
      title: 'Action',
    },
  ],
  rows: [], 
}

const void_disapprove_data: any = {
  formattedColumns: {
    disapprove_action: ['disapprove_action'],
    // bubble_chat: ['remarks']
  },
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'plan_type',
    'principal_name',
    'relationship_to_principal',
    'remarks',
  ]),
  filterExtensions: [
    {
      columnName: 'disapprove_action',
      filteringEnabled: false,
      sortingEnabled: false,
    },
  ],
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
    },
    {
      columnName: 'disapprove_action',
      width: 300,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'principal_name',
      title: 'Name of Principal',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship to Principal',
    },
    {
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'disapprove_action',
      title: 'Action',
    },
  ],
  rows: [],
};

const void_approved_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_name',
    'plan_type',
    'member_id',
    'date_of_birth',
    'gender',
    'civil_status',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'effectivity_date'
  ]),
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_columnName',
      wordWrapEnabled: true,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
    }, 
  ],
  columns: [
    {
      name: 'no',
      title: 'No.',
    },
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'date_of_birth',
      title: 'Date of Birth',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
  ],
  rows: [],
};

const void_all_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'member_issue',
  ]),
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_issue',
      wordWrapEnabled: true,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Name',
    },
    {
      name: 'member_issue',
      title: 'Issues',
    },
  ],
  rows: [],
};

const void_existing_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_name',
    'plan_type',
    'member_id',
    'date_of_birth',
    'gender',
    'civil_status',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'effectivity_date',
  ]),
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },{
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'date_of_birth',
      title: 'Date Of Birth',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },{
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
  ],
  rows: [],
}

const void_voided_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'member_name',
    'civil_status',
    'gender',
    'date_of_birth',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'plan_type',
    'remarks',
    'action'
  ]),
  columnExtensions: [
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },{
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'remarks',
      wordWrapEnabled: true,
    },
    {
      columnName: 'action',
      wordWrapEnabled: true,
    },
  ],
  columns: [
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'date_of_birth',
      title: 'Birthdate',
    },
    {
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },{
      name: 'remarks',
      title: 'Remarks',
    },
    {
      name: 'action',
      title: 'Action',
    },
  ],
  rows: [],
}

const void_reactivated_data: any = {
  formattedColumns: {},
  intgFltrColumnExtensions: getIntgFltrColumnExtensions([
    'no',
    'member_name',
    'plan_type',
    'member_id',
    'date_of_birth',
    'gender',
    'civil_status',
    'member_type',
    'relationship_to_principal',
    'principal_name',
    'effectivity_date',
  ]),
  columnExtensions: [
    {
      columnName: 'no',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'plan_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'member_id',
      wordWrapEnabled: true,
    },
    {
      columnName: 'date_of_birth',
      wordWrapEnabled: true,
    },
    {
      columnName: 'gender',
      wordWrapEnabled: true,
    },
    {
      columnName: 'civil_status',
      wordWrapEnabled: true,
    },{
      columnName: 'member_type',
      wordWrapEnabled: true,
    },
    {
      columnName: 'relationship_to_principal',
      wordWrapEnabled: true,
    },
    {
      columnName: 'principal_name',
      wordWrapEnabled: true,
    },
    {
      columnName: 'effectivity_date',
      wordWrapEnabled: true,
    },
  ],
  columns: [
    {
      name: 'no',
      title: 'No',
    },
    {
      name: 'member_name',
      title: 'Member Name',
    },
    {
      name: 'plan_type',
      title: 'Plan Type',
    },
    {
      name: 'member_id',
      title: 'Member ID',
    },
    {
      name: 'date_of_birth',
      title: 'Date Of Birth',
    },
    {
      name: 'gender',
      title: 'Gender',
    },
    {
      name: 'civil_status',
      title: 'Civil Status',
    },{
      name: 'member_type',
      title: 'Member Type',
    },
    {
      name: 'relationship_to_principal',
      title: 'Relationship',
    },
    {
      name: 'principal_name',
      title: 'Principal Name',
    },
    {
      name: 'effectivity_date',
      title: 'Effectivity Date',
    },
  ],
  rows: [],
}

const void_multiple_data: any = {

}

export {
  // Verify Void Masterlist table data
  void_partial_member,
  void_partial_items,
  void_partial_columns,
  void_incomplete_data,
  void_conflict_data,
  void_unmatched_data,
  void_other_validation_data,
  void_supporting_data,
  void_existing_data,
  void_voided_data,
  void_reactivated_data,
  void_multiple_data,
  void_disapprove_data,
  void_approved_data,
  void_all_data,
  // Verify Edit table data
  verifyEdit_conflictOnData,
  verifyEdit_forValidation,
  verifyEdit_terminatedMembers,
  verifyEdit_activeMembers,
  verifyEdit_approveMembers,
  verifyEdit_disapproveMembers,
  verifyEdit_other,
  verifyEdit_supporting,
  // Verify Member table data
  partial_member,
  partial_items,
  partial_columns,
  incomplete_data,
  conflict_data,
  validation_data,
  other_validation_data,
  supporting_data,
  disapprove_data,
  approved_data,
  all_data,

  // Verify Member tab data
  default_tab_list,
  // Verify Edit tab data
  edit_tab_list,
}