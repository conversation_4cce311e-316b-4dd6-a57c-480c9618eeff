export const ActionConsts = {
	VerifyMember: {
		ResetReducer: 'VerifyMember_ResetReducer',
		SetReducer: 'VerifyMember_SetReducer',
	},
};


 export const combineRemarks = (array, status, temp_id, isFromException = false) => {
  // Filter array by status and temp_id

  const data = isFromException ? array.map(item => item.member_details) : array
  const filteredArray = data.filter(obj =>
	obj.validation_status.status.toUpperCase() === status.toUpperCase() &&
	obj.temp_id === temp_id
  );

  // Use reduce to group remarks
  const grouped = filteredArray.reduce((acc, obj) => {
	if (!acc[obj.temp_id]) {
	  acc[obj.temp_id] = {
		id: obj.temp_id,
		system_remarks: [],
		user_remarks: []
	  };
	}

	// Add system_remarks
	if (obj.validation_status?.message || obj.validation_status?.previous_message) {
	  const systemRemarks = obj.validation_status.message !== ''
		? [obj.validation_status.message.trim()]
		: obj.validation_status.previous_message
			?.split(',')
			.map(remark => remark.trim())
			.filter(Boolean) || [];
	  acc[obj.temp_id].system_remarks.push(...systemRemarks);
	}

	// Add user_remarks
	if (obj.validation_status.user_remarks) {
	  const userRemarks = obj.validation_status.user_remarks
		.split(',')
		.map(remark => remark.trim())
		.filter(Boolean);
	  acc[obj.temp_id].user_remarks.push(...userRemarks);
	}

	return acc;
  }, {});

  const result = grouped[temp_id];

  return result ? {
	temp_id: result.id,
	system_remarks: result.system_remarks.join(', '),
	user_remarks: result.user_remarks.join(', ')
  } : {
	temp_id,
	system_remarks: '',
	user_remarks: ''
  };
}