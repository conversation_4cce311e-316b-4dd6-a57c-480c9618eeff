import * as React from 'react';
import {
  Grid,
  Typography,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@material-ui/core/';
import { Link } from 'react-router-dom';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';

const hmoitemStyles = makeStyles({
  hmoname: {
    fontSize: '11px',
    fontWeight: 200,
    color: 'rgba(39, 46, 76, 0.5)',
    textTransform: 'uppercase',
    paddingRight: 16,
  },
  hmovalue: {
    fontSize: '0.75rem',
    fontWeight: 600,
    color: 'rgb(39, 46, 76)',
  },
  hmovaluelink: {
    fontSize: '0.75rem',
    fontWeight: 600,
    color: 'rgb(39, 46, 76)',
    textDecoration: 'underline',
    lineHeight: 1.25,
  },
});

interface HMOItemProps {
  name?: string;
  value?: string;
  link?: string;
}

const HMOItem: React.FC<HMOItemProps> = (props: HMOItemProps): JSX.Element => {
  const classes = hmoitemStyles(props);

  return (
    <Grid
      container
      alignItems="center"
      style={{ paddingTop: 4, paddingBottom: 4 }}
    >
      <Grid item xs={5}>
        <Typography className={classes.hmoname}>{props.name}</Typography>
      </Grid>
      <Grid item xs={7}>
        {props.link ? (
          <Link to={props.link} className={classes.hmovaluelink}>
            {props.value}
          </Link>
        ) : (
          <Typography className={classes.hmovalue}>{props.value}</Typography>
        )}
      </Grid>
    </Grid>
  );
};





const hmoinfoStyles = makeStyles(theme => ({
  subtitle: {
    fontSize: '1.125rem',
    fontWeight: 600,
  },
  hmocheckboxlabel: {
    fontSize: '0.75rem',
    color: 'rgb(39, 46, 76)',
  },
  hmographsubtitle: {
    fontSize: '0.75rem',
    textTransform: 'uppercase',
    color: 'rgb(39, 46, 76, 0.6)',
    marginBottom: 16,
  },
  hmodependentsbox: {
    height: 175,
    borderRadius: 0,
    overflow: 'auto',
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
  },
  hmoremarksbox: {
    height: 175,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    border: '1px solid #272E4C4D',
  },
  hmodependentsbox_link: {
    color: 'rgb(39, 46, 76)',
  },
  change_status_link: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
    textDecoration: 'underline',
  },
  icons: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
  },
  popper: {
    top: '4px',
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '0 1em 1em 1em',
        borderColor: `transparent transparent ${theme.palette.background.paper} transparent`,
      },
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '1em 1em 0 1em',
        borderColor: `${theme.palette.background.paper} transparent transparent transparent`,
      },
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 1em 1em 0',
        borderColor: `transparent ${theme.palette.background.paper} transparent transparent`,
      },
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 0 1em 1em',
        borderColor: `transparent transparent transparent ${theme.palette.background.paper}`,
      },
    },
  },
  arrow: {
    position: 'absolute',
    fontSize: 7,
    width: '3em',
    height: '3em',
    '&::before': {
      content: '""',
      margin: 'auto',
      display: 'block',
      width: 0,
      height: 0,
      borderStyle: 'solid',
    },
  },
  paper: {
    padding: '15px',
    width: 110,
  },
  status: {
    padding: '5px',
    cursor: 'pointer',
  },
}));

interface HMOInformationProps {
  memberData?: any;
  clientData?: any;
  memberDependents: any[];
  contractId?: string;
  contractName?: string;
  clientFields?: any[];
  MBL?: string;
	ABL?: string;
  PEC?: string;
  benefit_plan_tree: any;
}

export const HMOInformation: React.FC<HMOInformationProps> = (
  props: HMOInformationProps,
): JSX.Element => {
  const classes = hmoinfoStyles(props);
  const { memberData, memberDependents, clientData , MBL, ABL, PEC} = props;
  const [isComponentDidMount, setComponentDidMount] = React.useState(false);
  const [contractVersion, setContractVersion] = React.useState('');

  React.useEffect(() => {
    if (props.benefit_plan_tree.length > 0 && !isComponentDidMount) {
      props.benefit_plan_tree.filter(benefit => {
        if (benefit.level === 1 && benefit.name.toLowerCase() === 'base mother contract') {
          console.log('TEST', benefit);
          setContractVersion(benefit.custom_metadata.version);
        }
      });
      
      setComponentDidMount(true);
    }
  });
  
  function isValidDate(date: string) {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  }

  let leftsideCF: any[] = [];
  let rightsideCF: any[] = [];
  if(props.clientFields){
    if(props.clientFields.length > 2){
      let halfwayThrough = Math.floor(props.clientFields.length / 2);
      leftsideCF = props.clientFields.slice(0, halfwayThrough).map(item => {
        let value = '';
        if(item.field_name === 'registered_name' || item.field_name === 'name_on_card'){
          value = clientData && clientData[item.field_name] ? clientData[item.field_name] : '';
        }
        else if(item.input_type === 'date'){
          value = memberData && memberData[item.field_name] ? moment(memberData[item.field_name]).format('MMM DD, YYYY') : '';
        }
        else{
          value = memberData && memberData[item.field_name] ? memberData[item.field_name] : '';
        }
        return (
          <HMOItem
            name={item.system_name}
            value={value ? value  : '-'}
          />
        );
      });
      rightsideCF = props.clientFields.slice(halfwayThrough, props.clientFields.length).map(item => {
        let value = '';
        if(item.field_name === 'registered_name' || item.field_name === 'name_on_card'){
          value = clientData && clientData[item.field_name] ? clientData[item.field_name] : '';
        }
        else if(item.input_type === 'date'){
          value = memberData && memberData[item.field_name] ? moment(memberData[item.field_name]).format('MMM DD, YYYY') : '';
        }
        else{
          value = memberData && memberData[item.field_name] ? memberData[item.field_name] : '';
        }
        return (
          <HMOItem
            name={item.system_name}
            value={value ? value  : '-'}
          />
        );
      });
    }
    else if(props.clientFields.length > 0){
      leftsideCF = [...props.clientFields].map(item => {
        let value = '';
        if(item.field_name === 'registered_name' || item.field_name === 'name_on_card'){
          value = clientData && clientData[item.field_name] ? clientData[item.field_name] : '';
        }
        else if(item.input_type === 'date'){
          value = memberData && memberData[item.field_name] ? moment(memberData[item.field_name]).format('MMM DD, YYYY') : '';
        }
        else{
          value = memberData && memberData[item.field_name] ? memberData[item.field_name] : '';
        }
        return (
          <HMOItem
            name={item.system_name}
            value={value ? value  : '-'}
          />
        );
      });
    }
  }
  // console.log('HMOINFO PAGE TEST', memberData);
  return (
    <Grid container>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOItem name="Member Account No." value="-" />
        <HMOItem
          name="Member ID"
          value={memberData && memberData.member_id ? memberData.member_id : '-'}
        />
        <HMOItem
          name="Contract"
          // value={contractName ? contractName : '-'}
          value={`Contract ${contractVersion}`}
          // link={contractId ? '/membership/member-contract/' + contractId : '#'}
          // link={contractId ? `/membership/member-contract/${memberData._id}/${contractId}` : '#'}
        />
        <HMOItem
          name="Effectivity Date"
          value={
            memberData && isValidDate(memberData.effectivity_date)
              ? moment(memberData.effectivity_date).format('MMMM D, YYYY')
              : '-'
          }
        />
        <HMOItem name="Contract Renewal Date" value="-" />
        <HMOItem
          name="Contract Expiration Date"
          value={
            memberData && isValidDate(memberData.termination_date)
              ? moment(memberData.termination_date).format('MMMM D, YYYY')
              : '-'
          }
        />
        <HMOItem
          name="Date Printed"
          value={
            memberData && isValidDate(memberData.date_printed)
              ? moment(memberData.date_printed).format('MMM DD, YYYY')
              : '-'
          }
        />
        <HMOItem
          name="VIP Member"
          value={memberData ? (memberData.is_vip ? 'Yes' : 'No') : '-'}
        />
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOItem
          name="Type"
          value={memberData && memberData.member_type ? memberData.member_type : '-'}
        />
        <HMOItem
          name="Plan Type"
          value={memberData && memberData.plan_type ? memberData.plan_type : '-'}
        />
        <HMOItem name="MBL" value={MBL ? MBL : '-'} />
        <HMOItem name="ABL" value={ABL ? ABL : '-'} />
        <HMOItem name="PEC" value={PEC ? PEC : '-'} />
        <HMOItem
          name="Philhealth Rider"
          value={memberData ? (memberData.is_philhealth_rider ? 'Yes' : 'No') : '-'}
        />
        <HMOItem name="Batch Process No." value="-" />
        <HMOItem
          name="Member Consent"
          value={memberData ? (memberData.member_consent ? 'Yes' : 'No') : '-'} />
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOItem
          name="Date of Birth"
          value={
            memberData && memberData.date_of_birth !== '' && isValidDate(memberData.date_of_birth)
              ? moment(memberData.date_of_birth).format('MMMM D, YYYY')
              : '-'
          }
        />
        <HMOItem
          name="Age"
          value={
            memberData && memberData.date_of_birth !== '' && isValidDate(memberData.date_of_birth)
              ? '' + moment().diff(memberData.date_of_birth, 'years', false)
              : '-'
          }
        />
        <HMOItem name="Gender" value={memberData ? memberData.gender : '-'} />
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOItem name="Civil Status" value={memberData ? memberData.civil_status : '-'} />
        <HMOItem
          name="Title/Prefix"
          value={memberData && memberData.title ? memberData.title : '-'}
        />
        <HMOItem
          name="Suffix"
          value={memberData && memberData.suffix ? memberData.suffix : '-'}
        />
      </Grid>
      <Grid
        item
        xs={12}
        md={6}
        style={{ paddingTop: 48, paddingBottom: 48, paddingRight: 12 }}
      >
        <Typography className={classes.hmographsubtitle}>
          Enrolled Dependents
        </Typography>
        <Paper className={classes.hmodependentsbox}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Dependent Name</TableCell>
                <TableCell>Relationship</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {memberDependents && memberDependents.length > 0 ? (
                memberDependents.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell component="th" scope="row">
                      <Link to={'#'} className={classes.hmodependentsbox_link}>
                        {row['first_name'] +
                          ' ' +
                          (row['middle_name']
                            ? row['middle_name'].charAt(0) + '. '
                            : '') +
                          row['last_name']}
                      </Link>
                    </TableCell>
                    <TableCell>{row['relationship_to_principal']}</TableCell>
                    <TableCell>{row['member_status']}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    component="th"
                    scope="row"
                    style={{ textAlign: 'center' }}
                    colSpan={3}
                  >
                    No enrolled dependents
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Paper>
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 48, paddingBottom: 48 }}>
        <Typography className={classes.hmographsubtitle}>Remarks</Typography>
        <Box className={classes.hmoremarksbox}></Box>
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        {
          leftsideCF
        }
      </Grid>
      <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
        {
          rightsideCF
        }
      </Grid>
    </Grid>
  );
};

HMOInformation.defaultProps = {
  memberData: {},
  memberDependents: [],
  clientData: {},
  contractId: '',
  contractName: '',
  clientFields: [],
  MBL: '',
	ABL: '',
	PEC: '',
};
