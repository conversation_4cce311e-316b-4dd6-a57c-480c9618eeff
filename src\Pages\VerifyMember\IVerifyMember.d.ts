//#region Global Imports
import { StringNullableChain } from 'lodash';
import { Props } from 'prop-types';
//#endregion Global Imports

declare namespace IVerifyMember {
  export type IProps = IOwnProps & IStateProps & IDispatchProps;
  export interface IOwnProps extends Props<{}> {
    match: any;
    history: any;
    location: any;
  }
  export interface IState {
    check_internet_flag: boolean;
    plan_types?: any[] | null;
    memberData?: any | null;
    memberDataFields?: any[];
    anchorEl?: HTMLAnchorElement | null;
    pmaker_task: string;
    row: number;
    partialRow: number;
    // supportingRow: number;
    validationRow: number;
    breadcrumbs: any[];
    tab: number;
    verifyTabs: any[];
    partialtable: any;
    partial_items: any;
    incompletetable: any;
    conflicttable: any;
    validationtable: any;
    othervalidationtable: any;
    supportingtable: any;
    disapprovetable: any;
    approvedtable: any;
    alltable: any;
    allValidated: any[];
    rowData: any[];
    tableName?: any;
    temp_id?: any;
    prefix?: any;
    isUpload?:boolean;
    data?: any;
    rowDataForEdit: any;
    dataForEdit: any;
    otherTableRow: any;
    tab?:any;
    details?:any;
    remarks?:any;
    defaultTableCopy: any;
    partialModalOpen: boolean;
    matchPrincipalModalOpen: boolean;
    approveModalOpen: boolean;
    memberFilesModalOpen: boolean; //for DisapprovedMemberFilesModal
    disapproveModalOpen: boolean;
    forConfirmationModalOpen: boolean;
    approveDependent: {
      isModalOpen?: boolean;
      possiblePrincipals?: any;
      row?: number;
      action?: 'Approve' | 'Disapprove';
    };
    // approveDependentOpen: boolean;
    generateFlag: boolean;
    member_upload_data: any;
    loading_state: boolean;
    // defaultFilter: any[];
    allFilter: any[];
    approvedFilter: any[];
    disapprovedFilter: any[];
    conflictFilter: any[];
    incompleteFilter: any[];
    partialFilter: any[];
    validationFilter: any[];
    othervalidationFilter: any[];
    supportingFilter: any[];
    partialColumns: any;
    allData: any;
    tmpDataModal: boolean;
    tmpData: AnyObject;
    currentTablePage: number;
    isActionRemarksModalOpen: boolean;
    actionRemarksModalProps: any;
    snackbarNotifProps: any;
    pageSizes: any;
    corporateAccountNo: string;
    memberCardNo: string;
    corporateAccountName: string;
    contractName: string;
    contractId: string;
    personId: string;
    member_id: string;
    clientId: string;
    fullName: string;
    filterTimeout: any;
    forValidationTab: number;
    memberFileUploadDetails: any;
    memberDataEdit:any;
    isEdit: boolean;
    pageType?: any;
    tableName?: any;
    prefix?: any;
    // rowData: any[];
    verifyPageType: string;
    verifyEditAllData: any;
    verifyEditTable: any;
    verifyEditFilters: any;
    verifyEditPageSize: any;
    verifyEditTableCopy: any;
    verifyVoidAllData: any;
    verifyVoidTable: any;
    verifyVoidFilters: any;
    verifyVoidPageSize: any;
    verifyVoidTableCopy: any;
    redirectUrl: any;
    redirectPage: boolean;
    member_edit_data: any;
    isModalOpen: boolean;
    modalTitle: string;
    modalMessage: string;
    isConfirmModalOpen: boolean;
    modalCloseText: string;
    modalConfirmText: string;
    customModalMessage: any;
    next: any;
    redirectFrom: any;
    noCancelButton: boolean;
    ocpPath: string;
    ocpPayload: any;
    verifierData: any;
    actionHolder: any[];
    remarksHolder: any[];
    ticket_id: string;
    isMemberInfoModalOpen: boolean;
    clientData: any;
    infoData: any;
    benefitPlanTree: any[];
    initApprovedMembers: any[];
    isErrorModalOpen: boolean;
    errorTitle: string;
    errorMessage: string;
    isRemarksModal: boolean;
    remarksData: any;
    user_remarks:any;
    dateSorting: boolean;
    selectedPrincipal: any;
    ticket_closed: boolean;
    documentData: any;
    initialNumberValidatedMembers: number;
    verifyEditMembers: any[];
    displayUserRemarks:any[];
    principalList?: any[];
    approvedList?:any[];
  }
  export interface IState2 {
    updata: any;
  }
  export interface IStateProps {
    selected_tab: number;
  }

  export interface IDispatchProps {
    Map(payload: Actions.IMapPayload): Actions.IMapResponse;
  }

  namespace Actions {
    export interface IMapPayload { }
    export interface IMapResponse { }
  }
}
