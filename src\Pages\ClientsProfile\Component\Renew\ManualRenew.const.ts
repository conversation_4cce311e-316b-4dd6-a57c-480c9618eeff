import { map } from 'lodash';

const getIntgFltrColumnExtensions = (columnNames: string[]) => {
  const predicate = () => true;

  return map(columnNames, name => {
    return {
      columnName: name,
      predicate: predicate,
    };
  });
};

const MEMBERS_TO_RENEW_COLUMNS = [
  { name: 'member_id', title: 'Member ID', },
  { name: 'member_name', title: 'Member Name', },
  { name: 'member_type', title: 'Member Type', },
  { name: 'formatted_member_status', title: 'Status', },
]

const MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMNS = [
  { name: 'member_id', title: 'Member ID', },
  { name: 'member_name', title: 'Member Name', },
  { name: 'member_type', title: 'Member Type', },
  { name: 'formatted_member_status', title: 'Status', },
]

const MEMBERS_TO_RENEW_COLUMN_EXTENSIONS = [
  { columnName: 'member_id', wordWrapEnabled: true, width: 200 },
  { columnName: 'member_name', wordWrapEnabled: true, width: 200 },
  { columnName: 'member_type', wordWrapEnabled: true },
  { columnName: 'formatted_member_status', wordWrapEnabled: true },
];


const MEMBERS_TO_RENEW_FILTER_EXTENSIONS = [
  { columnName: 'member_id', filteringEnabled: true },
  { columnName: 'member_name', filteringEnabled: true },
  { columnName: 'member_type', filteringEnabled: true },
  { columnName: 'formatted_member_status', filteringEnabled: true },
];

const MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMN_EXTENSIONS = [
  { columnName: 'member_id', wordWrapEnabled: true, width: 200 },
  { columnName: 'member_name', wordWrapEnabled: true, width: 200 },
  { columnName: 'member_type', wordWrapEnabled: true },
  { columnName: 'formatted_member_status', wordWrapEnabled: true },
];


const MEMBERS_TO_EXCLUDE_FROM_RENEWAL_FILTER_EXTENSIONS = [
  { columnName: 'member_id', filteringEnabled: true },
  { columnName: 'member_name', filteringEnabled: true },
  { columnName: 'member_type', filteringEnabled: true },
  { columnName: 'formatted_member_status', filteringEnabled: true },
];

const MEMBERS_TO_RENEW_INTEGRATED_FILTERS = getIntgFltrColumnExtensions([
  'member_id',
  'member_name',
  'member_type',
  'formatted_member_status',
])

const MEMBERS_TO_EXCLUDE_INTEGRATED_FILTERS = getIntgFltrColumnExtensions([
  'member_id',
  'member_name',
  'member_type',
  'formatted_member_status',
])

const pageSizes = [10, 15, 25, 50];


const EXCLUDE_STATIC_DATA = [
  { member_name: 'Alpha', new_member_id: '0001', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Bravo', new_member_id: '0002', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Charlie', new_member_id: '0003', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Delta', new_member_id: '0004', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Echo', new_member_id: '0005', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Foxtrot', new_member_id: '0006', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Golf', new_member_id: '0007', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Hotel', new_member_id: '0008', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'India', new_member_id: '0009', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Juliet', new_member_id: '0010', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Kilo', new_member_id: '0011', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Lima', new_member_id: '0013', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Momo', new_member_id: '0014', member_type: 'Principal', member_status_before_expiration: 'Active' },
  { member_name: 'Nancy', new_member_id: '0015', member_type: 'Principal', member_status_before_expiration: 'Active' },
]

export {
  pageSizes,
  MEMBERS_TO_RENEW_COLUMNS,
  MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMNS,
  MEMBERS_TO_RENEW_COLUMN_EXTENSIONS,
  MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMN_EXTENSIONS,
  MEMBERS_TO_RENEW_FILTER_EXTENSIONS,
  MEMBERS_TO_EXCLUDE_FROM_RENEWAL_FILTER_EXTENSIONS,
  MEMBERS_TO_RENEW_INTEGRATED_FILTERS,
  MEMBERS_TO_EXCLUDE_INTEGRATED_FILTERS,
  EXCLUDE_STATIC_DATA,
}