import React, { useState, useEffect } from 'react';
import {
  Grid,
  Typography,
  Box,
  Link,
  Select,
  MenuItem,
} from '@material-ui/core/';
/*
import { 
    withStyles,
} from '@material-ui/core/styles';
*/
import clsx from 'clsx';
import './style.scss';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { faEdit } from '@fortawesome/free-solid-svg-icons'; //faEye
import moment from 'moment';
import { LoadingIndicator } from './LoadingIndicator';
import { GridHorizontalDivider } from './GridHorizontalDivider';
import { TableComponent } from 'Components/UI/TableComponent';
import { ClientHMOInformationTable } from 'Components/UI/TableComponent/ClientHMOInformationTable';
import { HMOTextItem } from './HMOTextItem';
import { Components } from '@hims/core';
// import { SuspensionHistoryModal } from '../../Components/SuspensionHistoryModal';
// import { clientInformationStyles } from '../../ClientProfilePage';
import { KeyboardDatePicker } from '@material-ui/pickers';
// import { Link as RouterLink } from 'react-router-dom';
import { makeStyles } from '@material-ui/core/styles';

import { API } from '../../API';


console.log(React)

const columns: any = [
  {
    name: 'plan_type_name',
    title: 'Plan Type',
  },
  {
    name: 'benefit_count',
    title: 'Benefits',
  },
  {
    name: 'principal_count',
    title: 'Principals',
  },
  {
    name: 'dependent_count',
    title: 'Dependents',
  },
  {
    name: 'date_created',
    title: 'Date Created',
  },
  {
    name: 'date_updated',
    title: 'Date Modified',
  },
  {
    name: 'version',
    title: 'Contract Version',
  },
  {
    name: 'link',
    title: ' ',
  },
];

const historyColumns: any = [
  {
    name: 'contract_name',
    title: 'Contract Name',
  },
  {
    name: 'version',
    title: 'Version',
  },
  {
    name: 'date_updated',
    title: 'Date Updated',
  },
  {
    name: 'effectivity_date',
    title: 'Effectivity Date',
  },
  {
    name: 'date_created',
    title: 'Date Created',
  },
  {
    name: 'uploaded_by',
    title: 'Uploaded By',
  },
  {
    name: 'approved_by',
    title: 'Approved By',
  },
  {
    name: 'action',
    title: 'Action',
  },
  {
    name: 'changes',
    title: 'Changes',
  },
];

const formattedColumns = {
  date: ['date_created', 'date_updated'],
  // view_action: ['link']
  viewlink: ['link'],
};

const columnExtensions = [
  { columnName: 'plan_type', wordWrapEnabled: true },
  { columnName: 'plan_type_name', wordWrapEnabled: true },
  { columnName: 'date_created', wordWrapEnabled: true },
  { columnName: 'date_updated', wordWrapEnabled: true },
  { columnName: 'link', width: 80, wordWrapEnabled: true },
];


interface HMOInformationProps {
  clientData?: any;
  clientId: string;
  contractId?: string;
  planTypes: any[];
  setEditInfo?: (e) => void;
  setClientInfo?: (e) => void;
  setSideNav?: (e) => void;
  setProposalInfo?: (e) => void;
  viewOnly: boolean;
  proposalInfo?: any;
  abl?: any;
  MCTNode?: any;
  classes?: any;
  client_all_info?: any;
  contractType?: any;
  data?: any;
  planTypesNew?:any;
}
export const clientInformationStyles = makeStyles({
    gridItem: {
    },
    editContainer: {
      paddingRight: '20px',
    },
    gridContainer: {
      paddingTop: '30px',
      paddingBottom: '30px',
      borderBottom: '1px solid #ccc',
    },
    UploadFileContainer: {
      position: 'fixed',
      right: 0,
      bottom: 0,
      zIndex: 200,
      backgroundColor: '#f5f7fb',
      padding: '30px',
      borderRadius: '10px',
      boxShadow:
        '0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)',
    },
    UploadFileLink: {
      padding: '15px',
      textAlign: 'center',
    },
    dropzone: {
      width: '460px',
      height: '70px',
      marginTop: '10px',
      marginBottom: '20px',
      border: '2px dashed rgba(30, 32, 113, 0.3)',
      borderRadius: '5px',
      backgroundColor: '#e6e8ee',
      color: '#7b8094',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
    },
    ProposalRadio: {
      paddingLeft: '40px',
    },
    containerColor: {
      backgroundColor: '#E5E5E5',
    },
    linkStyleNotSelected: {
      fontSize: 14,
      fontWeight: 700,
      color: 'rgba(58, 183, 125, 0.3)',
      cursor: 'pointer',
    },
    linkStyleSelected: {
      fontSize: 14,
      fontWeight: 700,
      color: 'rgba(58, 183, 125, 1)',
      cursor: 'pointer',
    },
    linkStyleDisabled: {
      fontSize: 14,
      fontWeight: 700,
      color: '#9EA3AC',
      cursor: 'pointer',
    },
    documentTopNavNotSelected: {
      color: 'rgba(39, 46, 76, 0.5)',
      fontSize: '0.875rem',
      fontWeight: 400,
      cursor: 'pointer',
      paddingRight: '20px',
    },
    documentTopNavSelected: {
      textDecoration: 'underline',
      color: '#3AB77D',
      fontSize: '0.875rem',
      fontWeight: 600,
      cursor: 'pointer',
      paddingRight: '20px',
    },
    HistoryTopNavNotSelected: {
      color: 'rgba(39, 46, 76, 0.5)',
      paddingLeft: 24,
      paddingRight: 24,
      fontSize: '0.875rem',
      fontWeight: 400,
      cursor: 'pointer',
      marginRight: '20px',
    },
    HistoryTopNavSelected: {
      textDecoration: 'underline',
      color: '#3AB77D',
      paddingLeft: 24,
      paddingRight: 24,
      fontSize: '0.875rem',
      fontWeight: 600,
      cursor: 'pointer',
      marginRight: '20px',
    },
    searchField: {
      backgroundColor: 'white',
    },
    fieldContainer: {
      padding: '5px',
    },
    fieldLabel: {
      color: 'rgba(39, 46, 76, 0.6)',
      fontSize: '12px',
      marginBottom: '5px',
    },
    fieldStyle: {},
    searchInputAdornment: {
      color: '#00000061',
      marginRight: '8px',
    },
    generateReport: {
      paddingRight: '15px',
      marginTop: 15,
      textAlign: 'center',
    },
    downloadIcon: {
      color: 'rgba(58, 183, 125, 1)',
      fontSize: 14,
      marginRight: '6px',
    },
    downloadDisabled: {
      color: '#9EA3AC',
      fontSize: 14,
      marginRight: '6px',
    },
    contactheader: {
      fontSize: 14,
    },
    chipdes: {
      marginTop: 10,
      marginRight: '6px',
    },
    inputLabel: {
      color: '#272e4c',
    },
    outlinedInput: {
      marginTop: '10px',
      marginBottom: '20px',
      width: '100%',
      textOverflow: 'scroll',
    },
    keyboardDatePicker: {
      marginTop: '10px',
      marginBottom: '20px',
      width: '212px',
    },
    radio: {
      '&.Mui-checked': {
        color: '#1e2071',
      },
    },
    typooverflow: {
      overflow: 'scroll',
      maxWidth: '80%',
    },
    AddPropHeader: {
      fontSize: 14,
    },
    AddPropInst: {
      fontSize: 12,
    },
    CheckDesc: {
      paddingTop: '15px',
    },
    outlineinputadornment: {
      maxWidth: '100%',
    },
    editinputadornment: {
      backgroundColor: '#757575',
      borderRadius: '14px',
      padding: '5px',
      fontSize: 12,
      marginRight: '5px',
    },
    contactHeader: {
      fontWeight: 'bold',
    },
    uploadFile: {
      border: '1.5px dashed',
      color: '#7b8099',
      backgroundColor: '#E6E8EE',
      borderRadius: 5,
      width: 290,
      fontSize: 10,
    },
    button: {
      color: 'rgb(58, 183, 125)',
    },
    uploadParagraph: {
      textAlign: 'center',
      marginLeft: '25px',
      marginTop: '-20px',
    },
  });
const ownershipList = [
  {
    name: 'Prestige',
    value: 'Prestige',
  },
  {
    name: 'Jumbo',
    value: 'Jumbo',
  },
  {
    name: 'Large',
    value: 'Large',
  },
  {
    name: 'SME',
    value: 'SME',
  },
];

export const HMOInformation: React.FC<HMOInformationProps> = (
  props: HMOInformationProps,
): JSX.Element => {
  const { clientData,  clientId, client_all_info, contractId, contractType, planTypesNew} = props;
  // console.log('clientData', clientData);
  // console.log('props', props)
  console.log('client_all_info', client_all_info)
  console.log('planTypesNew', planTypesNew)
  const [loading, setLoading] = useState(false);
  const [view, setView] = useState(false);
  const [cName, setCName] = useState('');
  const [pdfViewerOpen, setPDFViewerOpen] = useState(false);
  const [pdfURL, setPDFUrl] = useState('');
  // const [contractStartDisplay, setContractStartDisplay] = useState('');
  // const [contractEndDisplay, setContractEndDisplay] = useState('');
  const [contractDetails, setContractDetails] = useState(['','','']);
  const classes = clientInformationStyles();
  const isValidDate = (date: string) => {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  };
console.log(contractId)
console.log(loading)
console.log(setView)
console.log(view)
console.log(cName)
console.log(setCName)
console.log(contractDetails)
console.log(setContractDetails)
console.log(classes)
console.log(ownershipList)
console.log(setContractDetails)
console.log(pdfViewerOpen)
console.log(pdfURL)
console.log(Link)
  useEffect(() => {
    getClientContracts(clientId)
},[clientData])

  let planTypes: any[] = [];

  props.planTypes.map(item => {
    console.log('planTypes 222', props.planTypes);
    item['date_created'] = moment(item['date_created']).format('MMMM DD, YYYY');
    item['date_updated'] = moment(item['date_updated']).format('MMMM DD, YYYY');

    planTypes.push(item);
    
  });

  useEffect(() => {
    console.log('call viewPlantype func',)
   
  })


  const displayPDF = blob => {
    //console.log('blob',blob)
    const url = URL.createObjectURL(blob);
    console.log('URL', url)
    //console.log('url',url)
    setPDFUrl(url);
    setPDFViewerOpen(true);
    setLoading(false);
  };
  // const handleViewContract = () => {
  //   setLoading(true);
  //   // setView(true);
  // };

  // console.log(handleViewContract)
  console.log(displayPDF)
  //@ts-ignore
  let broker_company_name = '-';
  let sales_channel = '-';
  if (props.clientData.sales_channel && props.clientData.sales_channel.type) {
    if (
      props.clientData.sales_channel.type == 'agent' &&
      props.clientData.sales_channel.agent_name
    ) {
      sales_channel = 'Agent';
    } else if (props.clientData.sales_channel.type == 'broker') {
      sales_channel = 'Broker';
      if (props.clientData.sales_channel.company_name) {
        broker_company_name = props.clientData.sales_channel.company_name;
      }
    } else if (
      props.clientData.sales_channel.type == 'bdo' &&
      props.clientData.sales_channel.bdo_name
    ) {
      sales_channel = 'BDO';
    }
  }


  if(props.clientData && props.clientData['benefit_plan_tree']) {
    let bptData: any = clientData['benefit_plan_tree'];

    let mctNode: any = null;
    let planTypeNode: any = null;
    console.log('Get MCTNODE before condition')
    if (bptData.length > 0) {
      console.log('inside bptData length', bptData.length)
      // mctNode = _.find(
      //     bptData,
      //     item => item.tree_id === 'MCT',
      // );
      console.log('mctNode val', mctNode)
      console.log('planTypeNode val', planTypeNode)
    }
  }
  let suspension_date = 'N/A';
  if (
    props.MCTNode &&
    props.MCTNode.suspension_schedule &&
    props.MCTNode.suspension_schedule.implementation_date &&
    isValidDate(props.MCTNode.suspension_schedule.implementation_date)
  ) 
  {
    console.log('MCTNODE', props.MCTNode)
    console.log('MCTNODE', suspension_date)
    if (props.clientData.status && props.clientData.status === 'Suspended') {
      suspension_date = moment(
        props.MCTNode.suspension_schedule.implementation_date,
      )
        .subtract(8, 'hours')
        .format('MMM D, YYYY');
    } else if (
      props.MCTNode.lift_suspension_schedule &&
      props.MCTNode.suspension_schedule.implementation_date
    ) {
      if (
        new Date(props.MCTNode.suspension_schedule.implementation_date) <
        new Date(props.MCTNode.lift_suspension_schedule.implementation_date)
      ) {
        if (
          new Date(props.MCTNode.lift_suspension_schedule.implementation_date) >
          new Date()
        ) {
          suspension_date = moment(
            props.MCTNode.suspension_schedule.implementation_date,
          )
            .subtract(8, 'hours')
            .format('MMM D, YYYY');
        }
      }
    } else {
      suspension_date = moment(
        props.MCTNode.suspension_schedule.implementation_date,
      )
        .subtract(8, 'hours')
        .format('MMM D, YYYY');
    }
  }


  const handleSetProposalInfoData = (name: string, value: any) => {
    let data = { ...props.proposalInfo, [name]: value };

    if (props.setProposalInfo) {
      props.setProposalInfo(data);
    }
  };

  const handleSetClientInfoData = (name: string, parentName?: string) => (
    event: any,
  ) => {
    let value = event.target.value;
    let data = {};
    if (parentName) {
      data = {
        ...clientData,
        [parentName]: {
          ...clientData[parentName],
          [name]: value,
        },
      };
    } else {
      data = { ...clientData, [name]: value };
    }

    if (props.setClientInfo) {
      props.setClientInfo(data);
    }
  };

  console.log(handleSetClientInfoData)
  console.log(handleSetProposalInfoData)


 const getClientContracts = (clientId: string) => {
    // const contractId = props.contract_id;
    let planTypeTable 
    // let contractStart:any = props.clientData && props.clientData.coverage_start_date !== undefined ? moment(clientData.coverage_start_date).format('DD/MM/YYYY') : ''
    // let contractEnd:any = props.clientData && props.clientData.coverage_end_date !== undefined ? moment(clientData.coverage_end_date).format('DD/MM/YYYY') : ''
    // setContractStartDisplay(contractStart)
    // setContractEndDisplay(contractEnd)
    if(props.client_all_info && props.client_all_info.length > 0) {
      planTypeTable = props.client_all_info
    }
    console.log('planTypeTable11', planTypeTable)

   
    API.getClientContracts(clientId).then(response2 => {

    })
 
}

  return (
    <div className={clsx('ClientHMOInformation')}>
      {loading ? <LoadingIndicator /> : null}
      <Grid container>
        <GridHorizontalDivider height="40px" />
      </Grid>
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            marginBottom: 24,
            borderBottomWidth: '1px',
            borderBottomColor: 'rgba(39, 46, 76, 0.3)',
            borderBottomStyle: 'solid',
          }}
        >
          <Grid container>
            <Grid item xs={12} md={3}>
              <HMOTextItem
                name="Client ID"
                value={
                  props.clientData && props.clientData.client_id
                    ? props.clientData.client_id
                    : '-'
                }
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <HMOTextItem
                name="Corporate Account No."
                value={
                  props.clientData && props.clientData.corporate_account_no
                    ? props.clientData.corporate_account_no
                    : '-'
                }
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Grid
                container
                alignItems="flex-start"
                style={{ paddingTop: 8, paddingBottom: 8, paddingRight: 16 }}
              >
                <Grid item xs={12}>
                  <Typography
                    style={{
                      fontSize: '0.75rem',
                      color: 'rgba(39, 46, 76, 0.6)',
                      textTransform: 'uppercase',
                      paddingBottom: 4,
                    }}
                  >
                    Contract
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Box display="flex" flexDirection="row" alignItems="center">
                    <Typography
                      style={{
                        fontSize: '0.875rem',
                        color: 'rgba(39, 46, 76, 1)',
                        fontWeight: 600,
                        marginRight: '15px',
                      }}
                    >
                      {/* {props.clientData && props.clientData.latest_version ?  `${props.clientData.client_id} ${props.clientData.latest_version}` : '-'} */}
                      {/* ${contractStartDisplay} - ${contractEndDisplay} */}
                      {props.clientData && props.clientData.contract_id ?  `${props.clientData.contract_id}` : '-'}
                    </Typography>

                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      <Grid
         item
         xs={12}
         style={{
           marginBottom: 24,
           borderBottomWidth: '1px',
           borderBottomColor: 'rgba(39, 46, 76, 0.3)',
           borderBottomStyle: 'solid',
         }}
       >
        <Grid container>
          {/* Registered Company Name */}
          <Grid item xs={12} md={3}>
              <HMOTextItem
                name="Registered Company Name"
                value={props.clientData && props.clientData.registered_name ?  
                  props.clientData && props.clientData.registered_name : '-'
                }
              />
           </Grid>
           <Grid item xs={12} md={3}>
            <HMOTextItem
                  name="Trade/Brand Name"
                  value={ props.clientData && props.clientData.registered_name ?  
                    props.clientData && props.clientData.registered_name : '-'
                  }
                />
           </Grid>
           <Grid item xs={12} md={4}>  
            <HMOTextItem
                name="Name Displayed In Card"
                value={props.clientData && props.clientData.registered_name ?  
                  props.clientData && props.clientData.registered_name : '-'
                }
              />
            </Grid>
        </Grid>
      </Grid>
        <Grid
          item
          xs={12}
          style={{
            marginBottom: 24,
            borderBottomWidth: '1px',
            borderBottomColor: 'rgba(39, 46, 76, 0.3)',
            borderBottomStyle: 'solid',
          }}
        >
          <Grid container>
           
{/* Effectivity Date */}
            <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name="Effectivity Date"
                  value={props.clientData && props.clientData.coverage_start_date ?
                    moment(props.clientData['coverage_start_date']).format('MMM DD, YYYY') : '-'
                  }
                />
              
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                    Effectivity Date
                  </Typography>
                </div>
              )}
            </Grid>
{/* Last Date of Coverage */}
            <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name=" Last Date of Coverage"
                  value={props.clientData && props.clientData.coverage_end_date ?
                    moment.utc(props.clientData['coverage_end_date']).format('MMM DD, YYYY') : '-'
                  }
                />
              
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                    Last Date of Coverage
                  </Typography>
                </div>
              )}
            </Grid>

{/* Expiry Date  */}
          <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name="Expiry Date"
                  value={
                    // props.proposalInfo.coverage_end_date &&
                    // isValidDate(props.proposalInfo.coverage_end_date)
                    //   ? //? moment(props.proposalInfo.coverage_end_date).format('MMM D, YYYY')
                    //     moment(props.proposalInfo.coverage_end_date)
                    //       .subtract(1, 'days')
                    //       .format('MMM D, YYYY')
                    //   : '-'
                    //.subtract(1, 'days')
                     props.clientData && props.clientData.coverage_end_date ?
                     moment.utc(props.clientData['coverage_end_date']).add(1, 'days').format('MMM DD, YYYY') : '-'
                  }
                />
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                    Expiry Date
                  </Typography>
                  <KeyboardDatePicker
                    id="effectivityDate"
                    variant="dialog"
                    format="MM/DD/YYYY"
                    inputVariant="outlined"
                    style={{ width: '100%' }}
                    value={
                      props.proposalInfo.coverage_end_date
                        ? moment(props.proposalInfo.coverage_end_date)
                            .subtract(1, 'days')
                            .format('MMM D, YYYY') //.subtract(8, 'hours').add(1, 'days')
                        : // props.proposalInfo.coverage_end_date
                          null
                    }
                    onKeyPress={e => e.preventDefault()}
                    onChange={date => {
                      console.log(date);
                      if (date !== null) {
                        const val = date.toDate().toISOString();
                        handleSetProposalInfoData('coverage_end_date', val);
                      }
                    }}
                  />
                </div>
              )}
            </Grid>

{/* Renewal Dates */}
            <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name="Renewal Dates"
                  value= "N/A"
                  // value={props.clientData && props.clientData.coverage_end_date ?  
                  //   moment(props.clientData['coverage_end_date']).format('MMM D, YYYY') : '-'
                  // }
                />
              
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                  Renewal Dates
                  </Typography>
                </div>
              )}
            </Grid>
{/* Termination Date */}
          <Grid item xs={12} md={3}>
                <HMOTextItem
                  name="Termination Date"
                  value={(() => {
                    if (
                      props.MCTNode &&
                      props.MCTNode?.termination_schedule &&
                      props.MCTNode?.termination_schedule?.implementation_date &&
                      isValidDate(props.MCTNode?.termination_schedule?.implementation_date)
                    ) {
                      return moment(props.MCTNode?.termination_schedule?.implementation_date)
                        .subtract(8, 'hours')
                        .format('MMM D, YYYY');
                    }
                    
                    return 'N/A';
                  })()}
                />

            </Grid>
{/* Suspension Date */}
          <Grid item xs={12} md={3}>
                <HMOTextItem
                  name="Suspension Date"
                  value={
                    props.clientData && props.clientData.suspension_date ?  
                    props.clientData.suspension_date : 'N/A'
                  }
                />
            </Grid>
{/* Authorized Signatory  */}
          </Grid>
        </Grid>
        <Grid
          item
          xs={12}
          style={{
            marginBottom: 24,
            borderBottomWidth: '1px',
            borderBottomColor: 'rgba(39, 46, 76, 0.3)',
            borderBottomStyle: 'solid',
          }}
        >
          <Grid container>
{/* //Authorized Signatory             */}
            <Grid item xs={12} md={3}>
              <HMOTextItem
                name="Authorized Signatory"
                value={
                  props.clientData && props.clientData.authorized_signatory ?  
                  props.clientData && props.clientData.authorized_signatory : '-'
                }
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <HMOTextItem name="Contact Person" 
              value={
                props.clientData && clientData.main_office_address.contact_persons[0]['name'] ?  
                props.clientData && clientData.main_office_address.contact_persons[0]['name'] : '-'
                } />
            </Grid>
            <Grid item xs={12} md={2}>
              <HMOTextItem name="Position" 
                value={
                  props.clientData && clientData.main_office_address.contact_persons[0]['designation'] ?  
                  props.clientData && clientData.main_office_address.contact_persons[0]['designation'] : '-'
                } />
            </Grid>
            <Grid item xs={12} md={2}>
              <HMOTextItem name="Contact No." value={
                props.clientData && clientData.main_office_address.contact_persons[0]['contact_no'] ?  
                props.clientData && clientData.main_office_address.contact_persons[0]['contact_no'] : '-'
                } />
            </Grid>
            <Grid item xs={12} md={2}>
              <HMOTextItem name="Contact Email"value={
                props.clientData && clientData.main_office_address.contact_persons[0]['email_address'] ?  
                props.clientData && clientData.main_office_address.contact_persons[0]['email_address'] : '-'
                } />
            </Grid>

          </Grid>
        </Grid>
        <Grid
          item
          xs={12}
          style={{
            marginBottom: 24,
            borderBottomWidth: '1px',
            borderBottomColor: 'rgba(39, 46, 76, 0.3)',
            borderBottomStyle: 'solid',
          }}
        >
          <Grid container>
            <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name="Previous HMO"
                  value={
                    props.clientData && props.clientData.prev_hmo_provider ?  
                    props.clientData && props.clientData.prev_hmo_provider : '-'
                    }
                />
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                    Previous HMO
                  </Typography>
                  <Select
                    style={{ width: '100%' }}
                    value={
                      props.clientData && props.clientData.prev_hmo_provider ?  
                      props.clientData && props.clientData.prev_hmo_provider : '-'
                      }
                    variant="outlined"
                    onChange={handleSetClientInfoData('prev_hmo_provider')}
                  >
                      return (
                           <MenuItem 
                              value={
                                props.clientData && props.clientData.prev_hmo_provider ?  
                                props.clientData && props.clientData.prev_hmo_provider : '-'
                                }>
                                ITEM NAME
                        </MenuItem>
                      );
                  </Select>
                </div>
              )}
            </Grid>
            {sales_channel === 'Broker' ? (
              <Grid item xs={12} md={3}>
                <HMOTextItem
                  name="Broker Company"
                  value={
                    props.clientData && props.clientData.latest_version ?  
                    props.clientData && props.clientData.latest_version : '-'
                    }
                />
              </Grid>
            ) : null}
            <Grid item xs={12} md={3}>
              <HMOTextItem name="Sales Channel" value={sales_channel} />
            </Grid>
            <Grid item xs={12} md={sales_channel === 'Broker' ? 3 : 6}>
              <HMOTextItem
                name="Account Officer"
                value={
                  props.clientData && props.clientData.account_officer ?  
                  props.clientData && props.clientData.account_officer : '-'
                  }
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <HMOTextItem
                name="Contract Type"
                value={
                  props.clientData && contractType ? contractType : '-'
                  }
              />
            </Grid>
            <Grid item xs={12} md={3}>
              {props.viewOnly ? (
                <HMOTextItem
                  name="Client Segment"
                  value={
                    props.clientData && props.clientData.client_segment ?  
                    props.clientData && props.clientData.client_segment : '-'
                    }
                />
              ) : (
                <div className={classes.fieldContainer}>
                  <Typography className={classes.fieldLabel}>
                    Client Segment
                  </Typography>
                  <Select
                    style={{ width: '100%' }}
                    value={
                      props.clientData && props.clientData.latest_version ?  
                      props.clientData && props.clientData.latest_version : '-'
                      }
                    variant="outlined"
                    onChange={handleSetClientInfoData('client_segment')}
                  >
                    {ownershipList.map(item => {
                      return (
                        <MenuItem value={item.value}>{item.name}</MenuItem>
                      );
                    })}
                  </Select>
                </div>
              )}
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} style={{ marginBottom: 24 }}>
          <ClientHMOInformationTable
            id={'clienthmo-benefit-table'}
            message={'No items'}
            onClickRow={() => {}}
            disableSearch={true}
            disableSelect={true}
            disablePaging={true}
            columnExtensions={columnExtensions}
            formattedColumns={formattedColumns}
            rows={planTypes}
            columns={columns}
          />
        </Grid>

        {/* History table */}
        <Grid item xs={12} style={{ marginBottom: 24 }}>
          <Typography className={classes.fieldLabel}>
            CONTRACT EDIT HISTORY
          </Typography>
          <TableComponent
            id={'clienthmo-benefit-table'}
            message={'No items'}
            onClickRow={() => {}}
            disableSearch={true}
            disableSelect={true}
            disablePaging={true}
            rows={[]}
            columns={historyColumns}
          />
        </Grid>
      </Grid>
      <Components.UI.RendererButton
        clientID={clientId}
        clientName={clientData.registered_name}
        fileType={'pdf'}
        download={view}
        setDownload={setView}
        hideButton
        label="Download Proposal"
        renderType={'renderContract'}
        objId={contractId}
        viewOnly={displayPDF}
        apiUrl={
          localStorage.getItem('XDEV_CLIENT_URL')
            ? localStorage.getItem('XDEV_CLIENT_URL')
            : ''
        }
        onError={() => {
          console.log('error');
        }}
        onStartDownload={() => {
          //setDownloadScreen(true);
          console.log('done');
        }}
      />

       {/* <RenderPdfModal
        id="render_pdf_modal"
        isModalOpen={pdfViewerOpen}
        pdfUrl={pdfURL}
        onClose={() => {
          setPDFViewerOpen(false);
        }}
      /> */}

      {/* <SuspensionHistoryModal
        isModalOpen={isSuspensionHistoryOpen}
        onClose={setIsSuspensionHistoryOpen}
        MCTNode={props.MCTNode}
        suspend_date={suspension_date}
      />  */}
    </div>
  );
};