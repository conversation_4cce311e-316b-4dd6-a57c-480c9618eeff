import {  Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid, LinearProgress } from '@material-ui/core'
import { createStyles, Theme, makeStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import { TableComponent } from 'Components/UI/TableComponent';
import { useRenewalContext } from '../Context/RenewalContext';



const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      flexWrap: 'wrap',
    },
    dialog: {
      align: 'center',
      justify: 'center',
      width: '100%',
      fontFamily: 'usual',
    },
    dialogTitle: {
      marginTop: theme.spacing(2),
      marginBottom: theme.spacing(2),
      fontSize: '1rem',
      fontWeight: 600,
    },
    dialogTitleProcessing: {
      color: '#272E4C',
    },
    dialogTitleDone: {
      color: '#3ab77d',
    },
    dialogTitleTicket: {
      marginLeft: '16px',
      fontSize: '0.875rem',
      fontWeight: 500,
      color: '#151c1b80',
      textTransform: 'uppercase',
    },
    dialogTitleSubtext: {
      fontSize: '0.875rem',
      fontWeight: 400,
      color: '#272E4C',
    },
    dialogContent: {
      paddingTop: '0px',
    },
    dialogContentText: {
      fontSize: '0.875rem',
    },
    container: {},
    dialogAction: {
      marginBottom: theme.spacing(2),
      marginTop: theme.spacing(2),
      justifyContent: 'center',
    },
    boldText: {
      fontWeight: 'bold',
    },
    cancelButton: {
      marginRight: 25,
      border: '1px solid #3AB77D',
      color: '#3AB77D',
      backgroundColor: '#FFFFFF',
    },
    submitButton: {
      marginLeft: 25,
    },
    loadingCircularButton:{
      paddingLeft: 25,
      marginRight: 25,
    },
    closeButton: {
      position: 'absolute',
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    linearProgress: {
      height: 10,
      colorPrimary: {
        backgroundColor: '#3ab77d',
      },
      barColorPrimary: {
        backgroundColor: '#151c1b80',
      },
    },
    progress: {
      fontSize: '1rem',
      fontWeight: 600,
    },
    progressCount: {
      color: '#3ab77d',
    },
    progressTotalCount: {
      color: '#151c1b80',
    },
  }),
);
/**
 * IMPORTANT NOTE:
 * The preview data displayed from the heading down to the table
 * is based on the collection saved for renewal (for_renewal). That was save on submitting the auto renewal modal.
 */

const ModalFinishedRenewal = (props: any) => {
  const renewalsStateProps = useRenewalContext();
  const { handleBackButton, membersForRenewal, clientName,dataFields,columnExtensions } = renewalsStateProps?.props
  const { isOpenFinishRenewalModal, setIsOpenFinishRenewalModal } = renewalsStateProps?.modalStateProps
  const classes = useStyles();

  

  const actionButtons = ()=>{
    return(
      <>
       <Button
          data-cy={'member_upload_status_cancel_btn'}
          id={'member_upload_status_cancel_btn'}
          key={'cancel-process-btn'}
          className={classes.cancelButton}
          variant="contained"
          color="secondary"
          onClick={()=> setIsOpenFinishRenewalModal(false)}
        >
          Cancel
        </Button>,
        <Button
          data-cy={'member_upload_status_confirm_btn'}
          id={'member_upload_status_confirm_btn'}
          key={'confirm-enrollment-btn'}
          className={classes.submitButton}
          variant="contained"
          color="primary"
        >
          Confirm Enrollment
        </Button>,
      </>
    )
  }

  return (
    <>
     <Dialog
           id='finished-renewal-modal'
           fullWidth={true}
           maxWidth="xl"
           open={isOpenFinishRenewalModal}
           aria-labelledby="max-width-dialog-title"
         >
           <DialogTitle
             id={`${props.id}-dialog-title`}
             className={classes.dialogTitle}
             disableTypography={true}
           >
             <div>
               <span
                 id={'member_upload_status_title'}  
                 className={clsx(classes.dialogTitle, classes.dialogTitleDone)}>{"Finished Auto Renew of Members"}</span>
               <span
                 id={'member_upload_status_ticket_id'} 
                 className={classes.dialogTitleTicket}
               >
                 TICKET ID: {props.ticketId || '-'}
               </span>
             </div>
             <div>
               <span className={classes.dialogTitleSubtext}>{"Uploading the member list is done. You may proceed with the renewal."}</span>
             </div>
           </DialogTitle>
           <DialogContent className={classes.dialogContent}>
             <Grid
               container
               className={classes.container}
               justify="flex-start"
               alignItems="stretch"
               direction="column"
               spacing={1}
             >
               <Grid item xs={12}>
                 <LinearProgress
                   data-cy={'member_upload_status_progress'}
                   id={'member_upload_status_progress'}
                   className={classes.linearProgress}
                   variant="determinate"
                  //  value={percentage }
                 />
               </Grid>
               <Grid item xs={12}>
                 <div>
                   <span
                     data-cy={'member_upload_status_processed_entries'}
                     id={'member_upload_status_processed_entries'}  
                     className={clsx(classes.progress, classes.progressCount)}
                   >
                     {/* {`${processedCount}/`} */} {membersForRenewal?.length || 0}/
                   </span>
                   <span
                     data-cy={'member_upload_status_total_entries'}
                     id={'member_upload_status_total_entries'}  
                     className={clsx(classes.progress, classes.progressTotalCount)}
                   >
                    {/* {`${totalCount} entries`} */}
                    { membersForRenewal?.length || 0 } entries
                    </span>
                 </div>
               </Grid>
               <Grid item xs={12}>
                 <TableComponent
                   id="upload_file_table"
                   rows={
                    membersForRenewal || []
                   }
                   columns={dataFields||[]}                        

                   message="No available data"
                   onClickRow={() => {}}
                   disableSelect
                   disableSearch
                   disableFilter
                   disableSort
                   formattedColumns={[]
                   }
                   columnExtensions={
                    columnExtensions || []
                   }
                   pageSize={10}
                 />
               </Grid>
             </Grid>
           </DialogContent>
           <DialogActions className={classes.dialogAction}>
             {actionButtons()}
           </DialogActions>
         </Dialog>
    </>
  )
}

export default ModalFinishedRenewal