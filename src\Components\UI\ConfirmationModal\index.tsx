import * as React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Dialog, DialogTitle, DialogContent,
    DialogContentText, DialogActions, Grid,
    Button, IconButton} from '@material-ui/core/';
import CloseIcon from '@material-ui/icons/Close';

interface ConfirmationModalProps {
    title?: string | undefined;
    isModalOpen: boolean;
    id: string;
    company: string;
    member_count: number;
    onClose: () => void;
    onSubmit: () => void;
    disable?: boolean;
}

const useStyles = makeStyles(theme => ({
    root: {
        flexWrap:'wrap'
    },
    dialog:{
        align:'center',
        justify:'center',
        width:'100%',
        fontFamily: 'usual',
    },
    dialogContent :{
        paddingTop : '0px',
    },
    dialogContentText :{
        fontSize: '0.875rem'
    },
    container: {
        paddingRight:'15px',
        paddingLeft:'15px',
        width: '350px'
    },
    dialogTitle: {
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(2),
        fontSize: '16px',
        color: '#272E4C',
        fontWeight: 600
    },
    dialogAction: {
        marginBottom: theme.spacing(2),
        marginTop: theme.spacing(2),
        justifyContent:'center'
    },
    boldText:{
        fontWeight: 'bold'
    },
    cancelButton: {
        width:115,
        marginRight:25,
        border: '1px solid #3AB77D',
        color: '#3AB77D',
        backgroundColor: "#FFFFFF"
    },
    submitButton: {
        width:115,
        marginLeft:25,
    },
    closeButton: {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    }
}));
  

export const ConfirmationModal: React.FC<ConfirmationModalProps> = (
	props: ConfirmationModalProps,
): JSX.Element => {
    const {
        title,
        isModalOpen,
        id,
        disable
    } = props;
    const classes = useStyles();
    return (
        <Dialog
            id={id}
            open={isModalOpen}
            aria-labelledby="modal-title"
            className={classes.dialog}
            style={{zIndex: 9999}}
        >
            <div>
                <Grid container className={classes.container} justify="flex-start" alignItems="flex-start" direction="column">
                    <Grid item xs>
                        <DialogTitle className={classes.dialogTitle} disableTypography={true} id="modal-title" >
                            {title || "Confirm Submission"}
                            <IconButton 
                                data-cy={'confirmation_modal_close_btn'}
                                id={'confirmation_modal_close_btn'}
                                aria-label="close" 
                                className={classes.closeButton} 
                                onClick={props.onClose}
                            >
                                <CloseIcon />
                            </IconButton>
                        </DialogTitle>
                    </Grid>
                    <Grid item xs>
                        <DialogContent className={classes.dialogContent} style={{padding: '0px 20px 10px 20px'}}>
                            <DialogContentText className={classes.dialogContentText}>
                                Submitting this list will send it to the Account 
                                Processing Verifier for validation.
                                Are you sure you want to {title ? "renew":"add"} <span className={classes.boldText}>{new Intl.NumberFormat().format(props.member_count)}</span> members
                                to the corporate account <span className={classes.boldText}>{props.company}</span>?
                            </DialogContentText>
                        </DialogContent>
                        <DialogActions className={classes.dialogAction}>
                            <Button
                                data-cy={'confirmation_modal_cancel_btn'}
                                id={'confirmation_modal_cancel_btn'}
                                className={classes.cancelButton} 
                                variant="contained" 
                                color="secondary" 
                                onClick={props.onClose}
                            >
                                Cancel
                            </Button>
                            <Button
                                data-cy={'confirmation_modal_submit_btn'}
                                id={'confirmation_modal_submit_btn'}
                                className={classes.submitButton} 
                                variant="contained" 
                                color="primary" 
                                onClick={props.onSubmit}
                                disabled={disable ? disable : false}
                            >
                                Submit
                            </Button>
                        </DialogActions>
                    </Grid>
                </Grid>
            </div>
        </Dialog>
    )
}

ConfirmationModal.defaultProps = {
    isModalOpen: false,
    id: 'modal-id',
    company: '',
    member_count: 0
};
