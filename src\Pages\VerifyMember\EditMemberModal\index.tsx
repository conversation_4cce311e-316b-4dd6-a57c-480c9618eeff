import React, { useState, useEffect, useMemo } from 'react';
import Popover from '@material-ui/core/Popover';
import { makeStyles } from '@material-ui/core/styles';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Grid,
  Button,
  FormControl,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
  // InputLabel
} from '@material-ui/core/';
import { BasicTextField, BasicSelect } from 'Components/UI/';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { DatePicker } from './DatePicker';
import Link from '@material-ui/core/Link';

import moment from 'moment';

import { MemberFilesModal } from '../MemberFilesModal';


const useStyles = makeStyles(() => ({
  root: {
    flexWrap: 'wrap',
  },
  dialog: {
    align: 'center',
    justifyContent : 'center',
    width: '95%',
    fontFamily: 'usual',
  },
}));

interface EditMemberModalProps {
  pageType: string;
  anchorElement?: HTMLAnchorElement | null;
  memberData: any;
  planTypes?: any[] | null;
  handleChangeMemberData: (key: string, value: any) => void;
  handlePatchMemberData: (data: any) => void;
  handlePopoverClose: () => void;
  memberDataFields?: any[];
  uploadDetails?: {};
  id?: any;
  row: any;
  data?: any | {};
  prefix?:any;
  files?:any;
  onSubmit: (row: number, remarks: string, userRemarks?:any,  details?: any, data?: any, openFileModal?:any, rowData?:any, uploadedDocuments?:any) => void;
  onDelete?:  any;
  isModalOpen?:any;
  onClose: (row: number, details: any) => void;
  verifyEditTable?:any;
  handleClickUpload: any;
  tableName?: any;
  rowData?: any;
  tab?:any;
  tempId?: any;
  onSubmit2:  any;
  isUpload?: any;
  details: any;
  saveMemberFilesModal:any;
  remarks: any;
  rowDataForEdit:  any[] | null;
  dataForEdit:  any[] | null;
  otherTableRow: any | {};
  supportingDocumentChecker: any;
  onCloseMemberFilesModal?: any;
  handleClickUploadNewNew:(pageType: string, tableName: string, prefix: string, isUpload: boolean) => void;
  isEdit: boolean;
  // editData?:any;
  // memberEditSelected?:any;
  memberDataSelect?: any;
  tmp?:any;
}

export const EditMemberModal: React.FC<EditMemberModalProps> = (
  props: EditMemberModalProps,
): JSX.Element => {
  const {
    pageType,
    anchorElement,
    memberData,
    handleChangeMemberData,
    handlePatchMemberData,
    handlePopoverClose,
    memberDataFields,
    // handleClickUploadNewNew,
    // isUpload,
    // rowDataForEdit,
    // dataForEdit,
    // otherTableRow,
    // onClose,
    // isEdit,
    // onCloseMemberFilesModal,
    // editData,
    // memberEditSelected
    verifyEditTable,
    memberDataSelect,
    tmp
  } = props;

  const [anchorEl, setAnchorEl] = useState<
    HTMLAnchorElement | null | undefined
  >(anchorElement);
  const [popoverOpen, setPopoverOpen] = useState(Boolean(anchorElement));
  const [memberDetails1, setMemberDetails1] = useState<any[]>([]) 
  const [getTmp, setGetTmp] = useState<any[]>([]) 
  useEffect(() => {
    //  console.log('MEMBER DATA', editData)
    setAnchorEl(anchorElement);
    setPopoverOpen(Boolean(anchorElement));
  }, [anchorElement]);


  let memberDetails = pageType === 'edit' ? props.rowData : props.rowData['member_details']
  let getTempId = pageType === 'edit' ? props.rowData.temp_id : props.row.temp_id

  useEffect(() => {
    let memberDetailsSelected: any;
    let getTempId: any;

    if(memberDetails) {
      memberDetailsSelected = pageType === 'edit' ? props.rowData : props.rowData['member_details']
      getTempId = pageType === 'edit' ? props.rowData.temp_id : props.rowData['member_details']['temp_id']

    }
    setMemberDetails1(memberDetailsSelected)
    setGetTmp(getTempId)
  }, [memberDetails])


  const memberDetailsToEdit = useMemo(() => {
    if (memberData) return memberData;
    if (props?.rowData?.member_details) return props.rowData.member_details;
    return props?.rowData;
  }, [memberData, props?.rowData]);



  return (
    <Popover
      open={popoverOpen}
      anchorEl={anchorEl}
      onClose={() => {
        setAnchorEl(null);
        handlePopoverClose();
      }}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >


      <EditMemberForm
        // onSubmit={handleSubmitRelationship}
        id={props.id}
        row={props.row}
        uploadDetails={props.uploadDetails}
        data={props.data}
        // onClose={() => {
        //   setAnchorEl(null);
        //   handlePopoverClose();
        // } }
        // selectedMemberData={memberData}
        selectedMemberData={props.rowData}
        prefix={props.prefix}
        planTypes={props.planTypes}
        handleUpdateMember={handleChangeMemberData}
        handlePatchMemberData={handlePatchMemberData}
        memberDataFields={memberDataFields}
        pageType={pageType} 
        onSubmit={props.onSubmit} 
        onDelete={props.onDelete}      
        onClose={props.onClose}
        verifyEditTable={props.verifyEditTable}
        isModalOpen={props.isModalOpen}
        handleClickUpload={props.handleClickUpload}
        // memberDataSelect={memberDetails1}
        memberDataSelect={memberDetailsToEdit}
        tableName={props.tableName}
        rowData={props.rowData}
        tmp={getTmp}
        tempId={props.tempId}
        tab={props.tab}
        details={props.details}
        rowDataForEdit={props.rowDataForEdit}
        dataForEdit={props.dataForEdit}
        otherTableRow={props.otherTableRow}
        // isUpload={props.isUpload}
        onSubmit2={props.onSubmit2}
        isUpload={props.isUpload}
        saveMemberFilesModal={props.saveMemberFilesModal}
        remarks={props.remarks}
        supportingDocumentChecker={props.supportingDocumentChecker}
        onCloseMemberFilesModal={props.onCloseMemberFilesModal}
        handlePopoverClose={props.handlePopoverClose}
        // memberDetailsSelected={memberDetails1}
        handleClickUploadNewNew={props.handleClickUploadNewNew}
        />
    </Popover>
  );
};

interface EditMemberFormProps {
  pageType: string;
  onClose:  (row?: number, details?: any) => void;
  // onSubmit: any;
  prefix:any;
  selectedMemberData: any;
  // selectedMemberData2: any;
  planTypes?: any[] | null;
  handleUpdateMember: (key: string, value: any) => void;
  handlePatchMemberData: (data: any) => void;
  memberDataFields?: any[];
  id: string,
  row: number,
  data: any | {},
  uploadDetails: any,
  verifyEditTable: any,
  isModalOpen: boolean;
  onSubmit: (row: number, remarks: string, details: any,  files?: any,) => void,
  onDelete: (row: number, name: string, details: any) => void,
  handleClickUpload: (pageType: string, tableName: string, prefix: string, isUpload: boolean) => void,
  tableName: any,
  rowData: any,
  tempId: any,
  tab: any,
  details: any,
  onSubmit2: (row: number, data: any, remarks: string, details: any) => void,
  // saveMemberFilesModal: (row: number, data: any, remarks: string, details: any, openFileModal?: boolean) => void,
  saveMemberFilesModal: (row: number,remarks: string,user_remarks?: string, details?: any,rowData?:any,files?: any, openFileModal?:boolean, uploadedDocuments?: any) => void,
  // (row, remarks, user_remarks, details, rowData, data, openFileModal, uploadedDocuments)
  remarks: any,
  isUpload?: any,
  dataForEdit?: any | {},
  rowDataForEdit?: any | {},
  otherTableRow?: any | {},
  supportingDocumentChecker: any,
  handlePopoverClose: () => void;
  handleClickUploadNewNew: (pageType: string, tableName: string, prefix: string, isUpload: boolean) => void,
  onCloseMemberFilesModal: () => void,
  // editData?:any;
  // memberEditSelected?:any;
  memberDataSelect?:any;
  tmp?:any;
}


const EditMemberForm: React.FC<EditMemberFormProps> = (
  props: EditMemberFormProps,
): JSX.Element | null => {
  const {
    pageType,
    onClose,
    // onSubmit,
    planTypes,
    selectedMemberData,
    // selectedMemberData2,
    handleUpdateMember,
    handlePatchMemberData,
    memberDataFields,
    id,
    uploadDetails,
    // isModalOpen,
    data,
    // handleClickUploadNewNew,
    tableName, 
    prefix, 
    isUpload,
    row,
    rowData,
    memberDataSelect,
    tmp
  } = props;

  const classes = useStyles();

  const [invalidFields, setInvalidFields] = useState<string[]>([]);
  const [initialize, setInitialize] = useState(true);
  const [planTypesState, setPlanTypesState] = useState<any[] | null>([]);
  const [clientAddedFields, setClientAddedFields] = useState<any[]>([]);
    const [fieldsToCheck, setFieldsToCheck] = useState<string[]>([]);
  const [uploadSelected, setUploadSelected] = useState(false);
  const [memberFilesModal, setMemberFilesModal] = useState(false)
  // const [assignRow, setAssignRow] = useState<any[]>([]);
	const [openFileModal, setOpenFileModal] = useState(false);
  const [ filesDisplay, setFilesDisplay] = useState<any[] | null>([]);
  const [dateField, setDateField] = useState<any>({
    date_of_birth: '',
    effectivity_date: '',
    regularization_date: '',
    hire_date: '',
    date_printed: ''
  })
  // const [memberFileUploadData, setMemberFileUploadData] =  useState<any>({
  //   pageType: '',
  //   tableName: '',
  //   prefix: '',
  //   hire_date: '',
  //   rowData: ''
  // })

  // const [uploadFileData, setuploadFileData] =  useState<any>({
  //   pageType: '',
  //   tableName: '',
  //   prefix: '',
  //   hire_date: '',
  //   rowData: ''
  // })
  
  const [assignPrefix, setPrefix]  =  useState('');


  const [dataHolder, setDataHolder] = useState<any>({
    principal_name: '',
    relationship_to_principal: '',
    principal_employee_id: '',
  });


  useEffect(() => {
    console.log('selected member data', selectedMemberData)
  
    //selectedMemberData
    if(memberDataSelect) {
      if (Object.keys(rowData).length > 0) {
        setDateField({
          date_of_birth: rowData?.member_details?.date_of_birth
            ? moment(rowData.member_details.date_of_birth).format('MM/DD/YYYY')
            : rowData?.date_of_birth
            ? moment(rowData.date_of_birth).format('MM/DD/YYYY')
            : null,
        
          effectivity_date: rowData?.member_details?.effectivity_date
            ? moment(rowData.member_details.effectivity_date).format('MM/DD/YYYY')
            : rowData?.effectivity_date
            ? moment(rowData.effectivity_date).format('MM/DD/YYYY')
            : null,
        
          regularization_date: rowData?.member_details?.regularization_date
            ? moment(rowData.member_details.regularization_date).format('MM/DD/YYYY')
            : rowData?.regularization_date
            ? moment(rowData.regularization_date).format('MM/DD/YYYY')
            : null,
        
          hire_date: rowData?.member_details?.hire_date
            ? moment(rowData.member_details.hire_date).format('MM/DD/YYYY')
            : rowData?.hire_date
            ? moment(rowData.hire_date).format('MM/DD/YYYY')
            : null,
        
          date_printed: rowData?.member_details?.date_printed
            ? moment(rowData.member_details.date_printed).format('MM/DD/YYYY')
            : rowData?.date_printed
            ? moment(rowData.date_printed).format('MM/DD/YYYY')
            : null,
        });
        
  
        setDataHolder({
          principal_name: memberDataSelect && memberDataSelect['principal_name']
            ? memberDataSelect['principal_name'] : '',
          relationship_to_principal: memberDataSelect && memberDataSelect['relationship_to_principal']
            ? memberDataSelect['relationship_to_principal'] : '',
          principal_employee_id: memberDataSelect && memberDataSelect['principal_employee_id']
            ? memberDataSelect['principal_employee_id'] : '',
        })
      }
    }

  }, [memberDataSelect]) 
  //selectedMemberData - replaced memberDataSelect

  useEffect(() => {
  }, [dateField, dataHolder])

  useEffect(() => {
    if (initialize) {
      if (planTypes && planTypes.length > 0) {
        let temp: any[] | null = [];
        planTypes.forEach(planType => {
          if (temp !== undefined && temp !== null && planType !== undefined) {
            temp.push({
              name: planType['plan_type_name'],
              value: planType['plan_type_name'],
            });
          }
        });
        setPlanTypesState(temp);
      }

      /*  Automatically fills up 'Age' field */
      //memberDataSelect - replaced object selectedMemberData
      const date = 
          memberDataSelect && memberDataSelect['date_of_birth'] ?
          moment(memberDataSelect['date_of_birth']).isValid() ?
            moment(memberDataSelect['date_of_birth']).format('MM/DD/YYYY') :
            null :
          null;
      if (date) {
        handleUpdateMember('age', moment().diff(moment(date), 'years').toString());
      }

      setInitialize(false);
    }
    if (memberDataFields) {
      const temp = memberDataFields.filter(item => {
        return item.is_client_added
      })

      const requiredFields: string[] = 
      memberDataFields.filter((item) => item.is_required)
      .map((item) => item.field_name);

      setFieldsToCheck(requiredFields)

      setClientAddedFields(temp)
    }
  }, []);

  console.log(setUploadSelected)

  const handleCheckInvalidFields = () => {
    let temp: string[] = [];
    // check if dependent
 
    if (memberDataSelect?.member_type === 'Dependent'){
      fieldsToCheck.push("relationship_to_principal", "principal_name", "employee_id")
      
    }
    console.log("fieldsToCheckfieldsToCheck1", fieldsToCheck)
    console.log("temptemptemp1", temp)
    fieldsToCheck?.forEach(field => {
      
      if (memberDataSelect[field] === '' || memberDataSelect[field] === null) {
        temp.push(field);
      }
      
    });

    if(memberDataSelect?.member_type === 'Dependent' && (memberDataSelect.principal_name || memberDataSelect.employee_id || memberDataSelect.principal_employee_id)){
        temp = temp.filter((item) => item !== 'principal_name' && item !== 'employee_id' )
     
      }

    if(memberDataSelect?.member_type === 'Principal'){
      temp = temp.filter((item) => item !== 'principal_name' && item !== 'employee_id' && item !== 'relationship_to_principal')
    }
    


    
    setInvalidFields(temp);
    if (temp.length > 0) {
      return true;
    }
    return false;
  };

  const handleSave = () => {
  
    if (!handleCheckInvalidFields()) {
      // let editMemberData = [ ...memberDataSelect]
      //...rowData,
      // handlePatchMemberData(editMemberData); //selectedMemberData,
      //memberDataSelect - replaced object selectedMemberData
      // handlePatchMemberData(selectedMemberData);
      handlePatchMemberData(memberDataSelect)
      // console.log('all data handlesave', editMemberData)
      // setGetEditData(editMemberData)
      handleOnClose();
    }
  };

  const handleOnClose = () => {
    onClose();
    props.handlePopoverClose()
    // RESET VARIABLES ON CLOSE
    setDataHolder({
      principal_name: '',
      relationship_to_principal: '',
      principal_employee_id: '',
    })
  }

  const renderAdditionalFields = (field: any) => {
    if (field.input_type === 'Text Field') {
      if (field.data_type === 'Alphanumeric') {
        return (
          <Grid item xs={4}>
            <BasicTextField
              id={field.field_name}
              label={field.system_name}
              labelFontSize="14px"
              value={
                // selectedMemberData && selectedMemberData[field.field_name]
                //   ? selectedMemberData[field.field_name]
                //   : ''
                memberDataSelect && memberDataSelect[field.field_name]
                ? memberDataSelect[field.field_name]
                : ''
              }
              width="85%"
              error={invalidFields.includes(field.field_name)}
              onChange={(e: any) =>
                handleUpdateMember(field.field_name, e.target.value)
              }
            />
          </Grid>
        )
      } else if (field.data_type === 'Numeric') {
        return (
          <Grid item xs={4}>
            <BasicTextField
              type="number"
              id={field.field_name}
              label={field.system_name}
              labelFontSize="14px"
              value={
                // selectedMemberData && selectedMemberData[field.field_name]
                //   ? selectedMemberData[field.field_name]
                //   : ''
                memberDataSelect && memberDataSelect[field.field_name]
                ? memberDataSelect[field.field_name]
                : ''
              }
              width="85%"
              error={invalidFields.includes(field.field_name)}
              onChange={(e: any) =>
                handleUpdateMember(field.field_name, e.target.value)
              }
            />
          </Grid>
        )
      } else if (field.data_type === 'Date') {
        return (
          <Grid item xs={4}>
            {/* <InputLabel htmlFor={field.field_name} style={{ fontSize: '14px' }}>
              {field.system_name}
            </InputLabel> */}
            <BasicTextField
              id={field.field_name}
              label={field.system_name}
              labelFontSize="14px"
              value={
                // selectedMemberData && selectedMemberData[field.field_name]
                //   ? selectedMemberData[field.field_name]
                //   : ''
                memberDataSelect && memberDataSelect[field.field_name]
                ? memberDataSelect[field.field_name]
                : ''
              }
              width="85%"
              error={invalidFields.includes(field.field_name)}
              onChange={(e: any) =>
                handleUpdateMember(field.field_name, e.target.value)
              }
            />
          </Grid>
        )
      }
    }
    return null
  }
   // Start of code for MemberFilesModal
  const closeMemberFilesModal = (row: number, details: any) => {
    console.log('closeMemberFilesModal()',)
    setMemberFilesModal(false);
    setOpenFileModal(false)
    console.log(row)
    console.log(details)
  };

  console.log(closeMemberFilesModal)

  const supportingDocumentChecker2 = (files: any) => {
    console.log('supporting document checker', files)

  }

  const saveMemberFilesModal2 = async (row: number, files: any, remarks: string, details: any) => {
    console.log('saveMemberFilesModal2 edit>supportingfiles')
    row = props.row 
    details = props.details 
    props.saveMemberFilesModal(row, data, remarks, details)
       const tables = [
        'partialtable',
        'incompletetable',
        'conflicttable',
        'validationtable',
        'othervalidationtable',
        'disapprovetable',
       ]

        if(props.pageType === 'default') {
          let currentTab = props.tab
          console.log('Page type default - tabname')

          let data 
          // let currentTable
          if(currentTab === 1) {
            data = tables['incompletetable']
          } else if (currentTab === 2) {
            data = 'conflicttable'
          }  else if (currentTab === 3) {
            data = 'validationtable'
          } else if (currentTab === 4) {
            data = 'othervalidationtable'
          } else if (currentTab === 5) {
            data = 'disapprovetable'
          } else {
            console.log('error in currenttable')
          }
        
        }
      
    let newFiles: any[] = []
    // console.log('filesss new1', this.state.updata)
    if(files) {
      console.log('edit>supporting with files', files)
      const supportingDocumentCheckerFunc = props.supportingDocumentChecker
      newFiles = await supportingDocumentCheckerFunc(files)
      if(newFiles && newFiles.length > 0) {

        newFiles.map(a => {
          if(files.length > 0) {
            files.push(a)
          } else {
            files = [a]
          }


        })
      }
      files = files;
      remarks = remarks;
    }

    setOpenFileModal(false)
  };

 

  const deleteMemberFiles = (props) => {
      console.log('deleteMemberFiles', props)
      let deleteMemberFilesFunc = props.onDelete
      deleteMemberFilesFunc()

  }


  
  const handleClickUploadNew = (pageType: string, tableName: string, prefix: string, isUpload: boolean, row: number, rowData: any,) => {
    console.log('verify edit>handleClickUploadNew', row)
    // let disable = false;
    let prefix2 = props.tab === 1 ? tableName = 'incomplete' : props.tab === 2 ? tableName = 'conflict' : props.tab === 3 ? tableName = 'unmatched' : props.tab === 4 ? tableName = 'othervalidation' : props.tab === 5 ? tableName = 'supporting' : props.tab === 6 ? tableName = 'disapprove' : '';
    setPrefix(prefix2)
    const memberFileUploadDetails = {
      pageType: pageType,
      tableName: tableName,
      prefix: prefix,
      rowData: rowData,
      temp_id: rowData.member_details.temp_id,
    }

    console.log('edit>supportingfiles memberFileUploadDetailss', memberFileUploadDetails)
    if (pageType === 'default') {
      console.log('prefix-default1')

      // setPrefix(prefix2)
      if (prefix === 'Disapproved') {
        console.log('prefix-disapproved')
        if (rowData.member_details.validation_status.hasOwnProperty('matched_member')) {
          memberFileUploadDetails['temp_id'] = rowData.member_details.validation_status.matched_member.temp_id
        } else {
          memberFileUploadDetails['temp_id'] = selectedMemberData.temp_id
        }
      } else {
        console.log('else -prefix-default11')
        if ( memberFileUploadDetails['temp_id']) {
          memberFileUploadDetails['temp_id'] = tmp //selectedMemberData.temp_id
        // } else if(tmp) {
        //   memberFileUploadDetails['temp_id'] = tmp
        }
      }
    } else if (pageType === 'edit' || pageType === 'vd') {
      console.log('pagetype - edit/vd', pageType)
      memberFileUploadDetails['temp_id'] = selectedMemberData.temp_id
    }
  }


  return (
    <Grid item sm={12} style={{ padding: '10px' }}>
      <Dialog
        maxWidth={'xs'}
        fullScreen={true}
        fullWidth={true}
        open={true}
        scroll={'body'}
        style={{ width: '100%', transform: 'translate(0, 0)' }}
        aria-labelledby="modal-title"
        className={classes.dialog}
      // style={{zIndex: 9999}}
      >
        <DialogTitle id="form-dialog-title">
          <Grid container justify="space-between" alignItems="flex-end">
            <Grid item>Edit Member</Grid>
            <Grid item>
              <span id='close_modal' data-cy='close_modal' onClick={handleOnClose}>
                <FontAwesomeIcon icon={faTimes} />
              </span>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>

          <Grid
            container
            spacing={2}
            justifyContent ="space-between"
            style={{ paddingRight: '20px' }}
            alignItems="flex-start"
          >
            <Grid
              container
              item
              xs={6}
              // justify="space-between"
              style={{
                borderRight: '1px solid #ccc',
                minHeight: '100%',
                maxHeight: '100%',
              }}
            >
              {/* ROW 1 COL 1 */}
              <Grid item xs={4}>
                <BasicTextField
                  id="First Name"
                  label="First Name"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['first_name']
                    //   ? selectedMemberData['first_name']
                    //   : ''
                    memberDataSelect && memberDataSelect['first_name']
                    ? memberDataSelect['first_name']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('first_name')}
                  onChange={(e: any) =>
                    handleUpdateMember('first_name', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Middle Name"
                  label="Middle Name"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['middle_name']
                    //   ? selectedMemberData['middle_name']
                    //   : ''
                      memberDataSelect && memberDataSelect['middle_name']
                    ? memberDataSelect['middle_name']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('middle_name')}
                  onChange={(e: any) =>
                    handleUpdateMember('middle_name', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Last Name"
                  label="Last Name"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['last_name']
                    //   ? selectedMemberData['last_name']
                    //   : ''
                   
                       memberDataSelect && memberDataSelect['last_name']
                    ? memberDataSelect['last_name']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('last_name')}
                  onChange={(e: any) =>
                    handleUpdateMember('last_name', e.target.value)
                  }
                />
              </Grid>

              {/* ROW 2 COL 1 */}
              <Grid item xs={4}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Suffix"
                  label="Suffix"
                  error={invalidFields.includes('suffix')}
                  menuItems={menu_items['suffix']}
                  value={
                    // selectedMemberData && selectedMemberData['suffix'] &&
                    //   typeof selectedMemberData['suffix'] === 'string'
                    //   ? selectedMemberData['suffix'].replace('.', ' ').trim()
                    //   : ''
                    memberDataSelect && memberDataSelect['suffix'] &&
                      typeof memberDataSelect['suffix'] === 'string'
                      ? memberDataSelect['suffix'].replace('.', ' ').trim()
                      : ''
                  }
                  onChange={(e: any) =>
                    handleUpdateMember('suffix', e.target.value)
                  }
                  width="85%"
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Title"
                  label="Title"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['title']
                    //   ? selectedMemberData['title']
                    //   : ''
                    memberDataSelect &&  memberDataSelect['title']
                    ? memberDataSelect['title']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('title')}
                  onChange={(e: any) =>
                    handleUpdateMember('title', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Gender"
                  label="Gender"
                  error={invalidFields.includes('gender')}
                  menuItems={menu_items['gender']}
                  value={
                    // selectedMemberData && selectedMemberData['gender']
                    //   ? selectedMemberData['gender']
                    //   : ''
                    memberDataSelect &&  memberDataSelect['gender']
                    ? memberDataSelect['gender']
                    : ''
                  }
                  onChange={(e: any) =>
                    handleUpdateMember('gender', e.target.value)
                  }
                  width="85%"
                />
              </Grid>

              {/* ROW 3 COL 1 */}
              <Grid item xs={4}>
                <DatePicker
                  data-cy={'client_member_birthdate_start_picker'}
                  id={'client_member_birthdate_start_picker'}
                  placeholder="Birthdate"
                  labelFontSize="14px"
                  value={dateField['date_of_birth']}
                  error={invalidFields.includes('date_of_birth')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      date_of_birth: date,
                    })
                    handleUpdateMember('date_of_birth', date)
                    handleUpdateMember('age', moment().diff(moment(date), 'years').toString())
                  }}
                  width="85%"
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Age"
                  label="Age"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['age']
                    //   && !isNaN(selectedMemberData['age'])
                    //   ? selectedMemberData['age']
                    //   : ''
                    memberDataSelect && memberDataSelect['age']
                    && !isNaN(memberDataSelect['age'])
                    ? memberDataSelect['age']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('age')}
                  disabled
                />
              </Grid>
              <Grid item xs={4}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Civil Status"
                  label="Civil Status"
                  error={invalidFields.includes('civil_status')}
                  menuItems={menu_items['civil_status']}
                  value={
                    // selectedMemberData && selectedMemberData['civil_status']
                    //   ? selectedMemberData['civil_status']
                    //   : ''
                    memberDataSelect &&  memberDataSelect['civil_status']
                    ? memberDataSelect['civil_status']
                    : ''
                  }
                  onChange={(e: any) =>
                    handleUpdateMember('civil_status', e.target.value)
                  }
                  width="85%"
                />
              </Grid>

              {/* ROW 4 COL 1 */}
              <Grid item xs={4}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Member Type"
                  label="Member Type"
                  error={invalidFields.includes('member_type')}
                  menuItems={menu_items['member_type']}
                  value={
                    // selectedMemberData && selectedMemberData['member_type']
                    //   ? selectedMemberData['member_type']
                    //   : ''
                    memberDataSelect &&  memberDataSelect['member_type']
                    ? memberDataSelect['member_type']
                    : ''
                  }
                  onChange={(e: any) => {
                    handleUpdateMember('member_type', e.target.value)
                    handleUpdateMember('principal_name', e.target.value === 'Principal'
                      ? '' : dataHolder['principal_name'])
                    handleUpdateMember('relationship_to_principal', e.target.value === 'Principal'
                      ? '' : dataHolder['relationship_to_principal'])
                    if(invalidFields.length > 0 && e.target.value === 'Principal')
                    setInvalidFields(invalidFields?.filter((item) =>  item !== 'principal_name' && item !== 'employee_id' && item !== 'relationship_to_principal'))
                  }}
                  width="85%"
                />
              </Grid>
              <Grid item xs={8}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Plan Type"
                  label="Plan Type"
                  error={invalidFields.includes('plan_type')}
                  menuItems={
                    planTypesState !== null &&
                      planTypesState !== undefined &&
                      planTypesState.length > 0
                      ? planTypesState
                      : []
                  }
                  value={
                    // selectedMemberData && selectedMemberData['plan_type']
                    //   ? selectedMemberData['plan_type']
                    //   : ''
                     //   : ''
                     memberDataSelect &&  memberDataSelect['plan_type']
                     ? memberDataSelect['plan_type']
                     : ''
                  }
                  onChange={(e: any) =>
                    handleUpdateMember('plan_type', e.target.value)
                  }
                  width="93%"
                />
              </Grid>

              {/* ROW 5 COL 1 */}
              <Grid item xs={8} style={{ paddingTop: '20px' }}>
                <BasicTextField
                  id="Name of Principal"
                  label="Name of Principal"
                  labelFontSize="14px"
                  value={
                    //selectedMemberData
                    memberDataSelect && memberDataSelect['principal_name']
                      ? memberDataSelect['principal_name']
                      : 'N/A'
                  }
                  width="93%"
                  error={invalidFields.includes('principal_name')}
                  onChange={(e: any) => {
                    handleUpdateMember('principal_name', e.target.value)
                    setDataHolder({
                      ...dataHolder,
                      principal_name: e.target.value
                    })
                  }}
                  disabled={
                    memberDataSelect && memberDataSelect['member_type']
                      && memberDataSelect['member_type'] === 'Principal'
                      ? true : false
                  }
                />
              </Grid>
              <Grid item xs={4} style={{ paddingTop: '20px' }}>
                <BasicSelect
                  marginTop="10px"
                  marginBottom="10px"
                  labelFontSize="14px"
                  id="Relationship to Principal"
                  label="Relationship to Principal"
                  error={invalidFields.includes('relationship_to_principal')}
                  menuItems={menu_items['relationship_to_principal']}
                  value={
                    // selectedMemberData &&
                    //   selectedMemberData['relationship_to_principal']
                    //   ? selectedMemberData['relationship_to_principal']
                    //   : ''
                     //   : ''
                     memberDataSelect &&  memberDataSelect['relationship_to_principal']
                     ? memberDataSelect['relationship_to_principal']
                     : ''
                  }
                  onChange={(e: any) => {
                    handleUpdateMember('relationship_to_principal', e.target.value)
                    setDataHolder({
                      ...dataHolder,
                      relationship_to_principal: e.target.value
                    })
                  }}
                  width="85%"
                  disabled={
                    // selectedMemberData && selectedMemberData['member_type']
                    //   && selectedMemberData['member_type'] === 'Principal'
                    //   ? true : false

                      memberDataSelect && memberDataSelect['member_type']
                      && memberDataSelect['member_type'] === 'Principal'
                      ? true : false
                  }
                />
              </Grid>

              {/* ROW 6 COL 1 */}
              <Grid item xs={4}>
                <BasicTextField
                  id="Principal Employee ID"
                  label="Principal Employee ID"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['principal_employee_id']
                    //   ? selectedMemberData['principal_employee_id']
                    //   : ''
                     //   : ''
                     memberDataSelect &&  memberDataSelect['principal_employee_id']
                     ? memberDataSelect['principal_employee_id']
                     : ''
                  }
                  width="85%"
                  error={invalidFields.includes('principal_employee_id')}
                  onChange={(e: any) => {
                    handleUpdateMember('principal_employee_id', e.target.value)
                    setDataHolder({
                      ...dataHolder,
                      principal_employee_id: e.target.value
                    })
                  }}
                  disabled={
                    memberDataSelect && memberDataSelect['member_type']
                      && memberDataSelect['member_type'] === 'Principal'
                      ? true : false
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <DatePicker
                  data-cy={'client_member_effectivity_date_picker'}
                  id={'client_member_effectivity_date_picker'}
                  placeholder="Effective Date"
                  labelFontSize="14px"
                  value={dateField['effectivity_date']}
                  error={invalidFields.includes('effectivity_date')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      effectivity_date: date,
                    })
                    handleUpdateMember('effectivity_date', date)
                  }}
                  width="85%"
                  disabled={
                    (pageType === 'default' || pageType === 'edit' || pageType === 'vd') ||
                    memberDataSelect && memberDataSelect['member_status'] &&
                      (
                        memberDataSelect['member_status'] === 'Awaiting Activation' ||
                        memberDataSelect['member_status'] === 'For Validation'
                      ) ? false : true
                  }
                />
              </Grid>
      
                {/* <Grid item xs={4}>
                <DatePicker
                  data-cy={'client_member_termination_date_picker'}
                  id={'client_member_termination_date_picker'}
                  placeholder="Termination Date"
                  labelFontSize="14px"
                  value={selectedMemberData && selectedMemberData['termination_date']
                    ? moment(selectedMemberData['termination_date']).isValid() ?
                      moment(selectedMemberData['termination_date']).format('MM/DD/YYYY')
                      : null : null
                  }
                  error={invalidFields.includes('termination_date')}
                  onChange={(date: any) => {
                    if (!moment(date).isValid()) {
                      date = ''
                    }
                    handleUpdateMember('termination_date', date)
                  }}
                  width="85%" 
                />
              </Grid> */} 

            {/* <Grid item xs={4}> */}
                {/* <DatePicker
                  data-cy={'client_member_date_printed_picker'}
                  id={'client_member_date_printed_picker'}
                  placeholder="Card Printed Date"
                  labelFontSize="14px"
                  value={dateField['date_printed']}
                  error={invalidFields.includes('date_printed')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      date_printed: date,
                    })
                    handleUpdateMember('date_printed', date)
                  }}
                  width="85%"
                />
              </Grid> */}
              <Grid item xs={4}>
                <DatePicker
                  data-cy={'client_member_regularization_date_picker'}
                  id={'client_member_regularization_date_picker'}
                  placeholder="Regularization Date"
                  labelFontSize="14px"
                  value={dateField['regularization_date']}
                  error={invalidFields.includes('regularization_date')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      regularization_date: date,
                    })
                    handleUpdateMember('regularization_date', date)
                  }}
                  width="85%"
                />
              </Grid>

             
              {/* <Grid item xs={4}></Grid> */}
              {/* <Grid item xs={4}></Grid> */}

            </Grid>
            {/* <span style={{ paddingRight : '5px'}}/>  */}


            <Grid
              container
              item
              xs={6}
              style={{
                // minHeight: '100%',
                // maxHeight: '100%',
                paddingLeft: '40px',
              }}
            >
              {/* ROW 1 COL 2 */}
              <Grid item xs={4}>
                <BasicTextField
                  id="Employee ID"
                  label="Employee ID"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['employee_id']
                    //   ? selectedMemberData['employee_id']
                    //   : ''
                    memberDataSelect && memberDataSelect['employee_id']
                      ? memberDataSelect['employee_id']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('employee_id')}
                  onChange={(e: any) =>
                    handleUpdateMember('employee_id', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <DatePicker
                  data-cy={'client_member_hire_date_picker'}
                  id={'client_member_hire_date_picker'}
                  placeholder="Date of Hire"
                  labelFontSize="14px"
                  value={dateField['hire_date']}
                  error={invalidFields.includes('hire_date')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      hire_date: date,
                    })
                    handleUpdateMember('hire_date', date)
                  }}
                  width="85%"
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Site Assigned"
                  label="Site Assigned"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['site']
                    //   ? selectedMemberData['site']
                    //   : ''
                    memberDataSelect && memberDataSelect['site']
                    ? memberDataSelect['site']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('site')}
                  onChange={(e: any) =>
                    handleUpdateMember('site', e.target.value)
                  }
                />
              </Grid>

              {/* ROW 2 COL 2 */}
              <Grid item xs={8}>
                <BasicTextField
                  id="Site Address"
                  label="Site Address"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['site_address']
                    //   ? selectedMemberData['site_address']
                    //   : ''
                    memberDataSelect && memberDataSelect['site_address']
                    ? memberDataSelect['site_address']
                    : ''
                  }
                  width="93%"
                  error={invalidFields.includes('site_address')}
                  onChange={(e: any) =>
                    handleUpdateMember('site_address', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Floor"
                  label="Floor"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['floor']
                    //   ? selectedMemberData['floor']
                    //   : ''
                    memberDataSelect && memberDataSelect['floor']
                      ? memberDataSelect['floor']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('floor')}
                  onChange={(e: any) =>
                    handleUpdateMember('floor', e.target.value)
                  }
                />
              </Grid>

              {/* ROW 3 COL 2 */}
              <Grid item xs={4}>
                <BasicTextField
                  id="Designation"
                  label="Designation"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['designation']
                    //   ? selectedMemberData['designation']
                    //   : ''
                    memberDataSelect && memberDataSelect['designation']
                    ? memberDataSelect['designation']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('designation')}
                  onChange={(e: any) =>
                    handleUpdateMember('designation', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Job Grade/Level"
                  label="Job Grade/Level"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['job_lvl']
                    //   ? selectedMemberData['job_lvl']
                    //   : ''
                    memberDataSelect && memberDataSelect['job_lvl']
                      ? memberDataSelect['job_lvl']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('job_lvl')}
                  onChange={(e: any) =>
                    handleUpdateMember('job_lvl', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Supervisor"
                  label="Supervisor"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['supervisor']
                    //   ? selectedMemberData['supervisor']
                    //   : ''
                    memberDataSelect && memberDataSelect['supervisor']
                    ? memberDataSelect['supervisor']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('supervisor')}
                  onChange={(e: any) =>
                    handleUpdateMember('supervisor', e.target.value)
                  }
                />
              </Grid>

              {/* ROW 4 COL 2 */}
              <Grid item xs={4}>
                <BasicTextField
                  id="Cost Center"
                  label="Cost Center"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['cost_center']
                    //   ? selectedMemberData['cost_center']
                    //   : ''
                    memberDataSelect && memberDataSelect['cost_center']
                      ? memberDataSelect['cost_center']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('cost_center')}
                  onChange={(e: any) =>
                    handleUpdateMember('cost_center', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Financial Code"
                  label="Financial Code"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['financial_code']
                    //   ? selectedMemberData['financial_code']
                    //   : ''
                    memberDataSelect && memberDataSelect['financial_code']
                    ? memberDataSelect['financial_code']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('financial_code')}
                  onChange={(e: any) =>
                    handleUpdateMember('financial_code', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                <BasicTextField
                  id="Subcompany"
                  label="Subcompany"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['sub_company']
                    //   ? selectedMemberData['sub_company']
                    //   : ''
                    memberDataSelect && memberDataSelect['sub_company']
                    ? memberDataSelect['sub_company']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('sub_company')}
                  onChange={(e: any) =>
                    handleUpdateMember('sub_company', e.target.value)
                  }
                />
              </Grid>

              {/* ROW 5 COL 2 */}
              <Grid item xs={4}>
                {/* <BasicTextField
                  id="Batch No."
                  label="Batch No."
                  labelFontSize="14px"
                  value={
                    selectedMemberData && selectedMemberData['batch_name']
                      ? selectedMemberData['batch_name']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('batch_name')}
                  onChange={(e: any) =>
                    handleUpdateMember('batch_name', e.target.value)
                  }
                /> */}
                <BasicTextField
                  id="Department"
                  label="Department"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['department']
                    //   ? selectedMemberData['department']
                    //   : ''
                    memberDataSelect && memberDataSelect['department']
                    ? memberDataSelect['department']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('department')}
                  onChange={(e: any) =>
                    handleUpdateMember('department', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                {/* <BasicTextField
                  id="Card Printed Date"
                  label="Card Printed Date"
                  labelFontSize="14px"
                  value={
                    selectedMemberData && selectedMemberData['date_printed']
                      ? selectedMemberData['date_printed']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('date_printed')}
                  onChange={(e: any) =>
                    handleUpdateMember('date_printed', e.target.value)
                  }
                /> */}
                <BasicTextField
                  id="Email"
                  label="Email"
                  labelFontSize="14px"
                  value={
                    // selectedMemberData && selectedMemberData['email_address']
                    //   ? selectedMemberData['email_address']
                    //   : ''
                    memberDataSelect && memberDataSelect['email_address']
                    ? memberDataSelect['email_address']
                    : ''
                  }
                  width="85%"
                  error={invalidFields.includes('email_address')}
                  onChange={(e: any) =>
                    handleUpdateMember('email_address', e.target.value)
                  }
                />
              </Grid>
              <Grid item xs={4}>
                {/* <BasicTextField
                  id="Card Printed Date"
                  label="Card Printed Date"
                  labelFontSize="14px"
                  value={
                    selectedMemberData && selectedMemberData['date_printed']
                      ? selectedMemberData['date_printed']
                      : ''
                  }
                  width="85%"
                  error={invalidFields.includes('date_printed')}
                  onChange={(e: any) =>
                    handleUpdateMember('date_printed', e.target.value)
                  }
                /> */}
                <DatePicker
                  data-cy={'client_member_date_printed_date_picker'}
                  id={'client_member_date_printed_date_picker'}
                  placeholder="Card Date Printed"
                  labelFontSize="14px"
                  value={dateField['date_printed'] ? dateField['date_printed'] : ''}
                  error={invalidFields.includes('date_printed')}
                  onChange={(date: any) => {
                    setDateField({
                      ...dateField,
                      date_printed: date,
                    })
                    handleUpdateMember('date_printed', date)
                  }}
                  width="85%"
                />
              </Grid>
              {/* <Grid item xs={4}></Grid> */}

              {clientAddedFields.map(item => renderAdditionalFields(item))}

              {/* ROW 6 COL 2 */}
              <Grid
                item
                xs={12}
                style={{ paddingBottom: '15px', paddingTop: '15px' }}
              >
                <FormControl component="fieldset">
                  <FormGroup aria-label="position" row>
                    <span />
                    <FormControlLabel
                      value="VIP"
                      control={
                        <Checkbox
                          id='checkbox_is_vip'
                          data-cy='checkbox_is_vip'
                          color="primary"
                          onChange={(e: any) =>
                            handleUpdateMember('is_vip', e.target.checked)
                          }
                          checked={
                            // selectedMemberData && selectedMemberData['is_vip']
                            //   ? selectedMemberData['is_vip']
                            //   : false

                            memberDataSelect && memberDataSelect['is_vip']
                            ? memberDataSelect['is_vip']
                            : false
                          }
                        />
                      }
                      label="VIP"
                      labelPlacement="end"
                    />
                    <span style={{ paddingRight: '120px' }} />
                    {/* <FormControlLabel
                      value="Expat"
                      control={
                        <Checkbox
                          id='checkbox_is_expat'
                          data-cy='checkbox_is_expat'
                          color="primary"
                          onChange={(e: any) =>
                            handleUpdateMember('is_expat', e.target.checked)
                          }
                          checked={
                            // selectedMemberData && selectedMemberData['is_expat']
                            //   ? selectedMemberData['is_expat']
                            //   : false
                            memberDataSelect && memberDataSelect['is_expat']
                            ? memberDataSelect['is_expat']
                            : false
                          }
                        />
                      }
                      label="Expat"
                      labelPlacement="end"
                    /> */}
                     <FormControlLabel
                      value="Philhealth Rider"
                      control={
                        <Checkbox
                          id='is_philhealth_rider'
                          data-cy='is_philhealth_rider'
                          color="primary"
                          onChange={(e: any) =>
                            handleUpdateMember(
                              'is_philhealth_rider',
                              e.target.checked,
                            )
                          }
                          checked={
                            memberDataSelect &&
                            memberDataSelect['is_philhealth_rider']
                            ? memberDataSelect['is_philhealth_rider']
                            : false
                          }
                        />
                      }
                      label="Philhealth Rider"
                      labelPlacement="end"
                    />
                    <span style={{ paddingRight: '105px' }} />
                    <FormControlLabel
                      value="Member Consent"
                      control={
                        <Checkbox
                          id='checkbox_is_member_consent'
                          data-cy='checkbox_is_member_consent'
                          color="primary"
                          onChange={(e: any) =>
                            handleUpdateMember(
                              'is_member_consent',
                              e.target.checked,
                            )
                          }
                          checked={
                            // selectedMemberData &&
                            //   selectedMemberData['is_member_consent']
                            //   ? selectedMemberData['is_member_consent']
                            //   : false
                            memberDataSelect &&
                            memberDataSelect['is_member_consent']
                            ? memberDataSelect['is_member_consent']
                            : false
                          }
                        />
                      }
                      label="Member Consent"
                      labelPlacement="end"
                    />
                  </FormGroup>
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="flex-start">
            <Grid item xs={6} alignItems="flex-start">
              {console.log('edit>supporingfile modal2', memberDataSelect)}
              <Link
                onClick={() => {
                handleClickUploadNew(pageType, tableName, prefix, isUpload, row, rowData )
                // setOpenFileModal(true)
                setMemberFilesModal(true)
                if(rowData && rowData.member_details.documents) {
                  // setFilesDisplay(rowData.member_details.documents)
                  setFilesDisplay(rowData.documents)
                }
                if(openFileModal === false) {
                  setOpenFileModal(true)
                } else {
                  setOpenFileModal(false)
                }
                }}
              >
              <Typography
                style={{
                  textAlign: 'left',
                  textDecoration: 'underline',
                  fontSize: '13px',
                  color: '#3AB77D',
                  paddingBottom: '20px',
                }}
              >
                Upload Supporting Document
              </Typography>
              </Link>
              {console.log('edit>uploadsupporting', props)}
             { //memberFilesModalOpen uploadSelected openFileModal
            openFileModal === true ? (
                <>
                 {/* openFileModal */}
                 <MemberFilesModal 
                    isModalOpen={memberFilesModal}
                    id={id}
                    row={row}
                    // uploadDetails={uploadDetails}
                    pageType={uploadDetails['prefix']}
                    uploadDetails={rowData}
                    openFileModal={openFileModal}
                    data={data}
                    // documents
                    // pageType={pageType}
                    tableName={tableName}
                    prefix={prefix}
                    rowData={rowData}
                    // tempId={rowData && rowData.member_details['temp_id'] ? rowData.member_details['temp_id'] : ''}
                    tempId={tmp}
                    onClose={closeMemberFilesModal}
                    onSubmit2={(
                      row: number,
                      data: any,
                      remarks?:string,
                      details?: any,
                    ) => props.saveMemberFilesModal(row, data, details, remarks)}
                    onSubmit={(
                      row: number,
                      remarks: string,
                      user_remarks?: string,
                      details?: any,
                      rowData?: any,
                      data?:any,
                      openFileModal?:any,
                      uploadedDocuments?:any
                    ) => {
                      props.saveMemberFilesModal(row, remarks, user_remarks, details, rowData, data, openFileModal, uploadedDocuments)
                      console.log('edit>files1', row)
                    }
                  }
                    onDelete={props.onDelete}
                 />
                 </>
               ) : null
             }
            </Grid>
            <Grid
              container
              item
              xs={6}
              alignItems="flex-start"
              style={{ paddingTop: '0px' }}
            >
              <Grid item xs={6}></Grid>
              <Grid container item xs={6}>
                <Grid
                  item
                  xs={6}
                  alignItems="flex-start"
                  style={{ paddingRight: '30px' }}
                >
                  <Button
                    id='cancel_button'
                    data-cy='cancel_button'
                    variant="outlined"
                    color="default"
                    fullWidth
                    onClick={handleOnClose}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    id='save_button'
                    data-cy='save_button'
                    variant="contained"
                    color="primary"
                    fullWidth
                    onClick={handleSave}
                  >
                    Save
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>

      {/* <DialogActions style={{ paddingBottom: '20px' }}>

      </DialogActions> */}
    </Grid>
  );
};

EditMemberModal.defaultProps = {};

const menu_items = {
  suffix: [
    { name: 'None', value: '' },
    { name: 'Jr.', value: 'Jr' },
    { name: 'Sr.', value: 'Sr' },
    { name: 'I', value: 'I' },
    { name: 'II', value: 'II' },
    { name: 'III', value: 'III' },
    { name: 'IV', value: 'IV' },
    { name: 'V', value: 'V' },
    { name: 'Others', value: 'Others' },
  ],
  // title: [{ name: 'Mr.', value: 'Mr.' }],
  gender: [
    { name: 'Female', value: 'Female' },
    { name: 'Male', value: 'Male' },
    { name: 'Unspecified', value: 'Unspecified' },
  ],
  civil_status: [
    { name: 'Single', value: 'Single' },
    { name: 'Married', value: 'Married' },
    { name: 'Single Parent', value: 'Single Parent' },
    { name: 'Annulled', value: 'Annulled' },
    { name: 'Widowed', value: 'Widowed' },
    { name: 'Divorced', value: 'Divorced' },
    { name: 'Separated', value: 'Separated' },
    // { name: 'Unspecified', value: 'Unspecified' },
  ],
  member_type: [
    { name: 'Principal', value: 'Principal' },
    { name: 'Dependent', value: 'Dependent' },
    { name: 'Extended Dependent', value: 'Extended Dependent' },
  ],
  // plan_type: [{ name: 'VIP', value: 'VIP' }],
  relationship_to_principal: [
    // { name: 'Parent', value: 'Parent' },
    { name: 'Mother', value: 'Mother' },
    { name: 'Father', value: 'Father' },
    { name: 'Brother', value: 'Brother' },
    { name: 'Sister', value: 'Sister' },
    { name: 'Husband', value: 'Husband' },
    { name: 'Wife', value: 'Wife' },
    { name: 'Son', value: 'Son' },
    { name: 'Daughter', value: 'Daughter' },
    { name: 'Grandmother', value: 'Grandmother' },
    { name: 'Grandfather', value: 'Grandfather' },
    { name: 'Grandson', value: 'Grandson' },
    { name: 'Granddaughter', value: 'Granddaughter' },
    { name: 'Common Law Partner', value: 'Common Law Partner' },
    { name: 'Same Sex Partner', value: 'Same Sex Partner' },
    { name: 'Auntie', value: 'Auntie' },
    { name: 'Uncle', value: 'Uncle' },
    { name: 'Niece', value: 'Niece' },
    { name: 'Nephew', value: 'Nephew' },
    { name: 'Cousin', value: 'Cousin' },
    { name: 'Household Help', value: 'Household Help' },
  ],
  // site: [{ name: '', value: '' }],
};

// const fields_to_check = [
//   'first_name',
//   'last_name',
//   // 'middle_name',
//   // 'suffix',
//   // 'title',
//   'gender',
//   'date_of_birth',
//   // 'age',
//   // 'civil_status',
//   'member_type',
//   // 'plan_type',
//   // 'principal_name',
//   // 'relationship_to_principal',
//   // 'effectivity_date',
//   // 'termination_date',
//   // 'employee_id',
//   // 'hire_date',
//   // 'site',
//   // 'site_address',
//   // 'floor',
//   // 'designation',
//   // 'job_lvl',
//   // 'supervisor',
//   // 'cost_center',
//   // 'financial_code',
//   // 'sub_company',
//   // 'batch_name',
//   // 'date_printed'
// ];

export default EditMemberModal;

function foreach(arg0: boolean) {
  throw new Error('Function not implemented.');
}
