import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Typography,
    IconButton,
    Grid,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>l,
    Step<PERSON>ontent,
    <PERSON>con,
    <PERSON><PERSON>,
} from '@material-ui/core/';
import _ from 'lodash';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import CloseIcon from '@material-ui/icons/Close';
import moment from 'moment';
import CommentIcon from '@material-ui/icons/Comment';
import PersonIcon from '@material-ui/icons/Person';
import { Loader } from 'Components/UI/LoadingIndicator';
import { API } from '../../API';
import { DynamicTable } from './DynamicTable';

const hmoitemStyles = makeStyles({
    hmoname: {
        fontSize: '0.75rem',
        color: 'rgba(39, 46, 76, 0.6)',
        textTransform: 'uppercase',
        paddingRight: 32,
    },
    hmovalue: {
        fontSize: '1rem',
        fontWeight: 600,
        color: 'rgb(39, 46, 76)',
    },
    hmovaluelink: {
        fontSize: '1rem',
        fontWeight: 600,
        color: 'rgb(39, 46, 76)',
        textDecoration: 'underline',
        lineHeight: 1.5,
    },
});

interface HMOItemProps {
    name?: string;
    value?: string;
    link?: string;
    handleClick?: any;
}

const HMOItem: React.FC<HMOItemProps> = (
    props: HMOItemProps,
): JSX.Element => {
    const classes = hmoitemStyles(props);

    let lnk = props.link ? props.link : '';
    return (
        <Grid
            container
            alignItems="center"
            style={{ paddingTop: 4, paddingBottom: 4 }}
        >
            <Grid item xs={5}>
                <Typography className={classes.hmoname}>{props.name}</Typography>
            </Grid>
            <Grid item xs={7}>
                {lnk ? (
                    <Link href={lnk} className={classes.hmovaluelink}>
                        {props.value}
                    </Link>
                ) : props.handleClick ? (
                    <Link
                        onClick={() => {
                            props.handleClick();
                        }}
                        className={classes.hmovaluelink}
                    >
                        {props.value}
                    </Link>
                ) : (
                            <Typography className={classes.hmovalue}>{props.value}</Typography>
                        )}
            </Grid>
        </Grid>
    );
};

const useStyles = makeStyles(theme => ({
    dialog: {
        align: 'center',
        justify: 'center',
        fontFamily: 'usual',
    },
    dialogTitle: {
        paddingTop: theme.spacing(3),
        paddingBottom: theme.spacing(1),
        minWidth: 500,
        paddingLeft: 40,
        paddingRight: 40,
    },
    dialogContent: {
        paddingTop: 10,
        paddingLeft: 40,
        paddingRight: 40,
        // backgroundColor: '#F5F7FB',
        height: 400,
    },
    dialogAction: {
        paddingTop: theme.spacing(2),
        paddingBottom: theme.spacing(2),
        paddingLeft: 40,
        paddingRight: 40,
        // backgroundColor: '#F5F7FB',
    },
    dialogTitleText: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    dialogTitleSubText: {
        fontSize: 14,
        fontWeight: 'bold',
    },
    closeButton: {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
    fieldLabel: {
        fontSize: '0.75rem',
        textTransform: 'uppercase',
        color: 'rgb(39, 46, 76, 0.6)',
        marginBottom: 20,
    },
    remarksStep: {
        width: '100%',
        height: 175,
        borderRadius: 0,
        overflow: 'auto',
        padding: 10,
        boxShadow: '0px 3px 6px #272E4C19',
        backgroundColor: '#fff',
    },
    noRemarksStep: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: 175,
        borderRadius: 0,
        padding: 10,
        boxShadow: '0px 3px 6px #272E4C19',
        backgroundColor: '#fff',
    },
    closeButton2: {
        display: 'flex',
        width: 115,
        border: '1px solid #3AB77D',
        backgroundColor: '##3AB77D',
        color: '#FFFFFF',
    },
}));

interface MemberInformationModalProps {
    id: string,
    isModalOpen: boolean,
    data: any,
    clientData: any,
    tableData: any,
    onClose: () => void,
    memberDataFields: any,
    memberData?: any
}

export const MemberInformationModal: React.FC<MemberInformationModalProps> = (
    props: MemberInformationModalProps
): JSX.Element => {
    console.log('clientDataFields', props.memberDataFields)
    console.log('member data123', props.data)
    console.log('clientData', props.clientData)
    console.log('memberData', props.memberData)
    const classes = useStyles();
    let {
        id,
        isModalOpen,
        data,
        clientData,
        memberData,
        tableData,
    } = props;

    const [memberType, setMemberType] = useState<string>('Principal');
    const [clientInfo, setClientInfo] = useState<any>(null);
    //const [memberInfo, setMemberInfo] = useState<any>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [memberState, setMemberState] = useState<any>(null);
    const [tableState, setTableState] = useState<any>(null);
    const [clientFields, setClientFields] = useState<any>([]);
    let leftsideCF: any[] = [];
    let rightsideCF: any[] = [];
    if (clientFields && clientFields !== undefined) {
        if (clientFields.length > 2) {
            let halfwayThrough = Math.floor(clientFields.length / 2);
            leftsideCF = clientFields.slice(0, halfwayThrough).map(item => {
                let value = '';
                if (
                    item.field_name === 'registered_name' ||
                    item.field_name === 'name_on_card'
                ) {
                    value =
                        clientData && clientData.client[item.field_name]
                            ? clientData.client[item.field_name]
                            : '';
                } else if (item.input_type === 'date') {
                    value =
                        props.data && props.data[item.field_name]
                            ? moment(props.data[item.field_name]).format('MMM DD, YYYY')
                            : '';
                } else {
                    value =
                        props.data && props.data[item.field_name]
                            ? props.data[item.field_name]
                            : '';
                }
                return <HMOItem name={item.system_name} value={value ? value : '-'} />;
            });
            rightsideCF = clientFields
                .slice(halfwayThrough, clientFields.length)
                .map(item => {
                    let value = '';
                    if (
                        item.field_name === 'registered_name' ||
                        item.field_name === 'name_on_card'
                    ) {
                        value =
                            clientData && clientData.client[item.field_name]
                                ? clientData.client[item.field_name]
                                : '';
                    } else if (item.input_type === 'date') {
                        value =
                            props.data && props.data[item.field_name]
                                ? moment(props.data[item.field_name]).format('MMM DD, YYYY')
                                : '';
                    } else {
                        value =
                            props.data && props.data[item.field_name]
                                ? props.data[item.field_name]
                                : '';
                    }
                    return (
                        <HMOItem name={item.system_name} value={value ? value : '-'} />
                    );
                });
        } else if (clientFields.length > 0) {
            leftsideCF = [...clientFields].map(item => {
                let value = '';
                if (
                    item.field_name === 'registered_name' ||
                    item.field_name === 'name_on_card'
                ) {
                    value =
                        clientData && clientData.client[item.field_name]
                            ? clientData.client[item.field_name]
                            : '';
                } else if (item.input_type === 'date') {
                    value =
                        props.data && props.data[item.field_name]
                            ? moment(props.data[item.field_name]).format('MMM DD, YYYY')
                            : '';
                } else {
                    value =
                        props.data && props.data[item.field_name]
                            ? props.data[item.field_name]
                            : '';
                }
                return <HMOItem name={item.system_name} value={value ? value : '-'} />;
            });
        }
    }
    useEffect(() => {
        if (
            isModalOpen &&
            data && clientData &&
            Object.keys(data).length > 0 &&
            Object.keys(clientData).length > 0
        ) {
            console.log('member data val', data)
            console.log('clientData val', clientData)
            console.log('memberdata value', memberData)

            if (data && data['member_type']) {
                setMemberType(data['member_type']);
            }
            setClientInfo(clientDataSetter(clientData));

            if (props.memberDataFields && props.memberDataFields.length > 0) {
                let client_fields: any[] = [
                    {
                        system_name: 'Corporate Acct. Name',
                        field_name: 'registered_name',
                        is_required: false,
                        input_type: 'text',
                        is_client_added: false,
                        is_editable: false,
                    },
                    {
                        system_name: 'Card Name',
                        field_name: 'name_on_card',
                        is_required: false,
                        input_type: 'text',
                        is_client_added: false,
                        is_editable: false,
                    },
                ];

                const excludedFields: string[] = [
                    'member_card_no',
                    'member_account_no',
                    'date_printed',
                ];

                props.memberDataFields.forEach(datafield => {
                    // console.log('client data fields', datafield)
                    if (
                        datafield.field_type === 'Client-required' &&
                        !excludedFields.includes(datafield.field_name)
                    ) {
                        let input_type =
                            datafield.input_type === 'Date Field'
                                ? 'date'
                                : datafield.data_type === 'Numeric'
                                    ? 'number'
                                    : 'text';

                        if (
                            datafield.field_name &&
                            datafield.input_type &&
                            datafield.input_type !== 'Date Field'
                        ) {
                            let strData = datafield.field_name.split('_');
                            if (strData.includes('date') === true) {
                                input_type = 'date';
                            }
                        }

                        client_fields.push({
                            system_name: datafield.system_name,
                            field_name: datafield.field_name,
                            is_required: datafield.is_required,
                            input_type: input_type,
                            is_client_added: datafield.is_client_added,
                            is_editable: true,
                        });
                    }
                });
                console.log('CLIENT REQUIRED FIELDS', client_fields)
                console.log(clientFields)
                setClientFields(client_fields)
            }
        }
    }, [isModalOpen, data, clientData, tableData])

    useEffect(() => {
        console.log('memberType val', memberType)
        console.log('clientInfo val', clientInfo)
        console.log('memberState val', memberState)
        console.log('tableState val', tableState)
    }, [memberType, clientInfo, memberState, tableState])

    const handleViewMember = async (memberId: string) => {
        setIsLoading(true);

        await API.getMemberData(memberId)
            .then(async (res) => {
                if (res && res.error) {
                    console.log('getMemberData modal err', res.error)
                } else if (res) {
                    console.log('getMemberData modal res', res)
                    let memberType: string = res['member_type'] ? res['member_type'] : '';
                    let dataObj: any = null;

                    console.log('member information member type', memberType)
                    if (memberType === 'Principal') {
                        if (res['temp_id']) {
                            await callForPrincipalMember(
                                res['temp_id'],
                                (newDataObj: any) => {
                                    dataObj = newDataObj;
                                }
                            )
                        }
                    } else {
                        if (res['principal_temp_id']) {
                            await callForDependentMember(
                                res['principal_temp_id'],
                                (newDataObj: any) => {
                                    console.log('callForDependentMember modal res', newDataObj)
                                    dataObj = newDataObj;
                                }
                            )
                        }
                    }

                    setMemberType(memberType);
                    setMemberState(res);
                    setTableState(dataObj);
                }
            })
    }

    const clientDataSetter = (clientData: any) => {
        let modifiedData: any = {};

        if (clientData && clientData['client']) {
            let cData: any = clientData['client'];
            modifiedData['clientName'] = cData['registered_name'] ? cData['registered_name'] : null;
            modifiedData['cardName'] = cData['name_on_card'] ? cData['name_on_card'] : null;
        }

        if (clientData && clientData['benefit_plan_tree']) {
            let bptData: any = clientData['benefit_plan_tree'];

            let mctNode: any = null;
            let planTypeNode: any = null;

            if (bptData.length > 0) {
                mctNode = _.find(
                    bptData,
                    item => item.tree_id === 'MCT',
                );
                console.log('mctNode val', mctNode)

                planTypeNode = _.find(
                    bptData,
                    item =>
                        data && data['plan_type'] &&
                        item.name === data['plan_type'] &&
                        item.type === 'PlanType',
                );
                console.log('planTypeNode val', planTypeNode)

                if (!_.isNil(mctNode)) {
                    modifiedData['contractName'] = 'Contract ' + (mctNode.version
                        ? mctNode.version : '1');

                    if (mctNode['custom_metadata'] && mctNode['custom_metadata']['coverage_end_date']
                        && moment(mctNode['custom_metadata']['coverage_end_date']).isValid()) {
                        modifiedData['contractExpiration'] = moment(
                            mctNode['custom_metadata']['coverage_end_date']
                        ).format('MMM DD, YYYY');
                    }
                }

                if (!_.isNil(planTypeNode)) {
                    const rnbNode = _.find(
                        bptData,
                        item =>
                            item.tree_id ===
                            'MCT-CT-' + planTypeNode.code + '-RB',
                    );
                    console.log('rnbNode val', rnbNode)
                    if (!_.isNil(rnbNode)) {
                        const rnbLimits = _.get(rnbNode, 'limits', []);
                        console.log('rnbLimits val', rnbLimits)
                        _.forEach(rnbLimits, rnbLimit => {
                            switch (rnbLimit.type) {
                                case 'ABL':
                                    modifiedData['abl'] = `${
                                        rnbLimit.limit_unit
                                        } ${GlobalFunction.formatNumber(
                                            rnbLimit.limit,
                                        )}`;
                                    break;
                                case 'MBL':
                                    modifiedData['mbl'] = `${
                                        rnbLimit.limit_unit
                                        } ${GlobalFunction.formatNumber(
                                            rnbLimit.limit,
                                        )}`;
                                    break;
                                case 'PEC':
                                    const limitUnit = _.get(
                                        rnbLimit,
                                        'limit_unit',
                                        '',
                                    );
                                    switch (limitUnit) {
                                        case 'PHP':
                                            modifiedData['pec'] = `${
                                                rnbLimit.limit_unit
                                                } ${GlobalFunction.formatNumber(
                                                    rnbLimit.limit,
                                                )}`;
                                            break;

                                        case 'Percentage':
                                            modifiedData['pec'] = `${GlobalFunction.formatNumber(
                                                rnbLimit.limit,
                                            )} %`;
                                            break;

                                        case 'Months':
                                            const timeBasedLimit: any[] = _.get(
                                                rnbLimit,
                                                'time_based_limit',
                                                [],
                                            );
                                            if (timeBasedLimit.length > 0) {
                                                const tblLimits: any[] = [];
                                                _.forEach(timeBasedLimit, tblLimit => {
                                                    tblLimits.push(
                                                        `${GlobalFunction.formatNumber(
                                                            _.get(tblLimit, 'number_month', 0),
                                                        )} ${_.get(
                                                            tblLimit,
                                                            'day_based',
                                                            '',
                                                        )}`,
                                                    );
                                                });
                                                modifiedData['pec'] = tblLimits.join(', ');
                                            }
                                            break;
                                        default:
                                            modifiedData['pec'] = '';
                                    }
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                }
            }
        }

        return modifiedData;
    }

    const callForPrincipalMember = async (tempId: string, callBack: any) => {
        let dependentsArr: any[] = [];
        await fetchMemberDependents(
            tempId,
            (resData: any) => {
                dependentsArr = resData;
            }
        )

        callBack({
            dependents: dependentsArr
        });

        setIsLoading(false);
    }
    const callForDependentMember = async (principalTempId: string, callBack: any) => {
        let principalsArr: any[] = [];
        let dependentsArr: any[] = [];

        await fetchMemberPrincipal(
            principalTempId,
            (resData: any) => {
                principalsArr = resData;
            }
        )
        await fetchMemberDependents(
            principalTempId,
            (resData: any) => {
                dependentsArr = resData;
            }
        )

        callBack({
            principals: principalsArr,
            dependents: dependentsArr,
        });

        setIsLoading(false);
    }

    const fetchMemberDependents = async (tempId: string, callBack: any) => {
        await API.getMemberDependents(tempId)
            .then(res => {
                if (res && res.error) {
                    console.log('getMemberDependents modal err', res.error)
                } else if (res) {
                    console.log('getMemberDependents modal res', res)
                    callBack(res);
                }
            })
    }

    const fetchMemberPrincipal = async (principalTempId: string, callBack: any) => {
        await API.getMemberPrincipal(principalTempId)
            .then(res => {
                if (res && res.error) {
                    console.log('getMemberPrincipal modal err', res.error)
                } else if (res) {
                    console.log('getMemberPrincipal modal res', res)
                    callBack(res);
                }
            })
    }

    const handleClose = () => {
        // reset variables
        setMemberType('Principal');
        setClientInfo(null);
        setMemberState(null);
        setTableState(null);
        // 

        props.onClose();
    }

    let displayData: any = memberState !== null ? memberState : data;
    let displayTableData: any = tableState !== null ? tableState : tableData;

    console.log('displayData val', displayData)
    console.log('displayTableData val', displayTableData)

    return (
        <Dialog
            id={id}
            maxWidth={'md'}
            open={isModalOpen}
            scroll={'paper'}
            aria-labelledby="modal-title"
            className={classes.dialog}
        >
            {isLoading ? (<Loader />) : null}
            <DialogTitle
                className={classes.dialogTitle}
                disableTypography={true}
                id="modal-title"
            >
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <Typography
                            gutterBottom
                            className={classes.dialogTitleText}
                            variant="h1"
                            component="h1"
                        >
                            {`${displayData && displayData['first_name'] ? displayData['first_name'].trim() : ''} 
                            ${displayData && displayData['middle_name'] ? displayData['middle_name'].trim() : ''} 
                            ${displayData && displayData['last_name'] ? displayData['last_name'].trim() : ''}`}
                        </Typography>
                    </Grid>
                    <Grid item xs={12}>
                        <Typography
                            gutterBottom
                            className={classes.dialogTitleSubText}
                            variant="h2"
                            component="h2"
                        >
                            {clientInfo && clientInfo['clientName'] ? clientInfo['clientName'] : '-'}
                        </Typography>
                    </Grid>
                </Grid>
                <IconButton
                    id='verify_information_modal_close_btn'
                    data-cy='verify_information_modal_close_btn'
                    aria-label="close"
                    className={classes.closeButton}
                    onClick={handleClose}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent className={classes.dialogContent}>
                <Grid container spacing={2}>
                    <Grid container item xs={6}>
                        <HMOItem
                            name={'Member Account No.'}
                            value={displayData && displayData['member_account_no'] ? displayData['member_account_no'] : '-'}
                        />
                        <HMOItem
                            name={'Member ID'}
                            value={displayData && displayData['member_id'] ? displayData['member_id'] : '-'}
                        />
                        <HMOItem
                            name={'Contract'}
                            value={clientInfo && clientInfo['contractName'] ? clientInfo['contractName'] : '-'}
                        />
                        <HMOItem
                            name={'Effectivity Date'}
                            value={(() => {
                                const dateValue = displayData && displayData['effectivity_date']
                                    ? displayData['effectivity_date'] : null;

                                if (dateValue !== null && moment(dateValue).isValid()) {
                                    return moment(dateValue).format('MMM DD, YYYY');
                                }

                                return '-';
                            })()}
                        />
                        {/* //1 */}
                        <HMOItem
                            name={'Contract Renewal Date'}
                            value={clientInfo && clientInfo['contractRenewal'] ? clientInfo['contractRenewal'] : '-'}
                        />
                        <HMOItem
                            name={'Contract Expiration Date'}
                            value={clientInfo && clientInfo['contractExpiration'] ? clientInfo['contractExpiration'] : '-'}
                        />
                        <HMOItem
                            name={'Card Printed Date'}
                            value={displayData && displayData['date_printed'] ? displayData['date_printed'] : '-'}
                        />
                        {memberType === 'Principal' ? (
                            <HMOItem
                                name={'Termination Date'}
                                value={(() => {
                                    const dateValue = displayData && displayData['member_termination']
                                        && displayData['member_termination']['termination_date']
                                        ? displayData['member_termination']['termination_date'] : null;

                                    if (dateValue !== null && moment(dateValue).isValid()) {
                                        return moment(dateValue).format('MMM DD, YYYY');
                                    }

                                    return '-';
                                })()}
                            />
                        ) : (
                                <HMOItem
                                    name={'VIP'}
                                    value={displayData && displayData['is_vip'] ? 'Yes' : 'No'}
                                />
                            )}
                    </Grid>
                    <Grid container item xs={6}>
                        <HMOItem
                            name={'Type'}
                            value={displayData && displayData['member_type'] ? displayData['member_type'] : '-'}
                        />
                        <HMOItem
                            name={'Plan Type'}
                            value={displayData && displayData['plan_type'] ? displayData['plan_type'] : '-'}
                        />
                        <HMOItem
                            name={'MBL'}
                            value={clientInfo && clientInfo['mbl'] ? clientInfo['mbl'] : '-'}
                        />
                        <HMOItem
                            name={'ABL'}
                            value={clientInfo && clientInfo['abl'] ? clientInfo['abl'] : '-'}
                        />
                        <HMOItem
                            name={'PEC'}
                            value={clientInfo && clientInfo['pec'] ? clientInfo['pec'] : '-'}
                        />
                        <HMOItem
                            name={'Philhealth Rider'}
                            value={displayData && displayData['is_philhealth_rider'] ? 'Yes' : 'No'}
                        />
                        <HMOItem
                            name={'Member Consent'}
                            value={displayData && displayData['is_member_consent'] ? 'Yes' : 'No'}
                        />
                        {/* //2 */}
                        <HMOItem
                            name={'Ticket ID'}
                            value={'-'}
                        />
                        <HMOItem
                            name={'Batch Process No.'}
                            value={displayData && displayData['batch_name'] ? displayData['batch_name'] : '-'}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={2} style={{ paddingTop: 32, paddingBottom: 0 }}>
                    <Grid container item xs={6}>
                        <HMOItem
                            name={'Date of Birth'}
                            value={(() => {
                                const dateValue = displayData && displayData['date_of_birth']
                                    ? displayData['date_of_birth'] : null;

                                if (dateValue !== null && dateValue !== '' && moment(dateValue).isValid()) {
                                    return moment(dateValue).format('MMM DD, YYYY');
                                }

                                return '-';
                            })()}
                        />
                        <HMOItem
                            name={'Age'}
                            value={(() => {
                                const dateValue = displayData && displayData['date_of_birth']
                                    ? displayData['date_of_birth'] : null;

                                if (dateValue !== null && dateValue !== '' && moment(dateValue).isValid()) {
                                    let memberAge = Math.floor(moment(new Date()).diff(dateValue, 'years', true));
                                    return memberAge <= -1 ? '0' : memberAge.toString();
                                }

                                return '-';
                            })()}
                        />
                        <HMOItem
                            name={'Gender'}
                            value={displayData && displayData['gender'] ? displayData['gender'] : '-'}
                        />
                    </Grid>
                    <Grid container item xs={6}>
                        <HMOItem
                            name={'Civil Status'}
                            value={displayData && displayData['civil_status'] ? displayData['civil_status'] : '-'}
                        />
                        <HMOItem
                            name={'Title/Prefix'}
                            value={displayData && displayData['title'] ? displayData['title'] : '-'}
                        />
                        <HMOItem
                            name={'Suffix'}
                            value={displayData && displayData['suffix'] ? displayData['suffix'] : '-'}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={2} style={{ paddingTop: 32, paddingBottom: 0 }}>
                    <Grid container item xs={6}>
                        {/* 'Enrolled Dependents/Principals' table */}
                        <Grid container item xs={12}>
                            <Typography className={classes.fieldLabel}>
                                {memberType === 'Principal' ? (
                                    <span>Enrolled Dependents</span>
                                ) : (
                                        <span>Principals</span>
                                    )}
                            </Typography>
                        </Grid>
                        <Grid container item xs={12}>
                            <DynamicTable
                                rows={
                                    memberType === 'Principal' ?
                                        displayTableData && displayTableData['dependents'] ? displayTableData['dependents'] : []
                                        : displayTableData && displayTableData['principals'] ? displayTableData['principals'] : []
                                }
                                message={memberType === 'Principal'
                                    ? 'No enrolled dependents' : 'No enrolled principal'}
                                memberTypeHeader={memberType === 'Principal' ? 'Dependent' : 'Principal'}
                                onClickView={handleViewMember}
                            />
                        </Grid>
                    </Grid>
                    <Grid container item xs={6}>
                        {memberType === 'Principal' ? (
                            <>
                                <Grid container item xs={12}>
                                    <Typography className={classes.fieldLabel}>
                                        Remarks
                                    </Typography>
                                </Grid>
                                <Grid container item xs={12}>
                                    <Stepper
                                        activeStep={displayData &&
                                            displayData.member_remarks
                                            ? displayData.member_remarks.length - 1 : 0}
                                        orientation="vertical"
                                        className={displayData &&
                                            displayData.member_remarks && displayData &&
                                            displayData.member_remarks.length > 0
                                            ? classes.remarksStep : classes.noRemarksStep}
                                    >
                                        {displayData &&
                                            displayData.member_remarks &&
                                            displayData.member_remarks.length > 0 ? (
                                                displayData.member_remarks.map((row, index) => (
                                                    <Step active key={index}>
                                                        <StepLabel
                                                            icon={
                                                                <Icon color={displayData.member_remarks.length - 1 === index
                                                                    ? 'primary' : 'default'}><CommentIcon /></Icon>
                                                            }
                                                        >
                                                            <Grid container>
                                                                <Grid item xs={6}
                                                                    style={{
                                                                        display: 'flex',
                                                                        justifyContent: 'flex-start',
                                                                        alignItems: 'center',
                                                                    }}
                                                                >
                                                                    <Typography style={{ display: 'flex', fontWeight: 'bold' }}>
                                                                        {row['date_created']
                                                                            ? moment(row['date_created']).format('MMM DD, YYYY').toString().toUpperCase()
                                                                            : null}
                                                                    </Typography>
                                                                </Grid>
                                                                <Grid item xs={6}
                                                                    style={{
                                                                        display: 'flex',
                                                                        justifyContent: 'flex-end',
                                                                        alignItems: 'center'
                                                                    }}
                                                                >
                                                                    <Icon
                                                                        color={displayData.member_remarks.length - 1 === index
                                                                            ? 'primary' : 'default'}
                                                                        fontSize={'small'}
                                                                        style={{ display: 'flex', marginRight: '5px' }}
                                                                    >
                                                                        <PersonIcon />
                                                                    </Icon>
                                                                    <Typography style={{ display: 'flex', fontWeight: 'bold' }}>
                                                                        {row['user_fullname'] ? row['user_fullname'] : null}
                                                                    </Typography>
                                                                </Grid>
                                                            </Grid>
                                                        </StepLabel>
                                                        <StepContent>
                                                            <Typography>
                                                                {row['remarks'] ? row['remarks'] : null}
                                                            </Typography>
                                                        </StepContent>
                                                    </Step>
                                                ))
                                            ) : (
                                                <Typography
                                                    style={{ display: 'flex' }}
                                                >
                                                    <b>No Remarks</b>
                                                </Typography>
                                            )}
                                    </Stepper>
                                </Grid>
                            </>
                        ) : (
                                // 'Other Dependents' table
                                // NOTE: Exclude the current Dependent from the data to be displayed in the table
                                <>
                                    <Grid container item xs={12}>
                                        <Typography className={classes.fieldLabel}>
                                            <span>Other Dependents</span>
                                        </Typography>
                                    </Grid>
                                    <Grid container item xs={12}>
                                        <DynamicTable
                                            rows={
                                                displayTableData && displayTableData['dependents']
                                                    ? displayTableData['dependents'].map(tData => {
                                                        if (tData['temp_id'] && displayData['temp_id'] && tData['temp_id'] === displayData['temp_id']) {
                                                            return null;
                                                        } else {
                                                            return tData;
                                                        }
                                                    }).filter(currData => currData !== null) : []
                                            }
                                            message={'No other dependents'}
                                            memberTypeHeader={'Dependent'}
                                            onClickView={handleViewMember}
                                        />
                                    </Grid>
                                </>
                            )}
                    </Grid>
                </Grid>

                {/* {memberType === 'Principal' ? ( */}
                <Grid container spacing={2} style={{ paddingTop: 32, paddingBottom: 0 }}>
                    {/* <Grid container item xs={6}>
                        <HMOItem
                            name={'Corporate Acct. Name'}
                            value={clientInfo && clientInfo['clientName'] ? clientInfo['clientName'] : '-'}
                        />
                        <HMOItem
                            name={'Card Name'}
                            value={clientInfo && clientInfo['cardName'] ? clientInfo['cardName'] : '-'}
                        />
                        <HMOItem
                            name={'Employee ID'}
                            value={displayData && displayData['employee_id'] ? displayData['employee_id'] : '-'}
                        />
                        <HMOItem
                            name={'Date of Hire'}
                            value={displayData && displayData['hire_date'] ? displayData['hire_date'] : '-'}
                        />
                        <HMOItem
                            name={'Site Assigned'}
                            value={displayData && displayData['site'] ? displayData['site'] : '-'}
                        />
                        <HMOItem
                            name={'Site Address'}
                            value={displayData && displayData['site_address'] ? displayData['site_address'] : '-'}
                        />
                        <HMOItem
                            name={'Floor'}
                            value={displayData && displayData['floor'] ? displayData['floor'] : '-'}
                        />
                        <HMOItem
                            name={'Designation'}
                            value={displayData && displayData['designation'] ? displayData['designation'] : '-'}
                        />
                    </Grid>

                    <Grid container item xs={6}>
                        <HMOItem
                            name={'Job Grade/Level'}
                            value={displayData && displayData['job_lvl'] ? displayData['job_lvl'] : '-'}
                        />
                        <HMOItem
                            name={'Supervisor'}
                            value={displayData && displayData['supervisor'] ? displayData['supervisor'] : '-'}
                        />
                        <HMOItem
                            name={'Cost Center'}
                            value={displayData && displayData['cost_center'] ? displayData['cost_center'] : '-'}
                        />
                        <HMOItem
                            name={'Financial Code'}
                            value={displayData && displayData['financial_code'] ? displayData['financial_code'] : '-'}
                        />
                        <HMOItem
                            name={'Sub-Company'}
                            value={displayData && displayData['sub_company'] ? displayData['sub_company'] : '-'}
                        />
                        <HMOItem
                            name={'Department'}
                            value={displayData && displayData['department'] ? displayData['department'] : '-'}
                        />
                        <HMOItem
                            name={'Email'}
                            value={displayData && displayData['email_address'] ? displayData['email_address'] : '-'}
                        />
                        <HMOItem
                            name={'Regularization Date'}
                            value={displayData && displayData['regularization_date'] ? displayData['regularization_date'] : '-'}
                        />
                        <HMOItem
                            name={'Expat'}
                            value={displayData && displayData['is_expat'] ? 'Yes' : 'No'}
                        />
                    </Grid> */}
                    <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
                        {leftsideCF}
                    </Grid>
                    <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
                        {rightsideCF}
                    </Grid>
                </Grid>
                {/* ) : null} */}
            </DialogContent>
            <DialogActions className={classes.dialogAction}>
                <Grid container>
                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Button
                            data-cy={'verify_information_modal_close2_btn'}
                            id={'verify_information_modal_close2_btn'}
                            className={classes.closeButton2}
                            variant="contained"
                            color={'primary'}
                            onClick={handleClose}
                        >
                            Close
                        </Button>
                    </Grid>
                </Grid>
            </DialogActions>
        </Dialog>
    )
}
