//#region Global Imports
import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  Grid,
  InputLabel,
  Typography,
  TextField,
  ListItem,
  ListItemText,
  List,
  Button,
  Box,
  Link,
} from '@material-ui/core/';
import Dropzone from 'react-dropzone';
import clsx from 'clsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileUpload, faTimesCircle, faDownload } from '@fortawesome/free-solid-svg-icons';
import { makeStyles } from '@material-ui/core/styles';
import { find, map, isNil, cloneDeep, toLower } from 'lodash';
import * as XLSX from 'xlsx';
// import { CorrectSuffixes } from 'Components/UI/TableComponent/DropdownConstants';
//#endregion Global Imports

//#region Interface Imports
import { TableComponent } from 'Components/UI/TableComponent';
import { AddSelect } from 'Components/UI/AddSelect';
import { ModalComponent } from 'Components/UI/ModalComponent';
import { SnackbarNotification } from 'Components/UI/SnackbarNotification';
import { ConfirmationModalComponent } from 'Components/UI/ConfirmationModalComponent';
import { MissingColumnErrorModal } from './MissingColumnErrorModal';
import { ConflictMemberNumberErrorModal } from './ConflictMemberNumberErrorModal';
import { AddNewDataMapModal } from './AddNewDataMapModal';
import { MemberUploadStatusModal } from './MemberUploadStatusModal';
import { Components } from '@hims/core';
import { TableSelect } from './Table';
//#endregion Interface Imports
// import {
// 	forEach,
// 	// toLower
// } from 'lodash';
import '@hims/core/dist/index.css';

import { API } from 'Pages/API';


type ISelectItem = Components.UI.InputSelect.ISelectItem;

const useStyles = makeStyles({
  listItemTextRoot: {
    flex: 'none',
  },
  listItemTextPrimarySelected: {
    fontSize: 14,
    fontWeight: 700,
    color: 'rgba(44, 46, 142, 1)',
    cursor: 'pointer',
    textDecoration: 'underline',
  },
  listItemTextPrimaryNotSelected: {
    fontSize: 14,
    fontWeight: 400,
    color: 'rgba(39, 46, 76, 0.5)',
    cursor: 'pointer',
  },
  listBorder: {
    borderRight: '1px solid rgba(60, 57, 74, 0.16)',
  },
  error: {
    marginTop: '15px',
    color: '#E53935',
  },
  spanFont: {
    fontWeight: 700,
  },
});

let staticFormattedColumns: any = {
  boolean_field: [
    // 'is_philhealth_rider',
    // 'is_member_consent',
    // 'is_vip',
    'philhealth_rider',
    'member_consent',
    'vip',
    'site'
  ]
}


interface UploadMemberProps {
  case_id: string;
  ticketId: string;
  pmaker_task: string;
  uploaded_data: any;
  column_names: any;
  system_names: any;
  details_data: any;
  corporate_account: string;
  data_maps: any;
  selected_data_map: any;
  gender_data: any;
  suffix_data: any;
  civil_status_data: any;
  plan_type_data: any;
  type_data: any;
  relationship_data: any;
  vip_data: any;
  ph_rider_data: any;
  member_consent_data: any;
  site_data: any;
  branch_options?: any;
  raw_uploaded_data: any;
  add_new_data_map: any;
  rows_start_at: string;
  filtered_raw_data: any;
  client_maps: any;
  updateDetails: (value: Object, attr: String) => void;
  showHideLoader: (flag: boolean) => void;
  updateState: (data: any, attr: string) => void;
  savingNewDataMap: (payload: any) => void;
  updatingDataMap: (values_payload: any) => void;
  plan_type_options: any;
  other_branches?: any[]; 
  civil_status_options: any;
  relationship_options: any;
  site_assigned_options?: any;
  uploadMemberData: any;
  updateUploadMemberData: (key: string, value: any) => void;
  isProcessing: boolean;
  processingStatus: any;
  getProcessingStatus: () => void;
  resumeStatusBgPolling: () => void;
  cancelStatusBgPolling: () => void;
  onCancelProcess?: () => void;
  onBackToDashboard?: () => void;
  onCancelEnrollment?: () => void;
  onConfirmEnrollment?: () => void;
  data?: any | any[];
  page?: any;
  client_id?: any; 
  classes?: any;
  client?: any;
  rows?: [] | null;
  clientInfo?: any;
  proposalInfo?: any;
  clientData?:any; 
  saveSuffixErrorMessage?: (title: string, message: string) => void;
  deleteSuffixErrorMessage?: () => void;
  disable_next?: boolean;
  ocpfile?:any[];
  is_ocp?:boolean;
  default_data_mapping_save_for_now?:any[];
  save_for_now_system_name?:any[];
  clientId?:any;
  system_names2?:any;
  updatedMapping?:any;
  responseData?:any;
  reload_table:boolean;
  renewalType?: string;
}

export const UploadMember: React.FC<UploadMemberProps> = (
  props: UploadMemberProps,
): JSX.Element => { 
  const getUploadMemberData = (key: string): any => {
    return props.uploadMemberData[key];
  };
  const [
    isMemberUploadStatusModalOpen,
    setIsMemberUploadStatusModalOpen,
  ] = useState(false);
  const [ ocpCounter, setOcpCounter] = useState(0);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [confirmModalTitle] = useState('Cancel processing of members');
  const [confirmModalMessage, setConfirmModalMessage] = useState('');
  const [column_names_new, setColumn_names_new] = useState({});
  //  const [column_names_rows, setColumn_names_rows] = useState();
  const [uploaded_data, setUploaded_data] = useState(props.uploaded_data);
  const [uploaded_file_flag, setUploadFileFlag] = useState(false);
  const [selected_filename, setSelectedFileName] = useState<any>(
    getUploadMemberData('selected_filename'),
  );

  const [selectedDataMap,setSelectedDataMap] = useState<any>({
    id:'',
    value:'',
    label:''
  });
  
  const [selectedValues, setSelectedValues] = useState('gender');
  const [openErrorModal, showErrorModal] = useState(false);
  const [upload_error_message, setUploadMessage] = useState('');
  const [upload_error_title, setUploadTitle] = useState('');
  const [openMissingColumnModal, showMissingColumnModal] = useState(false);
  const [missin_column_message, setMissinColumnMessage] = useState<any>(null);
  const [openConflictNumberModal, showConflictNumberModal] = useState(false);
  const [conflict_error_data, setUploadedRowCount] = useState<any>({});
  const [openAddNewDataMapModal, showAddNewDataMap] = useState(false);
  const [isSnackbarNotifOpen, setIsSnackbarNotifOpen] = useState(false);
  // const [branchName, setBranchName]  = useState<any[]>(['', '', '', '']);
  // const [branchListArr, setBranchListArr]  = useState<any[]>([]);
  // const [ branchesGetData, setBranchesGetData]  = useState<any[]>([]);
  const [snackbarNotifMsg] = useState('File successfully uploaded.');
  const [snackbarNotifVariant] = useState('success');
  const [newMapping, setNewMapping] = useState<any>('') // useState<any[]>([]);
  const [displayMapping2, setDisplayMapping2] = useState<any[]>([]);
//   const [isError, setIsError] = React.useState<any>({
//     action_date: false,
//     ticket_no: false,
//     files: false
// })
  // const [isSuffixError, setIsSuffixError] = useState<boolean>(false);

  // const [add_new_data_map, setAddNewDataMap] =  useState<any>({});
  useEffect(() => {
    if(props.clientData && props.clientData.member_data_fields){
      setNewMapping(props.clientData.member_data_fields)
    }
  }, [props.clientData]);

  useEffect(()=>{
    setColumn_names_new(props.column_names)
  },[props.column_names])


  // useEffect(() => {
  //   setColumn_names(props.column_names);
  // }, [props.column_names]);

  useEffect(() => {
    setUploaded_data(props.uploaded_data);
  }, [props.uploaded_data]);

  useEffect(() => {
    if (props.isProcessing) {
      props.getProcessingStatus();
    }
  }, [props.isProcessing]);

  useEffect(() => {
    if (props.processingStatus) {

      const {
        status,
        active_index,
        total_count,
        message,
      } = props.processingStatus;
      
      const { value } = props.details_data.sent_through_channel
      
      if (!isNil(active_index) && !isNil(total_count)) {
        setConfirmModalMessage(
          `${active_index} out of ${total_count} members are already processed. Cancelling this will stop the enrollment and all progress will not be saved. Are you sure you want to cancel?`,
        );
      }

      if (['UPLOADING','UPLOADED', 'ONGOING','INITIAL'].includes(status)) {
        if(value === 'Client Portal'){
          if(ocpCounter === 0){
            setOcpCounter(1);
          }else{
            if(['UPLOADING','UPLOADED', 'ONGOING','INITIAL'].includes(status) && ocpCounter === 1){
              setIsMemberUploadStatusModalOpen(true);
            }
          }
        }else{
          setIsMemberUploadStatusModalOpen(true);
        }
      } else if (status === 'FAILED') {
        setUploadTitle('Processing of Members Failed');
        if (toLower(message) === toLower("Attempt to write outside buffer bounds")) {
          setUploadMessage('Processing of members failed. Please try again.');
        } else {
          setUploadMessage(message);
        }
        setIsMemberUploadStatusModalOpen(false);
        showErrorModal(true);
      } 
    }
  }, [props.processingStatus]);
  
  useEffect(()=>{
    if(props.ocpfile && props.ocpfile.length>0){
      loadOcpFile(props.ocpfile);
    }
  },[props.ocpfile])
  const loadOcpFile = async (file:any) => {
    const fileRes = await fetch(file[0].path)
    const dataFile:any = await fileRes.blob();
    dataFile.name = file[0].name;
    handleFileUpload([dataFile]);
  }
  useEffect(() => {
    if(props.selected_data_map && 
      props.selected_data_map.id &&
      props.selected_data_map.id.length>0 &&
      selectedDataMap.id!==props.selected_data_map.id){
        //Selected Data Map To Given Selected Data Map (No Reload of Data Map Table)
        setSelectedDataMap(props.selected_data_map)
    }
  }, [props.selected_data_map]);

  useEffect(()=>{
    if(props.reload_table) {
      updateValuesState(false,'reload_table');
      reloadTable();
    }
  },[props.reload_table])

  /*
  WARNING!!!!! USE ONLY props.reload_table TO TRIGGER TO 
  MAKE SURE THAT TRIGGER WILL COME FROM PARENT COMPONENT
  THIS MAKE IT EASIER FOR THE SAVE FOR NOW AND TO ENSURE VARIABLE ACCURACY OF THIS COMPONENT
  */
  const reloadTable = () => {
    if (
      props.filtered_raw_data &&
      props.filtered_raw_data.columns &&
      props.filtered_raw_data.data_values &&
      props.filtered_raw_data.rows
    ) {
      generateTable(props.filtered_raw_data);
    }
  }

  useEffect(() => {
    const selectedFilename = getUploadMemberData('selected_filename');
    if (selectedFilename) {
      setUploadFileFlag(true);
    } else {
      setUploadFileFlag(false);
    }
    setSelectedFileName(selectedFilename);
  }, [props.uploadMemberData]);

  // useEffect(() => {
  //   if (props.selected_data_map && Object.keys(props.selected_data_map).length === 0) {
  //     const temp_data: any = {
  //       columns: [
  //         {
  //           name: 'values',
  //           title: 'Values',
  //         },
  //         {
  //           name: 'data_column',
  //           title: 'Data Column',
  //           type: 'select',
  //           options: [],
  //           callback: handleValuesChange,
  //         },
  //       ],
  //       rows: [],
  //     };
  //     updateValuesState(temp_data, 'gender_data');
  //     updateValuesState(temp_data, 'civil_status_data');
  //     updateValuesState(temp_data, 'plan_type_data');
  //     updateValuesState(temp_data, 'type_data');
  //     updateValuesState(temp_data, 'relationship_data');
  //     updateValuesState(temp_data, 'vip_data');
  //     updateValuesState(temp_data, 'ph_rider_data');
  //     updateValuesState(temp_data, 'member_consent_data');
  //     updateValuesState(temp_data, 'site_data');
  //     updateValuesState(temp_data, 'suffix_data');
  //   }
  // }, [props.selected_data_map])

  const updateUploadMemberData = (key: string, value: any) => {
    if (props.updateUploadMemberData) {
      props.updateUploadMemberData(key, value);
    }
  };
  const classes = useStyles();

  const default_columns: any = {
    formattedColumns: {},
    columnExtensions: [],
    columns: [
      {
        name: 'system_name',
        title: 'System Name',
      },
      {
        name: 'sheet_name',
        title: 'Column Header',
      },
    ],
    rows: [
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
      {
        sheet_name: '',
        system_name: '',
      },
    ],
  };

  /****** function that handles uploading of file ******/
  async function handleFileUpload(acceptedFile: any) {
    const validFiles = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv',];
    if (validFiles.includes(acceptedFile[0].type)) {
      if (acceptedFile.length > 0) {
        // if(acceptedFile[0].size > 1000000) {

        // }
        props.showHideLoader(true);
        const reader = new FileReader();
        reader.onabort = () => console.log('file reading was aborted');
        reader.onerror = () => console.log('file reading has failed');
        reader.onload = () => {
          const binaryStr = reader.result;
          const workbook = XLSX.read(binaryStr, {
            type: 'array',
            raw: true,
          });
          const sheet_names = workbook.SheetNames;
          const worksheet = workbook.Sheets[sheet_names[0]];
          const sheetdata: any = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            raw: false,
            blankrows: false,
          });
          /***** check if empty file *****/
          if (sheetdata.length === 0) {
            props.showHideLoader(false);
            let message: string =
              'The file you’re trying to upload is empty ' +
              'or has no available data. Please upload a new one.';
            showErrorModal(true);
            setUploadTitle('No available data in the file');
            setUploadMessage(message);
            return;
          }

          const restrictedChars = [/\$/g, /\./g];
          const headers: any = map(sheetdata[0], header => {
            let vHeader: string = header;
            if (!isNil(vHeader)) {
              for (const idx in restrictedChars) {
                if (!isNil(restrictedChars[idx])) {
                  vHeader = vHeader.replace(restrictedChars[idx], '');
                }
              }
            }

            return vHeader;
          });

          /***** check if uploaded file has an empty header *****/
          if (headers && headers.length > 0) {
            let emptyHeaderCnt: number = 0;
            headers.forEach(column => {
              if (
                column === undefined ||
                column === false ||
                column === null ||
                (column && column.trim() === '')
              ) {
                emptyHeaderCnt++;
              }
            })

            if (emptyHeaderCnt > 0) {
              props.showHideLoader(false);
              let message: string =
                'The file you’re trying to upload has an empty header. ' +
                'Please upload a new one with complete headers.';
              showErrorModal(true);
              setUploadTitle('Empty header found in the file');
              setUploadMessage(message);
              return;
            }
            // const suffixColIndex = headers.findIndex(header => {
            //   return header.toUpperCase().includes("SUFFIX");
            // });
            // if(sheetdata.length > 0){
            //   sheetdata.map((member, index) => {
            //     if(!isNil(member[suffixColIndex]) && member[suffixColIndex].trim() !== '' && !CorrectSuffixes.includes(member[suffixColIndex]) && index > 0){
            //       let message: string = 'One or more uploaded member have suffix values that are unaccepted. Kindly adjust and reupload from among the accepted values: Sr., Jr., I, II, III, IV, V';
            //       showErrorModal(true);
            //       setUploadTitle('Submission Failed');
            //       setUploadMessage(message);
            //       setIsSuffixError(true);
            //       if (props.saveSuffixErrorMessage) { props.saveSuffixErrorMessage('Submission Failed', message); } 
            //       reader.onabort = () => console.log('file reading was aborted');
              
            //         setIsError({
            //             ...isError,
            //             files: false,
            //         })
            //       // return;
            //       setUploadMessage(message);
            //       return;
                 
            //     }
            //   })
            // }
            
          }

          sheetdata.shift();

          let raw_data: any = {
            columns: headers,
            rows: sheetdata,
            data_values: {},
          };
          updateValuesState('', 'rows_start_at');
          raw_data = getValuesData(headers, sheetdata, raw_data);
          /***** check if uploaded file meets the no. of members set *****/
          if (props.details_data && props.details_data['isOcp']
            && props.details_data['number_of_members']) {
            let memberLimit: any = props.details_data['number_of_members'];

            if (!isNaN(memberLimit)) {
              memberLimit = parseInt(memberLimit);
            } else {
              memberLimit = 0;
            }

            if (memberLimit !== 0 &&
              raw_data['rows'] && raw_data['rows'].length !== memberLimit) {
              props.showHideLoader(false);
              let message: string =
                'The file you’re trying to upload does not meet the number of members set. ' +
                'Please upload a new one with the correct number of members.';
              showErrorModal(true);
              setUploadTitle('Incorrect number of members in the file');
              setUploadMessage(message);
              return;
            }
          }

          /***** check if uploaded file is empty *****/
          if (raw_data['rows'] && raw_data['rows'].length === 0) {
            props.showHideLoader(false);
            let message: string =
              'The file you’re trying to upload is empty ' +
              'or has no available data. Please upload a new one.';
            showErrorModal(true);
            setUploadTitle('No available data in the file');
            setUploadMessage(message);
            return;
          }

          /***** check uploaded file and system names *****/
          let system_name_cnt: number = 0;
          for (var cnt in props.system_names) {
            const system_name = props.system_names[cnt];
            if (system_name['is_required'] === true) {
              system_name_cnt++;
            }
          }
          if (headers.length < system_name_cnt) {
            props.showHideLoader(false);
            showMissingColumnModal(true);
            setMissinColumnMessage(
              <div>
                <span>
                  The file you’re trying to upload has less columns than the
                required data types. Please upload again with at least{' '}
                  <span className={classes.spanFont}>
                    {system_name_cnt} columns.
                </span>
                </span>
              </div>,
            );
            return;
          }
          /***** check number of members and uploaded file *****/
          /***** remove validation for number of members *****/
          // if(parseInt(props.details_data["number_of_members"]) != raw_data["rows"].length){
          //     setUploadedRowCount({
          //         number_of_members: parseInt(props.details_data["number_of_members"]),
          //         rows: raw_data["rows"].length
          //     });
          //     showConflictNumberModal(true);
          // }

          props.showHideLoader(false);
          updateValuesState(raw_data, 'raw_uploaded_data');
          if (props.rows_start_at) {
            const row_cnt: number = parseInt(props.rows_start_at);
            let temp_raw_data: any = {
              rows: [],
              columns: raw_data['columns'],
              data_values: {},
            };
            if (row_cnt > 0) {
              const temp_cnt = row_cnt - 1;
              if (temp_cnt < raw_data['rows'].length) {
                temp_raw_data['rows'] = JSON.parse(
                  JSON.stringify(raw_data['rows']),
                );
                temp_raw_data['rows'].splice(0, temp_cnt);
                temp_raw_data = getValuesData(
                  headers,
                  temp_raw_data['rows'],
                  temp_raw_data,
                );
              }
            }
            updateValuesState(temp_raw_data, 'filtered_raw_data');
          } else {
            updateValuesState(raw_data, 'filtered_raw_data');
          }

          updateUploadMemberData('selected_filename', acceptedFile[0]['name']);
          setUploadFileFlag(true);
          setIsSnackbarNotifOpen(true);
        };
        reader.readAsArrayBuffer(acceptedFile[0]);
        
      }
    } else {
      props.showHideLoader(false);
      let message: string =
        'The file you’re trying to upload is ' +
        'not supported.';
      showErrorModal(true);
      setUploadTitle('File not supported');
      setUploadMessage(message);
      return;
    }
  }

  /****** function that get unique values in every column of uploaded file ******/
  function getValuesData(headers: any, sheetdata: any, raw_data: any) {
    for (var col_cnt in headers) {
      const col = headers[col_cnt];
      if (raw_data['data_values'][col] === undefined) {
        raw_data['data_values'][col] = [];
      }
      for (var row_cnt in sheetdata) {
        if (
          sheetdata[row_cnt][col_cnt] !== undefined &&
          sheetdata[row_cnt][col_cnt] !== null &&
          sheetdata[row_cnt][col_cnt] !== '' &&
          raw_data['data_values'][col].indexOf(sheetdata[row_cnt][col_cnt]) ===
          -1
        ) {
          raw_data['data_values'][col].push(sheetdata[row_cnt][col_cnt]);
        }
      }
    }
    return raw_data;
  }

  /****** function that generate object for tables ******/
  function generateTable(raw_data: any) {
    let headers = raw_data['columns'];
    let rows = raw_data['rows'];
    let temp_tables: any = {
      formattedColumns: {},
      columnExtensions: [],
      columns: [],
      rows: [],
    };
    let temp_columns: any = {
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'sheet_name',
          title: 'Column Header',
          type: 'select',
          options: [
            {
              id: '',
              value: '',
              label: '',
            },
          ],
        },
      ],
      rows: [],
    };
    /***** generate columns for preview of file ******/
    for (var header_cnt in headers) {
      // const col_cnt = parseInt(header_cnt) + 1;
      // const col_name = "column_"+ col_cnt;
      if (headers[header_cnt]) {
        const col_name = headers[header_cnt].replace(' ', '_').toLowerCase();
        temp_tables['columns'].push({
          name: col_name,
          title: headers[header_cnt],
        });
        temp_tables['columnExtensions'].push({
          columnName: col_name,
          wordWrapEnabled: true,
        });
        temp_columns['columns'][1]['options'].push({
          id: headers[header_cnt],
          value: headers[header_cnt],
          label: headers[header_cnt],
        });
      }
    }

    /***** generate table for columns table ******/
    const tables: any = generateColumnsTable(
      temp_columns,
      raw_data,
      temp_tables,
      raw_data['rows'] && raw_data['rows'].length > 0 ? true : false,
    );
    temp_columns = tables['temp_table'];
    temp_tables = tables['temp_tables'];
    /***** generate rows for preview of file ******/
    for (var row_cnt in rows) {
      let temp_row: any = {};
      for (var col_cnt in temp_tables['columns']) {
        temp_row[temp_tables['columns'][col_cnt]['name']] =
          rows[row_cnt][col_cnt];
      }
      temp_tables['rows'].push(temp_row);
    }
    updateValuesState(temp_tables, 'uploaded_data');
    updateValuesState(temp_columns, 'columns_data');
  }

  /****** function that generate object for columns table ******/
  function generateColumnsTable(
    temp_table: any,
    raw_data: any,
    temp_upload_table: any,
    isFileUploaded?: boolean,
  ) {
    const values_array: any = [
      'Gender',
      'Civil Status',
      'Plan Type',
      'Type',
      'Relationship to Principal',
      'VIP',
      'Philhealth Rider',
      'Member Consent',
      'Site',
      'Suffix'
    ];
    let columnsData: any = [];
    const dataMapsProps = cloneDeep(props.data_maps);
    if (props.column_names) {
      columnsData = props.column_names.rows;
    }

    const getColumnData = systemName => {
      return columnsData.find(
        columnData => columnData.system_name === systemName,
      );
    };

    // const matched = (headers, target) => {
    //   const found = find(headers, header => {
    //     const lowerTarget = lowerCase(target);
    //     const lowerHeader = lowerCase(header);
    //     return (
    //       lowerTarget === lowerHeader ||
    //       replace(lowerTarget, ' ', '_') === lowerHeader
    //     );
    //   });

    //   return found !== null && found !== undefined;
    // };

    if (selectedDataMap && selectedDataMap.id && selectedDataMap.id.length>0) {
      let data_item = find(
        dataMapsProps,
        data => data['_id'] === selectedDataMap.id,
      );
      let modifiedColumnsMapping: any[] = [];
      if (props.column_names && props.column_names.rows) {
        modifiedColumnsMapping = props.column_names.rows;
      }

      const isInModifiedColumnsMapping = (target: any) => {
        return find(modifiedColumnsMapping, mapping => {
          return mapping.system_name === target.system_name;
        });
      };
      if (data_item && raw_data.columns) {
        let required_fields: any = [];
        setDisplayMapping2(data_item['mapping']);
        if(props && props.system_names.length > 0) {
          for (const memberDataField of props.system_names) {
            let sheet_name: string = '';
            let sheet_field: string = '';
            if(data_item && data_item.mapping && Array.isArray(data_item.mapping)){
              const datMap:any[] = [...data_item.mapping];
              const chkDatMap = datMap.find(item=>item.system_name===memberDataField.system_name);
              if(chkDatMap){
                sheet_name = chkDatMap.sheet_name;
                const columnData = getColumnData(sheet_name);
                if (columnData) {
                  sheet_name = columnData.sheet_name;
                }
              } else {
                const modifiedColumnMapping = isInModifiedColumnsMapping(map);
                if (!isNil(modifiedColumnMapping)) {
                  sheet_name = modifiedColumnMapping.sheet_name;
                }
              }
              if (values_array.indexOf(memberDataField['system_name']) !== -1) {
                generateValuesTable(
                  memberDataField['system_name'],
                  sheet_name,
                  raw_data,
                  data_item ? JSON.parse(JSON.stringify(data_item)) : undefined,
                  isFileUploaded,
                  sheet_field
                );
              }
              sheet_field = sheet_name.replace(' ', '_').toLowerCase();
              if (sheet_name.length>0 && memberDataField['is_required'] === true) {
                const col_name = sheet_name.replace(' ', '_').toLowerCase();
                required_fields.push(col_name);
              }
              
            }
            temp_table['rows'].push({
              system_name: memberDataField['system_name'],
              field_name: memberDataField['field_name'],
              sheet_name: sheet_name,
              sheet_field: sheet_field,
              required: memberDataField['is_required'],
            });
          } 
        }
        
        if (
          Object.keys(temp_upload_table).length > 0 &&
          required_fields.length > 0
        ) {
          temp_upload_table['formattedColumns'][
            'required_columns'
          ] = required_fields;
        } else if (Object.keys(temp_upload_table).length > 0) {
          temp_upload_table['formattedColumns'] = {};
        }
      }
    } 
    // else {
    //   for (const i in props.system_names) {
    //     const system_name = props.system_names[i];
    //     temp_table['rows'].push({
    //       system_name: system_name['system_name'],
    //       field_name: system_name['field_name'],
    //       sheet_name: '',
    //       sheet_field: '',
    //       required: system_name['is_required'],
    //     });
    //   }
    // }
    return {
      temp_table: temp_table,
      temp_tables: temp_upload_table,
    };


  }

  function updateValuesState(data: any, attr: string) {
    props.updateState(data, attr);
  }

  /****** function that generate object for values tables ******/
  function generateValuesTable(
    system_name: string,
    sheet_name: string,
    raw_data: any,
    data_map?: any,
    isFileUploaded?: boolean,
    sheet_field?:string
  ) {
    
    const temp_data: any = {
      columns: [
        {
          name: 'values',
          title: 'Values',
        },
        {
          name: 'data_column',
          title: 'Data Column',
          type: 'select',
          options: [],
          callback: handleValuesChange,
        },
      ],
      rows: [],
    };

    switch (system_name.toLowerCase()) {
      case 'gender':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const genders: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'Female',
              label: 'Female',
            },
            {
              id: '',
              value: 'Male',
              label: 'Male',
            },
            {
              id: '',
              value: 'Unspecified',
              label: 'Unspecified',
            },
          ];
          temp_data['columns'][1]['options'] = genders;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.gender_data) {
                const { rows: dataRows } = props.gender_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }

              const option_value = genders.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['gender_data']) {
          modifySavedValues(temp_data, data_map['values'], 'gender_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'gender_data');
        }
        break;
      case 'civil status':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          /* Removed
          let civil_statuses: any = [
            {
              id: '',
              value: 'no_data_available',
              label: 'No civil status available',
            },
          ];

          const { civil_status_options } = props;
          if (civil_status_options && civil_status_options.length > 0) {
            civil_statuses = [
              {
                id: '',
                value: '',
                label: '',
              },
              ...civil_status_options,
            ];
          }
          */
          const civil_statuses: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'Single',
              label: 'Single',
            },
            {
              id: '',
              value: 'Married',
              label: 'Married',
            },
            {
              id: '',
              value: 'Single Parent',
              label: 'Single Parent',
            },
            {
              id: '',
              value: 'Annulled',
              label: 'Annulled',
            },
            {
              id: '',
              value: 'Widowed',
              label: 'Widowed',
            },
            {
              id: '',
              value: 'Divorced',
              label: 'Divorced',
            },
            {
              id: '',
              value: 'Separated',
              label: 'Separated',
            },
          ];

          temp_data['columns'][1]['options'] = civil_statuses;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.civil_status_data) {
                const { rows: dataRows } = props.civil_status_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }
              const option_value = civil_statuses.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['civil_status_data']) {
          modifySavedValues(temp_data, data_map['values'], 'civil_status_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'civil_status_data');
        }
        break;
      case 'plan type':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          let plan_types: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'default_plantype_placeholder',
              label: 'Default',
            },
          ];
          const { plan_type_options } = props;
          if (plan_type_options && plan_type_options.length > 0) {
            plan_types = [
              {
                id: '',
                value: '',
                label: '',
              },
              ...plan_type_options,
            ];
          }
          temp_data['columns'][1]['options'] = plan_types;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.plan_type_data) {
                const { rows: dataRows } = props.plan_type_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );
                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }
              const option_value = plan_types.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
              
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['plan_type_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'plan_type_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'plan_type_data');
        }
        break;
      case 'type':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const types: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'Principal',
              label: 'Principal',
            },
            {
              id: '',
              value: 'Dependent',
              label: 'Dependent',
            },
            {
              id: '',
              value: 'Extended Dependent',
              label: 'Extended Dependent',
            },
          ];
          temp_data['columns'][1]['options'] = types;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.type_data) {
                const { rows: dataRows } = props.type_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }
              const option_value = types.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'] === values[t], // Dec 21, 2020: Fix for Gitlab Issue #1397
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['type_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'type_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'type_data');
        }
        break;
      case 'relationship to principal':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          /* Removed
          let relationships: any = [
            {
              id: '',
              value: 'no_data_available',
              label: 'No relationship available',
            },
          ];

          const { relationship_options } = props;
          if (relationship_options && relationship_options.length > 0) {
            relationships = [
              {
                id: '',
                value: '',
                label: '',
              },
              ...relationship_options,
            ];
          }
          */
          const relationships: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'Mother',
              label: 'Mother',
            },
            {
              id: '',
              value: 'Father',
              label: 'Father',
            },
            {
              id: '',
              value: 'Brother',
              label: 'Brother',
            },
            {
              id: '',
              value: 'Sister',
              label: 'Sister',
            },
            {
              id: '',
              value: 'Husband',
              label: 'Husband',
            },
            {
              id: '',
              value: 'Wife',
              label: 'Wife',
            },
            {
              id: '',
              value: 'Son',
              label: 'Son',
            },
            {
              id: '',
              value: 'Daughter',
              label: 'Daughter',
            },
            {
              id: '',
              value: 'Grandmother',
              label: 'Grandmother',
            },
            {
              id: '',
              value: 'Grandfather',
              label: 'Grandfather',
            },
            {
              id: '',
              value: 'Grandson',
              label: 'Grandson',
            },
            {
              id: '',
              value: 'Granddaughter',
              label: 'Granddaughter',
            },
            {
              id: '',
              value: 'Common Law Partner',
              label: 'Common Law Partner',
            },
            {
              id: '',
              value: 'Same Sex Partner',
              label: 'Same Sex Partner',
            },
            {
              id: '',
              value: 'Auntie',
              label: 'Auntie',
            },
            {
              id: '',
              value: 'Uncle',
              label: 'Uncle',
            },
            {
              id: '',
              value: 'Niece',
              label: 'Niece',
            },
            {
              id: '',
              value: 'Nephew',
              label: 'Nephew',
            },
            {
              id: '',
              value: 'Cousin',
              label: 'Cousin',
            },
            {
              id: '',
              value: 'Household Help',
              label: 'Household Help',
            },
          ];

          temp_data['columns'][1]['options'] = relationships;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.relationship_data) {
                const { rows: dataRows } = props.relationship_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }
              const option_value = relationships.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }

            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['relationship_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'relationship_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'relationship_data');
        }
        break;
      case 'vip':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const vip_options: any = [
            {
              id: '',
              value: '',
              label: '',
            }, 
            {
              id: '',
              value: true,
              label: 'Yes',
            },
            {
              id: '',
              value: false,
              label: 'No',
            },
          ];
          temp_data['columns'][1]['options'] = vip_options;
          if (values) {
            for (const t in values) {
              if (values[t] === true || values[t] === 'true') {
                values[t] = 'Yes';
              } else if (values[t] === false || values[t] === 'false') {
                values[t] = 'No';
              }

              let valueForComparison = values[t].toLowerCase();
              if (props.vip_data) {
                const { rows: dataRows } = props.vip_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toString().toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toString().toLowerCase() : valueForComparison;
                  }
                }
              }

              const option_value = vip_options.find(
                data => data['label'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'] === values[t],
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['vip_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'vip_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'vip_data');
        }
        break;
      case 'philhealth rider':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const ph_rider_options: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: true,
              label: 'Yes',
            },
            {
              id: '',
              value: false,
              label: 'No',
            },
          ];
          temp_data['columns'][1]['options'] = ph_rider_options;
          if (values) {
            for (const t in values) {
              if (values[t] === true || values[t] === 'true') {
                values[t] = 'Yes';
              } else if (values[t] === false || values[t] === 'false') {
                values[t] = 'No';
              }

              let valueForComparison = values[t].toLowerCase();
              if (props.ph_rider_data) {
                const { rows: dataRows } = props.ph_rider_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toString().toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toString().toLowerCase() : valueForComparison;
                  }
                }
              }

              const option_value = ph_rider_options.find(
                data => data['label'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'] === values[t],
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['ph_rider_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'ph_rider_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'ph_rider_data');
        }
        break;
      case 'member consent':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const member_consent_options: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: true,
              label: 'Yes',
            },
            {
              id: '',
              value: false,
              label: 'No',
            },
          ];
          temp_data['columns'][1]['options'] = member_consent_options;
          if (values) {
            for (const t in values) {
              if (values[t] === true || values[t] === 'true') {
                values[t] = 'Yes';
              } else if (values[t] === false || values[t] === 'false') {
                values[t] = 'No';
              }

              let valueForComparison = values[t].toLowerCase();
              if (props.member_consent_data) {
                const { rows: dataRows } = props.member_consent_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toString().toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toString().toLowerCase() : valueForComparison;
                  }
                }
              }

              const option_value = member_consent_options.find(
                data => data['label'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'] === values[t],
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          } 
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['member_consent_data']) 
        {
          modifySavedValues(temp_data, data_map['values'], 'member_consent_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'member_consent_data');
        }
        break;
      case 'site':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          let site_options: any = []
          let other_branches: any = []
          let addOptions: any = {}
          let defaultBranchAddress = ''
          if(props.clientData){
            const { main_office_address, branches } = props.clientData;
            if(main_office_address && main_office_address.street){
              defaultBranchAddress = `${main_office_address.street}, ${main_office_address.brgy}, ${main_office_address.city}`
            }
            if(branches){
              other_branches = branches.map((branch, idx) => {
                const otherBranchAddress = `${branch.street},   ${branch.brgy},  ${branch.city}`;
                let branchNum2 = Number(idx) + 2;
                addOptions = {
                    id: idx,
                    value: `Branch ${branchNum2} - ${otherBranchAddress}`, 
                    label: `Branch ${branchNum2} - ${otherBranchAddress}`
                  }
                return addOptions
              })
            }
          }
          site_options = [
            {
              id: '',
              value: '',
              label: '', 
            },
          ];
          if(other_branches && other_branches.length > 0) {
            site_options = [
              {
                id: '',
                value: '',
                label: ''
              },
              {
                id: '',
                value: 'Head Office - ' + defaultBranchAddress,
                label: 'Head Office - ' + defaultBranchAddress  //+  defaultBranch       
              },
              ...other_branches,
            ]
          } else {
            site_options = [
              {
                id: '',
                value: '',
                label: ''
              }, 
              {
                id: '',
                value: 'Head Office - ' +  defaultBranchAddress,
                label: 'Head Office - ' +  defaultBranchAddress
              }
            ]
          }
          temp_data['columns'][1]['options'] = site_options;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.site_data) {
                const { rows: dataRows } = props.site_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }
              const option_value = site_options.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['site_data']) {
          modifySavedValues(temp_data, data_map['values'], 'site_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'site_data');
        }
        break;
      case 'suffix':
        if (raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]) {
          const values: any = raw_data['data_values'][sheet_name];
          const suffixes: any = [
            {
              id: '',
              value: '',
              label: '',
            },
            {
              id: '',
              value: 'Sr.',
              label: 'Sr.',
            },
            {
              id: '',
              value: 'Jr.',
              label: 'Jr.',
            },
            {
              id: '',
              value: 'I',
              label: 'I',
            },
            {
              id: '',
              value: 'II',
              label: 'II',
            },
            {
              id: '',
              value: 'III',
              label: 'III',
            },
            {
              id: '',
              value: 'IV',
              label: 'IV',
            },
            {
              id: '',
              value: 'V',
              label: 'V',
            },
          ];
          temp_data['columns'][1]['options'] = suffixes;
          if (values) {
            for (const t in values) {
              let valueForComparison = values[t].toLowerCase();
              if (props.suffix_data) {
                const { rows: dataRows } = props.suffix_data;
                if (dataRows && dataRows.length > 0) {
                  const dataMapping = dataRows.find(
                    dataRow => dataRow['values'].toLowerCase() === valueForComparison,
                  );

                  if (dataMapping) {
                    valueForComparison = dataMapping['data_column']
                      ? dataMapping['data_column'].toLowerCase() : valueForComparison;
                  }
                }
              }

              const option_value = suffixes.find(
                data => data['value'].toLowerCase() === valueForComparison,
              );
              let data_column: string = '';
              if (option_value) {
                data_column = option_value['value'];
              }
              const isInList = temp_data['rows'].some(
                option => option['values'].toLowerCase() === values[t].toLowerCase(),
              );
              if (!isInList && values[t].trim() != "") {
                temp_data['rows'].push({
                  values: values[t],
                  data_column: data_column,
                });
              }
            }
          }
        }
        if(sheet_field){
          temp_data['sheet_field'] = sheet_field;
        }
        if (isFileUploaded && data_map && data_map['values'] &&
          data_map['values']['suffix_data']) {
          modifySavedValues(temp_data, data_map['values'], 'suffix_data', sheet_name, raw_data);
        } else {
          updateValuesState(temp_data, 'suffix_data');
        }
        break;
    }
  }

  function modifySavedValues(
    temp_data: any,
    data: any,
    attr: string,
    // system_name: string,
    sheet_name: string,
    raw_data: any,
  ) {
    let savedData: any = data[attr];
    if (
      savedData['rows'] && savedData['rows'].length > 0
      && raw_data && raw_data['data_values'] && raw_data['data_values'][sheet_name]
    ) {
      let options:any[]=[{id:'',value:'',label:''}];

      if(temp_data.columns && temp_data.columns.length>1 && temp_data.columns[1].options){
        options = temp_data.columns[1].options;
      }
      for(const idx in temp_data['rows']){
        const row = temp_data['rows'][idx];
        let value:string = row.data_column;
        const chkSavedValue = savedData['rows'].find(item=>item.values===row.values);
        //Check If There is a already saved Value
        if(chkSavedValue){
          //Check if the value exists in the sheet
          let dataIdx: number = options.indexOf(item=>item.value===chkSavedValue.data_column);
          if(dataIdx>-1){
            //Assign if all conditions passed
            value = chkSavedValue.data_column;
          }
        }
        temp_data['rows'][idx].data_column = value;
      }
      // remove saved values when they are not present in the current uploaded file 
      // temp_data['rows'] = temp_data['rows'].map(item => {
      //   if (item['values']) {
      //     let dataIdx: number = sheet_values.indexOf(item['values']);
      //     if (dataIdx !== -1) {
      //       return item;
      //     } else {
      //       return null;
      //     }
      //   } else {
      //     return item;
      //   }
      // }).filter(currData => currData !== null);
      // 

      // if (savedData['rows'].length > 0) {
      //   if (savedData['columns'] && savedData['columns'][1]) {
      //     savedData['columns'][1]['callback'] = handleValuesChange; // imitated default value for callback in generateValuesTable()
      //   }
      //   updateValuesState(savedData, attr);
      // } else {
      //   updateValuesState(temp_data, attr);
      // }
    }
    updateValuesState(temp_data, attr);
  }

  /****** function that handles onchange of data map select ******/
  function handleDataMapChange(item: ISelectItem) {
    // setIsDataMapped(true);
    if (item.value !== 'add') {
      if(item.id!==selectedDataMap.id){
        setSelectedDataMap(item);
        updateValuesState(item, 'selected_data_map');
      }
    } else {
      generateAddNewDataMap();
      showAddNewDataMap(true);
    }
  }

  /****** function that generate object data for add new data map ******/
  function generateAddNewDataMap() {
    let temp_columns: any = {
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
          minWidth: '100px',
        },
        {
          name: 'sheet_name',
          title: 'Column Header',
          type: 'select',
          options: [
            {
              id: '',
              value: '',
              label: '',
            },
          ],
        },
      ],
      rows: [],
    };

    //set system_name here
    for (var i in props.system_names) {
      const system_name = props.system_names[i];
      temp_columns['rows'].push({
        system_name: system_name['system_name'],
        field_name: system_name['field_name'],
        sheet_name: '',
        sheet_field: '',
      });
    }

    let headers = props.raw_uploaded_data['columns'];
    for (var header_cnt in headers) {
      if (headers[header_cnt]) {
        temp_columns['columns'][1]['options'].push({
          id: headers[header_cnt],
          value: headers[header_cnt],
          label: headers[header_cnt],
        });
      }
    }
    updateValuesState(temp_columns, 'add_new_data_map');
  }

  /****** function that handles onchange in select in tables ******/
  function handleValuesChange(value: string, row: string, table_name: string) {
    let data: any = {};
    switch (table_name) {
      case 'column names':
        data = props.column_names;
        let uploaded_column:any = {
          name:'',
          title:''
        };
        if(props.uploaded_data && props.uploaded_data.columns){
          const chkUploadedData = props.uploaded_data.columns.find(item=>item.title && item.title===value);
          if(chkUploadedData){
            uploaded_column = chkUploadedData;
          }
          
        }

        data['rows'][parseInt(row)]['sheet_name'] = uploaded_column.title;
        data['rows'][parseInt(row)]['sheet_field'] = uploaded_column.name;

        let datamap_item: any = undefined;
        if (selectedDataMap && selectedDataMap.id) {
          const dataMapsProps = cloneDeep(props.data_maps);
          let data_item = find(
            dataMapsProps,
            data => data['_id'] === selectedDataMap.id,
          );
          datamap_item = data_item;
        }

        if( data['rows'] && data['rows'][parseInt(row)]) {
          generateValuesTable(
            data['rows'][parseInt(row)]['system_name'],
            data['rows'][parseInt(row)]['sheet_name'],
            props.filtered_raw_data,
            datamap_item ? JSON.parse(JSON.stringify(datamap_item)) : undefined,
            uploaded_file_flag,
            data['rows'][parseInt(row)]['sheet_field']
          );
          updateValuesState(data, 'columns_data');
        } else {

          generateValuesTable(
            data['rows'][parseInt(row)]['system_name'],
            data['rows'][parseInt(row)]['sheet_name'],
            props.filtered_raw_data,
            datamap_item ? JSON.parse(JSON.stringify(datamap_item)) : undefined,
            uploaded_file_flag,
            // data['rows'][parseInt(row)]['sheet_field']
          );
        }
       
       

        if (data['rows'][parseInt(row)] &&
             data['rows'][parseInt(row)]['required'] !== undefined && 
              data['rows'][parseInt(row)]['required'] === true) {
          let required_fields: any = [];
          for (const i in data['rows']) {
            const data_row = data['rows'][i];
            if (data_row['required'] === true && data_row['sheet_name']) {
              const col_name = data_row['sheet_name']
                .replace(' ', '_')
                .toLowerCase();
              required_fields.push(col_name);
            }
          }

          const upload_data: any = props.uploaded_data;
          if (Object.keys(upload_data).length > 0) {
            upload_data['formattedColumns'] = {};
            if (required_fields.length > 0) {
              upload_data['formattedColumns'][
                'required_columns'
              ] = required_fields;
            }
            updateValuesState(upload_data, 'uploaded_data');

         
          }
        } else {
          let required_fields: any = [];
          for (const i in  props.uploaded_data['rows']) {
            const data_row =  props.uploaded_data['rows'][i];
            if (data_row['required'] === true && data_row['sheet_name']) {
              const col_name = data_row['sheet_name']
                .replace(' ', '_')
                .toLowerCase();
              required_fields.push(col_name);
            }
          }

          const upload_data: any = props.uploaded_data;
          if (Object.keys(upload_data).length > 0) {
            upload_data['formattedColumns'] = {};
            if (required_fields.length > 0) {
              upload_data['formattedColumns'][
                'required_columns'
              ] = required_fields;
            }
            updateValuesState(upload_data, 'uploaded_data');
          }
        }
        break;
      case 'gender':
        data = props.gender_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'gender_data');
        break;
      case 'civil status':
        data = props.civil_status_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'civil_status_data');
        break;
      case 'plan type':
        data = props.plan_type_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'plan_type_data');
        break;
      case 'type':
        data = props.type_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'type_data');
        break;
      case 'relationship':
        data = props.relationship_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'relationship_data');
        break;
      case 'vip':
        data = props.vip_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'vip_data');
        break;
      case 'philhealth rider':
        data = props.ph_rider_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'ph_rider_data');
        break;
      case 'member consent':
        data = props.member_consent_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'member_consent_data');
        break;
      case 'site':
        data = props.site_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'site_data');
        break;
      case 'suffix':
        data = props.suffix_data;
        data['rows'][parseInt(row)]['data_column'] = value;
        updateValuesState(data, 'suffix_data');
        break;
    }

  }

  /****** dummy function ******/
  function handleClickRow() { }


  function handleClickFile(data: any, suspensionGenerateTb2Report: any) {
    if(props.is_ocp){
      if(props.ocpfile){
        const file_url = props.ocpfile[0].path;
        const file_name = props.ocpfile[0].name;
        const file_type = props.ocpfile[0].type;
        API.downloadOcpFile(file_url)
        .then(res=>{
          download(res,file_name,file_type); 
        });
      }
    } else {
      const csvData = objectToCsv(data, suspensionGenerateTb2Report);
      download(csvData, selected_filename);
    }
  }

  const objectToCsv = (data, tableHeaders) => {
    const csvRows: any = [];

    // set table headers
    const table_headers = Object.values(tableHeaders);

    csvRows.push(table_headers.join(','));

    // get the headers
    const headers = Object.keys(data[0]);

    // loop over the rows
    for (const row of data) {
      const values = headers.map(header => {
        const escaped = ('' + row[header]).replace(/"/g, '\\"');
        return `"${escaped}"`;
      })
      csvRows.push(values.join(','));
    }
    return csvRows.join('\n');
  }

  const download = (data, filename: string,type?:string) => {
    if(type===undefined) type = 'text/csv'
    const blob = new Blob([data], { type:  type});
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
  /****** function that handles delete of file ******/
  function handleDeleteFile() {
    setUploadFileFlag(false);
    let temp_tables: any = {
      rows: [],
      columns: [],
      formattedColumns: {},
    };
    let temp_columns: any = {
      columns: [
        {
          name: 'system_name',
          title: 'System Name',
        },
        {
          name: 'sheet_name',
          title: 'Column Header',
          type: 'select',
          options: [
            {
              id: '',
              value: '',
              label: '',
            },
          ],
        },
      ],
      rows: [],
    };
    const raw_data: any = {
      columns: [],
      rows: [],
      data_values: {},
    };
    const filtered_raw_data: any = {};
    const tables: any = generateColumnsTable(
      temp_columns,
      raw_data,
      temp_tables,
      selectedDataMap,
    );
    
    temp_columns = tables['temp_table'];
    temp_tables = tables['temp_tables'];
  
    updateValuesState(raw_data, 'raw_uploaded_data');
    updateValuesState(filtered_raw_data, 'filtered_raw_data');
    updateValuesState(temp_tables, 'uploaded_data');
    updateValuesState(temp_columns, 'columns_data');
    // updateValuesState('', 'rows_start_at');
    updateUploadMemberData('selected_filename', null);

    // if (isSuffixError) {
    //   if (props.deleteSuffixErrorMessage) {props.deleteSuffixErrorMessage()}
    // }
  }

  /****** function that handles closing of error message modal ******/
  function closeErrorMessageModal() {
    showErrorModal(false);
  }

  /****** function that handles closing of missing column modal ******/
  function closeMissingColumnModal() {
    showMissingColumnModal(false);
  }

  /****** function that handles closing of conflict in number of members modal ******/
  function closeConflictNumberModal() {
    setUploadFileFlag(false);
    updateValuesState({}, 'uploaded_data');
    updateValuesState(default_columns, 'columns_data');
    setUploadedRowCount({});
    showConflictNumberModal(false);
  }

  function saveConflictNumber(member: any) {
    const details = props.details_data;
    details['number_of_members'] = member;
    props.updateDetails(details, 'number_of_members');
    setUploadedRowCount({});
    showConflictNumberModal(false);
  }

  function closeAddNewDataMapModal() {
    showAddNewDataMap(false);
  }

  function handleRowsStartAtValue(evt: React.KeyboardEvent<HTMLInputElement>) {
    if (
      evt.key === '-' ||
      evt.key === '.' ||
      evt.key === '+' ||
      evt.key === 'e' ||
      evt.key === 'E'
    ) {
      evt.preventDefault();
    }
  }

  function handlePasteEvt(evt: React.ClipboardEvent<HTMLDivElement>) {
    const { clipboardData } = evt;

    if (clipboardData && clipboardData.getData('text')) {
      const pastedText = clipboardData.getData('text');
      try {
        const parsedData = parseFloat(pastedText);

        if (isNaN(parsedData) || parsedData < 0 || pastedText.includes('.')) {
          evt.preventDefault();
        }
      } catch (e) {
        evt.preventDefault();
      }
    }
  }

  /****** function that handles onchange of rows start at field ******/
  function handleRowsStartAt(e: React.ChangeEvent<HTMLInputElement>) {
    const { value } = e.target;
    updateValuesState(value, 'rows_start_at');
    if (value) {
      const row_cnt: number = parseInt(value);
      let temp_raw_data: any = {
        rows: [],
        columns: props.raw_uploaded_data['columns'],
        data_values: {},
      };
      if (row_cnt > 0) {
        const temp_cnt = row_cnt - 1;
        if (temp_cnt < props.raw_uploaded_data['rows'].length) {
          temp_raw_data['rows'] = JSON.parse(
            JSON.stringify(props.raw_uploaded_data['rows']),
          );
          temp_raw_data['rows'].splice(0, temp_cnt);
          temp_raw_data = getValuesData(
            props.raw_uploaded_data['columns'],
            temp_raw_data['rows'],
            temp_raw_data,
          );
        }
      } else {
        temp_raw_data['rows'] = props.raw_uploaded_data['rows'];
        temp_raw_data['data_values'] = props.raw_uploaded_data['data_values'];
      }
      updateValuesState(temp_raw_data, 'filtered_raw_data');
      generateTable(temp_raw_data); 
    }
  }

  

  function cancelProcess() {
    setIsMemberUploadStatusModalOpen(false);
    setIsConfirmModalOpen(true);

    if (props.cancelStatusBgPolling) {
      props.cancelStatusBgPolling();
    }
  }

  function cancelProcessCancel() {
    if (props.resumeStatusBgPolling) {
      props.resumeStatusBgPolling();
    }

    setIsConfirmModalOpen(false);
    setIsMemberUploadStatusModalOpen(true);
  }

  function cancelProcessConfirm() {
    setIsConfirmModalOpen(false);
    setIsMemberUploadStatusModalOpen(false);

    if (props.onCancelProcess) {
      props.onCancelProcess();
    }
  }

  function backToDashboard() {
    if (props.onBackToDashboard) {
      props.onBackToDashboard();
    }
  }

  function cancelEnrollment() {
    setIsMemberUploadStatusModalOpen(false);

    if (props.onCancelEnrollment) {
      props.onCancelEnrollment();
    }
  }

  function confirmEnrollment() {
    if (props.onConfirmEnrollment) {
      props.onConfirmEnrollment();
    }
  }

  function onDropRejected() {
    showErrorModal(true);
    setUploadTitle('File not supported');
    setUploadMessage('Maximum File upload size is 66mb');
  }

  function onUpdateSelectedDataMap() {
    let values_payload: any = {
      gender_data: props.gender_data,
      civil_status_data: props.civil_status_data,
      plan_type_data: props.plan_type_data,
      type_data: props.type_data,
      relationship_data: props.relationship_data,
      vip_data: props.vip_data,
      ph_rider_data: props.ph_rider_data,
      member_consent_data: props.member_consent_data,
      site_data: props.site_data,
      suffix_data: props.suffix_data
    };
    let final_payload: any = JSON.parse(JSON.stringify(values_payload))
    props.updatingDataMap(final_payload);
  }

  return (
    <Grid container alignItems="stretch" className={clsx('member-body')}>
      {!uploaded_file_flag ? (
        <Grid item xs={12} className={clsx('padding-top-20')}>
          <InputLabel htmlFor="upload_member_file_drop" className={clsx('h2-label')}>
            Upload File
          </InputLabel>
          <Dropzone
            onDropAccepted={acceptedFiles => handleFileUpload(acceptedFiles)}
            maxSize={66 * 1024 * 1024}
            onDropRejected={onDropRejected}
          // accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .csv"
          >
            {({ getRootProps, getInputProps }) => (
              <section>
                <div className={clsx('upload-file')} {...getRootProps()}>
                  <input
                    data-cy={'upload_member_file_drop'}
                    id={'upload_member_file_drop'}
                    {...getInputProps()}
                  />
                  <FontAwesomeIcon
                    className="fa-2x"
                    icon={faFileUpload}
                    style={{ color: 'rgba(39, 46, 76, 0.56)' }}
                  />
                  <p
                    style={{ color: 'rgba(39, 46, 76, 0.56)', fontWeight: 400 }}
                  >
                    Click or drag/drop file to preview member list
                  </p>
                  <p
                    style={{
                      color: 'rgba(39, 46, 76, 0.56)',
                      width: '370px',
                      textAlign: 'center',
                      fontWeight: 400,
                    }}
                  >
                    Make sure the dates are in the format where the
                    <span style={{ fontWeight: 700 }}>
                      {' '}
                      month is written first
                    </span>{' '}
                    (ex: MM/DD/YYYY or YYYY/MM/DD)
                  </p>
                </div>
              </section>
            )}
          </Dropzone>
        </Grid>
      ) : null}
      {uploaded_file_flag ? (
        <Grid item xs={12} className={clsx('padding-top-20')}>
          <Grid item container spacing={4} alignItems="stretch">
            <Grid item xs={12}>
              <Typography className={clsx('sub-title')} color="textPrimary">
                Preview of File
              </Typography>
              {props.details_data['sent_through_channel'].value == "Client Portal" && !props.ocpfile ? 
                <Dropzone
                  onDropAccepted={acceptedFiles => handleFileUpload(acceptedFiles)}
                  maxSize={66 * 1024 * 1024}
                  onDropRejected={onDropRejected}
                // accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .csv"
                >
                {({ getRootProps, getInputProps }) => (
                  <section>
                    <div className={clsx('upload-file')} {...getRootProps()} style={{ height: '72px', width: '350px', padding: '10px' }}>
                      <input
                        data-cy={'upload_member_file_drop'}
                        id={'upload_member_file_drop'}
                        {...getInputProps()}
                      />
                      <FontAwesomeIcon
                        className="fa-2x"
                        icon={faFileUpload}
                        style={{ color: 'rgba(39, 46, 76, 0.56)' }}
                      />
                      <span
                        style={{ color: 'rgba(39, 46, 76, 0.56)', fontWeight: 400 }}
                      >
                        Drag or drop file to preview member list
                  </span>
                    </div>
                  </section>
                )}
              </Dropzone> : ''}

              <Typography color="textPrimary" style={{ paddingTop: '5px' }}>
                <span>{props.details_data['sent_through_channel'].value == "Client Portal" || props.ocpfile ? 
                <span ><FontAwesomeIcon
                  icon={faDownload}
                ></FontAwesomeIcon>
                  <Link onClick={() => handleClickFile(props.filtered_raw_data['rows'], props.filtered_raw_data['columns'])}>
                    {selected_filename}
                  </Link></span> :
                  <span>{selected_filename}</span>}
                  {props.is_ocp ? null : (
                      <span
                        data-cy={'upload_member_file_delete_btn'}
                        id={'upload_member_file_delete_btn'}
                        style={{ paddingLeft: '10px', fontSize: '15px' }}
                        onClick={handleDeleteFile}
                      >
                        <FontAwesomeIcon
                          icon={faTimesCircle}
                          style={{ color: '#B0AFB8' }}
                        />
                      </span>
                    )}
                </span>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="upload_file_table"
                rows={
                  Object.keys(uploaded_data).length > 0
                    ? uploaded_data['rows']
                    : []
                }
                columns={
                  Object.keys(uploaded_data).length > 0
                    ? uploaded_data['columns']
                    : []
                }
                message="No available data"
                onClickRow={handleClickRow}
                disableSelect
                disableSearch
                disableFilter
                disableSort
                formattedColumns={
                  // Object.keys(uploaded_data).length > 0
                  //   ? uploaded_data['formattedColumns']
                  //   : staticFormattedColumns
                  staticFormattedColumns
                }
                columnExtensions={
                  Object.keys(uploaded_data).length > 0
                    ? uploaded_data['columnExtensions']
                    : []
                }
                pageSize={10}
              />
            </Grid>
          </Grid>
        </Grid>
      ) : null}
      <Grid item xs={3} className={clsx('padding-top-20')}>
        {uploaded_file_flag ? (
          <AddSelect
            data-cy={'upload_member_data_map_select'}
            id={'upload_member_data_map_select'}
            options={props.client_maps}
            // options={saveForNowMapping}
            label={'Data Mapping'}
            value={selectedDataMap}
            addButton={true}
            addButtonLabel={'Add New Data Map'}
            onChange={handleDataMapChange}
            error={false}
            error_message={''}
          />
        ) : (
            <AddSelect
              data-cy={'upload_member_data_map_select'}
              id={'upload_member_data_map_select'}
              options={props.client_maps}
              label={'Data Mapping'}
              value={selectedDataMap}
              addButton={false}
              addButtonLabel={''}
              onChange={handleDataMapChange}
              error={false}
              error_message={''}
               disabled={true} //ANN debug disable data mapping upload members
            />
            // <AddSelect
            // data-cy={'upload_member_data_map_select'}
            // id={'upload_member_data_map_select'}
            // options={saveForNowMapping}
            // //props.default_data_mapping_save_for_now props.client_maps ? props.client_maps : 
            // label={'Data Mapping33373'}
            // // value={props.selected_data_map ? props.selected_data_map : saveForNowMapping}
            // value={props.selected_data_map}
            // addButton={true}
            // addButtonLabel={'Add New Data Map'}
            // onChange={handleDataMapChange}
            // error={false}
            // error_message={''}
          // />
          )}
      </Grid>
      <Grid item xs={12} className={clsx('padding-top-20')}>
        <InputLabel htmlFor="upload_member_row_start_field">Rows start at</InputLabel>
        <TextField
          data-cy={'upload_member_row_start_field'}
          id={'upload_member_row_start_field'}
          type="number"
          margin="normal"
          variant="outlined"
          inputProps={{ 'aria-label': 'bare', min: 0 }}
          InputProps={{
            onKeyDown: handleRowsStartAtValue,
            onPaste: handlePasteEvt,
          }}
          value={props.rows_start_at}
          onChange={handleRowsStartAt}
        />
      </Grid>
      <Grid item xs={12} className={clsx('padding-top-20')}>
        <Grid item container spacing={5} alignItems="stretch">
          <Grid item xs={4}>
            <InputLabel
              className={clsx('table-label')}
              htmlFor="upload_member_column_names_table"
            >
              Column Names
            </InputLabel>
            <TableSelect
              data-cy={'upload_member_column_names_table'}
              id={'upload_member_column_names_table'}
              rows={
                props.column_names['rows'].length > 0 ? props.column_names['rows'] : displayMapping2
              }
              columns={
                props.column_names && props.column_names['columns']
                ? props.column_names['columns']
                  // ? column_names['columns']
                  : []
              }
              empty_message={'No data available.'}
              name={'column names'}
              handleChange={(handleValuesChange)}
              maxHeight={'auto'}
            />
            <Box style={{ paddingTop: 16 }} display="flex" flexDirection="row-reverse">
              <Button
                data-cy={'upload_member_columns_save_btn'}
                id={'upload_member_columns_save_btn'}
                variant="contained"
                color="primary"
                size="small"
                disabled={false}
                onClick={onUpdateSelectedDataMap}
              >
                Save
              </Button>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <Grid item container direction="row" spacing={5}>
              <Grid item xs={5} sm={4} className={classes.listBorder}>
                <InputLabel
                  className={clsx('values-columns')}
                  htmlFor="upload_values_list"
                >
                  Values
                </InputLabel>
                <List id="upload_values_list">
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_gender_item'}
                      id={'upload_member_values_gender_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'gender'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('gender');
                      }}
                    >
                      Gender
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_civil_item'}
                      id={'upload_member_values_civil_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'civil status'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('civil status');
                      }}
                    >
                      Civil Status
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_plan_item'}
                      id={'upload_member_values_plan_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'plan type'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('plan type');
                      }}
                    >
                      Plan Type
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_type_item'}
                      id={'upload_member_values_type_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'type'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('type');
                      }}
                    >
                      Type
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_relationship_item'}
                      id={'upload_member_values_relationship_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'relationship to principal'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('relationship to principal');
                      }}
                    >
                      Relationship to Principal
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_vip_item'}
                      id={'upload_member_values_vip_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'vip'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('vip');
                      }}
                    >
                      VIP
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_ph_rider_item'}
                      id={'upload_member_values_ph_rider_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'philhealth rider'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('philhealth rider');
                      }}
                    >
                      Philhealth Rider
                    </ListItemText>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_member_consent_item'}
                      id={'upload_member_values_member_consent_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'member consent'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('member consent');
                      }}
                    >
                      Member Consent
                    </ListItemText>
                    </ListItem>
                    <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_suffix_item'}
                      id={'upload_member_values_suffix_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'suffix'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('suffix');
                      }}
                    >
                      Suffix
                    </ListItemText>
                    </ListItem>
                    <ListItem>
                    <ListItemText
                      data-cy={'upload_member_values_site_item'}
                      id={'upload_member_values_site_item'}
                      classes={{
                        root: classes.listItemTextRoot,
                        primary:
                          selectedValues === 'site'
                            ? classes.listItemTextPrimarySelected
                            : classes.listItemTextPrimaryNotSelected,
                      }}
                      onClick={() => {
                        setSelectedValues('site');
                      }}
                    >
                      Site
                    </ListItemText>
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={7} sm={8} className={clsx('values-tables')}>
                {selectedValues === 'gender' ? (
                  <TableSelect
                    data-cy={'upload_member_values_gender_table'}
                    id={'upload_member_values_gender_table'}
                    rows={
                      Object.keys(props.gender_data).length > 0
                        ? props.gender_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.gender_data).length > 0
                        ? props.gender_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'gender'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'civil status' ? (
                  <TableSelect
                    data-cy={'upload_member_values_civil_table'}
                    id={'upload_member_values_civil_table'}
                    rows={
                      Object.keys(props.civil_status_data).length > 0
                        ? props.civil_status_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.civil_status_data).length > 0
                        ? props.civil_status_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'civil status'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'plan type' ? (
                  <TableSelect
                    data-cy={'upload_member_values_plan_table'}
                    id={'upload_member_values_plan_table'}
                    rows={
                      Object.keys(props.plan_type_data).length > 0
                        ? props.plan_type_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.plan_type_data).length > 0
                        ? props.plan_type_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'plan type'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'type' ? (
                  <TableSelect
                    data-cy={'upload_member_values_type_table'}
                    id={'upload_member_values_type_table'}
                    rows={
                      Object.keys(props.type_data).length > 0
                        ? props.type_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.type_data).length > 0
                        ? props.type_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'type'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'relationship to principal' ? (
                  <TableSelect
                    data-cy={'upload_member_values_relationship_table'}
                    id={'upload_member_values_relationship_table'}
                    rows={
                      Object.keys(props.relationship_data).length > 0
                        ? props.relationship_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.relationship_data).length > 0
                        ? props.relationship_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'relationship'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'vip' ? (
                  <TableSelect
                    data-cy={'upload_member_values_vip_table'}
                    id={'upload_member_values_vip_table'}
                    rows={
                      Object.keys(props.vip_data).length > 0
                        ? props.vip_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.vip_data).length > 0
                        ? props.vip_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'vip'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'philhealth rider' ? (
                  <TableSelect
                    data-cy={'upload_member_values_ph_rider_table'}
                    id={'upload_member_values_ph_rider_table'}
                    rows={
                      Object.keys(props.ph_rider_data).length > 0
                        ? props.ph_rider_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.ph_rider_data).length > 0
                        ? props.ph_rider_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'philhealth rider'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                ) : selectedValues === 'member consent' ? (
                  <TableSelect
                    data-cy={'upload_member_values_member_consent_table'}
                    id={'upload_member_values_member_consent_table'}
                    rows={
                      Object.keys(props.member_consent_data).length > 0
                        ? props.member_consent_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.member_consent_data).length > 0
                        ? props.member_consent_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'member consent'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                )  : selectedValues === 'site' ? (
                  <>
                  <TableSelect
                    data-cy={'upload_member_values_site_table'}
                    id={'upload_member_values_site_table'}
                    rows={
                      Object.keys(props.site_data).length > 0
                        ? props.site_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.site_data).length > 0
                        ? props.site_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'site'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                  </>
                  
                 ) : selectedValues === 'suffix' ? (
                  <TableSelect
                    data-cy={'upload_member_values_suffix_table'}
                    id={'upload_member_values_suffix_table'}
                    rows={
                      Object.keys(props.suffix_data).length > 0
                        ? props.suffix_data['rows']
                        : []
                    }
                    columns={
                      Object.keys(props.suffix_data).length > 0
                        ? props.suffix_data['columns']
                        : []
                    }
                    empty_message={'No data available.'}
                    name={'suffix'}
                    handleChange={handleValuesChange}
                    maxHeight={'auto'}
                  />
                )  : null
                 }
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <ModalComponent
        id="error_upload_modal"
        isModalOpen={openErrorModal}
        title={upload_error_title}
        message={upload_error_message}
        onClose={closeErrorMessageModal}
      />
      <MissingColumnErrorModal
        id="missing_columns_error"
        isModalOpen={openMissingColumnModal}
        title={'Some columns are missing'}
        message={missin_column_message}
        onClose={closeMissingColumnModal}
      />
      <ConflictMemberNumberErrorModal
        id="conflict_number_error"
        isModalOpen={openConflictNumberModal}
        title={'Conflict in number of members'}
        state_data={conflict_error_data}
        onClose={closeConflictNumberModal}
        onSave={saveConflictNumber}
      />
      <AddNewDataMapModal
        id="add_new_data_map_modal"
        isModalOpen={openAddNewDataMapModal}
        title={'Define Data Map'}
        account={props.corporate_account}
        onClose={closeAddNewDataMapModal}
        data={props.add_new_data_map}
        setDataValues={updateValuesState}
        savingNewDataMap={async (payload:any)=>{
          await props.savingNewDataMap(payload);
          // handleValuesChange()
        }}
        filtered_raw_data={props.filtered_raw_data}
        branch_options={props.branch_options}
        plan_type_options={props.plan_type_options}
        uploaded_file_flag={uploaded_file_flag}
        newData={newMapping}
        newDataMapping={column_names_new}
        clientId={props.clientData._id}
        updatedMapping={props.updatedMapping}
        displayMapping={props.data_maps}
      />
      <SnackbarNotification
        isOpen={isSnackbarNotifOpen}
        message={snackbarNotifMsg}
        variant={snackbarNotifVariant}
        onSnackbarClose={() => {
          setIsSnackbarNotifOpen(false);
        }}
      />
      <MemberUploadStatusModal
        isOpen={isMemberUploadStatusModalOpen}
        ticketId={props.ticketId}
        status={props.processingStatus}
        uploadedData={uploaded_data}
        onCancelProcess={cancelProcess}
        onBackToDashboard={backToDashboard}
        onCancelEnrollment={cancelEnrollment}
        onConfirmEnrollment={confirmEnrollment}
        pageType={props.page}
        renewalType={props?.renewalType}
      />
      <ConfirmationModalComponent
        id="upload-member-confirm-modal"
        isModalOpen={isConfirmModalOpen}
        modalTitle={confirmModalTitle}
        modalMessage={confirmModalMessage}
        variant={'danger'}
        closeText="Continue Process"
        confirmText="Cancel Process"
        onClose={cancelProcessCancel}
        onConfirm={cancelProcessConfirm}
      />
    </Grid>
  );
};

UploadMember.defaultProps = {
  uploaded_data: {},
  column_names: {},
};
