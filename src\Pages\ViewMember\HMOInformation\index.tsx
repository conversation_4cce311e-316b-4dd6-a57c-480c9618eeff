import React, { useState, useEffect, useRef } from 'react';
import {
  Grid,
  Typography,
  Paper,
  Popper,
  ClickAwayListener,
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Link,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Icon,
} from '@material-ui/core/';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import CommentIcon from '@material-ui/icons/Comment';
import PersonIcon from '@material-ui/icons/Person';
import { makeStyles } from '@material-ui/core/styles';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUserCog } from '@fortawesome/free-solid-svg-icons';
import moment from 'moment';
import {
  isNil,
  // SEPT 3, 2020: change logic in showing member card no
  // find,
  get,
  toLower
} from 'lodash';
import { AddMemberRemarksModal } from './AddMemberRemarksModal';

const hmoitemStyles = makeStyles({
  hmoname: {
    fontSize: '0.75rem',
    color: 'rgba(39, 46, 76, 0.6)',
    textTransform: 'uppercase',
    paddingRight: 32,
  },
  hmovalue: {
    fontSize: '1rem',
    fontWeight: 600,
    color: 'rgb(39, 46, 76)',
  },
  hmovaluelink: {
    fontSize: '1rem',
    fontWeight: 600,
    color: 'rgb(39, 46, 76)',
    textDecoration: 'underline',
    lineHeight: 1.5,
  },
});

interface HMOItemProps {
  name?: string;
  value?: string;
  link?: string;
  handleClick?: any;
}

interface HMOInformationProps {
  memberData?: any;
  handleShowModal: (status: string) => void;
  handleRedirect: (url: string) => void;
  handleRemarkSubmit: (remark: string) => void;
  dependents: any[];
  principal: any;
  clientData?: any;
  contractId: string;
  contractName?: string;
  MBL?: string;
  ABL?: string;
  PEC?: string;
  clientFields: any[];
  planTypeNode: any;
  mctNode: any;
  currentUserData: any;
  memberStatus: any;
  updateMemberStatus: (data: any) => void;
  cancelUpdateMemberStatus: (data: any) => void;
}

const hmoinfoStyles = makeStyles(theme => ({
  subtitle: {
    fontSize: '1.125rem',
    fontWeight: 600,
  },
  hmocheckboxlabel: {
    fontSize: '0.75rem',
    color: 'rgb(39, 46, 76)',
  },
  hmographsubtitle: {
    fontSize: '0.75rem',
    textTransform: 'uppercase',
    color: 'rgb(39, 46, 76, 0.6)',
    marginBottom: 20,
  },
  hmoaddremarksbtn: {
    fontSize: '0.875rem',
    fontWeight: 600,
    color: '#3AB77D',
    marginBottom: 16,
    cursor: 'pointer',
  },
  hmodependentsbox: {
    maxHeight: 175,
    minHeight: 100,
    borderRadius: 0,
    overflow: 'auto',
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
  },
  hmoprincipalsbox: {
    borderRadius: 0,
    overflow: 'auto',
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
  },
  hmoheadercell: {
    minWidth: '150px',
    padding: '5px 10px',
  },
  hmodependentsbox_link: {
    // color: '#3AB77D',
    color: '#272E4C',
  },
  hmoprincipalsbox_link: {
    color: '#3AB77D',
  },
  change_status_link: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
    textDecoration: 'underline',
  },
  icons: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
  },
  popper: {
    top: '4px',
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '0 1em 1em 1em',
        borderColor: `transparent transparent ${theme.palette.background.paper} transparent`,
      },
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '1em 1em 0 1em',
        borderColor: `${theme.palette.background.paper} transparent transparent transparent`,
      },
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 1em 1em 0',
        borderColor: `transparent ${theme.palette.background.paper} transparent transparent`,
      },
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 0 1em 1em',
        borderColor: `transparent transparent transparent ${theme.palette.background.paper}`,
      },
    },
  },
  arrow: {
    position: 'absolute',
    fontSize: 7,
    width: '3em',
    height: '3em',
    '&::before': {
      content: '""',
      margin: 'auto',
      display: 'block',
      width: 0,
      height: 0,
      borderStyle: 'solid',
    },
  },
  paper: {
    padding: '15px',
    width: 130,
  },
  status: {
    padding: '5px',
    cursor: 'pointer',
  },
  remarksStep: {
    height: 175,
    borderRadius: 0,
    overflow: 'auto',
    padding: 10,
    boxShadow: '0px 3px 6px #272E4C19',
    backgroundColor: '#fff',
  },
  noRemarksStep: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
    borderRadius: 0,
    padding: 10,
    boxShadow: '0px 3px 6px #272E4C19',
    backgroundColor: '#fff',
  },
}));

export const HMOInformation: React.FC<HMOInformationProps> = (
  props: HMOInformationProps,
): JSX.Element => {
  const classes = hmoinfoStyles(props);
  const {
    memberData,
    dependents,
    principal,
    clientData,
    contractId,
    contractName,
    MBL,
    ABL,
    PEC,
    currentUserData,
    // clientFields,
  } = props;
  const anchorRef = useRef(null);
  const [arrowRef, setArrowRef] = useState<any>(null);
  const [isPopoverOpen, setPopupoverOpen] = useState(false);
  const [statusOptions, setStatusOptions] = useState<any[]>([]);
  const [addRemarkModalOpen, setAddRemarkModalOpen] = useState<boolean>(false);
  // const [loading, setLoading] = useState(false);

  const HMOItem: React.FC<HMOItemProps> = (
    props: HMOItemProps,
  ): JSX.Element => {
    const classes = hmoitemStyles(props);

    let lnk = props.link ? props.link : '';

    return (
      <Grid
        container
        alignItems="center"
        style={{ paddingTop: 4, paddingBottom: 4 }}
      >
        <Grid item xs={5}>
          <Typography className={classes.hmoname}>{props.name}</Typography>
        </Grid>
        <Grid item xs={7}>
          {lnk ? (
            <Link href={lnk} className={classes.hmovaluelink}>
              {props.value}
            </Link>
          ) : props.handleClick ? (
            <Link
              onClick={() => {
                props.handleClick();
              }}
              className={classes.hmovaluelink}
            >
              {props.value}
            </Link>
          ) : (
                <Typography className={classes.hmovalue}>{props.value}</Typography>
              )}
        </Grid>
      </Grid>
    );
  };

  const getStatusOptions = () => {
    const ACTIVE = 'active';
    const AWAITING_ACTIVATION = 'awaiting activation';
    const TERMINATED = 'terminated';
    const SUSPENDED = 'suspended';
    const CANCELLED = 'cancelled';
    const FOR_VALIDATION = 'for validation';
    
    if (props.memberData) {
      const {
        member_status,
        member_suspension,
        // member_termination,
      } = props.memberData;

      const options: any[] = [];
      const memberStatus = toLower(member_status);

      const isMarkedForSuspension = (() => {
        if (
          props.memberData &&
          (
            get(props.memberData, 'member_status', '') !== 'Suspended'
            && get(props.memberData, 'member_status', '') !== 'Terminated'
            && get(props.memberData, 'member_status', '') !== 'Cancelled'
            && get(props.memberData, 'member_status', '') !== 'Disapproved'
            && get(props.memberData, 'member_status', '') !== 'Expired'
          ) &&
          props.memberData['member_suspension'] &&
          props.memberData['member_suspension']['suspension_start_date'] &&
          moment(
            props.memberData['member_suspension']['suspension_start_date'],
          ).isValid() &&
          moment(props.memberData['member_suspension']['suspension_start_date']).diff(
            new Date(),
          ) > 0
        ) {
          return true;
        } else {
          return false;
        }
      })();

      const canEditSuspension = (() => {
        if (memberStatus !== SUSPENDED || isNil(member_suspension)) {
          return false;
        }

      const suspensionLiftDate = get(
          member_suspension,
          'suspension_lift_date',
          null,
        );
        if (isNil(suspensionLiftDate)) {
          return true;
        }

      const suspensionLiftDateMoment: moment.Moment = moment(
          suspensionLiftDate,
        );
        if (
          isNil(suspensionLiftDateMoment) ||
          !suspensionLiftDateMoment.isValid()
        ) {
          return false;
        }

        return suspensionLiftDateMoment.diff(moment().toDate()) > 0;
      })();

      const isMarkedForTermination = (() => {
        if (
          props.memberData &&
          (
            get(props.memberData, 'member_status', '') !== 'Terminated'
            && get(props.memberData, 'member_status', '') !== 'Suspended'
            && get(props.memberData, 'member_status', '') !== 'Cancelled'
            && get(props.memberData, 'member_status', '') !== 'Disapproved'
            && get(props.memberData, 'member_status', '') !== 'Expired'
          ) &&
          props.memberData['member_termination'] &&
          props.memberData['member_termination']['termination_date'] &&
          moment(
            props.memberData['member_termination']['termination_date'],
          ).isValid() &&
          moment(props.memberData['member_termination']['termination_date']).diff(
            new Date(),
          ) > 0
        ) {
          return true;
        } else {
          return false;
        }
      })();

      const transferee = (()=>{
        if (
          props.memberData && get(props.memberData, 'member_status', '') === 'Active' &&
          props.memberData['member_termination'] &&
          props.memberData['member_termination']['termination_date'] &&
          moment(
            props.memberData['member_termination']['termination_date'],
          ).isValid() &&
          moment(props.memberData['member_termination']['termination_date']).diff(
            new Date(),
          ) > 0 && props.memberData['new_member_id_display'] && props.memberData['new_member_id_display'].length>0
        ) {
          return true;
        } else {
          return false;
        }
      })();

   

      const isSuspendedMarkedForTermination = (() => {
        if (
          props.memberData &&
          (
            get(props.memberData, 'member_status', '') !== 'Terminated'
            && get(props.memberData, 'member_status', '') !== 'Active'
            && get(props.memberData, 'member_status', '') !== 'Cancelled'
            && get(props.memberData, 'member_status', '') !== 'Disapproved'
            && get(props.memberData, 'member_status', '') !== 'Expired'
          ) &&
          props.memberData['member_termination'] &&
          props.memberData['member_termination']['termination_date'] &&
          moment(
            props.memberData['member_termination']['termination_date'],
          ).isValid() &&
          moment(props.memberData['member_termination']['termination_date']).diff(
            new Date(),
          ) > 0
        ) {
          return true;
        } else {
          return false;
        }
      })();
      console.log("Check Status Option Member Data: ",props.memberData,' - ',memberStatus);
      // Add 'Activate' option
      if ([CANCELLED].includes(memberStatus)
        //&& GlobalFunction.checkUserRbacByPolicy('MS39', currentUserData, false)
        ) {
        options.push(
          <Grid key={`status-option-reactivate`} item xs={12}>
          <Typography
            data-cy={'hmo_info_status_reactivate_popover'}
            id={'hmo_info_status_reactivate_popover'}
            className={classes.status}
            onClick={openReactivateMemberModal}
          >
              Reactivate
            </Typography>
          </Grid>,
        );
      }

      // Add 'Reactivate' option
      if ([TERMINATED].includes(memberStatus)) {
        options.push(
          <Grid key={`status-option-reactivate`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_reactivate_popover'}
              id={'hmo_info_status_reactivate_popover'}
              className={classes.status}
              onClick={openReactivateMemberModal}
            >
              Reactivate
            </Typography>
          </Grid>,
        );
      }

      // Add 'Suspend' option
      if ([ACTIVE].includes(memberStatus) && !isMarkedForSuspension) {
        options.push(
          <Grid key={`status-option-suspended`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_suspend_popover'}
              id={'hmo_info_status_suspend_popover'}
              className={classes.status}
              onClick={openSuspendMemberModal}
            >
              Suspend
            </Typography>
          </Grid>,
        );
      }

      // Add 'Lift Suspension' option
      if ([SUSPENDED].includes(memberStatus) && canEditSuspension) {
        options.push(
          <Grid key={`status-option-lift-suspension`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_liftsuspend_popover'}
              id={'hmo_info_status_liftsuspend_popover'}
              className={classes.status}
              onClick={openLiftSuspensionMemberModal}
            >
              Lift Suspension
            </Typography>
          </Grid>,
        );
      }

      // Add 'Cancel Suspension' option
      if ([ACTIVE].includes(memberStatus) && isMarkedForSuspension) {
        options.push(
          <Grid key={`status-option-suspended`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_cancelsuspend_popover'}
              id={'hmo_info_status_cancelsuspend_popover'}
              className={classes.status}
              onClick={openCancelSuspensionModal}
            >
              Cancel Suspension
            </Typography>
          </Grid>,
        );
      }

      // Add 'Cancel' option
      if ([AWAITING_ACTIVATION].includes(memberStatus)
        && GlobalFunction.checkUserRbacByPolicy('MS40', currentUserData, false)) {
        options.push(
          <Grid key={`status-option-cancel`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_cancel_popover'}
              id={'hmo_info_status_cancel_popover'}
              className={classes.status}
              onClick={openCancelMemberModal}
            >
              Cancel
            </Typography>
          </Grid>,
        );
      }

      // Add 'Terminate' option
      if ([ACTIVE, SUSPENDED, FOR_VALIDATION].includes(memberStatus)
        && (!isMarkedForTermination || isSuspendedMarkedForTermination || transferee)
        && GlobalFunction.checkUserRbacByPolicy('MS40', currentUserData, false)) {
        options.push(
          <Grid key={`status-option-terminated`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_terminate_popover'}
              id={'hmo_info_status_terminate_popover'}
              className={classes.status}
              onClick={openTerminatedMemberModal}
            >
              Terminate
            </Typography>
          </Grid>,
        );
      }

      // Add 'Cancel Termination' option
      if ([ACTIVE, SUSPENDED].includes(memberStatus)
        && (isMarkedForTermination || isSuspendedMarkedForTermination)
        && !props.memberData.new_member_id) {
        options.push(
          <Grid key={`status-option-cancel-termination`} item xs={12}>
            <Typography
              data-cy={'hmo_info_status_cancelterminate_popover'}
              id={'hmo_info_status_cancelterminate_popover'}
              className={classes.status}
              onClick={openCancelTerminationModal}
            >
              Cancel Termination
            </Typography>
          </Grid>,
        );
      }
      setStatusOptions(options);
    }
  };

  function openCancelTerminationModal() {
    setPopupoverOpen(false);
    props.handleShowModal('cancel termination');
  }

  function openPopover() {
    setPopupoverOpen(true);
  }

  function closePopover() {
    setPopupoverOpen(false);
  }

  function openActiveMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('active');
  }

  function openReactivateMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('reactivate');
  }

  function openSuspendMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('suspend');
  }

  function openCancelMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('cancel');
  }

  function openTerminatedMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('terminate');
  }
  function openCancelSuspensionModal() {
    setPopupoverOpen(false);
    props.handleShowModal('cancel_suspension');
  }

  function openLiftSuspensionMemberModal() {
    setPopupoverOpen(false);
    props.handleShowModal('liftSuspension');
  }

  function isValidDate(date: string) {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  }


  function updateMemberStatus(memberData) {
    console.log('func called, active to for valid', memberData)
    const memberStatus = props.memberStatus

    
    props.updateMemberStatus(memberData)

    console.log('memberstatus100', memberStatus)
  }

  function cancelUpdateMemberStatus(memberData) {
    console.log('this func called for valid to active')
    const memberStatus = props.memberStatus
    props.cancelUpdateMemberStatus(memberData)

    console.log('memberStatus', memberStatus)
  }
  console.log(cancelUpdateMemberStatus)
  console.log(updateMemberStatus)

  useEffect(() => {
  }, props.memberData)

  function getCardNoDisplay() {

    /** NEW IMPLEMENTATION */
    // Display the member's card no. upon approval
    if (memberData && memberData['member_card_no']) {
      return memberData['member_card_no'];
    } else {
      return '-';
    }
    /** */
   
  }

   const getNewPlanType = () => {
    console.log('contract update here0', props)
    if(props.memberData && props.memberData?.new_plan_types !== undefined) {
      let newName =  props.memberData?.new_plan_types.filter(name => {
        console.log('lo0', name);
        console.log('lo1', props.memberData?.plan_type); 
        return name?.plan_type_code === props.memberData?.plan_type
      
      })
      console.log('lo2', newName); 
        //loop through each object
      return newName && newName[0]?.plan_type_name !== undefined ? newName[0].plan_type_name : props.memberData?.plan_type
    } else {
      console.log('no newplantype obj')
      return '-';
    }
  }
  

  console.log('openActiveMemberModal', openActiveMemberModal)///ts
  let leftsideCF: any[] = [];
  let rightsideCF: any[] = [];
  if (props.clientFields) {
    if (props.clientFields.length > 2) {

      let halfwayThrough = Math.floor(props.clientFields.length / 2);
      leftsideCF = props.clientFields.slice(0, halfwayThrough).map(item => {
        let value = '';
        if (
          item.field_name === 'registered_name' ||
          item.field_name === 'name_on_card'
        ) {
          value =
            clientData && clientData[item.field_name]
              ? clientData[item.field_name]
              : '';
        } else if (item.input_type === 'date') {
          value =
            memberData && memberData[item.field_name]
              ? moment(memberData[item.field_name]).format('MMM DD, YYYY')
              : '';
        } else {
          value =
            memberData && memberData[item.field_name]
              ? memberData[item.field_name]
              : '';
        }
        return <HMOItem name={item.system_name} value={value ? value : '-'} />;
      });
      rightsideCF = props.clientFields
        .slice(halfwayThrough, props.clientFields.length)
        .map(item => {
          let value = '';
          if (
            item.field_name === 'registered_name' ||
            item.field_name === 'name_on_card'
          ) {
            value =
              clientData && clientData[item.field_name]
                ? clientData[item.field_name]
                : '';
          } else if (item.input_type === 'date') {
            value =
              memberData && memberData[item.field_name]
                ? moment(memberData[item.field_name]).format('MMM DD, YYYY')
                : '';
          } else {
            value =
              memberData && memberData[item.field_name]
                ? memberData[item.field_name]
                : '';
          }
          return (
            <HMOItem name={item.system_name} value={value ? value : '-'} />
          );
        });
    } else if (props.clientFields.length > 0) {
      leftsideCF = [...props.clientFields].map(item => {
        let value = '';
        if (
          item.field_name === 'registered_name' ||
          item.field_name === 'name_on_card'
        ) {
          value =
            clientData && clientData[item.field_name]
              ? clientData[item.field_name]
              : '';
        } else if (item.input_type === 'date') {
          value =
            memberData && memberData[item.field_name]
              ? moment(memberData[item.field_name]).format('MMM DD, YYYY')
              : '';
        } else {
          value =
            memberData && memberData[item.field_name]
              ? memberData[item.field_name]
              : '';
        }
        return <HMOItem name={item.system_name} value={value ? value : '-'} />;
      });
    }
  }

  useEffect(() => {
    getStatusOptions();
    // isMarkedForValidation()
  }, [props.memberData]);

  return (
    <>
      <Grid container style={{ paddingLeft: 32 }}>
        <Grid item xs={12} style={{ paddingTop: 16, paddingBottom: 16 }}>
          <Grid container direction="row" justify="space-between">
            <Grid item xs={8}>
              <Typography className={classes.subtitle} color="textPrimary">
                {clientData && clientData['registered_name']
                  ? clientData['registered_name']
                  : ''}
              </Typography>
            </Grid>
            <Grid item xs={4}>
              {statusOptions.length > 0 ?
                GlobalFunction.checkUserRbacByPolicy('MS40', currentUserData, false) ? ( // MS903
                  <Grid container direction="row" justify="flex-end">
                    <Grid item>
                      <span
                        data-cy={'hmo_info_change_status_btn'}
                        id={'hmo_info_change_status_btn'}
                        ref={anchorRef}
                        className={classes.change_status_link}
                        onClick={openPopover}
                      >
                        <FontAwesomeIcon
                          icon={faUserCog}
                          className={classes.icons}
                        />{' '}
                      Change Status
                      <Popper
                          open={isPopoverOpen}
                          anchorEl={anchorRef.current}
                          placement={'bottom-end'}
                          className={classes.popper}
                          disablePortal={false}
                          style={{ marginTop: '4px' }}
                          modifiers={{
                            flip: {
                              enabled: false,
                            },
                            preventOverflow: {
                              enabled: true,
                              boundariesElement: 'undefined',
                            },
                            arrow: {
                              enabled: true,
                              element: arrowRef,
                            },
                          }}
                        >
                          <span className={classes.arrow} ref={setArrowRef} />
                          <Paper className={classes.paper}>
                            <ClickAwayListener onClickAway={closePopover}>
                              <Grid container>{statusOptions}</Grid>
                            </ClickAwayListener>
                          </Paper>
                        </Popper>
                      </span>
                    </Grid>
                  </Grid>
                ) : null
                : null}
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
          <HMOItem
            name="Member Account No."
            value={(() => {
              const memberAccountNo = get(
                memberData,
                'member_account_no',
                null,
              );
              if (memberAccountNo && memberAccountNo !== '') {
                return memberAccountNo;
              }

              return '-';
            })()}
          />
          <HMOItem name="Member Card No." value={getCardNoDisplay()} />
          <HMOItem
            name="Member ID"
            value={
              memberData && memberData.member_id ? memberData.member_id : '-'
            }
          />
          <HMOItem
            name="Contract"
            value={contractName ? contractName : '-'}
            handleClick={() => {
              let lnk =
                contractId && contractName
                  ? `/membership/member-contract/${memberData['_id']}/${contractId}/contract`
                  : '';
              if (lnk && props.handleRedirect) {
                props.handleRedirect(lnk);
              }
            }}
          />
          <HMOItem
            name="Effectivity Date"
            value={
              memberData && isValidDate(memberData.effectivity_date)
                ? moment(memberData.effectivity_date).format('MMM DD, YYYY')
                : '-'
            }
          />
          <HMOItem name="Contract Renewal Date" value="-" />
          <HMOItem
            name="Contract Expiration Date"
            value={(() => {
              const coverageEndDate = get(
                props.mctNode,
                'custom_metadata.coverage_end_date',
                null,
              )
              console.log('Contract Expiration Date > coverage end date val', coverageEndDate)

              if (!isNil(coverageEndDate) && moment(coverageEndDate).isValid()) {
                return  moment(coverageEndDate).format('MMM DD, YYYY');
                // return moment(coverageEndDate).utc(false).format('MMM DD, YYYY');
              }

              return '-';
            })()}
          />

          {/* {memberData && memberData?.member_status !== "Cancelled" ? ( */}
            <HMOItem
            name="Last Day"
            value={(() => {
              const terminationDate = get(
                memberData,
                'member_termination.termination_date',
                null,
              );
              const coverageEndDate = get(
                props.mctNode,
                'custom_metadata.coverage_end_date',
                null,
              )
              console.log('Last day of Effectivity > coverage end date val', coverageEndDate)

              if (!isNil(terminationDate)) {
                console.log('enter to lastday if')
                const terminationDateMoment: moment.Moment = moment.parseZone(
                  terminationDate,
                );
                if (
                  !isNil(terminationDateMoment) &&
                  terminationDateMoment.isValid()
                ) {

                  if (
                    (memberData && memberData.member_status_before_termination && (memberData.member_status_before_termination !== 'Awaiting Activation' && memberData.member_status_before_termination !== 'For Validation'))
                    || ((memberData && memberData.member_status && memberData.member_status !== 'Cancelled') && (memberData && memberData.member_status && memberData.member_status !== 'Awaiting Activation'))
                  ) {

                    // if (get(memberData, 'member_status_before_termination', '') !== 'Awaiting Activation'
                    //   || (get(memberData, 'member_status', '') !== 'Cancelled' && get(memberData, 'member_status', '') !== 'Awaiting Activation')) {
                    return terminationDateMoment
                      .add(-1, 'days')
                      .format('MMM DD, YYYY');
                  }
                }
              }

              if (get(memberData, 'member_status', '') !== 'Terminated' && get(memberData, 'member_status', '') !== 'Awaiting Activation' && get(memberData, 'member_status', '') !== 'Cancelled') {
                console.log('Active member w/out Termination Date data.')
                if (
                  get(memberData, 'member_status_before_termination', '') !== 'Awaiting Activation' &&
                  get(memberData, 'member_status_before_termination', '') !== 'For Validation'
                ) {
                  return get(
                    props.mctNode,
                    'custom_metadata.coverage_end_date',
                    '-',
                  ) !== '-' ?
                    moment(
                      props.mctNode.custom_metadata.coverage_end_date,
                    ).utc(false).format('MMM DD, YYYY')
                    // moment(
                    //   props.mctNode.custom_metadata.coverage_end_date,
                    // ).utc(false).format('MMM DD, YYYY')                    
                    : '-';
                // } else if (memberData && memberData?.member_status === "Cancelled") {
                //   console.log('member is cancelled empty last day')
                //   return '-'
                }
              // } else if(memberData && memberData.member_status === 'Cancelled') {
              //   return '-';
              } 
              return '-';
            }
          
          
          )()}
          />
          {get(memberData, 'member_status', '') === 'Suspended' 
            || memberData &&
            get(memberData, 'member_status', '') === 'Active' 
            &&
            memberData['member_suspension']  
            &&
            memberData['member_suspension']['suspension_start_date'] &&
            moment(
              memberData['member_suspension']['suspension_start_date'],
            ).isValid() &&
            moment(memberData['member_suspension']['suspension_start_date']).diff(
              new Date(),
            ) > 0 ? (
              <HMOItem
                name="Suspension Date"
                value={(() => {
                  const suspensionDate = get(
                    memberData,
                    'member_suspension.suspension_start_date',
                    null,
                  );

                  if (!isNil(suspensionDate)) {
                    const suspensionDateMoment: moment.Moment = moment(
                      suspensionDate,
                    );
                    if (
                      !isNil(suspensionDateMoment) &&
                      suspensionDateMoment.isValid()
                    ) {
                      return suspensionDateMoment.utc(false).format('MMM DD, YYYY');
                    }
                  }

                  return '-';
                })()}
              />
            ) : null}
          {get(memberData, 'member_status', '') === 'Suspended' 
          ||
            memberData &&
            get(memberData, 'member_status', '') === 'Active' &&
            memberData['member_suspension'] 
            &&
            memberData['member_suspension']['suspension_start_date'] &&
            moment(
              memberData['member_suspension']['suspension_start_date'],
            ).isValid() &&
            moment(memberData['member_suspension']['suspension_start_date']).diff(
              new Date(),
            ) > 0 ? (
              <HMOItem
                name="Suspension Lifting Date"
                value={(() => {
                  const suspensionLiftingDate = get(
                    memberData,
                    'member_suspension.suspension_lift_date',
                    null,
                  );

                  if (!isNil(suspensionLiftingDate)) {
                    const suspensionLiftingDateMoment: moment.Moment = moment(
                      suspensionLiftingDate,
                    );
                    if (
                      !isNil(suspensionLiftingDateMoment) &&
                      suspensionLiftingDateMoment.isValid()
                    ) {
                      return suspensionLiftingDateMoment.utc(false).format('MMM DD, YYYY');
                    }
                  }

                  return '-';
                })()}
              />
            ) : null}
          {get(memberData, 'member_status', '') === 'Terminated' ||
            get(memberData, 'member_status', '') === 'Cancelled' ||
            memberData &&
            get(memberData, 'member_status', '') !== 'Terminated' &&
            memberData['member_termination'] &&
            memberData['member_termination']['termination_date'] &&
            moment(
              memberData['member_termination']['termination_date'],
            ).isValid() &&
            moment(memberData['member_termination']['termination_date']).diff(
              new Date(),
            ) > 0 && !memberData['verify_termination'] ? (
              <HMOItem
                name={
                  get(memberData, 'member_status', '') === 'Cancelled' ||
                    get(memberData, 'member_status', '') === 'Awaiting Activation'
                    ? 'Cancellation Date' : 'Termination Date'
                }
                value={(() => {
                  // let terminationDate;
                  // if (memberData.member_status === 'Awaiting Activation') {
                  //   terminationDate = get(
                  //     memberData,
                  //     'member_termination.endorsed_date',
                  //     null,
                  //   );
                  // } else {
                  //   terminationDate = get(
                  //     memberData,
                  //     'member_termination.termination_date',
                  //     null,
                  //   );
                  // }

                  const terminationDate = get(
                    memberData,
                    'member_termination.termination_date',
                    null,
                  );

                  if (!isNil(terminationDate)) {
                    const terminationDateMoment: moment.Moment = moment.parseZone(
                      terminationDate,
                    );
                    if (
                      !isNil(terminationDateMoment) &&
                      terminationDateMoment.isValid()
                    ) {
                      return terminationDateMoment.format('MMM DD, YYYY');
                    }
                  }

                  return '-';
                })()}
              />
            ) : null}
          <HMOItem
            name="Card Printed Date"
            value={
              memberData && isValidDate(memberData.date_printed)
                ? moment(memberData.date_printed).format('MMM DD, YYYY')
                : '-'
            }
          />
          <HMOItem
            name="VIP Member"
            value={memberData ? (memberData.is_vip ? 'Yes' : 'No') : '-'}
          />
        </Grid>
        <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
          <HMOItem
            name="Type"
            value={
              memberData && memberData.member_type
                ? memberData.member_type
                : '-'
            }
          />
          <HMOItem
            name="Plan Type"
            // value={
            //   // memberData && memberData.plan_type ? memberData.plan_type : '-'
            //   updatedPlanTypeName ? updatedPlanTypeName : '-'
            // }
            value={getNewPlanType()}
          />
          <HMOItem
            name="Contract Plan Type"
            value={get(
              props.planTypeNode,
              'custom_metadata.contract_plan_type',
              '-',
            )}
          />
          <HMOItem name="MBL" value={MBL ? MBL : '-'} />
          <HMOItem name="ABL" value={ABL ? ABL : '-'} />
          <HMOItem name="PEC" value={PEC ? PEC : '-'} />
          <HMOItem
            name="Philhealth Rider"
            value={
              memberData ? (memberData.is_philhealth_rider ? 'Yes' : 'No') : '-'
            }
          />
          <HMOItem
            name="Batch Process No."
            value={get(memberData, 'batch_name', '-')}
          />
          <HMOItem
            name="Member Consent"
            value={
              memberData ? (memberData.is_member_consent ? 'Yes' : 'No') : '-'
            }
          />
        </Grid>
        <Grid
          container
          item
          xs={12}
          style={{ paddingTop: 32, paddingBottom: 0 }}
        >
          <Grid item xs={12} md={6}>
            <HMOItem
              name="Date of Birth"
              value={
                memberData //&& isValidDate(memberData.date_of_birth)
                && memberData.date_of_birth !== ''
                && moment(memberData.date_of_birth).format('MMM DD, YYYY') 
                !== moment(new Date()).format('MMM DD, YYYY')
                  ? moment(memberData.date_of_birth).format('MMM DD, YYYY')
                  : '-'
              }
            />
            <HMOItem
              name="Age"
              value={
                memberData && isValidDate(memberData.date_of_birth) && memberData.date_of_birth !== ''
                  ? '' + moment().diff(memberData.date_of_birth, 'years', false)
                  : '-'
              }
            />
            <HMOItem
              name="Gender"
              value={memberData && memberData.gender !== '' ? memberData.gender : '-'}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <HMOItem
              name="Civil Status"
              value={memberData  &&  memberData.civil_status !== '' ? memberData.civil_status : '-'}
            />
            <HMOItem
              name="Title/Prefix"
              value={memberData && memberData.title ? memberData.title : '-'}
            />
            <HMOItem
              name="Suffix"
              value={memberData && memberData.suffix ? memberData.suffix : '-'}
            />
          </Grid>
        </Grid>
        {memberData && memberData.member_type === 'Principal' ? (
          <Grid
            item
            xs={6}
            style={{ paddingTop: 48, paddingBottom: 48, paddingRight: 16 }}
          >
            <Typography className={classes.hmographsubtitle}>
              Enrolled Dependents
            </Typography>
            <Paper className={classes.hmodependentsbox}>
              <Table
                data-cy={'hmo_info_type_data_table'}
                id={'hmo_info_type_data_table'}
              >
                <TableHead>
                  <TableRow>
                    <TableCell className={classes.hmoheadercell}>
                      Dependent Name
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Relationship
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Status
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dependents && dependents.length > 0 ? (
                    dependents.map((row, index) => {
                      let dName = `${row['first_name'].trim()} 
                      ${row['middle_name'] ? row['middle_name'].trim() : ''} 
                      ${row['last_name'].trim()} 
                      ${row['suffix'] ? ', ' + row['suffix'].trim() : ''}`;
                      return (
                        <TableRow key={index}>
                          {/* <TableCell>{row['first_name']}</TableCell>
                        <TableCell>{row['middle_name']}</TableCell>
                        <TableCell>{row['last_name']}</TableCell>
                        <TableCell>{row['suffix']}</TableCell>
                        <TableCell>{row['civil_status']}</TableCell>
                        <TableCell>{row['gender']}</TableCell>
                        <TableCell>
                          {moment(new Date(row['date_of_birth'])).format(
                            'MMM DD, YYYY',
                          )}
                        </TableCell>
                        <TableCell>
                          {row['relationship_to_principal']}
                        </TableCell>
                        <TableCell>
                          <Link
                            onClick={() => {
                              window.location.href = `#/membership/view-member/${row._id}`;
                              window.location.reload();
                            }}
                            className={classes.hmodependentsbox_link}
                          >
                            View
                          </Link>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <Link
                            onClick={() => {
                              props.handleRedirect(
                                `/membership/view-member/${row._id}/${memberData._id}`,
                              );
                            }}
                            className={classes.hmodependentsbox_link}
                          >
                            Edit
                          </Link>
                        </TableCell> */}

                          <TableCell>
                            <Link
                              onClick={() => {
                                window.location.href = `#/membership/view-member/${row._id}`;
                                window.location.reload();
                              }}
                              className={classes.hmodependentsbox_link}
                              style={{ cursor: 'pointer' }}
                            >
                              {dName.trim()}
                            </Link>
                          </TableCell>
                          <TableCell>
                            {row['relationship_to_principal']}
                          </TableCell>
                          <TableCell>
                            {row['member_status']}
                          </TableCell>
                        </TableRow>
                      )
                    }
                    )
                  ) : (
                      <TableRow>
                        <TableCell
                          component="th"
                          scope="row"
                          style={{ textAlign: 'center' }}
                          colSpan={3}
                        >
                          No enrolled dependents
                      </TableCell>
                      </TableRow>
                    )}
                </TableBody>
              </Table>
            </Paper>
          </Grid>
        ) : memberData && memberData.member_type === 'Dependent' ? (
          <Grid
            item
            xs={6}
            style={{ paddingTop: 48, paddingBottom: 48, paddingRight: 16 }}
          >
            <Typography className={classes.hmographsubtitle}>
              Principals
            </Typography>
            <Paper className={classes.hmoprincipalsbox}>
              <Table
                data-cy={'hmo_info_type_data_table'}
                id={'hmo_info_type_data_table'}
                size="small"
              >
                <TableHead>
                  <TableRow>
                    {/* <TableCell className={classes.hmoheadercell}>
                      First Name
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Middle Name
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Last Name
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Suffix
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Civil Status
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Gender
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Birthdate
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Relationship to Principal
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}></TableCell> */}
                    <TableCell className={classes.hmoheadercell}>
                      Principal Name
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Relationship
                    </TableCell>
                    <TableCell className={classes.hmoheadercell}>
                      Status
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                 
                  {memberData.principal_name &&
                    memberData.principal_name !== '' &&
                    memberData.principal_temp_id &&
                    memberData.principal_temp_id !== '' ? (
                      <TableRow>
                        {/* <TableCell>{memberData.principal_first_name}</TableCell>
                        <TableCell>{memberData.principal_middle_name}</TableCell>
                        <TableCell>{memberData.principal_last_name}</TableCell>
                        <TableCell>
                          {principal && principal.length > 0
                            ? principal[0].suffix
                            : ''}
                        </TableCell>
                        <TableCell>
                          {principal && principal.length > 0
                            ? principal[0].civil_status
                            : ''}
                        </TableCell>
                        <TableCell>
                          {principal && principal.length > 0
                            ? principal[0].gender
                            : ''}
                        </TableCell>
                        <TableCell>
                          {principal && principal.length > 0
                            ? moment(new Date(principal[0].date_of_birth)).format(
                              'MMM DD, YYYY',
                            )
                            : ''}
                        </TableCell>

                        <TableCell>
                          {memberData.relationship_to_principal}
                        </TableCell>
                        <TableCell>
                          {principal && principal.length > 0 ? (
                            <Link
                              //href={`#/membership/view-member/${principal[0]._id}`}
                              //onClick={()=>{props.handleRedirect(`/membership/view-member/${principal[0]._id}`)}}
                              className={classes.hmodependentsbox_link}
                              onClick={() => {
                                window.location.href = `#/membership/view-member/${principal[0]._id}`;
                                window.location.reload();
                              }}
                            >
                              View
                            </Link>
                          ) : (
                              ''
                            )}
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        {principal && principal.length > 0 ? (
                            <Link
                              onClick={() => {
                                props.handleRedirect(
                                  `/membership/view-member/${principal[0]._id}/${memberData._id}`,
                                );
                              }}
                              //to={`/membership/view-member/${principal[0]._id}/${memberData._id}`}
                              className={classes.hmodependentsbox_link}
                            >
                              Edit
                            </Link>
                          ) : (
                              ''
                            )}
                        </TableCell> */}

                        <TableCell>
                          <Link
                            onClick={() => {
                              window.location.href = `#/membership/view-member/${principal[0]._id}`;
                              window.location.reload();
                            }}
                            className={classes.hmodependentsbox_link}
                            style={{ cursor: 'pointer' }}
                          >
                            {`${memberData.principal_first_name.trim()} 
                              ${memberData.principal_middle_name
                                ? memberData.principal_middle_name.trim() : ''} 
                              ${memberData.principal_last_name.trim()} 
                              ${principal && principal.length > 0 && principal[0].suffix
                                ? ', ' + principal[0].suffix.trim() : ''}`}
                          </Link>
                        </TableCell>
                        <TableCell>
                          {
                            memberData.relationship_to_principal
                          }
                        </TableCell>
                        <TableCell>
                          {principal && principal.length > 0 && principal[0].member_status
                            ? principal[0].member_status : ''}
                        </TableCell>
                      </TableRow>
                    ) : (
                      <TableRow>
                        <TableCell
                          component="th"
                          scope="row"
                          style={{ textAlign: 'center' }}
                          colSpan={3}
                        >
                          No enrolled principal
                      </TableCell>
                      </TableRow>
                    )}
                </TableBody>
              </Table>
            </Paper>
          </Grid>
        ) : null}
        <Grid
          item
          xs={6}
          style={{ paddingTop: 48, paddingBottom: 48, paddingRight: 16 }}
        >
          <Grid container>
            <Grid item xs={6}>
              <Typography className={classes.hmographsubtitle}>
                Remarks
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box display="flex" flexDirection="row-reverse">
                <Typography
                  data-cy={'hmo_info_add_remarks_btn'}
                  id={'hmo_info_add_remarks_btn'}
                  className={classes.hmoaddremarksbtn}
                  onClick={() => {
                   
                    setAddRemarkModalOpen(true);
                  }}
                >
                  + Remarks
                </Typography>
              </Box>
            </Grid>
          </Grid>
          <Stepper
            activeStep={memberData &&
              memberData.member_remarks
              ? memberData.member_remarks.length - 1 : 0}
            orientation="vertical"
            className={memberData &&
              memberData.member_remarks && memberData &&
              memberData.member_remarks.length > 0
              ? classes.remarksStep : classes.noRemarksStep}
          >
            {memberData &&
              memberData.member_remarks &&
              memberData.member_remarks.length > 0 ? (
                memberData.member_remarks.map((row, index) => (
                  <Step active key={index}>
                    <StepLabel
                      icon={
                        <Icon color={memberData.member_remarks.length - 1 === index
                          ? 'primary' : 'default'}><CommentIcon /></Icon>
                      }
                    >
                      <Grid container>
                        <Grid item xs={6}
                          style={{
                            display: 'flex',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                          }}
                        >
                          <Typography style={{ display: 'flex', fontWeight: 'bold' }}>
                            {row['date_created']
                              ? moment(row['date_created']).format('MMM DD, YYYY').toString().toUpperCase()
                              : null}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}
                          style={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center'
                          }}
                        >
                          <Icon
                            color={memberData.member_remarks.length - 1 === index
                              ? 'primary' : 'default'}
                            fontSize={'small'}
                            style={{ display: 'flex', marginRight: '5px' }}
                          >
                            <PersonIcon />
                          </Icon>
                          <Typography style={{ display: 'flex', fontWeight: 'bold' }}>
                            {row['user_fullname'] ? row['user_fullname'] : null}
                          </Typography>
                        </Grid>
                      </Grid>
                    </StepLabel>
                    <StepContent>
                      <Typography>
                        {row['remarks'] ? row['remarks'] : null}
                      </Typography>
                    </StepContent>
                  </Step>
                ))
              ) : (
                <Typography
                  style={{ display: 'flex' }}
                >
                  <b>No Remarks</b>
                </Typography>
              )}
          </Stepper>
        </Grid>
        <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
          {leftsideCF}
        </Grid>
        <Grid item xs={12} md={6} style={{ paddingTop: 0, paddingBottom: 0 }}>
          {rightsideCF}
        </Grid>
      </Grid>

      <AddMemberRemarksModal
        isModalOpen={addRemarkModalOpen}
        onClose={() => {
          setAddRemarkModalOpen(false);
        }}
        onActionSubmit={remarks => {
          setAddRemarkModalOpen(false);
          props.handleRemarkSubmit(remarks);
        }}
      />
    </>
  );
};

HMOInformation.defaultProps = {
  memberData: {},
  dependents: [],
  clientData: {},
  contractId: '',
  contractName: '',
  MBL: '',
  ABL: '',
  PEC: '',
  clientFields: [],
  mctNode: null,
};
