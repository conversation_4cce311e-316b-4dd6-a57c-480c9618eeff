import React, { useState, useEffect } from 'react';

import { createStyles, Theme, makeStyles } from '@material-ui/core/styles';
import Button from '@material-ui/core/Button';
import Dialog, { DialogProps } from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Grid from '@material-ui/core/Grid';
import LinearProgress from '@material-ui/core/LinearProgress';
import clsx from 'clsx';
import { isNil, toLower } from 'lodash';
// import { Utils } from '@hims/core';
// import { API } from '../../../API';
import { TableComponent } from 'Components/UI/TableComponent';
import { CircularProgress } from '@material-ui/core';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      flexWrap: 'wrap',
    },
    dialog: {
      align: 'center',
      justify: 'center',
      width: '100%',
      fontFamily: 'usual',
    },
    dialogTitle: {
      marginTop: theme.spacing(2),
      marginBottom: theme.spacing(2),
      fontSize: '1rem',
      fontWeight: 600,
    },
    dialogTitleProcessing: {
      color: '#272E4C',
    },
    dialogTitleDone: {
      color: '#3ab77d',
    },
    dialogTitleTicket: {
      marginLeft: '16px',
      fontSize: '0.875rem',
      fontWeight: 500,
      color: '#151c1b80',
      textTransform: 'uppercase',
    },
    dialogTitleSubtext: {
      fontSize: '0.875rem',
      fontWeight: 400,
      color: '#272E4C',
    },
    dialogContent: {
      paddingTop: '0px',
    },
    dialogContentText: {
      fontSize: '0.875rem',
    },
    container: {},
    dialogAction: {
      marginBottom: theme.spacing(2),
      marginTop: theme.spacing(2),
      justifyContent: 'center',
    },
    boldText: {
      fontWeight: 'bold',
    },
    cancelButton: {
      marginRight: 25,
      border: '1px solid #3AB77D',
      color: '#3AB77D',
      backgroundColor: '#FFFFFF',
    },
    submitButton: {
      marginLeft: 25,
    },
    loadingCircularButton:{
      paddingLeft: 25,
      marginRight: 25,
    },
    closeButton: {
      position: 'absolute',
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    linearProgress: {
      height: 10,
      colorPrimary: {
        backgroundColor: '#3ab77d',
      },
      barColorPrimary: {
        backgroundColor: '#151c1b80',
      },
    },
    progress: {
      fontSize: '1rem',
      fontWeight: 600,
    },
    progressCount: {
      color: '#3ab77d',
    },
    progressTotalCount: {
      color: '#151c1b80',
    },
  }),
);

let staticFormattedColumns: any = {
  boolean_field: [
    // 'is_philhealth_rider',
    // 'is_member_consent',
    // 'is_vip',
    'philhealth_rider',
    'member_consent',
    'vip',
  ]
}

interface MemberUploadStatusModalProps {
  id?: string;
  ticketId?: string;
  isOpen: boolean;
  uploadedData: any;
  status?: any;
  onCancelProcess?: () => void;
  onBackToDashboard?: () => void;
  onCancelEnrollment?: () => void;
  onConfirmEnrollment?: () => void;
  pageType?: any;
  renewalType?: any;
}

export const MemberUploadStatusModal: React.FC<MemberUploadStatusModalProps> = (
  props: MemberUploadStatusModalProps,
): JSX.Element => {
  const classes = useStyles();

  const [fullWidth] = useState(true);
  const [maxWidth] = useState<DialogProps['maxWidth']>('xl');

  const [status, setStatus] = useState('');
  const [title, setTitle] = useState('');
  const [titleSubtext, setTitleSubtext] = useState('');
  const [titleCss, setTitleCss] = useState(classes.dialogTitleDone);

  const [processedCount, setProcessedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [percentage, setPercentage] = useState(0);

  const [actionButtons, setActionButtons] = useState<any[]>([]);

  useEffect(() => {
    if (props.status) {
      const { status, active_index, total_count } = props.status;

      if (!isNil(status) && !isNil(active_index) && !isNil(total_count)) {
        const percentDone = (active_index / total_count) * 100;

        setStatus(status);
        setTotalCount(total_count);
        setProcessedCount(active_index);
        setPercentage(percentDone);
      }
    }
  }, [props.status]);

  useEffect(() => {
    setDialogTitleProperties();
    getActionButtons();
  }, [status]);

  const cancelProcess = () => {
    if (props.onCancelProcess) {
      props.onCancelProcess();
    }
  };

  const backToDashboard = () => {
    if (props.onBackToDashboard) {
      props.onBackToDashboard();
    }
  };

  const cancelEnrollment = () => {
    if(props.onCancelEnrollment){
      props.onCancelEnrollment();
    }
  }

  const confirmEnrollment = () => {
    if (props.onConfirmEnrollment) {
      // if(props && props.ticketId) {
      //   console.log('call confirmEnrollment activity memberuploadstatusmodal', props)
      //   Utils.StorageService('user_data').then(result => {
      //     let user_name: string = '';
      //     if (result && result !== undefined) {
      //       for (const i in result) {
      //         if (result[i]['key'] === 'username') {
      //           user_name = result[i]['value'];
      //         }  
      //       }
      //       API.activityEnrollMemberList( props.ticketId,props, user_name).then(response => { 
      //         console.log('memberuploadstatusmodal',response)
      //       }).catch(e => {
      //         console.log('claiming error', e)
      //       })
      //     };
      //   })
      // }
      props.onConfirmEnrollment();
    }
  };

  const setDialogTitleProperties = () => {
    let css = titleCss;
    let titleText = title;
    let subtext = titleSubtext;

    switch (toLower(status)) {
      case toLower('SUCCESS'):
        titleText = 'Finished Processing Members';
        subtext = props.pageType === 'void' ? 'Processing the void masterlist is done. You may proceed to confirm changes.' 
        : 'Processing the member list is done. You may proceed with the enrollment.';
        css = classes.dialogTitleDone;
        break;
      case toLower('INITIAL'):
      case toLower('ONGOING'):
        titleText = 'Processing Members';
        subtext = props.pageType === 'void' ? 'Please wait while we process the void masterlist you uploaded. You may go back to this page later or check the dashboard for its progress.'
        : 'Please wait while we process the members you uploaded. You may go back to this page later or check the dashboard for its progress.';
        css = classes.dialogTitleProcessing;
        break;
      case toLower('UPLOADING'):
        titleText = 'Uploading Members';
        subtext =
          'Please wait while we upload the members. You may go back to this page later or check the dashboard for its progress.';
        css = classes.dialogTitleProcessing;
        break;
      case toLower('UPLOADED'):
        titleText = 'Finished Uploading Members' + `${props?.renewalType === 'uploadRenewal' ? ' to Renew' : ''}`; ;
        subtext =
          'Uploading the member list is done. You may proceed with the ' + `${props?.renewalType === 'uploadRenewal' ? 'renewal' : 'enrollment'}.`;
        css = classes.dialogTitleProcessing;
        break;
      default:
        titleText = title;
        subtext = titleSubtext;
        css = titleCss;
    }

    setTitle(titleText);
    setTitleSubtext(subtext);
    setTitleCss(css);
  };

  const getActionButtons = () => {
    let buttons: any[] = [];
    if(props.pageType==='void'){
      switch (toLower(status)) {
        case toLower('SUCCESS'):
          buttons = [
            <Button
              data-cy={'member_upload_status_cancel_btn'}
              id={'member_upload_status_cancel_btn'}
              key={'cancel-process-btn'}
              className={classes.cancelButton}
              variant="contained"
              color="secondary"
              onClick={cancelEnrollment}
            >
              Cancel
            </Button>,
            <Button
              data-cy={'member_upload_status_confirm_btn'}
              id={'member_upload_status_confirm_btn'}
              key={'confirm-enrollment-btn'}
              className={classes.submitButton}
              variant="contained"
              color="primary"
              onClick={confirmEnrollment}
            >
              Confirm Enrollment
            </Button>,
          ];
          break;
        case toLower('INITIAL'):
        case toLower('ONGOING'):
          buttons = [
            <Button
              data-cy={'member_upload_status_cancel_btn'}
              id={'member_upload_status_cancel_btn'}
              key={'cancel-process-btn'}
              className={classes.cancelButton}
              variant="contained"
              color="secondary"
              onClick={cancelProcess}
            >
              Cancel Process
            </Button>,
            <Button
              data-cy={'member_upload_status_dashboard_btn'}
              id={'member_upload_status_dashboard_btn'}
              key={'back-dashboard-btn'}
              className={classes.submitButton}
              variant="contained"
              color="primary"
              onClick={backToDashboard}
            >
              Back to Dashboard
            </Button>,
          ];
          break;
        default:
          buttons = [];
      }
    } else {
      switch (toLower(status)) {
        case toLower('INITIAL'):
        case toLower('ONGOING'):
          buttons = [
            <Button
              data-cy={'member_upload_status_dashboard_btn'}
              id={'member_upload_status_dashboard_btn'}
              key={'back-dashboard-btn'}
              className={classes.cancelButton}
              variant="contained"
              color="primary"
              onClick={backToDashboard}
            >
              Back to Dashboard
            </Button>,
            <Button
              data-cy={'member_upload_status_confirm_btn'}
              id={'member_upload_status_confirm_btn'}
              key={'confirm-enrollment-btn'}
              // className={classes.loadingCircularButton}
              variant="contained"
              style={{backgroundColor:"#5b5b5b",color:'#ffffff'}}
              onClick={()=>{}}
            >
              <p style={{paddingLeft:10}} />
              <CircularProgress color="inherit" size='1.5rem'/>
              <p style={{paddingRight:10}} />
              Processing...
              <p style={{paddingRight:40}} />
            </Button>,
          ];
          break;
  
        case toLower('UPLOADED'):
          buttons = [
            <Button
              data-cy={'member_upload_status_cancel_btn'}
              id={'member_upload_status_cancel_btn'}
              key={'cancel-process-btn'}
              className={classes.cancelButton}
              variant="contained"
              color="secondary"
              onClick={cancelEnrollment}
            >
              Cancel
            </Button>,
            <Button
              data-cy={'member_upload_status_confirm_btn'}
              id={'member_upload_status_confirm_btn'}
              key={'confirm-enrollment-btn'}
              className={classes.submitButton}
              variant="contained"
              color="primary"
              onClick={confirmEnrollment}
            >
              {props?.renewalType === 'uploadRenewal' ? 'Confirm Renewal' : 'Confirm Enrollment'}
            </Button>,
          ];
          break;
        case toLower('UPLOADING'):
          buttons = [
            <Button
              data-cy={'member_upload_status_cancel_btn'}
              id={'member_upload_status_cancel_btn'}
              key={'cancel-process-btn'}
              className={classes.cancelButton}
              variant="contained"
              color="secondary"
              onClick={cancelProcess}
            >
              Cancel Process
            </Button>,
            <Button
              data-cy={'member_upload_status_dashboard_btn'}
              id={'member_upload_status_dashboard_btn'}
              key={'back-dashboard-btn'}
              className={classes.submitButton}
              variant="contained"
              color="primary"
              onClick={backToDashboard}
            >
              Back to Dashboard
            </Button>,
          ];
          break;
        default:
          buttons = [];
      }
    }
    

    setActionButtons(buttons);
  };

  return (
    <Dialog
      id={props.id}
      open={props.isOpen}
      fullWidth={fullWidth}
      maxWidth={maxWidth}
      aria-labelledby="max-width-dialog-title"
    >
      <DialogTitle
        id={`${props.id}-dialog-title`}
        className={classes.dialogTitle}
        disableTypography={true}
      >
        <div>
          <span
            id={'member_upload_status_title'}  
            className={clsx(classes.dialogTitle, titleCss)}>{title}</span>
          <span
            id={'member_upload_status_ticket_id'} 
            className={classes.dialogTitleTicket}
          >
            TICKET ID: {props.ticketId || '-'}
          </span>
        </div>
        <div>
          <span className={classes.dialogTitleSubtext}>{titleSubtext}</span>
        </div>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid
          container
          className={classes.container}
          justify="flex-start"
          alignItems="stretch"
          direction="column"
          spacing={1}
        >
          <Grid item xs={12}>
            <LinearProgress
              data-cy={'member_upload_status_progress'}
              id={'member_upload_status_progress'}
              className={classes.linearProgress}
              variant="determinate"
              value={percentage}
            />
          </Grid>
          <Grid item xs={12}>
            <div>
              <span
                data-cy={'member_upload_status_processed_entries'}
                id={'member_upload_status_processed_entries'}  
                className={clsx(classes.progress, classes.progressCount)}
              >
                {`${processedCount}/`}
              </span>
              <span
                data-cy={'member_upload_status_total_entries'}
                id={'member_upload_status_total_entries'}  
                className={clsx(classes.progress, classes.progressTotalCount)}
              >{`${totalCount} entries`}</span>
            </div>
          </Grid>
          <Grid item xs={12}>
            <TableComponent
              id="upload_file_table"
              rows={
                Object.keys(props.uploadedData).length > 0
                  ? props.uploadedData['rows']
                  : []
              }
              columns={
                Object.keys(props.uploadedData).length > 0
                  ? props.uploadedData['columns']
                  : []
              }
              message="No available data"
              onClickRow={() => {}}
              disableSelect
              disableSearch
              disableFilter
              disableSort
              formattedColumns={
                // Object.keys(props.uploadedData).length > 0
                //   ? props.uploadedData['formattedColumns']
                //   : {}
                staticFormattedColumns
              }
              columnExtensions={
                Object.keys(props.uploadedData).length > 0
                  ? props.uploadedData['columnExtensions']
                  : []
              }
              pageSize={10}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className={classes.dialogAction}>
        {actionButtons}
      </DialogActions>
    </Dialog>
  );
};

MemberUploadStatusModal.defaultProps = {
  id: 'member-upload-status-modal-id',
  isOpen: false,
};
