import * as React from 'react';
import { makeStyles, withStyles } from '@material-ui/core/styles';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Button,
  IconButton,
  Typography,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
} from '@material-ui/core/';
import { Link } from 'react-router-dom';
import CloseIcon from '@material-ui/icons/Close';
import { TerminationData } from '../IViewMemberPage';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import { Utils } from '@hims/core';
import { API } from 'Pages/API';
// import moment from 'moment';
interface IProps {
  id: string;
  isModalOpen: boolean;
  memberData: any;
  dependents: any[];
  clientData: any;
  terminationData: TerminationData;
  onClose: () => void;
  onTerminate: (data: any) => void;
}

const useStyles = makeStyles(theme => ({
  root: {
    flexWrap: 'wrap',
  },
  hmodependentsbox: {
    height: 175,
    borderRadius: 0,
    overflow: 'auto',
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
  },
  hmodependentsbox_link: {
    color: 'rgb(39, 46, 76)',
  },
  dialog: {
    align: 'center',
    justify: 'center',
    width: '95%',
    fontFamily: 'usual',
  },
  dialogContent: {
    paddingTop: '0px',
    maxWidth: 500,
  },
  dialogContentText: {
    fontSize: '0.875rem',
  },
  container: {
    paddingRight: '15px',
    paddingLeft: '15px',
    minWidth: '680px',
    width: 'auto',
  },
  dialogTitle: {
    paddingTop: theme.spacing(5),
    minWidth: 500,
  },
  dialogFirstTitle: {
    fontSize: '16px',
    color: '#272E4C',
    fontWeight: 600,
    marginBottom: '5px',
  },
  dialogSecondTitle: {
    fontSize: '14px',
    color: '#272E4C',
  },
  dialogAction: {
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(2),
  },
  boldText: {
    fontWeight: 'bold',
  },
  contentText: {
    fontWeight: 700,
  },
  cancelButton: {
    minWidth: 129,
    marginRight: 25,
    border: '1px solid #3AB77D',
    color: '#3AB77D',
    backgroundColor: '#FFFFFF',
  },
  terminateButton: {
    minWidth: 129,
    backgroundColor: '#FD5451',
  },
  previousButton: {
    minWidth: 120,
    fontWeight: 600,
  },
  nextButton: {
    minWidth: 130,
    fontWeight: 600,
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  tab: {
    textAlign: 'left',
    alignItems: 'left',
  },
  error: {
    marginTop: 4,
    fontSize: '10px',
    color: '#FF0000',
  },
}));

const TerminateButton = withStyles({
  root: {
    color: '#FFFFFF',
    backgroundColor: '#FD5451',
    '&:hover': {
      backgroundColor: '#fd2724',
    },
  },
})(Button);

const CancelButton = withStyles({
  root: {
    '&:hover': {
      backgroundColor: '#3AB77D',
      color: '#FFFFFF',
    },
  },
})(Button);

export const TerminateMemberConfirmationModal: React.FC<IProps> = (
  props: IProps,
): JSX.Element => {
  const classes = useStyles();

 
function handleTerminateMember() {
  // emailAddress.forEach(email => {
  //     console.log('isValid: ',isValidEmailAddress(email));
  //     if(!isValidEmailAddress(email)) {
  //         setError(true);
  //         setErrorMessage('Invalid email address.');
  //     } else {
  //         setError(false);
  //         setErrorMessage('');
  //         props.onSend(emailAddress);
  //     }
  // });

  // if(emailAddress.length === 0){

  // }

  if(props && props.memberData){
    let memberId = props.memberData.member_id;
    let membeData = props.memberData;
    const terminationData = props.terminationData;
    let memberAndTermData = {...membeData, ...terminationData};

    Utils.StorageService('user_data').then(result => {
      let user_name: string = '';
      console.log(user_name)
      //console.log('Utils', result)
      if (result && result !== undefined) {
        for (const i in result) {
          if (result[i]['key'] === 'username') {
            user_name = result[i]['value'];
          }
        }
        API.activityTerminate(memberId, memberAndTermData, user_name).then(response => {
          if(response == undefined){
            console.log('response', response)
          }
        }).catch(e => {
          console.log('terminate error', e)
        })
      };
    });
  };

  props.onTerminate(0);
  props.onClose();
}

  // function isValidEmailAddress(address) {
  //     return !! address.match(/.+@.+/);
  // }

  return (
    <Dialog
      maxWidth={false}
      open={props.isModalOpen}
      scroll={'paper'}
      aria-labelledby="modal-title"
      className={classes.dialog}
      style={{ zIndex: 9999 }}
    >
      <DialogTitle
        className={classes.dialogTitle}
        disableTypography={true}
        id="modal-title"
      >
        <div className={classes.dialogFirstTitle}>
          Confirm Membership Termination
        </div>
        <div className={classes.dialogSecondTitle}></div>
        <IconButton
          data-cy={'terminate_member_conmod_close_btn'}
          id={'terminate_member_conmod_close_btn'}
          aria-label="close"
          className={classes.closeButton}
          onClick={props.onClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent className={classes.dialogContent}>
        <Grid item xs={12}>
          <Typography>
            Are you sure you want to terminate the membership of
            <span style={{ fontWeight: 'bold' }}>
              {` ${props.memberData ?
                `${props.memberData['first_name'] ? props.memberData['first_name'] : ''} 
                ${props.memberData['middle_name'] ? props.memberData['middle_name'] : ''} 
                ${props.memberData['last_name'] ? props.memberData['last_name'] : ''}${props.memberData['suffix'] && props.memberData['suffix'] !== null && props.memberData['suffix'].trim() !== ''
                  ? ', ' + props.memberData['suffix'] : ''}` : ''}`}
            </span>{' '}
            in
            <span style={{ fontWeight: 'bold' }}>
              {` ${
                props.clientData ? props.clientData['registered_name'] : ''
                }`}
            </span>{' '}
            on
            <span style={{ fontWeight: 'bold' }}>
              {` ${GlobalFunction.toCommonDateString(
                props.terminationData.terminationDate,
              )}`}
            </span>
            ?
            <span>
              {` ${props.memberData ? props.memberData['first_name'] : ''}`}
            </span>{' '}
            and
            <span>
              {` ${
                props.memberData && props.memberData['gender'] === 'Female'
                  ? 'her'
                  : 'his'
                }`}
            </span>{' '}
            dependents will no longer be eligible to any benefits in this
            contract.
          </Typography>
        </Grid>
      </DialogContent>

      <DialogContent className={classes.dialogContent}>
        <div style={{ paddingBottom: '1rem' }} />
        <Paper className={classes.hmodependentsbox}>
          <Table
            data-cy={'terminate_member_conmod_dependents_table'}
            id={'terminate_member_conmod_dependents_table'}
            size="small"
          >
            <TableHead>
              <TableRow>
                <TableCell>Dependent Name</TableCell>
                <TableCell>Relationship</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {props.dependents && props.dependents.length > 0 ? (
                props.dependents.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell component="th" scope="row">
                      <Link to={'#'} className={classes.hmodependentsbox_link}>
                        {`${row['first_name']} ${row['middle_name'] ? row['middle_name'] : ''} ${row['last_name']}${row['suffix'] && row['suffix'] !== null && row['suffix'].trim() !== ''
                          ? ', ' + row['suffix'] : ''}`}
                      </Link>
                    </TableCell>
                    <TableCell>{row['relationship_to_principal']}</TableCell>
                    <TableCell>{row['member_status']}</TableCell>
                  </TableRow>
                ))
              ) : (
                  <TableRow>
                    <TableCell
                      component="th"
                      scope="row"
                      style={{ textAlign: 'center' }}
                      colSpan={3}
                    >
                      No enrolled dependents
                  </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </Paper>
      </DialogContent>

      <DialogActions className={classes.dialogAction}>
        <Grid
          container
          direction="row"
          justify="space-evenly"
          alignItems="stretch"
        >
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <CancelButton
              data-cy={'terminate_member_conmod_cancel_btn'}
              id={'terminate_member_conmod_cancel_btn'}
              className={classes.cancelButton}
              variant="contained"
              color="primary"
              onClick={props.onClose}
            >
              Cancel
            </CancelButton>
          </Grid>
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <TerminateButton
              data-cy={'terminate_member_conmod_btn'}
              id={'terminate_member_conmod_btn'}
              className={classes.terminateButton}
              variant="contained"
              color="primary"
              onClick={handleTerminateMember}
            >
              Terminate
            </TerminateButton>
          </Grid>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

TerminateMemberConfirmationModal.defaultProps = {
  isModalOpen: false,
};
