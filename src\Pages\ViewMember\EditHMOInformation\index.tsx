import * as React from 'react';
// import { useState } from 'react';
import {
  Grid,
  Typography,
  Checkbox,
  // Hide on Nov. 27, 2020 for MS890
  // ExpansionPanel,
  // ExpansionPanelSummary,
  // ExpansionPanelDetails,
  FormHelperText,
  Select,
  MenuItem,
  OutlinedInput,
} from '@material-ui/core/';
// import ExpandMoreIcon from '@material-ui/icons/ExpandMore'; // Hide on Nov. 27, 2020 for MS890
import { makeStyles, withStyles } from '@material-ui/core/styles';
import { Components } from '@hims/core';
import moment from 'moment';
// import clsx from 'clsx'; // Hide on Nov. 27, 2020 for MS890
import MomentUtils from '@date-io/moment';
import {
  KeyboardDatePicker,
  MuiPickersUtilsProvider,
} from '@material-ui/pickers';
import { MaterialUiPickersDate } from '@material-ui/pickers/typings/date';
import ErrorIcon from '@material-ui/icons/Error';
import { find, get, isNil } from 'lodash';

type ISelectItem = Components.UI.InputSelect.ISelectItem;
const InputSelect = Components.UI.InputSelect.InputSelect;
const InputTextField = Components.UI.InputTextField.InputTextField;

const suffix_options = [
  {
    id: 0,
    value: ' ',
    label: 'None',
  },
  {
    id: 1,
    value: 'Sr.',
    label: 'Sr.',
  },
  {
    id: 2,
    value: 'Jr.',
    label: 'Jr.',
  },
  {
    id: 3,
    value: 'I',
    label: 'I',
  },
  {
    id: 4,
    value: 'II',
    label: 'II',
  },
  {
    id: 5,
    value: 'III',
    label: 'III',
  },
  {
    id: 6,
    value: 'IV',
    label: 'IV',
  },
  {
    id: 7,
    value: 'V',
    label: 'V',
  },
  {
    id: 8,
    value: 'Others',
    label: 'Others',
  },
];

const gender_options: ISelectItem[] = [
  {
    id: 0,
    value: 'Male',
    label: 'Male',
  },
  {
    id: 1,
    value: 'Female',
    label: 'Female',
  },
  {
    id: 2,
    value: 'Unspecified',
    label: 'Unspecified',
  },
];

const cs_options: ISelectItem[] = [
  {
    id: 0,
    value: 'Single',
    label: 'Single',
  },
  {
    id: 1,
    value: 'Married',
    label: 'Married',
  },
  {
    id: 2,
    value: 'Single Parent',
    label: 'Single Parent',
  },
  {
    id: 3,
    value: 'Annulled',
    label: 'Annulled',
  },
  {
    id: 4,
    value: 'Widowed',
    label: 'Widowed',
  },
  {
    id: 5,
    value: 'Divorced',
    label: 'Divorced',
  },
  {
    id: 6,
    value: 'Separated',
    label: 'Separated',
  },
  // {
  //   id: 7,
  //   value: 'Unspecified',
  //   label: 'Unspecified',
  // },
];

const rtp_options: ISelectItem[] = [
  {
    id: 0,
    value: 'Mother',
    label: 'Mother',
  },
  {
    id: 1,
    value: 'Father',
    label: 'Father',
  },
  {
    id: 2,
    value: 'Brother',
    label: 'Brother',
  },
  {
    id: 3,
    value: 'Sister',
    label: 'Sister',
  },
  {
    id: 4,
    value: 'Husband',
    label: 'Husband',
  },
  {
    id: 5,
    value: 'Wife',
    label: 'Wife',
  },
  {
    id: 6,
    value: 'Son',
    label: 'Son',
  },
  {
    id: 7,
    value: 'Daughter',
    label: 'Daughter',
  },
  {
    id: 8,
    value: 'Grandmother',
    label: 'Grandmother',
  },
  {
    id: 9,
    value: 'Grandfather',
    label: 'Grandfather',
  },
  {
    id: 10,
    value: 'Grandson',
    label: 'Grandson',
  },
  {
    id: 11,
    value: 'Granddaughter',
    label: 'Granddaughter',
  },
  {
    id: 12,
    value: 'Common Law Partner',
    label: 'Common Law Partner',
  },
  {
    id: 13,
    value: 'Same Sex Partner',
    label: 'Same Sex Partner',
  },
  {
    id: 14,
    value: 'Auntie',
    label: 'Auntie',
  },
  {
    id: 15,
    value: 'Uncle',
    label: 'Uncle',
  },
  {
    id: 16,
    value: 'Niece',
    label: 'Niece',
  },
  {
    id: 17,
    value: 'Nephew',
    label: 'Nephew',
  },
  {
    id: 18,
    value: 'Cousin',
    label: 'Cousin',
  },
  {
    id: 19,
    value: 'Household Help',
    label: 'Household Help',
  },
];

const memberType_options: ISelectItem[] = [
  {
    id: 0,
    value: 'Dependent',
    label: 'Dependent',
  },
  {
    id: 1,
    value: 'Principal',
    label: 'Principal',
  },
];


const isValidDate = (date: string) => {
  const d = new Date(date);
  return d instanceof Date && !isNaN(d.getTime());
};
console.log('isValidDate', isValidDate)

/**
 * text field items
 */

const hmofielditemStyles = makeStyles({
  disabledhmoname: {
    fontSize: '0.875rem',
    color: 'rgba(39, 46, 76, 0.4)',
    paddingBottom: 4,
  },
  enabledhmoname: {
    fontSize: '0.875rem',
    color: '#151C1B',
    paddingBottom: 4,
  },
});

interface HMOFieldItemProps {
  name?: string;
  value: string;
  disabled?: boolean;
  handleChange?: any;
  error?: boolean;
}

const HMOFieldItem: React.FC<HMOFieldItemProps> = (
  props: HMOFieldItemProps,
): JSX.Element => {
  const classes = hmofielditemStyles(props);

  const errorMsg = props.error ? props.name + ' is required/invalid' : '';

  return (
    <Grid
      xs={12}
      container
      alignItems="flex-start"
      style={{ paddingTop: 12, paddingBottom: 12, paddingRight: 24 }}
    >
      <Grid item xs={12}>
        <Typography
          className={
            props.disabled ? classes.disabledhmoname : classes.enabledhmoname
          }
        >
          {props.name}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <InputTextField
          data-cy={props.name ? 'hmo_field_' + props.name.toLowerCase().replace(' ', '_') : ''}
          id={props.name ? 'hmo_field_' + props.name.toLowerCase().replace(' ', '_') : ''}
          disabled={props.disabled}
          onChange={props.handleChange}
          error={props.error}
          errorMessage={errorMsg}
          value={props.value}
        />
      </Grid>
    </Grid>
  );
};

HMOFieldItem.defaultProps = {
  disabled: false,
  name: '',
  value: '',
  error: false,
  handleChange: function () { },
};

/**
 * checkbox items
 */

const hmocheckboxStyle = {
  size: 12,
  padding: 0,
  color: '#272E4C',
  '&$checked': {
    color: '#272E4C',
  },
};

const hmocbitemStyles = makeStyles({
  disabledhmoname: {
    fontSize: '0.875rem',
    color: 'rgba(39, 46, 76, 0.4)',
    paddingBottom: 4,
  },
  enabledhmoname: {
    fontSize: '0.875rem',
    color: '#151C1B',
    paddingBottom: 4,
  },
});

interface HMOCheckboxItemProps {
  name?: string;
  checked?: boolean;
  disabled?: boolean;
  handleChange?: any;
}

const HMOCheckboxItem: React.FC<HMOCheckboxItemProps> = (
  props: HMOCheckboxItemProps,
): JSX.Element => {
  const classes = hmocbitemStyles(props);

  return (
    <Grid
      container
      alignItems="center"
      style={{ paddingTop: 50, paddingBottom: 12, paddingRight: 24 }}
    >
      <Grid item xs={2}>
        <Checkbox
          data-cy={props.name ? 'hmo_checkbox_' + props.name.toLowerCase().replace(' ', '_') : ''}
          id={props.name ? 'hmo_checkbox_' + props.name.toLowerCase().replace(' ', '_') : ''}
          style={hmocheckboxStyle}
          disabled={props.disabled}
          checked={props.checked}
          onChange={props.handleChange}
        />
      </Grid>
      <Grid item xs={10} style={{ marginTop: 4 }}>
        <Typography
          className={
            props.disabled ? classes.disabledhmoname : classes.enabledhmoname
          }
        >
          {props.name}
        </Typography>
      </Grid>
    </Grid>
  );
};

HMOCheckboxItem.defaultProps = {
  disabled: false,
  name: '',
  checked: false,
  handleChange: function () { },
};

/**
 * select items
 */

const hmoselectitemStyles = makeStyles(theme => ({
  disabledhmoname: {
    fontSize: '0.875rem',
    color: 'rgba(39, 46, 76, 0.4)',
    paddingBottom: 4,
  },
  enabledhmoname: {
    fontSize: '0.875rem',
    color: '#151C1B',
    paddingBottom: 4,
  },
  customSelect: {
    marginTop: theme.spacing(1),
    backgroundColor: 'white'
  },
  customSelectFormControl: {
    width: '100%'
  },
  customSelectIcon: {
    marginRight: theme.spacing(2.5),
    color: grayColor,
  },
}));

const labelColor = 'rgb(21,28,28)';
const grayColor = 'rgba(39,52,76,0.3)';
const grayColorFocused = 'rgba(39,52,76,0.5)';
const borderCss = {
  borderColor: grayColor,
  borderRadius: 5,
};
const borderStyles = {
  root: {
    '& label.Mui-focused': {
      color: labelColor,
    },
    '& .MuiInput-underline:after': {
      borderBottomColor: grayColor,
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': borderCss,
      '&:hover fieldset': borderCss,
      '&.Mui-focused fieldset': {
        ...borderCss,
        borderColor: grayColorFocused,
      },
    },
  },
};

interface HMOSelectItemProps {
  name?: string;
  value?: string;
  options: ISelectItem[];
  disabled?: boolean;
  handleChange?: any;
}

const HMOSelectItem: React.FC<HMOSelectItemProps> = (
  props: HMOSelectItemProps,
): JSX.Element => {
  const classes = hmoselectitemStyles(props);
  const StyledOutlinedInput = withStyles(borderStyles)(OutlinedInput);

  // add the name of the dropdown fields that need to be disabled into the array
  // since the old component doesn't apply the 'disabled' prop by default
  let disabledDropdownFields: any[] = [];

  if (props.disabled === true) {
    disabledDropdownFields.push(props.name);
  }

  // console.log('disabledDropdownFields val', disabledDropdownFields)

  return (
    <Grid
      xs={12}
      container
      alignItems="flex-start"
      style={{ paddingTop: 12, paddingBottom: 12, paddingRight: 24 }}
    >
      <Grid item xs={12}>
        <Typography
          className={
            props.disabled ? classes.disabledhmoname : classes.enabledhmoname
          }
        >
          {props.name}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        {disabledDropdownFields.includes(props.name) ? (
          <div className={classes.customSelectFormControl}>
            <Select
              className={classes.customSelect}
              value={props.value}
              disabled={props.disabled}
              input={
                <StyledOutlinedInput
                  id={props.name ? 'hmo_select_' + props.name.toLowerCase().replace(' ', '_') : ''}
                  name={props.name ? 'hmo_select_' + props.name.toLowerCase().replace(' ', '_') : ''}
                  error={false}
                  labelWidth={0}
                  fullWidth
                />
              }
            >
              <MenuItem key={''} value={''}>{''}</MenuItem>
              {props.options.map((option, idx) => {
                return (
                  <MenuItem key={idx} value={option.value}>{option.label}</MenuItem>
                )
              })}
            </Select>
          </div>
        ) : (
          <InputSelect
            data-cy={props.name ? 'hmo_select_' + props.name.toLowerCase().replace(' ', '_') : ''}
            id={props.name ? 'hmo_select_' + props.name.toLowerCase().replace(' ', '_') : ''}
            data={props.options}
            value={props.value}
            required={true}
            onChange={(item: ISelectItem) => {
              props.handleChange(item);
            }}
          />
        )}
      </Grid>
    </Grid>
  );
};

HMOSelectItem.defaultProps = {
  name: '',
  value: undefined,
  options: [],
  disabled: false,
  handleChange: function () { },
};

/**
 * picker items
 */

const hmopickeritemStyles = makeStyles({
  disabledhmoname: {
    fontSize: '0.875rem',
    color: 'rgba(39, 46, 76, 0.4)',
    paddingBottom: 4,
  },
  enabledhmoname: {
    fontSize: '0.875rem',
    color: '#151C1B',
    paddingBottom: 4,
  },
  error: {
    color: '#E53935',
    fontSize: '12px',
  },
  errorIcon: {
    color: '#E53935',
    fontSize: 'small',
    float: 'right',
  },
});

interface HMOPickerItemProps {
  name: string;
  value: Date;
  error: boolean;
  disabled?: boolean;
  handleChange?: any;
}

const HMOPickerItem: React.FC<HMOPickerItemProps> = (
  props: HMOPickerItemProps,
): JSX.Element => {
  const classes = hmopickeritemStyles(props);

  const errorMsg = props.error ? props.name + ' is required' : '';

  return (
    <Grid
      xs={12}
      container
      alignItems="flex-start"
      style={{ paddingTop: 12, paddingBottom: 4, paddingRight: 24 }}
    >
      <Grid item xs={12}>
        <Typography
          className={
            props.disabled ? classes.disabledhmoname : classes.enabledhmoname
          }
        >
          {props.name}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <MuiPickersUtilsProvider utils={MomentUtils}>
          <KeyboardDatePicker
            // id={props.name.split(' ').join('-')}
            data-cy={props.name ? 'hmo_picker_' + props.name.toLowerCase().replace(' ', '_') : ''}
            id={props.name ? 'hmo_picker_' + props.name.toLowerCase().replace(' ', '_') : ''}
            className={
              props.error && !moment(props.value).isValid()
                ? 'edithmo-date-picker-error' : 'edithmo-date-picker'
            }
            variant="inline"
            format="MM/DD/YYYY"
            autoOk
            value={props.value}
            onChange={(date: MaterialUiPickersDate | null) => {
              props.handleChange(date ? date.toDate() : null);
            }}
            disabled={props.disabled}
          />
        </MuiPickersUtilsProvider>
        {props.error && !moment(props.value).isValid() && (
          <div>
            <FormHelperText className={classes.error}>
              {errorMsg}
              <ErrorIcon className={classes.errorIcon} />
            </FormHelperText>
          </div>
        )}
      </Grid>
    </Grid>
  );
};

interface HMOClientFieldProps {
  label: string;
  fieldname: string;
  value: any;
  type: string;
  disabled?: boolean;
  handleChange?: any;
  error: boolean;
}

const HMOClientField: React.FC<HMOClientFieldProps> = (
  props: HMOClientFieldProps,
): JSX.Element => {
  if (props.type === 'date') {
    return (
      <HMOPickerItem
        disabled={props.disabled}
        name={props.label}
        error={props.error}
        value={props.value ? props.value : null}
        handleChange={value => {
          props.handleChange(value ? value.toString() : '');
        }}
      />
    );
  } else {
    return (
      <HMOFieldItem
        disabled={props.disabled}
        error={props.error}
        name={props.label}
        value={props.value ? props.value : ''}
        handleChange={value => {
          props.handleChange(value ? value.toString() : '');
        }}
      />
    );
  }
};

/**
 * main page
 */

// Hide on Nov. 27, 2020 for MS890
// const ordinal_suffix_of = i => {
//   let j = i % 10,
//     k = i % 100;
//   if (j === 1 && k !== 11) {
//     return i + 'st';
//   }
//   if (j === 2 && k !== 12) {
//     return i + 'nd';
//   }
//   if (j === 3 && k !== 13) {
//     return i + 'rd';
//   }
//   return i + 'th';
// };

const hmoinfoStyles = makeStyles({
  formtitle: {
    fontSize: '1.125rem',
    fontWeight: 600,
  },
  formsubtitle: {
    fontSize: '0.9375rem',
    fontWeight: 600,
  },
  formdesc: {
    fontSize: '0.875rem',
    color: 'rgba(39, 46, 76, 0.6)',
  },
  hmocheckboxlabel: {
    fontSize: '0.75rem',
    color: 'rgb(39, 46, 76)',
  },
  hmographsubtitle: {
    fontSize: '0.75rem',
    textTransform: 'uppercase',
    color: 'rgb(39, 46, 76, 0.6)',
    marginBottom: 16,
  },
  hmodependentsbox: {
    height: 175,
    borderRadius: 0,
    overflow: 'auto',
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
  },
  hmoremarksbox: {
    height: 175,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    border: '1px solid #272E4C4D',
  },
  hmodependentsbox_link: {
    color: 'rgb(39, 46, 76)',
  },
});

interface EditHMOInformationProps {
  currentUser?: any;
  memberData?: any;
  clientData?: any;
  contractName?: any;
  editMemberValues: any;
  clientFields: any[];
  errorFields: string[];
  dependentsErrorFields: any;
  MBL?: string;
  ABL?: string;
  PEC?: string;
  mctNode?: any;
  planTypeNode?: any;
  plan_type_options: ISelectItem[];
  handleTextChange: any;
  handleSelectChange: any;
  handleCheckboxChange: any;
  handleDependentsChange: any;
  handleClientFieldChange: any;
  onUpdateInvalidFields: (data: any[]) => void;
}

export const EditHMOInformation: React.FC<EditHMOInformationProps> = (
  props: EditHMOInformationProps,
): JSX.Element => {
  const classes = hmoinfoStyles(props);
  const {
    // currentUser,
    memberData,
    clientData,
    contractName,
    editMemberValues,
    clientFields,
    errorFields,
    // dependentsErrorFields, // Hide on Nov. 27, 2020 for MS890
    plan_type_options,
    MBL,
    ABL,
    PEC,
  } = props;

  let [invalidFields, setInvalidFields] = React.useState<any[]>([]);

  console.log('clientFields val', clientFields)

  React.useEffect(() => {
    console.log('invalidFields val', invalidFields)
    props.onUpdateInvalidFields(invalidFields);
  }, [invalidFields])


  const checkInvalidField = (fieldName: string, value: any) => {
    if (value !== '') {
      console.log('checkInvalidField value', value)
      if (moment(value).isValid()) {
        let currentArr = invalidFields.length > 1
          ? invalidFields.filter(iData => iData !== fieldName) : [];
        setInvalidFields(currentArr);
      } else {
        if (invalidFields.indexOf(fieldName) === -1) {
          let newArr = [...invalidFields];
          newArr.push(fieldName);
          setInvalidFields(newArr);
        }
      }
    } else {
      console.log('checkInvalidField value is blank')
      let currentArr = invalidFields.length > 1
        ? invalidFields.filter(iData => iData !== fieldName) : [];
      setInvalidFields(currentArr);
    }
  }

  // Hide on Nov. 27, 2020 for MS890
  // const dependents = editMemberValues['dependents'];
  // const dependentForms = Object.keys(dependents).map((key) => {
  //   const dependent = dependents[key];
  //   const dependentEF =
  //     dependentsErrorFields && dependentsErrorFields[key]
  //       ? dependentsErrorFields[key]
  //       : [];

  //   return (
  //     <ExpansionPanel key={key} style={{ boxShadow: 'none' }}>
  //       <ExpansionPanelSummary
  //         className={clsx(
  //           dependent['index'] % 2
  //             ? 'em-dependent-odd-summary'
  //             : 'em-dependent-even-summary',
  //         )}
  //         data-cy={'panel' + dependent['index'] + '-header'}
  //         id={'panel' + dependent['index'] + '-header'}
  //         aria-controls={'panel' + dependent['index'] + '-content'}
  //         expandIcon={<ExpandMoreIcon />}
  //       >
  //         <Typography className={clsx('em-dependent-header')}>
  //           {ordinal_suffix_of(dependent['index'] + 1) + ' Dependent'}
  //         </Typography>
  //       </ExpansionPanelSummary>
  //       <ExpansionPanelDetails>
  //         <Grid
  //           container
  //           direction="row"
  //           style={{ paddingLeft: 16, paddingRight: 16, paddingBottom: 24 }}
  //         >
  //           <Grid item xs={12} md={3}>
  //             <HMOFieldItem
  //               name="First Name"
  //               value={dependent['first_name'] ? dependent['first_name'] : ''}
  //               error={dependentEF.includes('first_name')}
  //               handleChange={value => {
  //                 props.handleDependentsChange(key, 'first_name', value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOFieldItem
  //               name="Middle Name"
  //               value={dependent['middle_name'] ? dependent['middle_name'] : ''}
  //               handleChange={value => {
  //                 props.handleDependentsChange(key, 'middle_name', value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOFieldItem
  //               name="Last Name"
  //               value={dependent['last_name'] ? dependent['last_name'] : ''}
  //               error={dependentEF.includes('last_name')}
  //               handleChange={value => {
  //                 props.handleDependentsChange(key, 'last_name', value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOFieldItem
  //               name="Title"
  //               value={dependent['title']}
  //               handleChange={value => {
  //                 props.handleDependentsChange(key, 'title', value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOSelectItem
  //               name="Suffix"
  //               value={dependent['suffix']}
  //               options={suffix_options}
  //               handleChange={item => {
  //                 props.handleDependentsChange(key, 'suffix', item.value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOPickerItem
  //               name="Birthday"
  //               value={dependent['date_of_birth'] !== '' ? dependent['date_of_birth'] : null}
  //               error={dependentEF.includes('date_of_birth')}
  //               handleChange={bd => {
  //                 checkInvalidField(
  //                   ordinal_suffix_of(dependent['index'] + 1) + ' Dependent Birthday',
  //                   bd !== null ? bd : ''
  //                 );
  //                 props.handleDependentsChange(
  //                   key,
  //                   'date_of_birth',
  //                   bd ? bd.toString() : '',
  //                 );
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOSelectItem
  //               name="Civil Status"
  //               value={dependent['civil_status']}
  //               options={cs_options}
  //               handleChange={item => {
  //                 props.handleDependentsChange(key, 'civil_status', item.value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOSelectItem
  //               name="Gender"
  //               value={dependent['gender']}
  //               options={gender_options}
  //               handleChange={item => {
  //                 props.handleDependentsChange(key, 'gender', item.value);
  //               }}
  //             />
  //           </Grid>
  //           <Grid item xs={12} md={3}>
  //             <HMOSelectItem
  //               name="Relationship to Principal"
  //               value={dependent['relationship_to_principal']}
  //               options={rtp_options}
  //               handleChange={item => {
  //                 props.handleDependentsChange(
  //                   key,
  //                   'relationship_to_principal',
  //                   item.value,
  //                 );
  //               }}
  //             />
  //           </Grid>
  //         </Grid>
  //       </ExpansionPanelDetails>
  //     </ExpansionPanel>
  //   );
  // });

  const additionalColumnFields: any[] = [];
  if (clientFields && clientFields.length > 0) {
    clientFields.forEach((item, index) => {
      if (
        item.field_name === 'registered_name' ||
        item.field_name === 'name_on_card'
      ) {
        additionalColumnFields.push(
          <Grid item xs={12} md={4}>
            <HMOFieldItem
              key={`hmo_field_item${index}`}
              disabled={true}
              name={item.system_name}
              value={get(clientData, item.field_name, '')}
            />
          </Grid>,
        );
      } else if (
        item.field_name === 'regularization_date' ||
        item.field_name === 'hire_date'
      ) {
        additionalColumnFields.push(
          <Grid item xs={12} md={4}>
            <HMOClientField
              key={`hmo_field_item${index}`}
              label={item.system_name}
              fieldname={item.field_name}
              value={get(editMemberValues, item.field_name, '')}
              type='date'
              disabled={!item.is_editable}
              error={errorFields && errorFields.includes(item.field_name)}
              handleChange={value => {
                checkInvalidField(item.system_name, value);
                props.handleClientFieldChange(item.field_name, value, item.input_type);
              }}
            />
          </Grid>,
        );
      } else {
        additionalColumnFields.push(
          <Grid item xs={12} md={4}>
            <HMOClientField
              key={`hmo_client_field${index}`}
              label={item.system_name}
              fieldname={item.field_name}
              value={get(editMemberValues, item.field_name, '')}
              type={item.input_type}
              disabled={!item.is_editable}
              error={errorFields && errorFields.includes(item.field_name)}
              handleChange={value => {
                props.handleClientFieldChange(
                  item.field_name,
                  value,
                  item.input_type,
                );
              }}
            />
          </Grid>,
        );
      }
    });
  }

  const getPossibleDate = (dateString: string) => {
    const MMDDYYYY = 'MM/DD/YYYY';
    const YYYYMMDD = 'YYYY/MM/DD';
    const MMDDYY = 'MM/DD/YY';

    const formats = [MMDDYYYY, YYYYMMDD, MMDDYY];

    const correctFormat = find(formats, format => {
      const parsedDate = moment(dateString, format, true);
      return !isNil(parsedDate) && parsedDate.isValid();
    });

    if (!isNil(correctFormat)) {
      return moment(dateString, correctFormat, true);
    }

    return null;
  };

  const getCardNoDisplay = () => {
    if (!memberData) {
      return '';
    }

    // Check can_display_card_no
    if (!get(memberData, 'can_display_card_no', false)) {
      return '';
    }

    // Check if member_card_no and date_printed exist
    const memberCardNo = get(memberData, 'member_card_no', null);
    const datePrinted = get(memberData, 'date_printed', null);
    if (isNil(memberCardNo) || isNil(datePrinted)) {
      return '';
    }

    // Check if datePrinted is not future date
    const parsedDate: moment.Moment | null = getPossibleDate(datePrinted);
    if (!isNil(parsedDate) && parsedDate.valueOf() <= moment().valueOf()) {
      return memberCardNo;
    }

    return '';
  };

  return (
    <Grid
      container
      style={{ paddingLeft: 32, paddingRight: 80, paddingBottom: 80 }}
    >
      <Grid item xs={12} style={{ paddingTop: 16, paddingBottom: 16 }}>
        <Typography className={classes.formtitle} color="textPrimary">
          Edit Member Details
        </Typography>
      </Grid>
      <Grid item xs={12} style={{ paddingBottom: 8 }}>
        <Typography className={classes.formsubtitle} color="textPrimary">
          HMO Information
        </Typography>
      </Grid>
      <Grid
        item
        container
        direction="row"
        xs={12}
        style={{ paddingTop: 0, paddingBottom: 0 }}
      >
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={false}
            error={errorFields && errorFields.includes('first_name')}
            name="First Name"
            value={
              editMemberValues && editMemberValues['first_name']
                ? editMemberValues['first_name']
                : ''
            }
            handleChange={value => {
              props.handleTextChange('first_name', value);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={false}
            name="Middle Name"
            value={
              editMemberValues && editMemberValues['middle_name']
                ? editMemberValues['middle_name']
                : ''
            }
            handleChange={value => {
              props.handleTextChange('middle_name', value);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={false}
            error={errorFields && errorFields.includes('last_name')}
            name="Last Name"
            value={
              editMemberValues && editMemberValues['last_name']
                ? editMemberValues['last_name']
                : ''
            }
            handleChange={value => {
              props.handleTextChange('last_name', value);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOSelectItem
            name="Suffix"
            value={
              editMemberValues && editMemberValues['suffix']
                ? editMemberValues['suffix']
                : ''
            }
            options={suffix_options}
            handleChange={item => {
              props.handleSelectChange('suffix', item, false);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOSelectItem
            name="Civil Status"
            value={
              editMemberValues && editMemberValues['civil_status']
                ? editMemberValues['civil_status'] : ''
            }
            options={cs_options}
            handleChange={item => {
              props.handleSelectChange('civil_status', item, false);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOSelectItem
            name="Gender"
            value={
              editMemberValues && editMemberValues['gender']
                ? editMemberValues['gender']
                : ''
            }
            options={gender_options}
            handleChange={item => {
              props.handleSelectChange('gender', item);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOClientField
            key="Birthdate"
            label="Birthdate"
            fieldname="date_of_birth"
            value={
              editMemberValues && editMemberValues['date_of_birth']
                ? editMemberValues['date_of_birth']
                : ''
            }
            type="date"
            disabled={false}
            error={errorFields && errorFields.includes('date_of_birth')}
            handleChange={value => {
              checkInvalidField('Birthdate', value);
              props.handleClientFieldChange('date_of_birth', value, 'date');
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={false}
            error={errorFields && errorFields.includes('title')}
            name="Title"
            value={
              editMemberValues && editMemberValues['title']
                ? editMemberValues['title']
                : ''
            }
            handleChange={value => {
              props.handleTextChange('title', value);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOClientField
            key="Effectivity Date"
            label="Effectivity Date"
            fieldname="effectivity_date"
            value={
              editMemberValues && editMemberValues['effectivity_date']
                ? editMemberValues['effectivity_date']
                : ''
            }
            type="date"
            disabled={editMemberValues && editMemberValues['member_status'] === 'Awaiting Activation'
              || editMemberValues && editMemberValues['member_status'] === 'For Validation' ? false : true}
            error={errorFields && errorFields.includes('effectivity_date')}
            handleChange={value => {
              checkInvalidField('Effectivity Date', value);
              props.handleClientFieldChange('effectivity_date', value, 'date');
            }}
          />
        </Grid>
        {memberData &&
          memberData['member_type'] &&
          (memberData['member_type'] === 'Dependent' ||
            memberData['member_type'] === 'Extended Dependent') ? (
          <React.Fragment>
            <Grid item xs={12} md={4}>
              <HMOFieldItem
                disabled={true}
                error={errorFields && errorFields.includes('principal_name')}
                name="Principal Name"
                value={
                  editMemberValues && editMemberValues['principal_name']
                    ? editMemberValues['principal_name']
                    : ''
                }
                handleChange={value => {
                  props.handleTextChange('principal_name', value);
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <HMOSelectItem
                disabled={true}
                name="Relationship to Principal"
                value={
                  editMemberValues &&
                    editMemberValues['relationship_to_principal']
                    ? editMemberValues['relationship_to_principal']
                    : ''
                }
                options={rtp_options}
                handleChange={item => {
                  props.handleSelectChange('relationship_to_principal', item);
                }}
              />
            </Grid>
          </React.Fragment>
        ) : null}
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Member Account No."
            value={get(memberData, 'member_account_no', '')}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Member ID"
            value={memberData ? memberData.member_id : ''}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Contract"
            value={contractName}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Member Card No."
            value={getCardNoDisplay()}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOClientField
            disabled={false} //true
            //name="Card Printed Date"
            // value={
            //   memberData && isValidDate(memberData.date_printed)
            //     ? moment(memberData.date_printed).format('MMM DD, YYYY')
            //     : ''
            // }
            //changes starts here datePrinted date_printed
            value={
              editMemberValues && editMemberValues['date_printed']
                ? editMemberValues['date_printed']
                : ''
            }
            type="date"
            label="Card Printed Date"
            fieldname="date_printed"
            error={errorFields && errorFields.includes('date_printed')}
            handleChange={value => {
              checkInvalidField('Date Printed', value);
              props.handleClientFieldChange('date_printed', value, 'date');
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Contract Plan Type"
            value={get(
              props.planTypeNode,
              'custom_metadata.contract_plan_type',
              '',
            )}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Contract Renewal Date"
            value="" 
            />
        </Grid>
        {console.log('bug error typescript1', props.mctNode, props)}
        <Grid item xs={12} md={4}> 
          <HMOFieldItem
            disabled={true}
            name="Contract Expiration Date"
            value={get(
              props.mctNode,
              'custom_metadata.coverage_end_date',
              '-') !==
                '-'
                ? // Match the Expiry Date calculation from Clients page: add 1 day and use UTC
                  moment.utc(props.mctNode.custom_metadata.coverage_end_date).add(1, 'days').format('MMM DD, YYYY')
                : ''
            }
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Last day of Effectivity"
            value={(() => {
              if (get(memberData, 'member_status', '') !== 'Terminated') {
                return get(
                  props.mctNode,
                  'custom_metadata.coverage_end_date',
                  '-',
                ) !== '-'
                  ? // Match the Last Date of Coverage calculation from Clients page: use UTC
                    moment.utc(props.mctNode.custom_metadata.coverage_end_date).format('MMM DD, YYYY')
                  : '';
              }

              const terminationDate = get(
                memberData,
                'member_termination.termination_date',
                null,
              );

              if (!isNil(terminationDate)) {
                const terminationDateMoment: moment.Moment = moment(
                  terminationDate,
                );
                if (
                  !isNil(terminationDateMoment) &&
                  terminationDateMoment.isValid()
                ) {
                  return terminationDateMoment
                    .add(-1, 'days')
                    .format('MMM DD, YYYY');
                }
              }

              return '';
            })()}
          />
        </Grid>
        {get(memberData, 'member_status', '') === 'Terminated' ? (
          <Grid item xs={12} md={4}>
            <HMOFieldItem
              disabled={true}
              name="Termination Date"
              value={(() => {
                const terminationDate = get(
                  memberData,
                  'member_termination.termination_date',
                  null,
                );

                if (!isNil(terminationDate)) {
                  const terminationDateMoment: moment.Moment = moment(
                    terminationDate,
                  );
                  if (
                    !isNil(terminationDateMoment) &&
                    terminationDateMoment.isValid()
                  ) {
                    return terminationDateMoment.format('MMM DD, YYYY');
                  }
                }

                return '';
              })()}
            />
          </Grid>
        ) : null}
        {get(memberData, 'member_status', '') === 'Suspended' ? (
          <Grid item xs={12} md={4}>
            <HMOFieldItem
              disabled={true}
              name="Suspension Date"
              value={(() => {
                const suspensionDate = get(
                  memberData,
                  'member_suspension.suspension_start_date',
                  null,
                );

                if (!isNil(suspensionDate)) {
                  const suspensionDateMoment: moment.Moment = moment(
                    suspensionDate,
                  );
                  if (
                    !isNil(suspensionDateMoment) &&
                    suspensionDateMoment.isValid()
                  ) {
                    return suspensionDateMoment.format('MMM DD, YYYY');
                  }
                }

                return '';
              })()}
            />
          </Grid>
        ) : null}
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="MBL"
            value={MBL ? MBL : ''}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="ABL"
            value={ABL ? ABL : ''}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="PEC"
            value={PEC ? PEC : ''}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOSelectItem
            name="Plan Type"
            value={editMemberValues['plan_type']}
            options={plan_type_options}
            handleChange={(item: ISelectItem) => {
              props.handleSelectChange('plan_type', item);
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          {/* //~ */}
          <HMOSelectItem
            name="Member Type"
            value={editMemberValues['member_type']}
            options={memberType_options}
            handleChange={(item: ISelectItem) => {
              props.handleSelectChange('member_type', item);
            }}
            disabled={true}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <HMOFieldItem
            disabled={true}
            name="Batch Process No."
            value={get(memberData, 'batch_name', '')}
          />
        </Grid>
        {additionalColumnFields}
      </Grid>
      <Grid item xs={12} md={4} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOCheckboxItem
          name="VIP Member"
          checked={editMemberValues['is_vip']}
          handleChange={() => {
            let currVal = editMemberValues['is_vip'];
            if (currVal === false) {
              currVal = true;
            } else {
              currVal = false;
            }
            props.handleCheckboxChange('is_vip', currVal);
          }}
        />
      </Grid>
      <Grid item xs={12} md={4} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOCheckboxItem
          name="Philhealth Rider"
          checked={editMemberValues['is_philhealth_rider']}
          handleChange={() => {
            let currVal = editMemberValues['is_philhealth_rider'];
            if (currVal === false) {
              currVal = true;
            } else {
              currVal = false;
            }
            props.handleCheckboxChange('is_philhealth_rider', currVal);
          }}
        />
      </Grid>
      <Grid item xs={12} md={4} style={{ paddingTop: 0, paddingBottom: 0 }}>
        <HMOCheckboxItem
          name="Member Consent"
          checked={editMemberValues['is_member_consent']}
          handleChange={() => {
            let currVal = editMemberValues['is_member_consent'];
            if (currVal === false) {
              currVal = true;
            } else {
              currVal = false;
            }
            props.handleCheckboxChange('is_member_consent', currVal);
          }}
        />
      </Grid>
      {/* Hide on Nov. 27, 2020 for MS890
      {memberData && memberData['member_type'] && memberData['member_type'] === 'Principal' ? (
        <>
          <Grid item xs={12} md={4} style={{ paddingTop: 0, paddingBottom: 0 }} />
          <Grid item xs={12} style={{ paddingTop: 24, paddingBottom: 16 }}>
            <Typography className={classes.formsubtitle} color="textPrimary">
              Dependent Information
            </Typography>
          </Grid>
          <Grid item xs={12} md={12}>
            {Object.keys(dependents).length > 0 ? (
              dependentForms
            ) : (
                <Typography
                  className={classes.formdesc}
                  style={{ paddingTop: 8, paddingBottom: 8 }}
                  color="textPrimary"
                >
                  No enrolled dependents.
                </Typography>
              )}
          </Grid>
        </>
      ) : null} */}
    </Grid>
  );
};

EditHMOInformation.defaultProps = {
  memberData: {},
  contractName: '',
  MBL: '',
  ABL: '',
  PEC: '',
};
