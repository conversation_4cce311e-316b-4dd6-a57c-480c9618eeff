import React, { createContext, useCallback, useEffect } from 'react'
import { Grid } from '@material-ui/core'
import clsx from 'clsx';
import { API } from 'Pages/API';
import { TableComponent } from 'Components/UI/TableComponent';
import RenewalFooterButton from './Components/FooterButton';
import { FloatingButtons } from 'Components/UI/FloatingButtons';
import { RenewalProvider } from './Context/RenewalContext';
import ModalFinishedRenewal from './Components/ModalFinishedRenewal';
import { Loader } from 'Components/UI';
import { ModalComponent } from 'Components/UI/ModalComponent';


interface Props {
  clientId: string,
  handleBackButton: () => void,
}


// state interfaces
interface ColExtension{
 columnName: string;
 wordWrapEnabled: boolean;
}

const AutoRenew : React.FC<Props> = (props) => {
  const { clientId, handleBackButton } = props;
  const [dataFields, setDataFields] = React.useState([]); // columns
  const [membersForRenewal, setMembersForRenewal] = React.useState([]); // data for table
  const [columnExtensions, setColumnExtensions] = React.useState<ColExtension[]>([]);
  const [clientName, setClientName] = React.useState<string>('');
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState({
      isError: false,
      message: '',
      title: '',
  });


  const getMemberField = useCallback(async () => {
    setIsLoading(true);
    try {
      const [clientInfo, membersForRenewal] = await Promise.all([
        API.getClientsInfo(clientId),
        API.getMembersForRenewals(clientId),
      ])
      
      // start data manipulation from client
      const { client } = clientInfo || {};
      const requiredFields = client?.member_data_fields?.filter(field => 
                    field.is_required === true && 
                    (field.field_type === 'Client-required' || field.field_type === 'Mandatory')
                )
                .sort((a, b) => a.sort_order - b.sort_order);
      const columnExtensionsTmp: ColExtension[] = requiredFields.map(item => ({columnName:item.field_name,wordWrapEnabled: true})) || [];
      const formattedForTblHeader = requiredFields.map(item =>{
        return ({
          name: item.field_name,
          title: item.system_name,
        })
      });

      setColumnExtensions(columnExtensionsTmp);
      setDataFields(formattedForTblHeader);
      setClientName(client?.registered_name);
      //end for client details manipulation

      // start data manipulation from members
      setMembersForRenewal(membersForRenewal);
      // end data manipulation
    } catch (error) {
      console.log('client info response error', error);
      setError((prev)=> ({
        ...prev,
        isError: true,
        message:  'An error occurred while fetching data.',
        title: 'Error Fetching Data',
      }));
    } finally {
      setIsLoading(false);
    }
   
  }, [clientId])
    
  useEffect(() => {
    getMemberField()
  }, [getMemberField])

  const renewalDatas = {
    membersForRenewal,

    columnExtensions,
    dataFields,
    clientName,
    ...props
  }

  if(isLoading) {
    return <Loader></Loader>
  }
  
  return (
    <RenewalProvider {...renewalDatas}>
        <ModalComponent
                id="upload_modal_error"
                isModalOpen={error.isError}
                title={error.title}
                message={error.message}
                onClose={()=>{
                  return setError((prev) => ({
                    ...prev,
                    isError: false,
                    message: '',
                    title: '',
                  }))
                }}
          
        />
      <Grid container alignItems="stretch" className={clsx('member-body')}>
        <Grid item xs={12}>
          <TableComponent
            id="upload_file_table"
            rows={membersForRenewal
            }
            columns={dataFields}                        
            message="No available data"
            onClickRow={() => {}}
            disableSelect
            disableSearch
            disableFilter
            disableSort
            formattedColumns={[]
            }
            columnExtensions={
              columnExtensions
            }
            pageSize={10}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="stretch">
        <Grid item xs={12}>
          <FloatingButtons
            leftButtons={
              <RenewalFooterButton
              />
            }
          />
        </Grid>
      </Grid>
      <ModalFinishedRenewal/>
    </RenewalProvider>
  )
}

export default AutoRenew