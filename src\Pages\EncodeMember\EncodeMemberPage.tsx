//#region Global Imports
import * as React from 'react';
import { Redirect } from 'react-router-dom';
import PropTypes from 'prop-types';
// import { useState, useEffect } from 'react';
import { bindActionCreators, Dispatch } from 'redux';
import { Grid, Button, Typography } from '@material-ui/core/';
import { makeStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import _ from 'lodash';
import moment from 'moment';

// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import {
//     faPlus,
// } from '@fortawesome/free-solid-svg-icons';
//#endregion Global Imports

//#region Local Imports
import './style.scss';
//#endregion Local Imports

//#region Interface Imports
import { Store } from 'Components/Stores/IStore';
import { IEncodeMember } from 'Pages/EncodeMember/IEncodeMember';
import { EncodeMemberActions } from './EncodeMemberActions';
import { ConfirmationModal } from 'Components/UI/ConfirmationModal';
import { ConfirmationModalComponent } from 'Components/UI/ConfirmationModalComponent';
import { PageHeaderComponent } from 'Components/UI/PageHeader';
import { FloatingButtons } from 'Components/UI/FloatingButtons';
import { AddDetails } from './AddDetailsPage';
import { UploadMember } from './UploadMemberPage';
import { EncodeMultipleMembers } from './EncodeMultipleMembers';
import { Loader } from 'Components/UI/LoadingIndicator';
import { ModalComponent } from 'Components/UI/ModalComponent';
import { ErrorWithListComponent } from 'Components/UI/ErrorWithListComponent';
import { API } from '../API';
import { Processmaker } from '../Processmaker';
import { Components } from '@hims/core';
import { LostInternetModal } from 'Components/UI';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import { Utils } from '@hims/core';
import { BirthDateFormats } from './DateFormatConstants';
import { ModalWithChildren } from 'Components/UI/ModalWithChildren';
import HowToRenewLayout from './Components/HowToRenewLayout';
import AutoRenew from 'Pages/EncodeMember/AutoRenewPage';
// let API_URL = localStorage.getItem('CLIENT_URL')
//   ? localStorage.getItem('CLIENT_URL')
//   : localStorage.getItem('XDEV_CLIENT_URL');
type ISelectItem = Components.UI.InputSelect.ISelectItem;

const ButtonStyle = makeStyles({
  root: {
    height: '40px',
    minWidth: '150px',
    '&.Mui-disabled': {
      backgroundColor: '#E4E4E4',
      color: 'rgba(0, 0, 0, 0.61)',
    },
  },
  rightButton: {
    height: '40px',
    minWidth: '99px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
  },
  leftButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    border: '2px solid #3AB77D',
  },
  backButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
    textTransform: 'none',
    '&:hover': {
      background: '#000000',
    },
  },
  leftText1: {
    fontSize: '13px',
  },
  leftText2: {
    fontSize: '7px',
  },
  buttonTitle: {
    fontWeight: 700,
    fontSize: 13,
  },
  buttonSubTitle: {
    fontWeight: 400,
    fontSize: 7,
  },
});

function SnackBarButton(props) {
  const buttonclasses = ButtonStyle();
  return (
    <div>
      <Button
        data-cy={'upload_member_save_btn'}
        id={'upload_member_save_btn'}
        className={buttonclasses.leftButton}
        style={{ marginRight: '10px' }}
        variant="contained"
        color="secondary"
        size="small"
        onClick={props.handleSaveForNow}
      >
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <Typography className={buttonclasses.buttonTitle}>
            Save For Now
          </Typography>
        </div>
      </Button>
      <Button
        data-cy={'upload_member_next_btn'}
        id={'upload_member_next_btn'}
        className={buttonclasses.root}
        variant="contained"
        color="primary"
        size="small"
        disabled={props.disabled}
        onClick={props.handleNextButton}
      >
        Next
      </Button>
    </div>
  );
}

SnackBarButton.propTypes = {
  handleSaveForNow: PropTypes.func,
  handleNextButton: PropTypes.func,
  disabled: PropTypes.bool,
};

function SnackBarButtonBack(props) {
  const buttonclasses = ButtonStyle();
  return (
    <div>
      <Button
        id={'encode_member_back_btn'}
        className={buttonclasses.backButton}
        variant="contained"
        size="small"
        disabled={props.disabled}
        onClick={props.onBackPressed}
      >
        Back
      </Button>
    </div>
  );
}

SnackBarButtonBack.propTypes = {
  onBackPressed: PropTypes.func,
  disabled: PropTypes.bool,
};

const BG_TASK_MEMBER_UPLOAD_ID =
  'ENCODE_MEMBER_BACKGROUND_TASK_MEMBER_UPLOAD_ID';
const BG_TASK_ID = 'ENCODE_MEMBER_BACKGROUND_TASK_ID';
const BG_TASK_STATUS = 'ENCODE_MEMBER_BACKGROUND_TASK_STATUS';

export class EncodeMember extends React.Component<
  IEncodeMember.IProps,
  IEncodeMember.IState
> {
  constructor(props: IEncodeMember.IProps) {
    super(props);

    let pageType = 'default';
    // console.log('encodememberpage1', pageType)
    if (props && props['match'] && props['match']['params']) {
      // console.log('encodememberpage2', props)
      if (props['match']['params']['type']) {
        pageType = props['match']['params']['type'];
      }
    }

    // this.state = {
    //   encodePageType: pageType || 'default', // Use the determined pageType from props
    //   pageData: null,
    // };

    const steps: any = ['Add Details', 'Add Members', 'Submit'];

    const { state, search } = props.location;
    let trueTicketId: any = '';
    let batchName: any = {
      id: '',
      value: '',
      label: '',
    };
    const batchNames: any = [];
    let mIsProcessing = false;
    let mMemberUploadId: any = null;
    let memberVoidUploadID =
      state && state.member_void_upload_id ? state.member_void_upload_id : null;
    let from_ocp = false;
    if (state) {
      const { ticket_id, isProcessing, member_upload_id, is_ocp } = state;

      mIsProcessing = isProcessing !== null && isProcessing === true;
      mMemberUploadId = !_.isNil(member_upload_id) ? member_upload_id : null;
      from_ocp = is_ocp ? is_ocp : false;
      if (ticket_id) {
        trueTicketId = ticket_id;

        batchName = {
          id: ticket_id,
          value: ticket_id,
          label: ticket_id,
        };

        batchNames.push(batchName);
      }
    } else if (search) {
      try {
        const searchParams = new URLSearchParams(search);
        const ticketId = searchParams.get('ticketId');
        const isProcessing = searchParams.get('isProcessing');
        const memberUploadId = searchParams.get('memberUploadId');

        mMemberUploadId = !_.isNil(memberUploadId) ? memberUploadId : null;

        if (ticketId) {
          trueTicketId = ticketId;

          batchName = {
            id: ticketId,
            value: ticketId,
            label: ticketId,
          };

          batchNames.push(batchName);
        }

        if (isProcessing && _.toLower(isProcessing) === _.toLower('true')) {
          mIsProcessing = true;
        }
      } catch (e) {}
    }

    const details: any = {
      account: {
        id: '',
        value: '',
      },
      contract: {
        id: '',
        value: '',
        label: '',
      },
      sender_name: '',
      sender_email: '',
      date_sent: new Date(),
      sent_through_channel: {
        id: '',
        value: '',
        label: '',
      },
      batch_name: batchName,
      ticket_id: '',
      file_url: '',
      number_of_members: '',
      data_mapping_save_for_now: [],
      default_data_map_save_for_now: [],
    };

    const default_values: any = {
      columns: [
        {
          name: 'values',
          title: 'Values',
        },
        {
          name: 'data_column',
          title: 'Data Column',
        },
      ],
      rows: [],
    };

    this.state = {
      check_internet_flag: false,
      isSaveForNowMemberList: false,
      isSaveForNow: false,
      pmaker_task: 'Enroll Members',
      initial_load: false,
      breadcrumbs: [],
      steps: steps,
      activeStep: 0,
      open: false,
      uploaded_file_flag: false,
      disabledNext: false,
      pageHeader:
        pageType === 'void'
          ? 'Encode Void Masterlist Details'
          : 'Encode Batch Details',
      details_data: details,
      corporate_account: '',
      clients_data: [],
      loading_state: true,
      sender_email_error: false,
      sender_email_error_message: '',
      account_error: false,
      account_error_message: '',
      contract_error: false,
      contract_error_message: '',
      sender_name_error: false,
      sender_name_error_message: '',
      date_sent_error: false,
      date_sent_error_message: '',
      sent_through_channel_error: false,
      sent_through_channel_error_message: '',
      // default_data_map_save_for_now,
      // data_map_save_for_now,
      default_data_mapping_save_for_now: [],
      save_for_now_system_name: [],
      data_mapping_save_for_now: [],
      batch_name_error: false,
      batch_name_error_message: '',
      number_of_members_error: false,
      number_of_members_error_message: '',
      uploaded_data: {},
      raw_uploaded_data: {
        columns: [],
        rows: [],
        data_values: {},
      },
      system_names: [],
      columns_data: {
        columns: [
          {
            name: 'system_name',
            title: 'System Name',
          },
          {
            name: 'sheet_name',
            title: 'Column Header',
          },
        ],
        rows: [
          {
            sheet_name: '',
            system_name: '',
          },
          {
            sheet_name: '',
            system_name: '',
          },
          {
            sheet_name: '',
            system_name: '',
          },
          {
            sheet_name: '',
            system_name: '',
          },
          {
            sheet_name: '',
            system_name: '',
          },
        ],
      },
      clients_data_maps: [],
      selected_data_map: {},
      contracts_data: [],
      gender_data: default_values,
      civil_status_data: default_values,
      plan_type_data: default_values,
      type_data: default_values,
      relationship_data: default_values,
      vip_data: default_values,
      ph_rider_data: default_values,
      member_consent_data: default_values,
      site_data: default_values,
      suffix_data: default_values,
      //branch_options: default_values,
      add_new_data_map: {},
      rows_start_at: '',
      filtered_raw_data: {
        columns: [],
        rows: [],
        data_values: {},
      },
      client_maps: [],
      isOpenModal: false,
      isConfirmModalOpen: false,
      modalTitle: '',
      modalMessage: '',
      customModalMessage: null,
      next: null,
      isOpenErrorWithListModal: false,
      error_list: [],
      submit_flag: false,
      batchnames: batchNames,
      plan_type_options: [],
      civil_status_options: [],
      relationship_options: [],
      principals_options: [],
      site_assigned_options: [],
      save_for_now_id: '',
      uploadMemberData: {},
      ticketId: props.match.params.ticket_id,
      isProcessing: mIsProcessing,
      processingStatus: {},
      trueTicketId: trueTicketId,
      isAddMembersTicket: false,
      isVoidMembersTicket: false,
      isFromEncodeMembersTicket: false,
      redirectFlag: false,
      redirectInfo: {},
      memberUploadId: mMemberUploadId,
      memberVoidUploadID: memberVoidUploadID,
      fetchContactsErr: false,
      encodePageType: pageType,
      // pageData: '',
      modalCloseText: '',
      modalConfirmText: '',
      clientStatus: '',
      ticket_details: {},
      handleNextCounter: 0,
      isSuffixError: false,
      isShowSuffixErrorModal: false,
      disable_next: false,
      isExpiredClientModalOpen: false,
      isExpiredClient: false,
      void_ticket_id: '',
      data_mapping_values: {},
      is_ocp: from_ocp,
      ocp_file: [],
      selected_client_details: {},
      refresh_data_map: false,
      contract_options: [],
      reload_table: false,
      assignTicketType: '',
      isHowToRenewModalOpen: false,
      ticketVariables: null
    };
    this.handleUpdateDetailsChannelAndName = this.handleUpdateDetailsChannelAndName.bind(
      this,
    ); // 5685
    this.handleUpdateDetails = this.handleUpdateDetails.bind(this);
    this.validateDetails = this.validateDetails.bind(this);
    this.handleNextButton = this.handleNextButton.bind(this);
    this.handleBackButton = this.handleBackButton.bind(this);
    this.getSaveForNowData = this.getSaveForNowData.bind(this);
    this.doSaveForNow = this.doSaveForNow.bind(this);
    this.handleSaveForNow = this.handleSaveForNow.bind(this);
    this.handleSaveForNowOnSubmit = this.handleSaveForNowOnSubmit.bind(this);
    this.deleteSaveForNow = this.deleteSaveForNow.bind(this);
    this.showHideLoader = this.showHideLoader.bind(this);
    this.updateState = this.updateState.bind(this);
    this.updateUploadMemberData = this.updateUploadMemberData.bind(this);
    this.savingNewDataMap = this.savingNewDataMap.bind(this);
    this.updatingSelectedDataMap = this.updatingSelectedDataMap.bind(this);
    this.closeModalMessage = this.closeModalMessage.bind(this);
    this.closeModal = this.closeModal.bind(this);
    this.onHandleSubmitClick = this.onHandleSubmitClick.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.closeModalWithList = this.closeModalWithList.bind(this);
    this.getBatchNames = this.getBatchNames.bind(this);
    this.getPlanTypes = this.getPlanTypes.bind(this);
    this.onSaveForNowClicked = this.onSaveForNowClicked.bind(this);
    this.createAddMemberTicket = this.createAddMemberTicket.bind(this);
    this.executeTicketTrigger = this.executeTicketTrigger.bind(this);
    this.setTicketVariable = this.setTicketVariable.bind(this);
    this.getProcessingStatus = this.getProcessingStatus.bind(this);
    this.getMuStatusOnBg = this.getMuStatusOnBg.bind(this);
    this.cancelStatusBgPolling = this.cancelStatusBgPolling.bind(this);
    this.resumeStatusBgPolling = this.resumeStatusBgPolling.bind(this);
    this.getMuStatusOnBg = this.getMuStatusOnBg.bind(this);
    this.onCancelProcess = this.onCancelProcess.bind(this);
    this.onBackToDashboard = this.onBackToDashboard.bind(this);
    this.onConfirmEnrollment = this.onConfirmEnrollment.bind(this);
    this.onCancelEnrollment = this.onCancelEnrollment.bind(this);
    this.handleDisableNext = this.handleDisableNext.bind(this);
  }

  componentWillUnmount() {
    if (this.state.encodePageType === 'default') {
      this.cancelStatusBgPolling();
      // this.updateState();
    }
  }

  componentDidMount() {
    this.initialLoad();
  }

  async initialLoad() {
    if (this.state.encodePageType === 'default') {
      this.cancelStatusBgPolling();
    }

    const { pathname, state, search } = this.props.location;
    const client_id = this.props.match.params.client_id;
    let trueTicketId = '';
    let isAddMembersTicket = false;
    let isFromEncodeMembersTicket = false;
    let mIsProcessing = false;
    let batchName: any = {
      id: '',
      value: '',
      label: '',
    };
    const batchNames: any = [];
    if (
      _.includes(_.toLower(pathname), _.toLower('add-members')) ||
      ['manual', 'client-manual'].includes(this.state.encodePageType) ||
      this.state.encodePageType === 'void'
    ) {
      isAddMembersTicket = true;
    }

    if (state) {
      const { encode_ticket, ticket_id, isProcessing } = state;
      isFromEncodeMembersTicket =
        encode_ticket !== null && encode_ticket === true;

      mIsProcessing = isProcessing !== null && isProcessing === true;

      if (ticket_id) {
        trueTicketId = ticket_id;
        batchName = {
          id: ticket_id,
          value: ticket_id,
          label: ticket_id,
        };

        batchNames.push(batchName);
      }
    } else if (search) {
      try {
        const searchParams = new URLSearchParams(search);
        const encodeTicket = searchParams.get('encodeTicket');
        const isProcessing = searchParams.get('isProcessing');
        const ticketId = searchParams.get('ticketId');

        if (encodeTicket && _.toLower(encodeTicket) === _.toLower('true')) {
          isFromEncodeMembersTicket = true;
        }

        if (isProcessing && _.toLower(isProcessing) === _.toLower('true')) {
          mIsProcessing = true;
        }

        if (ticketId) {
          trueTicketId = ticketId;

          batchName = {
            id: ticketId,
            value: ticketId,
            label: ticketId,
          };

          batchNames.push(batchName);
        }
      } catch (e) {
        isFromEncodeMembersTicket = false;
      }
    }

    if(this.getQueryParam('isAutoRenew')){
      const ticketID = this.props.match.params.ticket_id
      Processmaker.get(`cases/${ticketID}`)
      .then(res1 => {
        if (res1 && res1.current_task && res1.current_task[0]?.del_index) {
          const caseData: IEncodeMember.TicketVariables = res1;
          const app_index = caseData.current_task[0].del_index;
          // Fetch variables using del_index
          return Processmaker.get(
            `cases/${ticketID}/variables?app_index=${app_index}`
          ).then(res2 => {
            if (res2) {
              const variables: Record<string, any> = res2;
        
              // Store variables in caseVariables
                this.setState({ ticketVariables: {...variables, ...caseData.current_task[0]} });
              return variables;
            } else {
              throw new Error('No variables data received from API');
            }
          });
        } else {
          // throw new Error('No case data or del_index found');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        this.setState({
          isOpenModal: true,
          modalTitle: 'Error',
          modalMessage: 'Failed to retrieve case or variables data. Please try again.'
        });
        throw error;
      });
      }
    const { details_data, is_ocp } = this.state;
    details_data['batch_name'] = batchName;
    if (client_id) {
      details_data['account'] = {
        id: client_id,
        value: client_id,
        label: '',
      };
    }

    let breadcrumbs_items: any[] = [];
    if (this.state.encodePageType === 'default') {
      const isBatchUpload = !(
        this.props.match.params.ticket_id !== undefined &&
        this.props.match.params.proposal_id !== undefined &&
        this.props.match.params.client_id !== undefined
      );
      if (isBatchUpload && !is_ocp) {
        breadcrumbs_items = [
          {
            label: 'MEMBERS',
            url: '#/membership/members',
          },
          {
            label: 'UPLOAD MEMBERS',
            url: '',
          },
        ];
      } else {
        Processmaker.put(
          'cases/' + this.props.match.params.ticket_id + '/variable',
          { use_import: true },
        );
        breadcrumbs_items = [
          {
            label: 'CLIENTS',
            url: '#/membership/clients',
          },
          {
            label: '',
            url:
              client_id && client_id !== ''
                ? '#/membership/clients-profile/' + client_id
                : '',
          },
          {
            label: 'MEMBERS',
            url: '#/membership/members',
          },
          {
            label: 'UPLOAD MEMBERS',
            url: '',
          },
          {
            label: batchNames[0]['value'],
            url: '',
          },
        ];
      }
    } else if (
      ['manual', 'client-manual'].includes(this.state.encodePageType)
    ) {
      if (this.props.match.params.client_id) {
        breadcrumbs_items = [
          {
            label: 'CLIENTS',
            url: '#/membership/clients',
          },
          {
            label: '',
            url:
              client_id && client_id !== ''
                ? '#/membership/clients-profile/' + client_id
                : '',
          },
          {
            label: 'MEMBERS',
            url: '#/membership/members',
          },
          {
            label: 'ENCODE MULTIPLE MEMBERS',
            url: '',
          },
        ];
      } else {
        breadcrumbs_items = [
          {
            label: 'MEMBERS',
            url: '#/membership/members',
          },
          {
            label: 'ENCODE MULTIPLE MEMBERS',
            url: '',
          },
        ];
      }
    } else if (this.state.encodePageType === 'void') {
      if (this.props.match.params.client_id) {
        breadcrumbs_items = [
          {
            label: 'CLIENTS',
            url: '#/membership/clients',
          },
          {
            label: '',
            url:
              client_id && client_id !== ''
                ? '#/membership/clients-profile/' + client_id
                : '',
          },
          {
            label: 'MEMBERS',
            url: '#/membership/members',
          },
          {
            label: 'UPLOAD VOID DETAILS',
            url: '',
          },
        ];
      }
    }
    this.setState(
      {
        trueTicketId: trueTicketId,
        isProcessing: mIsProcessing,
        isAddMembersTicket: isAddMembersTicket,
        isFromEncodeMembersTicket: isFromEncodeMembersTicket,
        details_data: details_data,
        batchnames: batchNames,
        check_internet_flag: true,
      },
      async () => {
        await this.getAllClients(breadcrumbs_items);
        if (this.state.activeStep === 1) await this.getUploadOptions(true);
      },
    );
  }

  componentDidUpdate(
    prevProps: Readonly<IEncodeMember.IProps>,
    prevState: Readonly<IEncodeMember.IState>,
  ): void {
    if (prevProps) {
    }
    const currDetailsData = this.state.details_data
      ? this.state.details_data
      : {};
    const prevDetailsData = prevState.details_data
      ? prevState.details_data
      : {};

    const currClientId =
      currDetailsData.account && currDetailsData.account.id
        ? currDetailsData.account.id
        : '';
    if (String(currClientId).length > 0) {
      const prevClientId =
        prevDetailsData.account && prevDetailsData.account.id
          ? prevDetailsData.account.id
          : '';
      if (prevClientId !== currClientId) {
        this.getClientDetails();
      }
    } else if (
      currDetailsData.contract &&
      currDetailsData.contract.id &&
      String(currDetailsData.contract.id).length > 0
    ) {
      currDetailsData['contract'] = {
        id: '',
        value: '',
        label: '',
      };
      this.setState({
        details_data: currDetailsData,
      });
    }

    const currActiveStep = this.state.activeStep;
    const prevActiveStep = prevState.activeStep;
    if (currActiveStep === 1) {
      if (currActiveStep !== prevActiveStep) {
        this.getUploadOptions(true);
      }
      if (this.state.refresh_data_map) {
        this.setState({
          refresh_data_map: false,
        });
        console.log('Check Refresh Data Map');
        this.getUploadOptions();
      }
    }
  }

  getUploadOptions = async (get_plan_type?: boolean) => {
    this.setState({
      check_internet_flag: true,
      loading_state: true,
    });
    if (get_plan_type) await this.getPlanTypes();

    //2nd page mapping, and getting client data
    await this.getClientDataMap(this.state.details_data.account.value);
    await this.getSingleClient(this.state.details_data.account.value);
    this.setState({
      check_internet_flag: false,
      loading_state: false,
    });
  };

  getClientDetails = async () => {
    const { details_data } = this.state;
    this.setState({
      check_internet_flag: true,
      loading_state: true,
    });
    const cont = await this.getClientData(details_data.account.id);
    if (cont) await this.getClientContracts(details_data.account.id, false);
    if (['manual', 'client-manual'].includes(this.state.encodePageType)) {
      this.getSingleClient(details_data.account.id, true);
    }
    this.setState({
      check_internet_flag: false,
      loading_state: false,
    });
  };

  getClientData = async (client_id: string) => {
    let cont = true;
    await API.getClient(client_id).then(response => {
      if (response && response.error) {
        this.setState({
          loading_state: false,
        });
        cont = false;
      } else if (
        response['status'] &&
        response['status'].toLowerCase() === 'terminated'
      ) {
        this.setState({
          loading_state: false,
          isOpenModal: true,
          modalTitle: 'Terminated Corporate Account',
          modalMessage:
            'Terminated account. Please try another Corporate Account.',
          isExpiredClient: false,
        });
        cont = false;
      } else {
        this.setState({
          isExpiredClient: false,
          selected_client_details: response,
        });
      }
    });

    return cont;
  };

  addFieldsToDataMap = (member_data_fields: any[]) => {
    let client_system_names: any[] =
      member_data_fields && member_data_fields.length > 0
        ? member_data_fields
        : [];

    const fieldsToAdd: any[] = [
      // add new fields to be included to data map through FE here
      {
        field_name: 'is_vip',
        system_name: 'VIP',
      },
      {
        field_name: 'is_philhealth_rider',
        system_name: 'Philhealth Rider',
      },
      {
        field_name: 'is_member_consent',
        system_name: 'Member Consent',
      },
    ];

    fieldsToAdd.forEach((fieldData, idx) => {
      if (
        client_system_names
          .map(data => data['system_name'])
          .indexOf(fieldData['system_name']) === -1
      ) {
        // let minimumSortOrder = Math.min.apply(null, client_system_names.map(systemNames => {
        //   return systemNames.sort_order
        // }))
        let maximumSortOrder = Math.max.apply(
          null,
          client_system_names.map(systemNames => {
            return systemNames.sort_order;
          }),
        );

        client_system_names.push({
          data_type: 'Alphanumeric',
          field_choices: [],
          field_name: fieldData['field_name'],
          field_type: 'Mandatory',
          info_type: 'Member Profile',
          input_type: 'Text Field',
          is_client_added: false,
          is_required: false,
          sort_order: maximumSortOrder + (idx + 1),
          system_name: fieldData['system_name'],
        });
      }
    });

    return client_system_names;
  };

  getAllClients = (breadcrumbs_items: any[]) => {
    API.getClientsWithCorporateNo().then(response => {
      if (response && response.error === undefined) {
        const { details_data } = this.state;
        if (
          details_data.account &&
          details_data.account.id &&
          details_data.account.id.length > 0
        ) {
          const chkResponse = response.find(
            item => item._id === details_data.account.id,
          );
          if (chkResponse) {
            details_data['account']['label'] = chkResponse.registered_name;
            if (
              breadcrumbs_items.length >= 2 &&
              String(breadcrumbs_items[1]['url']).includes(
                details_data.account.id,
              )
            ) {
              breadcrumbs_items[1]['label'] = chkResponse.registered_name;
            }
          }
        }
        this.setState(
          {
            details_data: details_data,
            breadcrumbs: breadcrumbs_items,
            clients_data: response,
            loading_state: false,
            check_internet_flag: false,
            initial_load: true,
          },
          async () => {
            if (
              details_data.account &&
              details_data.account.id &&
              details_data.account.id.length > 0
            ) {
              await this.getClientDetails();
            }
            this.getSaveForNowData();
          },
        );
      } else {
        this.setState({
          system_names: [],
        });
      }
    });
  };

  getSingleClient = (client_id: string, isFromMemberPage?: boolean) => {
    API.getClient(client_id).then(async response => {
      if (response && response.error === undefined) {
        const data_item = response;
        if (data_item) {
          // setting up Site Assigned for Manual Encode
          let siteAssignedOptionsArr: any[] = [];
          if (data_item['main_office_address']) {
            let siteData = data_item['main_office_address'];
            let mBranch = {
              bldg_no: siteData['bldg_no'] ? siteData['bldg_no'] : '',
              bldg_name: siteData['bldg_name'] ? siteData['bldg_name'] : '',
              street: siteData['street'] ? siteData['street'] : '',
              brgy: siteData['brgy'] ? siteData['brgy'] : '',
              city: siteData['city'] ? siteData['city'] : '',
              region: siteData['region'] ? siteData['region'] : '',
              zip_code: siteData['zip_code'] ? siteData['zip_code'] : '',
            };

            let dropdownFormat = {
              id: 0,
              value: `${mBranch['bldg_no']} ${mBranch['bldg_name']} ${mBranch['street']} ${mBranch['brgy']} ${mBranch['city']} ${mBranch['region']} / ${mBranch['zip_code']}`,
              label: `${mBranch['bldg_no']} ${mBranch['bldg_name']} ${mBranch['street']} ${mBranch['brgy']} ${mBranch['city']} ${mBranch['region']} / ${mBranch['zip_code']}`,
            };
            siteAssignedOptionsArr.push(dropdownFormat);
          }

          if (data_item['branches'] && data_item['branches'].length > 0) {
            let branchData = data_item['branches'];
            branchData.forEach((bData, idx) => {
              let oBranch = {
                bldg_no: bData['bldg_no'] ? bData['bldg_no'] : '',
                bldg_name: bData['bldg_name'] ? bData['bldg_name'] : '',
                street: bData['street'] ? bData['street'] : '',
                brgy: bData['brgy'] ? bData['brgy'] : '',
                city: bData['city'] ? bData['city'] : '',
                region: bData['region'] ? bData['region'] : '',
                zip_code: bData['zip_code'] ? bData['zip_code'] : '',
              };

              let dropdownFormat = {
                id: idx + 1,
                value: `${oBranch['bldg_no']} ${oBranch['bldg_name']} ${oBranch['street']} ${oBranch['brgy']} ${oBranch['city']} ${oBranch['region']} / ${oBranch['zip_code']}`,
                label: `${oBranch['bldg_no']} ${oBranch['bldg_name']} ${oBranch['street']} ${oBranch['brgy']} ${oBranch['city']} ${oBranch['region']} / ${oBranch['zip_code']}`,
              };
              siteAssignedOptionsArr.push(dropdownFormat);
            });
          }
          const stateToUpdate: any = {
            corporate_account: data_item['registered_name'],
            site_assigned_options: siteAssignedOptionsArr,
            clientStatus: response['status'] ? response['status'] : '',
          };
          if (!isFromMemberPage) {
            stateToUpdate['principals_options'] = await API.getClientPrincipals(
              client_id,
            )
              .then(pRes => {
                let principalsOptionsArr: any[] = [];
                if (pRes && pRes.error === undefined) {
                  pRes.forEach((pData, idx) => {
                    let pName = {
                      last_name: pData['last_name'] ? pData['last_name'] : '',
                      first_name: pData['first_name']
                        ? `${', ' + pData['first_name']}`
                        : '',
                      middle_name: pData['middle_name']
                        ? `${', ' + pData['middle_name']}`
                        : '',
                      suffix: pData['suffix']
                        ? `${', ' + pData['suffix']}`
                        : '',
                    };

                    let dropdownFormat = {
                      id: idx,
                      value: `${pName['last_name']}${pName['first_name']}${pName['middle_name']}${pName['suffix']}`,
                      label: `${pName['last_name']}${pName['first_name']}${pName['middle_name']}${pName['suffix']}`,
                    };
                    principalsOptionsArr.push(dropdownFormat);
                  });
                  if (!isFromMemberPage) {
                    stateToUpdate['principals_options'] = principalsOptionsArr;
                    stateToUpdate[
                      'site_assigned_options'
                    ] = siteAssignedOptionsArr;
                    stateToUpdate['clientStatus'] = response['status']
                      ? response['status']
                      : '';
                  }
                }
                return principalsOptionsArr;
              })
              .catch(e => {
                console.log('Check Error Getting Principals: ', e);
                return [];
              });
          }
          this.setState(stateToUpdate);
        }
      }
    });
  };

  /****** parse api response ******/
async getClientContracts(client_id: string, from_save_for_now: boolean) {
  const { details_data, batchnames, clientStatus, is_ocp } = this.state;
  this.setState({
    check_internet_flag: true,
  });
  this.showHideLoader(true);

  const isAutoRenew = this.getQueryParam('isAutoRenew');
  const contractOptions: any[] = [];
  let isContractFound = false;
  let response: any;

  try {
    // Function to process multiple contracts (used only when isAutoRenew is false)
    const processContracts = async (clientContracts: any[], proposal_id?: string) => {
      let currIdx: number = -1;
      let acIdx: number = -1;
      let awIdx: number = -1;
      let contract_ids: string[] = [];

      clientContracts.forEach((item, idx) => {
        if (from_save_for_now && proposal_id && item._id === proposal_id) {
          if (['Active', 'Awaiting Activation'].includes(item.status || '')) {
            details_data['contract'] = {
              id: item._id,
              value: item._id,
              label: item.client_contract_id || '',
              client_id: item.client_id,
              client_contract_id: item.client_contract_id,
              client_contract_no: item.client_contract_no,
            };

            isContractFound = true;
          }
        }

        if (item.status === 'Active' && acIdx === -1) {
          acIdx = idx;
        } else if (item.status === 'Awaiting Activation' && awIdx === -1) {
          awIdx = idx;
        }

        if (!contract_ids.includes(item._id)) {
          contractOptions.push({
            id: item._id,
            value: item._id,
            label: item.client_contract_id || '',
            client_id: item.client_id,
            client_contract_id: item.client_contract_id,
            client_contract_no: item.client_contract_no,
            created_by: item.created_by || '',
            date_created: item.date_created || '',
          });
          contract_ids.push(item._id);
        }
      });

      if (!isContractFound) {
        if (acIdx > -1) {
          currIdx = acIdx;
        } else if (awIdx > -1) {
          currIdx = awIdx;
        }
        if (currIdx > -1) {
          details_data['contract'] = contractOptions[currIdx];
          isContractFound = true;
        } else {
          console.log('no other contracts found');
        }
      }
    };
    // Fetch contracts based on isAutoRenew
    if (isAutoRenew) {
      // change for proposal_id multiple contracts expiry case 5926
      response = await API.getClientRenewalContract(this.props.match.params.proposal_id, client_id);
      if (response) {
        // Fetch username for created_by
        response.map((responseData) => 
          contractOptions.push({
            id: responseData._id,
            value: responseData._id,
            label: responseData.client_contract_id || '',
            client_id: responseData.client,
            client_contract_id: responseData.client_contract_id,
            client_contract_no: responseData.client_contract_no,
            created_by: {
              username: responseData.created_by || ''
            },
            date_created: responseData.date_created,
            })
          )
          details_data['contract'] = contractOptions[0];
          isContractFound = true;
      }
    } else {
      response = await API.getClientContracts(client_id, true);
      if (response.length > 0) {
        for (const res of response) {
          if (res?.contract_objects?.length > 0) {
            await processContracts(res.contract_objects, this.props.match.params.proposal_id);
          }
        }
      }
    }

    // Shared logic for is_ocp and state updates
    if (this.state.is_ocp) {
      await API.getUploadMember(this.props.location.state.member_upload_id).then(res => {
        let getContract = {
          id: '',
          value: '',
          label: '',
        };
        const chk = contractOptions.find(cont => cont.id === res.contract_id);
        if (chk) {
          getContract = chk;
        }
        details_data['contract'] = getContract;
        details_data['ticket_id'] = res.batch_name;
        details_data['date_sent'] = res.date_sent;
        details_data['sender_email'] = res.sender_email;
        details_data['sender_name'] = res.sender_name;
        details_data['isOcp'] = true;
        details_data['sent_through_channel'] = {
          id: 3,
          value: 'Client Portal',
          label: 'Client Portal',
        };

        this.setState({
          ocp_file: [
            {
              ...res.ocp_file,
              path: API.getDownloadLink(res.ocp_file.path),
            },
          ],
        });
      });
    }

    let temp_batchnames: any = batchnames;
    if (!is_ocp) {
      if (isContractFound) {
        temp_batchnames = this.getBatchNames(
          details_data['contract']['id'],
          client_id,
          response,
        );
      } else if (
        details_data.account &&
        details_data.account.id &&
        String(details_data.account.id).length > 0
      ) {
        temp_batchnames = this.getBatchNames('', '', response);
      } else {
        details_data['contract'] = {
          id: '',
          value: '',
          label: '',
        };
        temp_batchnames = this.getBatchNames('', '', response);
      }
    }

    this.setState({
      check_internet_flag: false,
      loading_state: false,
      initial_load: true,
      contracts_data: response || [],
      details_data: details_data,
      batchnames: temp_batchnames,
      contract_options: contractOptions,
    });
  } catch (err) {
    console.log('Check Contract Error: ', err);
    this.setState({
      isOpenModal: true,
      modalTitle: clientStatus === 'Terminated' ? 'Terminated Corporate Account' : 'Fetching Contracts Failed',
      modalMessage:
        clientStatus === 'Terminated'
          ? 'Account Terminated. Please try another Corporate Account'
          : 'No Contracts found. Please try another Corporate Account',
      fetchContactsErr: true,
      loading_state: false,
      initial_load: true,
      contracts_data: [],
      contract_options: [],
    });
  }
}

  getBatchNames(contract_id: string, client_id: string, contracts_data: any) {
    const { batchnames } = this.state;
    const batchNames: any = batchnames;

    // let newDetails_data = this.state.details_data
    let newContract_id = this.state.details_data['contract'];
 
    if (contract_id && client_id && contracts_data) {
      let batchNames: any = batchnames;
      for (const item_cnt in contracts_data) {
        const contract = contracts_data[item_cnt];
        if (contract['_id'] === client_id) {
          for (var obj_cnt in contract['upload_batch_objects']) {
            const batch = contract['upload_batch_objects'][obj_cnt];

            if (batch['contract_id'] === contract_id) {
              batchNames.push({
                id: batch['batch_name'],
                value: batch['batch_name'],
                label: batch['batch_name'],
              });
            }
          }
          break;
        }
      }
    } else if (newContract_id !== undefined) {
    }

    return batchNames;
  }

  async getPlanTypes() {
    const { details_data } = this.state;
    const { id: contractId } = details_data.contract;
    if (contractId) {
      await API.getContractTree(contractId).then(response => {
        if (response && response.error === undefined) {
          /**
           * Plan Types
           */
          const filteredPlanTypes = response.filter(
            (item: any) =>
              item.type === 'PlanType' && item.name !== 'Base Plan Type',
          );
          const planTypeOptions: ISelectItem[] = filteredPlanTypes.map(
            (item: any, index: number) => {
              return {
                id: index,
                // value: item.name,
                value: item.code,
                label: item.name,
              };
            },
          );
          /**
           * Civil Status
           */
          const filteredStatuses = response.filter((item: any) => {
            const { tree_id: treeId, level, type } = item;
            if (
              treeId &&
              treeId.includes('-DP-') &&
              level === 7 &&
              type === 'Benefit'
            ) {
              return true;
            }

            return false;
          });

          const civilStatusOptions: ISelectItem[] = [];
          filteredStatuses.forEach(status => {
            const isInList = civilStatusOptions.some(
              option => option.value === status.name,
            );
            if (!isInList) {
              civilStatusOptions.push({
                id: status._id,
                value: status.name,
                label: status.name,
              });
            }
          });

          /**
           * Relationships
           */
          const filteredRelationships = response.filter((item: any) => {
            const { tree_id: treeId, level, type } = item;
            if (
              treeId &&
              treeId.includes('-DP-') &&
              level === 8 &&
              type === 'Sub-Benefit'
            ) {
              return true;
            }

            return false;
          });

          const relationshipOptions: ISelectItem[] = [];
          filteredRelationships.forEach(relationship => {
            const isInList = relationshipOptions.some(
              option => option.value === relationship.name,
            );
            if (!isInList) {
              relationshipOptions.push({
                id: relationship._id,
                value: relationship.name,
                label: relationship.name,
              });
            }
          });
          this.setState({
            plan_type_options: planTypeOptions,
            civil_status_options: civilStatusOptions,
            relationship_options: relationshipOptions,
          });
        }
      });
    }
  }

  async getClientDataMap(client_id: string) {
    let stateToUpdate: any = {
      clients_data_maps: [],
      client_maps: [],
    };
    if (client_id && client_id.trim()) {
      const {
        clients_data,
        selected_data_map: state_selected_map,
      } = this.state;
      const chkClients = clients_data.find(client => client._id === client_id);
      const sys_name: any[] = [];
      if (chkClients && chkClients.member_data_fields) {
        const member_data_fields = chkClients.member_data_fields;
        if(this?.state?.renewalType === "uploadRenewal"){
          member_data_fields.unshift( {
            data_type: 'Alphanumeric',
            field_choices: [],
            field_name: 'member_id',
            required: true,
            field_type: 'Mandatory',
            info_type: 'Member Profile',
            input_type: 'Text Field',
            is_client_added: false,
            is_required: true,
            sort_order: 1,
            system_name: 'Member ID',
            _id: '2134',
          })
        }
        sys_name.push(...member_data_fields);
      }
      await API.getClientDataMap(client_id)
        .then(response => {
          if (response.error === undefined) {
            let client_maps: any = [];
            let selected_map: any = {};
            let selected_mapping: any = [];
            const columns_data: any = {
              columns: [
                {
                  name: 'system_name',
                  title: 'System Name',
                },
                {
                  name: 'sheet_name',
                  title: 'Column Header',
                },
              ],
              rows: [
                // {
                //   sheet_name: '',
                //   system_name: '',
                // },
              ],
            };
            /******** get default mapping *******/
            for (const cnt in response) {
              if (response[cnt] !== null) {
                const resDataMap = response[cnt];
                const dataMapOption = {
                  id: resDataMap['_id'],
                  value: resDataMap['_id'],
                  label: resDataMap['name'],
                  is_default: resDataMap['is_default']
                    ? resDataMap['is_default']
                    : false,
                };
                if (
                  resDataMap['is_default'] === true &&
                  Object.keys(selected_map).length === 0
                ) {
                  selected_map = dataMapOption;
                  if (
                    resDataMap['mapping'] &&
                    resDataMap['mapping'].length > 0
                  ) {
                    selected_mapping = resDataMap['mapping'];
                  }
                }

                client_maps.push(dataMapOption);
                // this.setState({
                //   // default_data_mapping_save_for_now: client_maps, //'default test',
                //   // data_mapping_save_for_now: 'mapping list'
                // })
              }
            }
            /******** get default template *******/
            if (sys_name.length > 0 && selected_mapping.length > 0) {
              columns_data['rows'] = [];
              columns_data['columns'][1]['type'] = 'select';
              columns_data['columns'][1]['options'] = [
                {
                  id: '',
                  value: '',
                  label: '',
                },
              ];
              let sheet_names: any = [];
              for (var sys_cnt in sys_name) {
                const name = sys_name[sys_cnt];
                const row: any = {
                  system_name: name['system_name'],
                  field_name: name['field_name'],
                  sheet_name: '',
                  sheet_field: '',
                  required: name['is_required'],
                };
                for (var cnt in selected_mapping) {
                  const map = selected_mapping[cnt];
                  if (map['system_name'] === name['system_name']) {
                    row['sheet_name'] = map['sheet_name'];
                    sheet_names.push(map['sheet_name']);
                    break;
                  }
                }
                columns_data['rows'].push(row);
              }
              const temp_options: any[] = sheet_names.map(item => {
                return {
                  id: '',
                  value: item,
                  label: item,
                };
              });
              columns_data['columns'][1]['options'] = temp_options;
            }
            stateToUpdate = {
              ...stateToUpdate,
              system_names: sys_name,
              clients_data_maps: response,
              client_maps: client_maps,
            };
            const chkClientDataMaps = response.findIndex(
              item =>
                state_selected_map &&
                state_selected_map.id &&
                item._id === state_selected_map.id,
            );
            if (chkClientDataMaps === -1) {
              stateToUpdate['selected_data_map'] = selected_map;
              stateToUpdate['columns_data'] = columns_data;
            }
            this.setState(stateToUpdate);
          } else {
            this.setState(stateToUpdate);
          }
        })
        .catch(err => {
          console.log(err);
        });
    } else {
      this.setState(stateToUpdate);
    }
  }

  /******** open modal component *******/
  openModal() {
    this.setState({
      open: true,
    });
  }

  onHandleSubmitClick() {
    //ANN: used from encode back to dashboard to display ticket open dashboard
    this.closeModal();
    const {
      ticketId,
      isAddMembersTicket,
      isVoidMembersTicket,
      isFromEncodeMembersTicket,
      encodePageType,
    } = this.state;
    this.setState({
      check_internet_flag: true,
    });
    if (
      !ticketId ||
      ticketId === null ||
      ticketId === undefined ||
      ticketId === ''
    ) {
      if (encodePageType === 'void') {
        // console.log('activityvoid3', this.state)
        this.createUploadVoidMasterlistTicket(false);
      } else {
        this.createAddMemberTicket(false);
      }
    } else {
      const ticketFromPath = this.props.match.params.ticket_id;
      if (encodePageType === 'void') {
        // console.log('activityvoid4', this.state)
        this.setVoidTicketVariable(ticketId);
        this.handleSaveForNowOnSubmit();
      } else {
        if (
          isAddMembersTicket &&
          isFromEncodeMembersTicket &&
          ticketId === ticketFromPath
        ) {
          this.createAddMemberTicket(false);
        } else if (isVoidMembersTicket && ticketId === ticketFromPath) {
          this.createUploadVoidMasterlistTicket(false);
        } else {
          this.setTicketVariable(ticketId);
          this.setVoidTicketVariable(ticketId);
          this.handleSaveForNowOnSubmit();
        }
      }
    }
  }

  isStringSame(str1: string, str2: string): boolean {
    if (str1.toLowerCase() === str2.toLowerCase()) {
      return true;
    } else {
      return false;
    }
  }

  /******** function that handles submission of member file *******/
  handleSubmit() {
    this.setState({
      loading_state: true,
    });
    /******** generate payload ********/
    const {
      details_data,
      uploaded_data,
      columns_data,
      trueTicketId,
    } = this.state;

    let booleanDefaultFields: any[] = [
      'is_vip',
      'is_philhealth_rider',
      'is_member_consent',
      'philhealth_rider',
      'member_consent',
      'vip',
    ];

    const values_array: any = [
      'Gender',
      'Civil Status',
      'Plan Type',
      'Type',
      'Relationship to Principal',
      'VIP',
      'Philhealth Rider',
      'Member Consent',
      'Suffix',
      'Site',
    ];
    /***** sample payload *****/
    let payload: any = {
      client_id: details_data['account']['id'],
      contract_id: details_data['contract']['id'],
      client_contract_no: details_data['contract']['client_contract_no'],
      client_contract_id: details_data['contract']['client_contract_id'], //ANN
      sender_name: details_data['sender_name'],
      sender_email: details_data['sender_email'],
      channel: details_data['sent_through_channel']['value'],
      ticket_id: details_data['ticket_id'],
      file_url: details_data['file_url'],
      isOcp: details_data['isOcp'],
      date_sent: details_data['date_sent'],
      batch_name:
        details_data['batch_name'] && details_data['batch_name']['value']
          ? details_data['batch_name']['value'].toString()
          : trueTicketId.toString(),
      no_of_members:
        details_data['number_of_members'] &&
        details_data['number_of_members'] !== '' &&
        !isNaN(details_data['number_of_members'])
          ? parseInt(details_data['number_of_members'])
          : uploaded_data['rows'].length,
      members: [],
      default_data_map_save_for_now:
        details_data['default_data_map_save_for_now'],
      data_map_save_for_now: details_data['data_map_save_for_now'],
    };
    if (this.state.encodePageType === 'default') {
      console.log('routeWorkflow()', details_data);
      /****** generate data for members *******/
      // sort dependents by heirarchy (desc)
      let sorted_dependents: any = [];
      let sorted_members: any = [];
      let raw_members: any = uploaded_data['rows'];
      for (const memberIndex in raw_members) {
        // Dependent
        if (
          !_.isNil(raw_members[memberIndex].member_type) &&
          !this.isStringSame(raw_members[memberIndex].member_type, 'Principal')
        ) {
          if (
            !_.isNil(raw_members[memberIndex]['relationship_to principal']) &&
            (this.isStringSame(
              raw_members[memberIndex]['relationship_to principal'],
              'Husband',
            ) ||
              this.isStringSame(
                raw_members[memberIndex]['relationship_to principal'],
                'Wife',
              ))
          ) {
            sorted_dependents.unshift(raw_members[memberIndex]); // Spouse
          } else {
            sorted_dependents.push(raw_members[memberIndex]); // Other (Children, Parents, Siblings, etc.)
          }
        } else {
          // Principal
          if (sorted_dependents.length > 0) {
            for (const dependentIndex in sorted_dependents) {
              sorted_members.push(sorted_dependents[dependentIndex]);
            }
            sorted_dependents = [];
          }
          sorted_members.push(raw_members[memberIndex]);
        }
      }

      for (const dependentIndex in sorted_dependents) {
        sorted_members.push(sorted_dependents[dependentIndex]);
      }
      sorted_dependents = [];

      uploaded_data['rows'] = sorted_members;
      for (const row_cnt in uploaded_data['rows']) {
        const row: any = uploaded_data['rows'][row_cnt];
        let temp_row: any = {};
        for (const cnt in columns_data['rows']) {
          const col_data: any = columns_data['rows'][cnt];
          if (
            col_data &&
            col_data['sheet_name'] &&
            row[col_data['sheet_field']] !== undefined &&
            row[col_data['sheet_field']] !== null &&
            values_array.indexOf(col_data['system_name']) !== -1
          ) {
            temp_row = this.generateValuesData(
              row[col_data['sheet_field']],
              col_data['field_name'],
              col_data['system_name'],
              temp_row,
            );
          } else if (
            col_data &&
            col_data['sheet_name'] &&
            row[col_data['sheet_field']] !== undefined &&
            row[col_data['sheet_field']] !== null
          ) {
            if (col_data['field_name'] === 'suffix') {
              temp_row[col_data['field_name']] = row[
                col_data['sheet_field']
              ].trim();
              let value = row[col_data['sheet_field']].trim();
              if (
                row[col_data['sheet_field']] &&
                row[col_data['sheet_field']] !== undefined &&
                row[col_data['sheet_field']] !== null
              ) {
                let suffix = row[col_data['sheet_field']]
                  .replace('.', '')
                  .toLowerCase();
                switch (suffix) {
                  case 'sr':
                  case 'jr':
                    value =
                      suffix.charAt(0).toUpperCase() +
                      suffix.substring(1) +
                      '.';
                    break;
                  case 'others':
                    value =
                      suffix.charAt(0).toUpperCase() + suffix.substring(1);
                    break;
                  case 'i':
                  case 'ii':
                  case 'iii':
                  case 'iv':
                  case 'v':
                    value = suffix.toUpperCase();
                    break;
                }
                temp_row[col_data['field_name']] = value;
              }
            } else {
              temp_row[col_data['field_name']] = row[col_data['sheet_field']];
            }
          } else {
            let fieldIdx: number = booleanDefaultFields.indexOf(
              col_data['field_name'],
            );
            if (
              fieldIdx !== -1 &&
              booleanDefaultFields[fieldIdx] === col_data['field_name']
            ) {
              temp_row[col_data['field_name']] = false;
            } else {
              temp_row[col_data['field_name']] = '';
            }
          }
        }
        if (Object.keys(temp_row).length > 0) {
          console.log('temp_row()1', details_data);
          temp_row['contracts'] = [details_data['contract']['id']];
          // temp_row['contracts'] = details_data['contract']['client_contract_id']
          payload['members'].push(temp_row);
        }
      }
    } else if (
      ['manual', 'client-manual'].includes(this.state.encodePageType)
    ) {
      let currentData = JSON.stringify(uploaded_data['rows']);
      let parsedData = JSON.parse(currentData);
      let modifiedData = parsedData.map(data => {
        delete data['row_id'];
        delete data['age'];

        return data;
      });
      payload['members'] = modifiedData;
    } else if (this.state.encodePageType === 'void') {
      const rows = uploaded_data['rows'];
      const columnRow = columns_data['rows'];
      const mappings = this.state.data_mapping_values;
      const members: any[] = [];
      for (const row of rows) {
        const member: any = {};
        for (const key of Object.keys(row)) {
          const columnKey = columnRow.find(
            col => col.sheet_field && col.sheet_field === key,
          );
          if (columnKey) {
            if (
              mappings[key] &&
              mappings[key]['rows'] &&
              mappings[key]['rows'].length > 0
            ) {
              const rowMap = mappings[key]['rows'].find(
                map => map.values === row[key],
              );
              if (rowMap) {
                row[key] = rowMap['data_column'];
              }
            }

            if (booleanDefaultFields.includes(columnKey.field_name)) {
              if (typeof row[key] === 'string') {
                if (row[key] === 'No') {
                  row[key] = false;
                } else {
                  row[key] = true;
                }
              }
            }
            member[columnKey.field_name] = row[key];
          }
        }
        members.push(member);
      }
      payload['members'] = members;
    }

    /****** post member-upload integration *******/
    this.routeWorkflow(payload);
  }

  //route processmaker to verifier
  routeWorkflow(payload: any) {
    const { details_data, ticketId, encodePageType } = this.state;
    Processmaker.put(`cases/${ticketId}/variable`, {
      client_id: details_data['account']['id'],
      client_name: details_data['account']['label'],
      mother_contract_id: details_data['contract']['id'],
      // mother_contract_id: details_data['contract']['client_contract_id'],
      page_type: encodePageType,
      use_import: true,
      client_contract_id: details_data['contract']['client_contract_id'],
      client_contract_no: details_data['contract']['client_contract_no'],
    })
      .then(() => {
        this.postUploadMember(payload, false);
      })
      .catch((e: Error) => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            loading_state: false,
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.postUploadMember(payload, false);
        }
      });
  }
  //doSaveForNow
  postUploadMember(payload: any, isSaveForNow?: boolean) {
  
    if(this?.state?.renewalType === "uploadRenewal"){
      payload['renewalType'] = this?.state?.renewalType
    }
    const {
      ticketId,
      details_data,
      ticket_details,
      uploadMemberData,
    } = this.state;
 
    this.setState({
      check_internet_flag: true,
      isSaveForNow: false,
      isSaveForNowMemberList: false,
      uploadMemberData: {
        ...uploadMemberData,
        ...payload,
      },
    });


    if (
      details_data.isOcp &&
      ticket_details &&
      ticket_details.is_standard &&
      ticket_details.is_standard === true
    ) {
      // handling for ocp standard template
      // skip the upload member method and proceed with the confirm enrollment modal
      let processStatus = {
        status: 'SUCCESS',
        active_index: `${this.state.uploaded_data.rows.length}`,
        total_count: `${this.state.uploaded_data.rows.length}`,
        message: '',
      };
      this.setState({
        processingStatus: processStatus,
        loading_state: false,
      });
    } else if (this.state.encodePageType === 'void') {
      let voidPayload = payload['members'].map(member => {
        const temp: any = {};
        for (const key of Object.keys(member)) {
          if (member[key] !== null) {
            temp[key] = member[key];
          }
        }
        return temp;
      });
      let clientId = payload['client_id'];
      let endorsement_details = {
        source: 'Internal',
        endorsed_date: payload['date_sent'],
        endorsed_by: payload['sender_name'],
        channel: payload['channel'],
        ticket_no: this.state.void_ticket_id,
        reason: '',
      };
      API.postUploadVoidMasterlist(
        voidPayload,
        ticketId,
        clientId,
        endorsement_details,
      )
        .then(voidResp => {
          // console.log('activity void upload1', voidResp)
          if (voidResp && voidResp.error === undefined) {
            this.setState({
              check_internet_flag: false,
            });
            this.onPostVoidMasterlistSuccess(voidResp);
          } else {
            if (voidResp && voidResp.error) {
              let modalTitle = 'Error in Uploading';
              let modalMessage = '';

              const { statusCode, name, message, details } = voidResp.error;
              if (statusCode === 500) {
                modalTitle = 'Submission Failed';
                modalMessage = 'Something went wrong. Please try again later.';
              } else if (name === 'PayloadTooLargeError') {
                modalTitle = 'Submission Failed';
                modalMessage = 'The content of the file uploaded is too large.';
              } else if (statusCode === 422) {
                modalTitle = 'Submission Failed';
                modalMessage = message;
                if (details && details.length > 0) {
                  if (details[0].path.includes('suffix')) {
                    modalMessage =
                      'One or more uploaded member have suffix values that are unaccepted. Kindly adjust and reupload from among the accepted values: Sr., Jr., I, II, III, IV, V';
                  }
                }
              } else {
                modalTitle = 'Submission Failed';
                //modalMessage = 'WHYYYY';
                modalMessage = message;
              }

              this.setState({
                loading_state: false,
                modalTitle: modalTitle,
                modalMessage: modalMessage,
                isOpenModal: true,
              });
            } else {
              this.setState({
                loading_state: false,
                modalTitle: 'Submission Failed',
                modalMessage: 'Failed to submit members.',
                isOpenModal: true,
              });
            }
          }
        })
        .catch((voidErr: Error) => {
          if (voidErr.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState({
              loading_state: false,
              modalTitle: 'Submission Failed',
              modalMessage: 'Failed to submit members.',
              isOpenModal: true,
            });
          }
        });
    } else {
      this.setState({
        isSaveForNow: false,
        isSaveForNowMemberList: false,
      });
      API.postUploadMember(payload, ticketId)

        .then(response => {
          if (response && response.error === undefined) {
            this.setState({
              check_internet_flag: false,
            });
            console.log('postUploadMember()22', this.state, response);
            this.onPostUploadMemberSuccess(response);
          } else {
            if (response && response.error) {
              let modalTitle = 'Error in Uploading';
              let modalMessage = '';

              const { statusCode, name, message, details } = response.error;
              if (statusCode === 500) {
                modalTitle = 'Submission Failed';
                modalMessage = 'Something went wrong. Please try again later.';
              } else if (name === 'PayloadTooLargeError') {
                modalTitle = 'Submission Failed';
                modalMessage = 'The content of the file uploaded is too large.';
              } else if (statusCode === 422) {
                modalTitle = 'Submission Failed';
                modalMessage = message;
                if (details && details.length > 0) {
                  if (details[0].path.includes('suffix')) {
                    modalMessage =
                      'One or more uploaded member have suffix values that are unaccepted. Kindly adjust and reupload from among the accepted values: Sr., Jr., I, II, III, IV, V';
                  }
                }
              } else {
                modalTitle = 'Submission Failed';
                //modalMessage = 'WHYYYY';
                modalMessage = message;
              }

              this.setState({
                loading_state: false,
                modalTitle: modalTitle,
                modalMessage: modalMessage,
                isOpenModal: true,
              });
            } else {
              this.setState({
                loading_state: false,
                modalTitle: 'Submission Failed',
                modalMessage: 'Failed to submit members.',
                isOpenModal: true,
              });
            }
          }
        })
        .catch((e: Error) => {
          if (e.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState({
              loading_state: false,
              modalTitle: 'Submission Failed',
              modalMessage: 'Failed to submit members.',
              isOpenModal: true,
            });
          }
        });
    }
  }

  onPostUploadMemberSuccess(response: any) {

    const { ticketId } = this.state;
    const { _id } = response;
    this.setState(
      {
        loading_state: false,
        memberUploadId: _id,
        processingStatus: {},
      },
      () => {
        this.setTicketVariable(ticketId);
        this.startMemberUploadJob();
      },
    );
  }

  onPostVoidMasterlistSuccess(response: any) {
    const { ticketId } = this.state;
    const { member_void_upload_id } = response[0];
    this.setState(
      {
        loading_state: false,
        memberUploadId: member_void_upload_id,
        processingStatus: {},
      },
      () => {
        this.setVoidTicketVariable(ticketId);
        this.startVoidUploadJob(response);
      },
    );
  }

  startMemberUploadJob() {
    this.setState(
      {
        loading_state: true,
      },
      () => {
        this.getProcessingStatus();
       
      },
    );
  }

  startVoidUploadJob(memberUploadData: any) {
    const retryProps: any = {
      loading_state: false,
      isConfirmModalOpen: true,
      modalTitle: 'Submission Failed',
      modalMessage: 'Failed to submit members.',
      modalCloseText: 'Cancel',
      modalConfirmText: 'Continue Process',
      next: () => {
        this.startVoidUploadJob(memberUploadData);
      },
    };
    this.setState(
      {
        loading_state: true,
      },
      () => {
        const { member_void_upload_id } = memberUploadData[0];
        API.startVoidUpload(member_void_upload_id, this.state.ticketId)
          .then(response => {
            const resErr = _.get(response, 'error', null);
            if (_.isNil(resErr)) {
              this.getProcessingStatus();
            } else {
              this.setState(retryProps);
            }
          })
          .catch(e => {
            if (e.message === 'USER_DATA_NOT_FOUND') {
              this.setState({
                loading_state: false,
                modalTitle: 'User Data Error',
                modalMessage:
                  'User data not found. Please try logging in again.',
                isOpenModal: true,
                next: () => {
                  // window.location.replace('../index.html#/');
                },
              });
            } else {
              this.setState(retryProps);
            }
          });
      },
    );
  }

  /******** function that get value based on map values in the file *******/
  generateValuesData(
    attr_value: any,
    attr_name: string,
    name: string,
    row: any,
  ) {
    // include attributes to be exempt from 'For Validation' here
    let exemptedAttrNames: any[] = [
      'is_vip',
      'is_philhealth_rider',
      'is_member_consent',
    ];

    row[attr_name] = '';
    switch (name.toLowerCase()) {
      case 'gender':
        const { gender_data } = this.state;
        if (
          gender_data &&
          gender_data['rows'] &&
          gender_data['rows'].length > 0
        ) {
          for (const cnt in gender_data['rows']) {
            const gender = gender_data['rows'][cnt];
            if (gender['values'] === attr_value) {
              row[attr_name] = gender['data_column'];
              break;
            }
          }
        }
        break;
      case 'civil status':
        const { civil_status_data } = this.state;
        if (
          civil_status_data &&
          civil_status_data['rows'] &&
          civil_status_data['rows'].length > 0
        ) {
          for (const cnt in civil_status_data['rows']) {
            const status = civil_status_data['rows'][cnt];
            if (status['values'] === attr_value) {
              row[attr_name] = status['data_column'];
              break;
            }
          }
        }
        break;
      case 'plan type':
        const { plan_type_data } = this.state;
        if (
          plan_type_data &&
          plan_type_data['rows'] &&
          plan_type_data['rows'].length > 0
        ) {
          for (const cnt in plan_type_data['rows']) {
            const plan = plan_type_data['rows'][cnt];
            if (plan['values'] === attr_value) {
              row[attr_name] = plan['data_column'];
              break;
            }
          }
        }
        break;
      case 'type':
        const { type_data } = this.state;
        if (type_data && type_data['rows'] && type_data['rows'].length > 0) {
          for (const cnt in type_data['rows']) {
            const type = type_data['rows'][cnt];
            if (type['values'] === attr_value) {
              row[attr_name] = type['data_column'];
              break;
            }
          }
        }
        break;
      case 'relationship to principal':
        const { relationship_data } = this.state;
        if (
          relationship_data &&
          relationship_data['rows'] &&
          relationship_data['rows'].length > 0
        ) {
          for (const cnt in relationship_data['rows']) {
            const relationship = relationship_data['rows'][cnt];
            if (relationship['values'] === attr_value) {
              row[attr_name] = relationship['data_column'];
              break;
            }
          }
        }
        break;
      case 'vip':
        const { vip_data } = this.state;
        if (vip_data && vip_data['rows'] && vip_data['rows'].length > 0) {
          for (const cnt in vip_data['rows']) {
            const vip = vip_data['rows'][cnt];
            if (vip['values'] === attr_value) {
              row[attr_name] = vip['data_column'];
              break;
            }
          }
        }
        break;
      case 'philhealth rider':
        const { ph_rider_data } = this.state;
        if (
          ph_rider_data &&
          ph_rider_data['rows'] &&
          ph_rider_data['rows'].length > 0
        ) {
          for (const cnt in ph_rider_data['rows']) {
            const ph_rider = ph_rider_data['rows'][cnt];
            if (ph_rider['values'] === attr_value) {
              row[attr_name] = ph_rider['data_column'];
              break;
            }
          }
        }
        break;
      case 'member consent':
        const { member_consent_data } = this.state;
        if (
          member_consent_data &&
          member_consent_data['rows'] &&
          member_consent_data['rows'].length > 0
        ) {
          for (const cnt in member_consent_data['rows']) {
            const member_consent = member_consent_data['rows'][cnt];
            if (member_consent['values'] === attr_value) {
              row[attr_name] = member_consent['data_column'];
              break;
            }
          }
        }
        break;
      case 'site':
        const { site_data } = this.state;
        if (site_data && site_data['rows'] && site_data['rows'].length > 0) {
          for (const cnt in site_data['rows']) {
            const site = site_data['rows'][cnt];
            if (site['values'] === attr_value) {
              row[attr_name] = site['data_column'];
              break;
            }
          }
        }
        break;
      case 'suffix':
        const { suffix_data } = this.state;
        if (
          suffix_data &&
          suffix_data['rows'] &&
          suffix_data['rows'].length > 0
        ) {
          for (const cnt in suffix_data['rows']) {
            const suffix = suffix_data['rows'][cnt];
            if (suffix['values'] === attr_value) {
              row[attr_name] = suffix['data_column'];
              break;
            }
          }
        }
        break;
      //branch_options
    }

    if (row[attr_name] && row[attr_name] === 'default_plantype_placeholder') {
      row[attr_name] = '';
    } else if (
      !exemptedAttrNames.includes(attr_name) &&
      (!row[attr_name] || row[attr_name] === '')
    ) {
      row[attr_name] = `${attr_value} - For Validation`;
    }
    return row;
  }

  /******** close modal component *******/
  closeModal() {
    this.setState({
      open: false,
    });
  }

  validateDetails() {
    const { activeStep, details_data } = this.state;

    // Add details
    if (activeStep === 0) {
      /* checking on required fields */
      let error_cnt = 0;
      for (var i in details_data) {
        const error_message = this.getErrorMessage(i);
        switch (i) {
          case 'account':
          case 'contract':
          case 'sent_through_channel':
            // case 'batch_name': /** Remove validation of batch name */
            if (
              !(
                details_data[i] &&
                details_data[i]['value'] &&
                details_data[i]['value'] !== ''
              )
            ) {
              this.setState({
                [i + '_error']: true,
                [i + '_error_message']: error_message,
              } as any);
              error_cnt++;
            }
            break;
          case 'contract':
            // case 'batch_name': /** Remove validation of batch name */
            if (
              !(
                details_data[i] &&
                details_data[i]['value'] &&
                details_data[i]['value'] !== ''
              )
            ) {
              this.setState({
                [i + '_error']: true,
                [i + '_error_message']: error_message,
              } as any);

              error_cnt++;
            }
            break;

          case 'sender_name':
            // case "number_of_members":
            if (
              details_data[i] === '' ||
              !details_data[i] ||
              details_data[i] === null
            ) {
              this.setState({
                [i + '_error']: true,
                [i + '_error_message']: error_message,
              } as any);
              error_cnt++;
            }
            break;
          case 'date_sent':
            if (
              details_data[i] === '' ||
              !details_data[i] ||
              details_data[i] === null
            ) {
              this.setState({
                [i + '_error']: true,
                [i + '_error_message']: error_message,
              } as any);
              error_cnt++;
            } else if (!GlobalFunction.isValidDate(details_data[i])) {
              this.setState({
                date_sent_error: true,
                date_sent_error_message: 'Date is invalid',
              } as any);
              error_cnt++;
            }
            break;
          case 'sender_email':
            // Check if channel is email
            const channel = details_data['sent_through_channel'];
            if (channel && channel.value === 'Email') {
              if (
                details_data[i] === '' ||
                !details_data[i] ||
                details_data[i] === null
              ) {
                this.setState({
                  [i + '_error']: true,
                  [i + '_error_message']: error_message,
                } as any);
                error_cnt++;
              } else {
                const email_validation = this.validateEmail(
                  details_data['sender_email'],
                );
                if (email_validation) {
                  this.setState({
                    sender_email_error: true,
                    sender_email_error_message: 'Wrong format',
                  });
                  error_cnt++;
                }
              }
            }
            if (channel && channel.value !== 'Email') {
              if(channel.value === 'Fax'){
                if(details_data[i]){
                      const email_validation = this.validateEmail(
                  details_data['sender_email'],
                );
                if (email_validation) {
                  this.setState({
                    sender_email_error: true,
                    sender_email_error_message: 'Wrong format',
                  });
                  error_cnt++;
                }
                }
              }else{
                this.setState({
                              [i + '_error']: false,
                            } as any);
              }

           
            }
            break;
        }
      }
      if (error_cnt > 0) {
        return error_cnt;
      }
    }

    return 0;
  }

  onTerminateUpload() {
    this.setState({
      loading_state: false,
      redirectFlag: true,
      redirectInfo: {
        pathname: `/membership/clients-profile/batch-terminate/${this.state.contracts_data[0]._id}`,
      },
    });
  }

  /******** function that handles next button *******/
  getQueryParam(param: string) {
    const queryParams = new URLSearchParams(window.location.hash);
    return queryParams.get(param);
  }
  handleNextButton() {
    const {
      activeStep,
      uploaded_data,
      columns_data,
      details_data,
      handleNextCounter,
      ticketId,
    } = this.state;

   
    // if( this.getQueryParam("isAutoRenew")) // navigate na agad sa autoRenew 5685
    if(this.getQueryParam('isAutoRenew')){

      // validate first the encode details form 
        const error_cnt = this.validateDetails();
        if (error_cnt > 0) {
          return;
        }
    if (this.getQueryParam('isAutoRenew') === 'false') {
      // need to work on save for now on ticket manual renewal and upload renewal 5685
       //this is for the save for now requirement - if they SAVE FOR NOW in manual, it should redirect to manual renewal page NO MORE MODAL
      const _ticketVariablie = this.state?.ticketVariables;

      if (activeStep === 0) {
        if (
          _ticketVariablie &&
          (
            //manual renewal
            (_ticketVariablie.is_manual === 1 &&
              (_ticketVariablie.tas_title === 'Manual Renewal' || _ticketVariablie.tas_title === 'Finished Manual Renew of Members')) ||
            //upload renewal
            (_ticketVariablie.is_manual === 0 &&
              _ticketVariablie.tas_title === 'Upload Renewal')
          )
        ) {

          // Check if there's any save-for-now data for this ticket
          // API.getSaveForNowData( 'encode-member', this.props.match.params.ticket_id)
          API.getSaveForNowData(this.getQueryParam('isAutoRenew') === 'false' ? 'renew-members' :'encode-member', this.props.match.params.ticket_id)
            .then(response => {
              console.log('Save for now check response:', response);
              if (response && response.length > 0 && response[0]?.user_id  === 'renew-members' && response[0]?.data?.renewalType) {
                //for upload Renewal
                if ( response[0]?.data?.renewalType === "uploadRenewal") {
                  this.setState({
                    pageHeader: 'Upload Renewal File',
                    activeStep: 1,
                  });
                }
                // Save-for-now data exists, skip modal and go to manual renewal page
                else
                 if ( response[0]?.data?.renewalType === "manualRenewal")  {
                  this.setState({
                    loading_state: false,
                    redirectFlag: true,
                    redirectInfo: {
                      pathname: `/membership/clients-profile/manual-renewal/selection/${this.props.match.params.client_id}/${this.props.match.params.ticket_id}`,
                      state: this.state
                    },
                  });
                }
                this.setState((prevState) => ({
                  ...prevState,
                  renewalType: response[0].data?.renewalType,
                }))
              } else {
                // No save-for-now data, labas mo modal!
                this.setState(prev => ({
                  ...prev,
                  isHowToRenewModalOpen: true,
                }));
              }
            })
            .catch(error => {
              console.log('Error checking save-for-now data:', error);
              this.setState(prev => ({
                ...prev,
                isHowToRenewModalOpen: true,
              }));
            });
          return;
        } else {
          return this.setState(prev => {
            return {
              ...prev,
              isHowToRenewModalOpen: true,
            };
          });
        }
      }
    }
      

    if (this.getQueryParam('isAutoRenew') === 'true') {
      //5685 sir jacob autorenew navigate
      // If this is true, the route case API will be called, and ticketVariables.tas_title will be set to "Auto-renewal".
      // This is necessary to prevent multiple calls to the route case API when the Auto-renew ticket opens.
      // Multiple calls can cause the ticket's tas_title to become "Verify Renewal Memberlist"
      if(this.state.ticketVariables && this.state.ticketVariables.tas_title === 'Claimed Renew Memberlist' && this.state.ticketVariables.renew_members === 1){

        Processmaker.put('cases/' + ticketId + '/route-case', {})
          .then(() => {
            return this.setState(prev => {
              return {
                ...prev,
                loading_state: false,
                pageHeader: 'Auto Renew Members',
                activeStep: 1,
              };
            });
          })
          .catch(error => {
            console.error('Error routing case:', error);
            // Add error handling logic here
          });

      } else {
       return this.setState(prev => {
            return {
              ...prev,
              loading_state: false,
              pageHeader: 'Auto Renew Members',
              activeStep: 1,
            };
          });
      }
    }
    }
   

    if (this.state.isSuffixError) {
      this.setState({
        ...this.state,
        isShowSuffixErrorModal: true,
      });
    } else if (this.state.encodePageType === 'terminate') {
      this.onTerminateUpload();
    } else {
      this.setState({
        ...this.state,
        handleNextCounter: this.state.handleNextCounter + 1,
      });

      let newActiveStep =
        details_data.file_url && handleNextCounter === 0 ? 0 : activeStep;
      // Add details
      if (newActiveStep === 0) {
        const cnt = newActiveStep + 1;
        /* checking on required fields */
        const error_cnt = this.validateDetails();
        if (error_cnt > 0) {
          return;
        }
  
        if (cnt === 1) {
          if (this.state.encodePageType === 'default') {
            if (this.state.pmaker_task === 'Enroll Members') {
              this.setState({
                pmaker_task: 'Upload File',
                pageHeader: 'Upload Member File',
                activeStep: cnt,
              });
            } else {
              this.setState({
                pageHeader: 'Upload Member File',
                activeStep: cnt,
              });
            }
          } else if (
            ['manual', 'client-manual'].includes(this.state.encodePageType)
          ) {
            this.setState({
              pageHeader: 'Encode Multiple Members',
              activeStep: cnt,
            });
          } else if (this.state.encodePageType === 'void') {
            this.setState({
              pageHeader: 'Upload Void Masterlist File',
              activeStep: cnt,
            });
          } else if (this.state.encodePageType === 'renewal') {
            this.setState({
              pageHeader: 'Upload Renewal File',
              activeStep: cnt,
            });
          } else if (this.state.encodePageType === 'terminate') {
            this.setState({
              pageHeader: 'Upload Termination File',
              activeStep: cnt,
            });
          } else {
            this.setState({
              activeStep: cnt,
            });
          }
        } else {
          console.log('encodepagetype8');
        }
      }
      // if (this.state.encodePageType === 'default') {
      if (
        ['default', 'void', 'terminate'].includes(this.state.encodePageType)
      ) {
        // Encode Members
        if (newActiveStep === 1) {
          if (details_data.file_url && handleNextCounter === 0) {
          } else if (
            Object.keys(uploaded_data).length <= 0 ||
            uploaded_data['rows'].length <= 0
          ) {
            this.setState({
              modalTitle: 'Submission Failed',
              modalMessage:
                'You must upload a spreadsheet file before you can proceed.',
              isOpenModal: true,
            });

            return;
          }

          /****** validate mapping or required fields *******/
          if (columns_data['rows'] && columns_data['rows'].length === 0) {
            this.setState({
              isOpenModal: true,
              modalTitle: 'Error Message',
              modalMessage:
                'Mapping of data is required. Please complete first the mapping of data to continue.',
            });
            return;
          }

          /****** checked mapping of required system names ******/
          let required_fields: any = [];
          for (const cnt in columns_data['rows']) {
            const col_data: any = columns_data['rows'][cnt];
            if (
              col_data['required'] === true &&
              (col_data['sheet_name'] === '' ||
                col_data['sheet_name'] === undefined) &&
              required_fields.indexOf(col_data['system_name']) === -1
            ) {
              required_fields.push(col_data['system_name']);
            }
          }
          if (required_fields.length > 0) {
            this.setState({
              isOpenErrorWithListModal: true,
              modalTitle: 'Mapping of data is incomplete',
              modalMessage: 'Please map the following required fields: ',
              error_list: required_fields,
            });
            return;
          }

          /**
           * Check for date format.
           */
          let birthdateFormatError = false;
          let invalidDate = false;
          for (const row_cnt in uploaded_data['rows']) {
            const row: any = uploaded_data['rows'][row_cnt];
            for (const cnt in columns_data['rows']) {
              const col_data: any = columns_data['rows'][cnt];
              if (
                col_data &&
                col_data['sheet_name'] &&
                row[col_data['sheet_field']] !== undefined &&
                row[col_data['sheet_field']] !== null
              ) {
                if (col_data['field_name'] === 'date_of_birth') {
                  const dateOfBirth = row[col_data['sheet_field']];
                  let validDateCounter: number = 0;
                  const dateFormats = BirthDateFormats;
                  const fourDigitYearRegEx = /^(\d{4})$/;
                  let isValidDate = moment(dateOfBirth).isValid();
                  if (!isValidDate) {
                    invalidDate = true;
                    break;
                  }
                  const splits = ['/', '-', ' '];
                  let targetSplit: string | undefined = undefined;
                  for (const split of splits) {
                    if (dateOfBirth.indexOf(split) >= 0) {
                      targetSplit = split;
                      break;
                    }
                  }
                  if (
                    targetSplit &&
                    this.checkDateChanged(dateOfBirth, targetSplit)
                  ) {
                    invalidDate = true;
                    break;
                  }
                  /**
                   * NOTE:
                   * 1.) DD/MM/YYYY, DD-MM-YYYY, YYYY/DD/MM & YYYY-DD-MM are not valid date formats by default in momentjs
                   * 2.) If there are new date formats to be added, change the indexes in the 'if conditions' accordingly
                   */

                  for (const tempidx in dateFormats) {
                    const idx = parseInt(tempidx);
                    const format = dateFormats[idx];
                    isValidDate = moment(dateOfBirth, format, true).isValid();

                    if (isValidDate === true) {
                      if (idx === 0 || idx === 4 || idx === 8 || idx === 12) {
                        // MM/DD/YYYY or MMM/DD/YYYY or MMMM/DD/YYYY or YYYY/MM/DD
                        let yearIdx: number = idx === 12 ? 0 : 2;
                        const slashedDateData = dateOfBirth.split('/');
                        if (slashedDateData.length === 3) {
                          // check if the 'year' is 4 digits since momentjs accepts any number of digits
                          // ex. 1997 = 97. This is still acceptable for momentjs.
                          // then target the index for the 'year' data from .split()
                          if (
                            fourDigitYearRegEx.test(
                              slashedDateData[yearIdx],
                            ) === true
                          ) {
                            validDateCounter++;
                          }
                        }
                      } else if (
                        idx === 1 ||
                        idx === 5 ||
                        idx === 9 ||
                        idx === 13
                      ) {
                        // MM-DD-YYYY or MMM-DD-YYYY or MMMM-DD-YYYY or YYYY-MM-DD
                        let yearIdx: number = idx === 13 ? 0 : 2;
                        const dashedDateData = dateOfBirth.split('-');
                        if (dashedDateData.length === 3) {
                          if (
                            fourDigitYearRegEx.test(dashedDateData[yearIdx]) ===
                            true
                          ) {
                            validDateCounter++;
                          }
                        }
                      } else if (
                        idx === 2 ||
                        idx === 6 ||
                        idx === 10 ||
                        idx === 14
                      ) {
                        // MM DD YYYY or MMM DD YYYY or MMMM DD YYYY or YYYY MM DD
                        let yearIdx: number = idx === 14 ? 0 : 2;
                        const spacedDateData = dateOfBirth.split(' ');
                        if (spacedDateData.length === 3) {
                          if (
                            fourDigitYearRegEx.test(spacedDateData[yearIdx]) ===
                            true
                          ) {
                            validDateCounter++;
                          }
                        }
                      } else if (
                        idx === 3 ||
                        idx === 7 ||
                        idx === 11 ||
                        idx === 15
                      ) {
                        // MM.DD.YYYY or MMM.DD.YYYY or MMMM.DD.YYYY or YYYY.MM.DD
                        let yearIdx: number = idx === 15 ? 0 : 2;
                        const dottedDateData = dateOfBirth.split('.');
                        if (dottedDateData.length === 3) {
                          if (
                            fourDigitYearRegEx.test(dottedDateData[yearIdx]) ===
                            true
                          ) {
                            validDateCounter++;
                          }
                        }
                      }
                    }
                  }
                  if (validDateCounter === 0) {
                    birthdateFormatError = true;
                  } else {
                    //non standard format affects date of birth comparison in the backend. mongo db date format is YYYY-MM-DD
                    row[col_data['sheet_field']] = moment(dateOfBirth).format(
                      'YYYY-MM-DD',
                    );
                  }
                  break;
                }
              }
            }

            if (birthdateFormatError || invalidDate) {
              break;
            }
          }

          if (birthdateFormatError || invalidDate) {
            let title = birthdateFormatError
              ? 'Invalid date format'
              : 'Invalid Date';
            let message = birthdateFormatError
              ? 'Invalid format for date of birth. Please follow the MM/DD/YYYY or YYYY/MM/DD format.'
              : 'Invalid date entered for date of birth. Please check the source file.';
            this.setState({
              isOpenModal: true,
              modalTitle: title,
              modalMessage: message,
            });
            return;
          }

          this.openModal();
        }
      } else if (
        ['manual', 'client-manual'].includes(this.state.encodePageType)
      ) {
        if (newActiveStep === 1) {
          const { uploaded_data } = this.state;

          if (
            uploaded_data &&
            uploaded_data['rows'] &&
            uploaded_data['rows'].length > 0
          ) {
            this.openModal();
          } else {
            this.setState({
              isOpenModal: true,
              modalTitle: 'No Encoded Member',
              modalMessage: 'Please encode a member before proceeding.',
            });
          }
        }
      } else if (this.state.encodePageType === 'void') {
        if (newActiveStep === 1) {
          const { uploaded_data } = this.state;

          if (
            uploaded_data &&
            uploaded_data['rows'] &&
            uploaded_data['rows'].length > 0
          ) {
            this.openModal();
          } else {
            this.setState({
              isOpenModal: true,
              modalTitle: 'No Uploaded Void Masterlist',
              modalMessage:
                'Please upload a void masterlist before proceeding.',
            });
          }
        }
      } else if (this.state.encodePageType === 'terminate') {
        if (newActiveStep === 1) {
          const { uploaded_data } = this.state;

          if (
            uploaded_data &&
            uploaded_data['rows'] &&
            uploaded_data['rows'].length > 0
          ) {
            this.openModal();
          } else {
            this.setState({
              isOpenModal: true,
              modalTitle: 'No Uploaded Termination',
              modalMessage:
                'Please upload a file for termination before proceeding.',
            });
          }
        }
      }
    }
  }

  checkDateChanged(date: string, split: string) {
    const convertedDate = moment(date);
    let splitDate = date.split(split);
    //Check if Day Changed
    let dayNum = convertedDate.date();
    let dayFound = false;
    const dayString = dayNum.toString();
    let dayIndex = splitDate.findIndex(day => day === dayString);
    if (dayIndex >= 0) {
      dayFound = true;
      splitDate.splice(dayIndex, 1);
    } else if (dayNum < 10) {
      dayIndex = splitDate.findIndex(day => day === `0${dayString}`);
      if (dayIndex >= 0) {
        dayFound = true;
        splitDate.splice(dayIndex, 1);
      }
    }
    if (!dayFound) {
      return true;
    }
    //Check if Month Changed
    let monthFound = false;
    const monthFormats = ['M', 'MM', 'MMM', 'MMMM'];
    for (const format of monthFormats) {
      let monthToFind = convertedDate.format(format);
      if (splitDate.includes(monthToFind)) {
        monthFound = true;
      }
    }
    if (!monthFound) {
      return true;
    }
    return false;
  }

  handleBackButton() {
    const { activeStep } = this.state;
    if (activeStep - 1 === 0) {
      this.setState({
        pageHeader: 'Encode Batch Details',
        activeStep: activeStep - 1,
      });
    }
  }

  /******** function that handles next button *******/
  getErrorMessage(attr: string) {
    let message: string = '';
    switch (attr) {
      case 'account':
        message = 'Corporate account is required';
        break;
      case 'contract':
        message = 'Contract is required';
        break;
      case 'sent_through_channel':
        message = 'Sent through channel is required';
        break;
      case 'batch_name':
        message = 'Batch name is required';
        break;
      case 'sender_name':
        message = 'Sender name is required';
        break;
      case 'date_sent':
        message = 'Date sent is required';
        break;
      case 'number_of_members':
        message = 'Number of members is required';
        break;
      case 'sender_email':
        message = 'Sender email is required';
        break;
    }
    return message;
  }
 validateEmail(value: string): boolean {
  // Must match basic email format and TLD with at least 2 characters
  const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/;

  if (!emailRegex.test(value)) {
    return true; // Invalid email
  }
  if (value.includes(' ') || value.includes('..')) {
    return true; // Invalid due to space or double dot
  }
  return false; // Email is valid
}

  // 5685
  handleUpdateDetailsChannelAndName(fieldName: string, value: any) {
    if (fieldName === 'sender_name') {
      this.state.details_data['sender_name'] = value;
    } else {
      this.state.details_data.sent_through_channel = value;
    }
  }

  handleUpdateDetails(item: any, attr: String) {
    let error_cnt = 0;
    this.setState(
      {
        [attr + '_error']: false,
        [attr + '_error_message']: '',
        details_data: item,
        disabledNext: false,
      } as any,

      () => {
        if (attr === 'account') {
          this.setState({ corporate_account: item['account']['label'] });
          this.setState({
            contract_options: item['contract'],
            contracts_data: item['contract'],
          });
          if (item && item['contract']['client_contract_id']) {
            const batch_items = this.getBatchNames(
              item.contract.value,
              item.account.value,
              this.state.contracts_data,
            );
            this.setState({
              batchnames: batch_items,
            });

            // console.log('change contract name13', this.state.batchnames)
          } else {
            const batch_items = this.getBatchNames(
              this.state.details_data.contract['client_contract_id'],
              this.state.details_data.contract['client_id'],
              this.state.details_data.contract,
            );
            // console.log('change contract name555', item, batch_items, this.state.batchnames)
            this.setState({
              batchnames: batch_items,
            });
          }
        } else if (attr === 'contract') {
          if (item.contract && item.contract.id) {
            const batch_items = this.getBatchNames(
              item.contract.value,
              item.account.value,
              this.state.contracts_data,
            );
            // console.log('change contract name7', item, attr, batch_items, this.state)
            this.setState({
              batchnames: batch_items,
            });
          }
        } else if (attr === 'sender_email') {
          if (
            (item && item.sender_email === '') ||
            !item.sender_email ||
            item.sender_email === null
          ) {
            this.setState({
              [item.sender_email + '_error']: true,
              [item.sender_email + '_error_message']: '_error_message',
            } as any);

            error_cnt++;
          } else {
            const email_validation = this.validateEmail(item.sender_email);

            if (email_validation) {
              this.setState({
                sender_email_error: true,
                sender_email_error_message: 'Wrong format',
              });

              error_cnt++;
            }
          }
        }
      },
    );
  }

  handleClosenModalProps = () => {
    this.setState({
      isExpiredClientModalOpen: false,
      modalTitle: '',
      modalMessage: '',
    });
  };

  getSaveForNowData() {
    this.showHideLoader(true);

    if (_.isEmpty(this.props.match.params.ticket_id)) {
      this.showHideLoader(false);

      return;
    }
    const { isProcessing } = this.state;
       const userId = this.getQueryParam('isAutoRenew') === 'false' ? 'renew-members' :'encode-member';


    this.setState({
      check_internet_flag: true,
    });
    API.getSaveForNowData(userId, this.props.match.params.ticket_id)
      .then(async response => {
        if (response !== undefined && response.length > 0) {
          let {
            save_for_now_id,
            details_data,
            batchnames,
            uploadMemberData,
            raw_uploaded_data,
            filtered_raw_data,
            selected_data_map,
            rows_start_at,
            uploaded_data,
            columns_data,
            gender_data,
            civil_status_data,
            plan_type_data,
            site_data,
            type_data,
            relationship_data,
            vip_data,
            ph_rider_data,
            member_consent_data,
            principals_options,
            clients_data,
            system_names,
          } = this.state;

          const { file_url, _id, data: saveForNowData } = response[0];

          if (_id) {
            let sbnData: any = saveForNowData.batch_name;
            let newsbnData: any = {};
            for (var key in sbnData) {
              if (sbnData.hasOwnProperty(key)) {
                newsbnData[key] = sbnData[key].toString();
              }
            }

            let batchNameData =
              newsbnData &&
              Object.keys(newsbnData).length > 0 &&
              newsbnData['value'] &&
              newsbnData['value'].trim() !== '' &&
              newsbnData['value'] !== null
                ? newsbnData
                : details_data['batch_name'];
            if (
              this.props.match.params.client_id &&
              clients_data &&
              Array.isArray(clients_data)
            ) {
              const chkClients = clients_data.find(
                item => item._id === this.props.match.params.client_id,
              );
              if (chkClients) {
                details_data['account'] = {
                  id: chkClients._id,
                  value: chkClients._id,
                  label: chkClients.registered_name,
                };
                if (chkClients.member_data_fields) {
                  system_names = chkClients.member_data_fields;
                }
              }
            }
            details_data = {
              ...details_data,
              sender_name: saveForNowData.sender_name,
              sender_email: saveForNowData.sender_email,
              date_sent: saveForNowData.date_sent,
              sent_through_channel: saveForNowData.sent_through_channel,
              batch_name: batchNameData,
              ticket_id: saveForNowData.ticket_id,
              number_of_members: saveForNowData.number_of_members
                ? saveForNowData.number_of_members
                : saveForNowData.no_of_members
                ? saveForNowData.no_of_members
                : saveForNowData.sent_through_channel &&
                  saveForNowData.sent_through_channel.value ==
                    'Client Portal' &&
                  saveForNowData['uploaded_data'] &&
                  saveForNowData['uploaded_data']['rows']
                ? saveForNowData['uploaded_data']['rows'].length
                : '',
              isOcp:
                saveForNowData.sent_through_channel &&
                saveForNowData.sent_through_channel.value == 'Client Portal'
                  ? true
                  : false,
              default_data_map_save_for_now:
                saveForNowData.default_data_map_save_for_now,
              data_map_save_for_now: saveForNowData.data_map_save_for_now,
            };
            if (file_url) {
              details_data = {
                ...details_data,
                file_url: file_url,
              };
            }

            let batchNamesArr = saveForNowData.batchnames
              .map(batchName => {
                let newBatchName: any = {};
                for (var key in batchName) {
                  if (batchName.hasOwnProperty(key)) {
                    newBatchName[key] = batchName[key].toString();
                  }
                }

                if (
                  newBatchName &&
                  Object.keys(newBatchName).length > 0 &&
                  newBatchName['value'] &&
                  newBatchName['value'].trim() !== '' &&
                  newBatchName['value'] !== null
                ) {
                  return newBatchName;
                } else {
                  return null;
                }
              })
              .filter(currData => currData !== null);

            if (
              batchNameData['value'] &&
              batchNameData['value'].trim() ===
                details_data['batch_name']['value'].trim() &&
              batchNamesArr
                .map(bName => {
                  return bName.value.trim();
                })
                .indexOf(batchNameData['value'].trim()) === -1
            ) {
              batchNamesArr.unshift(batchNameData);
            }

            save_for_now_id = _id;
            batchnames = batchNamesArr.length > 0 ? batchNamesArr : batchnames;
            raw_uploaded_data = saveForNowData.raw_uploaded_data;
            uploadMemberData = saveForNowData.uploadMemberData;
            filtered_raw_data = saveForNowData.filtered_raw_data;
            selected_data_map = saveForNowData.selected_data_map;
            rows_start_at = saveForNowData.rows_start_at;
            uploaded_data = saveForNowData.uploaded_data;
            columns_data = saveForNowData.columns_data;
            gender_data = saveForNowData.gender_data;
            civil_status_data = saveForNowData.civil_status_data;
            plan_type_data = saveForNowData.plan_type_data;
            site_data = saveForNowData.site_data;
            type_data = saveForNowData.type_data;
            relationship_data = saveForNowData.relationship_data;
            vip_data = saveForNowData.vip_data;
            ph_rider_data = saveForNowData.ph_rider_data;
            member_consent_data = saveForNowData.member_consent_data;
            principals_options = saveForNowData.principals_options;
            // default_data_map_save_for_now = saveForNowData.default_data_map_save_for_now;
            // data_map_save_for_now = saveForNowData.data_map_save_for_now;
          }

          //get all manual renewal details that has values
           const manualRenewalDetails = Object.fromEntries(
            Object.entries({
              renew_members: saveForNowData?.renew_members,
              renew_selected: saveForNowData?.renew_selected,
              renew_selection: saveForNowData?.renew_selection,
              exclude_members: saveForNowData?.exclude_members,
              exclude_selected: saveForNowData?.exclude_selected,
              exclude_selection: saveForNowData?.exclude_selection,
              renewalType: saveForNowData?.renewalType,
            }).filter(([_, value]) => value !== undefined)
          );

          this.setState({
            ...this.state,
            save_for_now_id,
            details_data,
            batchnames,
            raw_uploaded_data,
            filtered_raw_data,
            uploadMemberData,
            selected_data_map,
            rows_start_at,
            uploaded_data,
            columns_data,
            gender_data,
            civil_status_data,
            plan_type_data,
            site_data,
            type_data,
            relationship_data,
            vip_data,
            ph_rider_data,
            member_consent_data,
            principals_options,
            system_names,
            activeStep: 0,
            pageHeader: 'Encode Batch Details',
            manualRenewalDetails,
            // default_data_map_save_for_now,
            // data_map_save_for_now
          });

          // get ticket details handling for ocp
          let ticketIDb =
            this.state && this.state.ticketId ? this.state.ticketId : '';
          let ticketIDa =
            details_data && details_data.ticket_id
              ? details_data.ticket_id
              : ticketIDb;
          //details_data.ticket_id
          await API.getTicketData(ticketIDa, true)
            .then(resp => {
              this.setState({
                ...this.state,
                ticket_details: resp,
              });
            })
            .catch(err => {
              console.log(err);
            });
          await this.getClientDetails();
          if (isProcessing) await this.getUploadOptions(true);
          this.setState({
            loading_state: false,
            check_internet_flag: false,
          });
        } else {
          this.setState({
            loading_state: false,
          });
        }
      })
      .catch(() => {
        this.setState({
          loading_state: false,
        });
      });
  }

  doSaveForNow(onSuccess?: any, onFailure?: any, isSaveForNow?: boolean) {
    console.log('doSaveForNow()');
    const userId = this.getQueryParam('isAutoRenew') === 'false' ? 'renew-members' :'encode-member';
    const {
      save_for_now_id,
      ticketId,
      details_data: {
        sender_name,
        sender_email,
        date_sent,
        sent_through_channel,
        ticket_id,
        file_url,
        isOcp,
        batch_name,
        number_of_members,
        default_data_map_save_for_now,
        data_map_save_for_now,
      },
      batchnames,
      uploadMemberData,
      raw_uploaded_data,
      filtered_raw_data,
      selected_data_map,
      rows_start_at,
      uploaded_data,
      columns_data,
      gender_data,
      civil_status_data,
      plan_type_data,
      site_data,
      type_data,
      relationship_data,
      vip_data,
      ph_rider_data,
      member_consent_data,
      principals_options,
      trueTicketId,
      encodePageType,
      isSaveForNowMemberList,
      renewalType
    } = this.state;

    let data = {
      sender_name,
      sender_email,
      date_sent,
      sent_through_channel,
      batchnames,
      batch_name,
      ticket_id,
      file_url,
      isOcp,
      number_of_members,
      uploadMemberData,
      raw_uploaded_data,
      filtered_raw_data,
      selected_data_map,
      rows_start_at,
      uploaded_data,
      columns_data,
      gender_data,
      civil_status_data,
      plan_type_data,
      site_data,
      type_data,
      relationship_data,
      vip_data,
      ph_rider_data,
      member_consent_data,
      principals_options,
      default_data_map_save_for_now,
      data_map_save_for_now,
      renewalType
    };

    if (data.batch_name['value'] === '') {
      data.batch_name = {
        id: trueTicketId.toString(),
        value: trueTicketId.toString(),
        label: trueTicketId.toString(),
      };
    }

    this.setState({
      check_internet_flag: true,
    });

    if (save_for_now_id && save_for_now_id !== '') {
      console.log('second void savefornow2', this.state, save_for_now_id);

      if (Object.keys(this.state.manualRenewalDetails || {}).length > 0) {
        data = {
        ...data,
        ...this.state.manualRenewalDetails
      }
    }

      API.patchSaveForNow(save_for_now_id, data)
        .then(() => {
           //#region routing case
          // Check if the renewalType is uploadRenewal and if the ticketVariables are defined
           if (
            this.state?.renewalType === 'uploadRenewal' // check if renewalType is uploadRenewal
            && this.state.ticketVariables  // check if ticketVariables is defined
            && this.state.ticketVariables.tas_title === 'Claimed Renew Memberlist' // check if tas_title is 'Claimed Renew Memberlist'
            && this.state.ticketVariables.renew_members === 0 // check if renew_members is 0 means for renewal (uploadRenewal or manualRenewal)
            && this.state.activeStep === 1 // check if activeStep is 1 mean for upload page
            ) {
            Processmaker.put('cases/' + ticketId + '/route-case', {})
              .then(() => {
                console.log('case routed');
              })
              .catch(error => {
                console.error('Error routing case:', error);
                // Add error handling logic here
              });

          }
          //#endregion for routeCase for renewal
          if (onSuccess) {
            this.setState({
              check_internet_flag: false,
            });
            onSuccess();

            if (isSaveForNow === undefined) {
              if (
                encodePageType === 'void' ||
                encodePageType === 'Void Masterlist'
              ) {
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';
                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = data.batch_name['id'];
                    API.activityVoidUploadSaveForNow(
                      batchId,
                      data.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });
              } else {
                console.log('not void patchSaveForNow()');
              }
            }
          }
        })
        .catch(e => {
          const errorDetails = `Error: ${JSON.stringify(e)}`;
          if (onFailure) {
            onFailure(errorDetails);
          }
        });
    } else {
      // const processName =
      //   encodePageType === 'void' ? 'Void Masterlist' : 'Encode Members';
      let processName = '';

      if (encodePageType === 'void') {
        processName = 'Void Masterlist';
      } else {
        processName = 'Encode Members';
      }

      if (this.state?.renewalType === 'uploadRenewal') {
        processName = 'Upload Renewal';
      }
      console.log('first savefornow', processName, data?.renewalType);
      API.saveForNow(userId, ticketId, processName, data)
        .then(response => {
          //#region routing case
          // Check if the renewalType is uploadRenewal and if the ticketVariables are defined
           if (
            this.state?.renewalType === 'uploadRenewal' // check if renewalType is uploadRenewal
            && this.state.ticketVariables  // check if ticketVariables is defined
            && this.state.ticketVariables.tas_title === 'Claimed Renew Memberlist' // check if tas_title is 'Claimed Renew Memberlist'
            && this.state.ticketVariables.renew_members === 0 // check if renew_members is 0 means for renewal (uploadRenewal or manualRenewal)
            && this.state.activeStep === 1 // check if activeStep is 1 mean for upload page
            ) {
            Processmaker.put('cases/' + ticketId + '/route-case', {})
              .then(() => {
                console.log('case routed');
              })
              .catch(error => {
                console.error('Error routing case:', error);
                // Add error handling logic here
              });

          }
          //#endregion for routeCase for renewal
          if (response !== undefined && response.error === undefined) {
            this.setState(
              {
                check_internet_flag: false,
                save_for_now_id: response._id,
              },
              () => {
                if (onSuccess) {
                  onSuccess();
                }
              },
            );
            if (
              isSaveForNow === undefined &&
              this.state.isSaveForNow !== false &&
              this.state.isSaveForNowMemberList !== false
            ) {
              if (processName === 'Void Masterlist') {
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';

                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = data.batch_name['id'];
                    API.activityVoidUploadSaveForNow(
                      batchId,
                      data.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });
              } else if (processName === 'Encode Members') {
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';
                  console.log(user_name);
                  //console.log('Utils', result)
                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = data.batch_name['id'];
                    API.activityUploadSaveForNow(
                      batchId,
                      data.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });
              }
            } else if (isSaveForNow === false) {
           
              if (this.state.pageHeader === 'Upload Member File') {
                // console.log('do you use this?1', this.state, isSaveForNow)
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';
                  console.log(user_name);
                  //console.log('Utils', result)
                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = data.batch_name['id'];
                    API.activityCreateAddMemberTicket(
                      batchId,
                      data.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });
              } else if (this.state.encodePageType === 'manual') {
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';
                  console.log(user_name);
                  //console.log('Utils', result)
                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = data.batch_name['id'];
                    API.activityCreateManualEncodeTicket(
                      batchId,
                      data.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });
              }
            } else {
              // console.log('create savefornow for add member', this.state)
              if (this.state.encodePageType === 'manual') {
                Utils.StorageService('user_data').then(result => {
                  let user_name: string = '';
                  console.log(user_name);
                  //console.log('Utils', result)
                  if (result && result !== undefined) {
                    for (const i in result) {
                      if (result[i]['key'] === 'username') {
                        user_name = result[i]['value'];
                      }
                    }
                    let batchId = this.state.trueTicketId;

                    API.activityEnrollManualSaveForNow(
                      batchId,
                      this.state.uploadMemberData,
                      user_name,
                    )
                      .then(response => {
                        if (response === undefined) {
                          console.log('response', response);
                        }
                      })
                      .catch(e => {
                        console.log('terminate error', e);
                      });
                  }
                });

                // if (save_for_now_id && save_for_now_id !== '') {

                // }
              }
            }
          } else {
            const errorDetails = `Error: ${JSON.stringify(response.error)}`;
            if (onFailure) {
              onFailure(errorDetails);
            }
          }
        })
        .catch(e => {
          const errorDetails = `Error: ${JSON.stringify(e)}`;
          if (onFailure) {
            onFailure(errorDetails);
          }
        });
    }
  }

  handleSaveForNow() {
    console.log('first savefornow()', this.state);
    const onSuccess = () => {
      this.setState({
        loading_state: false,
        isOpenModal: true,
        modalTitle: 'Save for now',
        modalMessage: 'Details are saved successfully.',
      });
    };

    const onFailure = (errorDetails: any) => {
      this.setState({
        loading_state: false,
        isOpenModal: true,
        modalTitle: 'Error saving information',
        modalMessage: errorDetails,
      });
    };

    //isAddMembersTicket
    if (
      this.state.isAddMembersTicket === true &&
      this.state.encodePageType === 'default'
    ) {
      console.log('first save for now', this.state);
      if (
        this.state.save_for_now_id === '' ||
        this.state.save_for_now_id === undefined
      ) {
        Utils.StorageService('user_data').then(result => {
          let user_name: string = '';
          console.log(user_name);
          //console.log('Utils', result)
          if (result && result !== undefined) {
            for (const i in result) {
              if (result[i]['key'] === 'username') {
                user_name = result[i]['value'];
              }
            }
            //
            let batchId = this.state.trueTicketId;
            API.activityCreateAddMemberTicket(
              batchId,
              this.state.uploadMemberData,
              user_name,
            )
              .then(response => {
                if (response === undefined) {
                  console.log('response', response);
                }
              })
              .catch(e => {
                console.log('terminate error', e);
              });
          }
        });
      }

      Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
        console.log(user_name);
        //console.log('Utils', result)
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
          let batchId = this.state.trueTicketId;

          API.activityUploadSaveForNow(
            batchId,
            this.state.uploadMemberData,
            user_name,
          )
            .then(response => {
              if (response === undefined) {
                console.log('response', response);
              }
            })
            .catch(e => {
              console.log('terminate error', e);
            });
        }
      });
    } else if (
      this.state.ticket_details['ticket_type'] === 'Enroll Memberlist'
    ) {
      // ENROLL MEMBERLIST1
      Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
        console.log(user_name);
        //console.log('Utils', result)
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
          let batchId = this.state.trueTicketId;
          API.activityUploadEnrollMemberlistSaveForNow(
            batchId,
            this.state.uploadMemberData,
            user_name,
          )
            .then(response => {
              if (response === undefined) {
                console.log('response', response);
              }
            })
            .catch(e => {
              console.log('terminate error', e);
            });
        }
      });
    } else if (this.state.encodePageType === 'manual') {
      console.log('manual');
      if (this.state.save_for_now_id && this.state.save_for_now_id !== '') {
        Utils.StorageService('user_data').then(result => {
          let user_name: string = '';
          console.log(user_name);
          //console.log('Utils', result)
          if (result && result !== undefined) {
            for (const i in result) {
              if (result[i]['key'] === 'username') {
                user_name = result[i]['value'];
              }
            }
            let batchId = this.state.trueTicketId;
            API.activityEnrollManualSaveForNow(
              batchId,
              this.state.uploadMemberData,
              user_name,
            )
              .then(response => {
                if (response === undefined) {
                  console.log('response', response);
                }
              })
              .catch(e => {
                console.log('terminate error', e);
              });
          }
        });
      } else {
        Utils.StorageService('user_data').then(result => {
          let user_name: string = '';
          console.log(user_name);
          //console.log('Utils', result)
          if (result && result !== undefined) {
            for (const i in result) {
              if (result[i]['key'] === 'username') {
                user_name = result[i]['value'];
              }
            }
            let batchId = this.state.trueTicketId;
            API.activityCreateManualEncodeTicket(
              batchId,
              this.state.uploadMemberData,
              user_name,
            )
              .then(response => {
                if (response === undefined) {
                  console.log('response', response);
                }
                // console.log('activityCreateManualEncodeTicket for saveForNow', response)
              })
              .catch(e => {
                console.log('terminate error', e);
              });
          }
        });
      }
    } else {
      //ELSE ENROLL MEMBERLIST2
      Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
        console.log(user_name);
        //console.log('Utils', result)
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
          let batchId = this.state.trueTicketId;
          API.activityUploadEnrollMemberlistSaveForNow(
            batchId,
            this.state.uploadMemberData,
            user_name,
          )
            .then(response => {
              if (response === undefined) {
                console.log('response', response);
              }
            })
            .catch(e => {
              console.log('terminate error', e);
            });
        }
      });
    }

    // } else {
    //   console.log('not first savefornow')
    // }

    this.showHideLoader(true);
    this.doSaveForNow(onSuccess, onFailure);
  }

  handleSaveForNowOnSubmit(isSaveForNow?: boolean) {
    // console.log('this???3333', this.state)
    const onSuccess = () => {
      this.setState(
        {
          loading_state: false,
        },
        () => {
          this.handleSubmit();
        },
      );
    };

    const onFailure = (errorDetails: any) => {
      this.setState({
        loading_state: false,
        isOpenModal: true,
        modalTitle: 'Error saving information',
        modalMessage: errorDetails,
      });
    };

    this.showHideLoader(true);
    this.setState({
      isSaveForNow: false,
    });
    this.doSaveForNow(onSuccess, onFailure, isSaveForNow);
  }

  onSaveForNowClicked() {
    const {
      activeStep,
      details_data,
      ticketId,
      isAddMembersTicket,
      isFromEncodeMembersTicket,
    } = this.state;
    const nextProcess = () => {
      if (!ticketId || ticketId === null || ticketId === undefined) {
        if (this.state.encodePageType === 'void') {
          this.createUploadVoidMasterlistTicket(true);
        } else {
          this.createAddMemberTicket(true);
        }
      } else {
        const ticketFromPath = this.props.match.params.ticket_id;
        if (
          isAddMembersTicket &&
          isFromEncodeMembersTicket &&
          ticketId === ticketFromPath
        ) {
          this.createAddMemberTicket(true);
        } else {
          this.setTicketVariable(ticketId);
          this.handleSaveForNow();
        }
      }
    };

    if (this.state.encodePageType === 'default' && activeStep === 0) {
      let noClientError = false;
      let emptyFieldCounter = 0;
      let maxFieldCount = 6;

      for (var i in details_data) {
        switch (i) {
          case 'account':
            if (
              details_data[i]['value'] === '' ||
              details_data[i]['value'] === null ||
              details_data[i]['value'] === 0
            ) {
              noClientError = true;
            }
            break;
          case 'contract':
          case 'sent_through_channel':
          case 'batch_name':
            if (
              details_data[i]['value'] === '' ||
              details_data[i]['value'] === null ||
              details_data[i]['value'] === 0
            ) {
              emptyFieldCounter++;
            }
            break;
          case 'sender_name':
          case 'sender_email':
          case 'number_of_members':
            if (
              details_data[i] === '' ||
              details_data[i] === null ||
              details_data[i] === 0
            ) {
              emptyFieldCounter++;
            }
            break;
        }
      }

      if (noClientError === true) {
        this.setState({
          isOpenModal: true,
          modalTitle: 'No Client Selected',
          modalMessage: 'Please select a client first.',
        });
      } else if (emptyFieldCounter === maxFieldCount) {
        this.setState({
          isOpenModal: true,
          modalTitle: 'No Batch Details',
          modalMessage: 'Please fill out some batch details first.',
        });
      }

      if (noClientError === false && emptyFieldCounter < maxFieldCount) {
        nextProcess();
      }
    } else {
      nextProcess();
    }
  }

  createAddMemberTicket(isSaveForNow: boolean) {
    this.showHideLoader(true);

    let t_uid = '5098598465df6225498bc87032787138';

    if (['manual', 'client-manual'].includes(this.state.encodePageType)) {
      t_uid = '2119930415ed0ef9d320f75021575895';
    }
    Processmaker.post('cases/', {
      pro_uid: '5310535875df6222bae2930014295490',
      tas_uid: t_uid,
    })
      .then(response => {
        this.showHideLoader(false);
        if (response.app_uid !== undefined) {
          const ticketId = response.app_uid;
          this.setState(
            {
              ticketId: ticketId,
              trueTicketId: response.app_number,
            },
            () => {
              this.executeTicketTrigger(ticketId);
              this.setTicketVariable(ticketId, response);
              if (isSaveForNow === true) {
                this.handleSaveForNow();
              } else {
                this.handleSaveForNowOnSubmit(false);
              }
            },
          );
        }
      })
      .catch((e: Error) => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            loading_state: false,
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.showHideLoader(false);
        }
      });
  }

  createUploadVoidMasterlistTicket(isSaveForNow: boolean) {
    this.showHideLoader(true);
    Processmaker.post('cases/', {
      pro_uid: '813402549637c73dfa72cf2058392000',
      tas_uid: '612682980637c743056fa42023148514',
    })
      .then(response => {
        this.showHideLoader(false);
        if (response.app_uid !== undefined) {
          const ticketId = response.app_uid;
          this.setState(
            {
              ticketId: ticketId,
              trueTicketId: response.app_number,
            },
            () => {
              this.executeTicketTrigger(ticketId);
              this.setVoidTicketVariable(ticketId, response);
              if (isSaveForNow) {
                this.handleSaveForNow();
              } else {
                this.handleSaveForNowOnSubmit(isSaveForNow);
              }
            },
          );
        }
      })
      .catch((e: Error) => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            loading_state: false,
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.showHideLoader(false);
        }
      });
  }

  executeTicketTrigger(appUid: string) {
    Processmaker.put(
      'cases/' + appUid + '/execute-trigger/6066708415df623339fa4f4056468668',
      {},
    );
  }

  setTicketVariable(appUid: string, pmaker_response?: any) {
    const {
      details_data: { account, contract },
      memberUploadId,
      encodePageType,
    } = this.state;
    Processmaker.put(
      'cases/' + appUid + '/variable',
      {
        client_id: account.value || '',
        client_name: account.label || '',
        mother_contract_id: contract.id || '',
        from_membership: true,
        member_upload_id: memberUploadId,
        page_type: encodePageType,
      },
      true,
    )
      .then(() => {
        if (pmaker_response) {
          // console.log('ASSIGN TICKET TYPE1', this.state)
          let ticket_type = 'Add Member';
          let isEnrollMemberList;
          if (['manual', 'client-manual'].includes(this.state.encodePageType)) {
            ticket_type = 'Manual Encode';
          }
          console.log(isEnrollMemberList);
          let payload = {
            status: 'OPEN',
            ticket_id: pmaker_response.app_number.toString(),
            pmaker_case_uid: appUid,
            ticket_type: ticket_type,
            isEnrollMemberList: true,
            client: account.label,
            client_id: account.value,
          };
          API.postTicket(payload, true)
            .then(() => {})
            .catch(() => {});
        } else {
          let payload = {
            client: account.label,
            client_id: account.value,
          };
          API.patchTicket(payload, appUid, true);
        }
      })
      .catch(e => {
        console.log('Set Ticket Variable Error: ', e);
      });
  }

  setVoidTicketVariable(appUid: string, pmaker_response?: any) {
    const {
      details_data: { account, contract },
      memberUploadId,
      memberVoidUploadID,
    } = this.state;
    // console.log('SET VOID TICKET VARIABLE4', details_data)
    Processmaker.put(
      'cases/' + appUid + '/variable',
      {
        client_id: account.value || '',
        client_name: account.label || '',
        mother_contract_id: contract.id || '',
        from_membership: true,
        member_void_upload_id:
          memberVoidUploadID !== null ? memberVoidUploadID : memberUploadId, //memberUploadId
        member_upload_id: memberUploadId,
      },
      true,
    )
      .then(() => {
        if (pmaker_response) {
          let ticket_type = 'Upload Void Masterlist';

          let payload = {
            status: 'OPEN',
            ticket_id: pmaker_response.app_number.toString(),
            pmaker_case_uid: appUid,
            ticket_type: ticket_type,
            client: account.label,
            client_id: account.value,
          };
          this.setState({
            ...this.state,
            void_ticket_id: pmaker_response.app_number.toString(),
          });
          // console.log('activityvoid7', ticket_type, payload, pmaker_response)
          if (ticket_type === 'Upload Void Masterlist') {
            Utils.StorageService('user_data').then(result => {
              let user_name: string = '';
              //console.log('Utils', result)
              if (result && result !== undefined) {
                for (const i in result) {
                  if (result[i]['key'] === 'username') {
                    user_name = result[i]['value'];
                  }
                }
                //post ticket API
                console.log('uploadVoid1', payload);
                API.activityVoidUploadStart(
                  payload['ticket_id'],
                  payload,
                  user_name,
                )
                  .then(response => {
                    if (response === undefined) {
                      console.log('response', response);
                    }
                    console.log('activityVoidUploadStart', response);
                  })
                  .catch(e => {
                    console.log('terminate error', e);
                  });
              } //here
              // console.log('activityvoid6', this.state)
            }); //here
          }
          //  console.log('upload voidstart?2', payload)
          API.postTicket(payload, true);
        } else {
          console.log('debug2', this.state, pmaker_response);
          let payload = {
            client: account.label,
            client_id: account.value,
          };

          // console.log('create ticket+next phase+patch ticket?', payload, pmaker_response)
          // console.log('activityvoid777', this.state, )
          API.patchTicket(payload, appUid, true);
        }
      })
      .catch(e => {
        console.log('Set Ticket Variable Error: ', e);
      });
  }

  deleteSaveForNow() {
    const { save_for_now_id } = this.state;

    if (save_for_now_id && save_for_now_id !== '') {
      this.setState({
        check_internet_flag: true,
      });
      API.deleteSaveForNow(save_for_now_id)
        .then(() => {
          this.setState({
            check_internet_flag: false,
          });
        })
        .catch(e => {
          console.log('deleteSaveForNow Error: ', e);
        });
    }
  }

  showHideLoader(flag: boolean) {
    this.setState({
      loading_state: flag,
    });
  }

  updateState(data: any, attr: string) {
    let callback = () => {};
    let stateToUpdate: any = {};
    if (
      [
        'gender_data',
        'civil_status_data',
        'plan_type_data',
        'type_data',
        'relationship_data',
        'vip_data',
        'ph_rider_data',
        'site_data',
        'member_consent_data',
        'suffix_data',
      ].includes(attr)
    ) {
      let attrName = attr.substring(0, attr.indexOf('_data'));
      if (data.sheet_field && data.sheet_field.trim() !== '') {
        attrName = data.sheet_field;
      }
      const data_map = this.state.data_mapping_values;
      data_map[attrName] = data;
      this.setState({
        data_mapping_values: {
          ...data_map,
        },
      });
      stateToUpdate['data_mapping_values'] = {
        ...data_map,
      };
      callback = () => {
        this.checkDisableSubmit();
      };
    } else if (
      ['selected_data_map', 'data_maps', 'filtered_raw_data'].includes(attr)
    ) {
      const default_values: any = {
        columns: [
          {
            name: 'values',
            title: 'Values',
          },
          {
            name: 'data_column',
            title: 'Data Column',
          },
        ],
        rows: [],
      };
      stateToUpdate = {
        gender_data: default_values,
        civil_status_data: default_values,
        plan_type_data: default_values,
        type_data: default_values,
        relationship_data: default_values,
        vip_data: default_values,
        ph_rider_data: default_values,
        member_consent_data: default_values,
        site_data: default_values,
        suffix_data: default_values,
        ...stateToUpdate,
      };
      callback = () => {
        this.setState({
          reload_table: true,
        });
      };
      // data = this.state[attr];
    }
    stateToUpdate[attr] = data;
    this.setState(stateToUpdate, callback);
  }

  checkDisableSubmit() {
    const data_check = [
      'gender_data',
      'civil_status_data',
      'plan_type_data',
      'type_data',
      'relationship_data',
      'vip_data',
      'ph_rider_data',
      'member_consent_data',
      'site_data',
      'suffix_data',
    ];

    const disable_submit: any[] = [];

    for (const param of data_check) {
      const data_option = this.state[param];
      if (data_option && data_option.rows) {
        const chk = data_option.rows.filter(i => i.data_column === '');
        if (chk.length > 0) {
          disable_submit.push({
            param,
            orig: data_option.rows,
            res: chk,
          });
        }
      }
    }
    this.handleDisableNext(disable_submit.length > 0);
  }

  updateUploadMemberData(key: string, value: any) {
    const uploadMemberData = {
      ...this.state.uploadMemberData,
      [key]: value,
    };

    this.setState({
      uploadMemberData,
    });
  }

  /***** function that handles saving of data map ******/
  savingNewDataMap(payload: any) {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });
    // payload['client_id'] = this.state.details_data['account'].value;
    // payload['client_id'] = this.props.match.params.client_id;
    // this.state.ticket_details.client_id //this.state.uploadMemberData.client_id;
    //this.state.ticket_details.client_id
    if (
      payload.client_id === undefined &&
      this.state.details_data.account &&
      this.state.details_data.account.id
    ) {
      payload.client_id = this.state.details_data.account.id;
    }
    payload['client_name'] = this.state.ticket_details.client;
    API.postNewDataMap(payload).then(response => {
      if (response && response.error === undefined) {
        const { clients_data_maps, client_maps } = this.state;
        let newColumnsData: any = {
          columns: [
            {
              name: 'system_name',
              title: 'System Name',
            },
            {
              name: 'sheet_name',
              title: 'Column Header',
              type: 'select',
              options: [
                {
                  id: '',
                  value: '',
                  label: '',
                },
              ],
            },
          ],
          rows: [],
        };

        const map: any = {
          id: response['_id'],
          value: response['_id'],
          label: response['name'],
          is_default: response['is_default'] ? response['is_default'] : false,
        };

        clients_data_maps.push(response);
        client_maps.push(map);
        this.setState({
          check_internet_flag: false,
          loading_state: false,
          clients_data_maps: clients_data_maps,
          client_maps: client_maps,
          columns_data: newColumnsData,
          selected_data_map: map,
          modalTitle: 'Success',
          modalMessage: 'New data map was successfully added.',
          isOpenModal: true,
          gender_data: {},
          civil_status_data: {},
          plan_type_data: {},
          type_data: {},
          relationship_data: {},
          vip_data: {},
          ph_rider_data: {},
          site_data: {},
          member_consent_data: {},
          suffix_data: {},
          next: () => {
            this.setState({
              reload_table: true,
            });
          },
        });
      } else {
        if (response && response.error && response.error.message) {
          this.setState({
            loading_state: false,
            modalTitle: 'Error',
            modalMessage: response.error.message,
            isOpenModal: true,
          });
        } else {
          this.setState({
            loading_state: false,
            modalTitle: 'Error',
            modalMessage: 'Failed to add new data map.',
            isOpenModal: true,
          });
        }
      }
    });
  }

  updatingSelectedDataMap(values_payload: any) {
    let selectedDataMapID =
      this.state.selected_data_map && this.state.selected_data_map.id
        ? this.state.selected_data_map.id.trim()
        : '';
    let selectedDataMap = null;
    if (selectedDataMapID) {
      selectedDataMap =
        this.state.client_maps && this.state.client_maps.length > 0
          ? this.state.client_maps[
              _.findIndex(this.state.client_maps, { id: selectedDataMapID })
            ]
          : null;
    }
    let column_data_rows =
      this.state.columns_data &&
      this.state.columns_data.rows &&
      this.state.columns_data.rows.length > 0
        ? this.state.columns_data.rows
        : [];
    if (selectedDataMap && column_data_rows) {
      this.setState({
        loading_state: true,
        check_internet_flag: true,
      });

      let payload: any = {};
      payload['client_id'] = this.state.details_data['account'].value;
      payload['name'] = selectedDataMap['label'];
      payload['is_default'] = selectedDataMap['is_default'];
      payload['mapping'] = [];
      payload['values'] = values_payload;
      for (const row_cnt in column_data_rows) {
        const row = column_data_rows[row_cnt];
        if (row['sheet_name'] !== '' && row['sheet_name'] !== undefined) {
          payload['mapping'].push({
            system_name: row['system_name'],
            sheet_name: row['sheet_name'],
          });
        }
      }
      API.patchDataMap(selectedDataMapID, payload).then(response => {
        const resErr = _.get(response, 'error', null);
        if (_.isNil(resErr)) {
          this.setState({
            modalTitle: 'Success',
            modalMessage: 'Data map was successfully updated.',
            isOpenModal: true,
            next: () => {
              this.setState({
                refresh_data_map: true,
              });
            },
          });
        } else {
          if (response && response.error && response.error.message) {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
              modalTitle: 'Error',
              modalMessage: response.error.message,
              isOpenModal: true,
            });
          } else {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
              modalTitle: 'Error',
              modalMessage: 'Failed to update data map.',
              isOpenModal: true,
            });
          }
        }
      });
    }
  }

  setShouldUpdateStatus(shouldUpdate: boolean) {
    localStorage.setItem(BG_TASK_STATUS, shouldUpdate ? 'true' : 'false');
  }

  getShouldUpdateStatus() {
    const shouldUpdate = localStorage.getItem(BG_TASK_STATUS);
    if (shouldUpdate !== null) {
      return shouldUpdate === 'true';
    }

    return false;
  }

  setBgTaskMuId(memberUploadId: string) {
    localStorage.setItem(BG_TASK_MEMBER_UPLOAD_ID, memberUploadId);
  }

  setBgTaskMuIdVoid(memberUploadId: string) {
    localStorage.setItem(BG_TASK_MEMBER_UPLOAD_ID, memberUploadId);
  }

  isEqualToSavedBgTaskMuId(memberUploadId: string): boolean {
    const savedBgTaskMuId = localStorage.getItem(BG_TASK_MEMBER_UPLOAD_ID);
    if (savedBgTaskMuId !== null) {
      return savedBgTaskMuId === memberUploadId;
    }

    return false;
  }

  setBgPollingTask(bgPollingTask: number) {
    // Clear previously saved task
    const storedTimeout = this.getBgPollingTask();
    window.clearTimeout(storedTimeout);

    // Save new task
    localStorage.setItem(BG_TASK_ID, `${bgPollingTask}`);
  }

  getBgPollingTask() {
    const bgPollingTask = localStorage.getItem(BG_TASK_ID);
    if (bgPollingTask !== null) {
      try {
        return parseInt(bgPollingTask);
      } catch (e) {
        console.log(
          `getBgPollingTask Error parsing timeout id: ${bgPollingTask}`,
        );
      }
    }

    return 0;
  }

  cancelStatusBgPolling() {
    const storedTimeout = this.getBgPollingTask();
    window.clearTimeout(storedTimeout);

    this.setShouldUpdateStatus(false);
    this.setBgTaskMuId('');
    this.setBgPollingTask(0);
  }

  resumeStatusBgPolling() {
    this.setShouldUpdateStatus(true);
    this.setBgTaskMuId(this.state.memberUploadId);

    const bgPollingTask = window.setTimeout(this.getMuStatusOnBg, 1000);
    this.setBgPollingTask(bgPollingTask);
  }

  getMuStatusOnBg() {
    this.setState({
      check_internet_flag: true,
    });

    if (this.state.encodePageType === 'void') {
      API.getVoidUploadStatus(this.state.memberUploadId).then(response => {
        if (
          response &&
          response.error === undefined &&
          this.getShouldUpdateStatus()
        ) {
          this.setState({
            check_internet_flag: false,
          });
          const { member_upload_id, active_index, status } = response;
          if (
            !_.isNil(member_upload_id) &&
            !this.isEqualToSavedBgTaskMuId(member_upload_id)
          ) {
            return;
          }

          // Check if processingStatus is null or if total_counts are similar
          if (
            _.isEmpty(this.state.processingStatus) ||
            this.state.processingStatus.active_index <= active_index
          ) {
            this.setState({
              processingStatus: response,
            });
          }
          if (['INITIAL', 'ONGOING'].includes(status)) {
            this.setBgTaskMuId(this.state.memberUploadId);
            // this.setBgTaskMuIdVoid(this.state.memberUploadId)
            const bgPollingTask = window.setTimeout(this.getMuStatusOnBg, 5000);
            this.setBgPollingTask(bgPollingTask);
          }
        }
      });
    } else {
      const nextClicked = () => {
        const { memberUploadId, ticketId, encodePageType } = this.state;
        this.setState({
          loading_state: true,
        });
        this.successfulConfirmEnrollment(
          memberUploadId,
          ticketId,
          ['manual', 'client-manual'].includes(encodePageType),
        );
      };
      API.getMemberUploadStatus(this.state.memberUploadId).then(response => {
        // Check if shouldUpdateStatus
        if (this.state.pageHeader === 'Upload Member File') {
          // console.log('here??3', this.state)
        }
        if (
          response &&
          response.error === undefined &&
          this.getShouldUpdateStatus()
        ) {
          this.setState({
            check_internet_flag: false,
          });
          const { member_upload_id, active_index, status } = response;
          if (
            !_.isNil(member_upload_id) &&
            !this.isEqualToSavedBgTaskMuId(member_upload_id)
          ) {
            return;
          }

          // Check if processingStatus is null or if total_counts are similar
          if (
            _.isEmpty(this.state.processingStatus) ||
            this.state.processingStatus.active_index <= active_index
          ) {
            this.setState({
              processingStatus: response,
            });
          }

          if (['UPLOADING', 'ONGOING', 'INITIAL'].includes(status)) {
            this.setBgTaskMuId(this.state.memberUploadId);

            const bgPollingTask = window.setTimeout(this.getMuStatusOnBg, 5000);
            this.setBgPollingTask(bgPollingTask);
          } else if (['SUCCESS'].includes(status)) {
            this.setState({
              loading_state: false,
              isOpenModal: true,
              modalTitle: 'Submission Successful',
              modalMessage: 'Member file was successfully uploaded.',
              next: nextClicked,
            });
          }
        }
      });
    }
  }

  getProcessingStatus() {
    if (
      _.isNil(this.state.memberUploadId) &&
      this.state.encodePageType !== 'void'
    ) {
      console.log('aaaa3');
      return;
    }
    this.setState(
      {
        loading_state: true,
        check_internet_flag: true,
      },
      () => {
        this.setShouldUpdateStatus(true);
        if (this.state.memberVoidUploadID !== null) {
          this.setBgTaskMuId(this.state.memberVoidUploadID);
        } else {
          this.setState({
            assignTicketType: 'Enroll Memberlist',
          });
          this.setBgTaskMuId(this.state.memberUploadId);
        }
      },
    );

    if (this.state.encodePageType === 'void') {
      let voidId = this.state.memberUploadId
        ? this.state.memberUploadId
        : this.state.memberVoidUploadID;
      API.getVoidUploadStatus(voidId)
        .then(response => {
          this.setState(
            {
              loading_state: false,
              check_internet_flag: false,
            },
            () => {
              // Check if shouldUpdateStatus
              if (
                response &&
                response.error === undefined &&
                this.getShouldUpdateStatus()
              ) {
                const { member_upload_id, active_index, status } = response;

                // Check if member_upload_id is same with localStorage.
                if (
                  !_.isNil(member_upload_id) &&
                  !this.isEqualToSavedBgTaskMuId(member_upload_id)
                ) {
                  return;
                }

                // Check if processingStatus is null or if total_counts are similar
                if (
                  _.isEmpty(this.state.processingStatus) ||
                  this.state.processingStatus.active_index <= active_index
                ) {
                  this.setState({
                    processingStatus: response,
                  });
                }

                if (['INITIAL', 'ONGOING'].includes(status)) {
                  this.setBgTaskMuId(this.state.memberUploadId);
                  const bgPollingTask = window.setTimeout(
                    this.getMuStatusOnBg,
                    5000,
                  );
                  this.setBgPollingTask(bgPollingTask);
                }
              }
            },
          );
        })
        .catch(() => {
          this.setState({
            loading_state: false,
          });
        });
    } else {
      const nextClicked = () => {
        const { memberUploadId, ticketId, encodePageType } = this.state;
        this.setState({
          loading_state: true,
        });
        this.successfulConfirmEnrollment(
          memberUploadId,
          ticketId,
          ['manual', 'client-manual'].includes(encodePageType),
        );
      };
      //duplicate upload member logs
      // if(this.state.pageHeader === 'Upload Member File') {

      //     Utils.StorageService('user_data').then(result => {
      //     let user_name: string = '';
      //     if (result && result !== undefined) {
      //       for (const i in result) {
      //         if (result[i]['key'] === 'username') {
      //           user_name = result[i]['value'];
      //         }
      //       }
      //       API.activityEnrollMemberList(this.state.trueTicketId, this.state.uploadMemberData, user_name)
      //       .catch(e => {
      //         console.log('claiming error', e)
      //       })
      //     };
      //   })
      //   }
      API.getMemberUploadStatus(this.state.memberUploadId)
        .then(response => {
          // console.log('encodepage not void5',response)
          // if(this.state.pageHeader === 'Upload Member File') {
          //   console.log('ann123 upload confirm final22', this.state  )
          //   Utils.StorageService('user_data').then(result => {
          //   let user_name: string = '';
          //   if (result && result !== undefined) {
          //     for (const i in result) {
          //       if (result[i]['key'] === 'username') {
          //         user_name = result[i]['value'];
          //       }
          //     }
          //     console.log('natam1', this.state, )
          //     API.activityEnrollMemberList(this.state.trueTicketId, this.state.uploadMemberData, user_name)
          //     .catch(e => {
          //       console.log('claiming error', e)
          //     })
          //   };
          // })
          // }

          this.setState(
            {
              loading_state: false,
              check_internet_flag: false,
            },
            () => {
              // Check if shouldUpdateStatus
              if (
                response &&
                response.error === undefined &&
                this.getShouldUpdateStatus()
              ) {
                const { member_upload_id, active_index, status } = response;

                // Check if member_upload_id is same with localStorage.
                if (
                  !_.isNil(member_upload_id) &&
                  !this.isEqualToSavedBgTaskMuId(member_upload_id)
                ) {
                  return;
                }

                // Check if processingStatus is null or if total_counts are similar
                if (
                  _.isEmpty(this.state.processingStatus) ||
                  this.state.processingStatus.active_index <= active_index
                ) {
                  this.setState({
                    processingStatus: response,
                  });
                }

                if (['UPLOADING', 'ONGOING', 'INITIAL'].includes(status)) {
                  this.setBgTaskMuId(this.state.memberUploadId);
                  const bgPollingTask = window.setTimeout(
                    this.getMuStatusOnBg,
                    5000,
                  );
                  this.setBgPollingTask(bgPollingTask);
                } else if (['SUCCESS'].includes(status)) {
                  this.setState({
                    loading_state: false,
                    isOpenModal: true,
                    modalTitle: 'Submission Successful',
                    modalMessage: 'Member file was successfully uploaded.',
                    next: nextClicked,
                  });
                }
              }
            },
          );
        })
        .catch(() => {
          this.setState({
            loading_state: false,
          });
        });
    }
  }

  successfulConfirmEnrollment(
    memberUploadId: any,
    ticketId: any,
    encodePageType: boolean,
  ) {
    console.log('successfulConfirmEnrollment()');

    // // if(this.state.pageHeader === 'Upload Member File') {
    //   Utils.StorageService('user_data').then(result => {
    //   let user_name: string = '';
    //   if (result && result !== undefined) {
    //     for (const i in result) {
    //       if (result[i]['key'] === 'username') {
    //         user_name = result[i]['value'];
    //       }
    //     }
    //     API.activityEnrollMemberList(memberUploadId, this.state.uploadMemberData, user_name)
    //     .catch(e => {
    //       console.log('claiming error', e)
    //     })
    //   };
    // })
    // // }

    API.confirmMemberUpload(memberUploadId, ticketId, encodePageType)
      .then(response => {
        // console.log('ann123 upload confirm final2', response )

        if (response) {
          // Utils.StorageService('user_data').then(result => {
          //   let user_name: string = '';
          //   if (result && result !== undefined) {
          //     for (const i in result) {
          //       if (result[i]['key'] === 'username') {
          //         user_name = result[i]['value'];
          //       }
          //     }
          //     API.activityEnrollMemberList(this.state.memberUploadId, this.state.ticketId, user_name)
          //     .catch(e => {
          //       console.log('claiming error', e)
          //     })
          //   };
          // })
        }
        if (response && response.error !== undefined) {
          this.setState({
            loading_state: false,
            check_internet_flag: false,
          });
        } else {
          this.deleteSaveForNow();
          this.onBackToDashboard();
        }
      })
      .catch(e => {
        if (e.message === 'USER_DATA_NOT_FOUND') {
          this.setState({
            loading_state: false,
            modalTitle: 'User Data Error',
            modalMessage: 'User data not found. Please try logging in again.',
            isOpenModal: true,
            next: () => {
              // window.location.replace('../index.html#/');
            },
          });
        } else {
          this.setState({
            loading_state: false,
          });
        }
      });

    // if(this.state.ticketId) {
    //   Utils.StorageService('user_data').then(result => {
    //     let user_name: string = '';
    //     if (result && result !== undefined) {
    //       for (const i in result) {
    //         if (result[i]['key'] === 'username') {
    //           user_name = result[i]['value'];
    //         }
    //       }
    //       API.activityEnrollMemberList( this.state.ticketId,
    //         {
    //           id:this.state.uploadMemberData._id,
    //           ticketId: this.state.ticketId,
    //           uploadedData: this.state.uploadMemberData,
    //           status:this.state.processingStatus
    //         }
    //         , user_name).then(response => {
    //         console.log('memberuploadstatusmodal',response)
    //       }).catch(e => {
    //         console.log('claiming error', e)
    //       })
    //     };
    //   })
    //   Utils.StorageService('user_data').then(result => {
    //     // console.log('activitylogs result', result)
    //     let user_name: string = '';
    //     if (result && result !== undefined) {
    //       // console.log('ACTIVITY LOGS result not undefined', result)
    //       for (const i in result) {
    //         if (result[i]['key'] === 'username') {
    //           user_name = result[i]['value'];
    //         }
    //       }
    //       console.log('activity logs username',user_name)
    //       // console.log('LEO MESSI! 0',this.state.ticketId)
    //       // console.log('LEO MESSI! 0', user_name)
    //       // API.activityEnrollMemberList( this.state.ticketId, this.state, user_name).then(response => {
    //       //   console.log('LEO MESSI! 1',response)
    //       // }).catch(e => {
    //       //   console.log('claiming error', e)
    //       // })
    //     };
    //   })
    // }
  }

  closeModalMessage() {
    const { submit_flag, next } = this.state;
    if (submit_flag) {
      this.setState({
        redirectFlag: true,
        redirectInfo: {
          pathname: '/membership/',
        },
      });
    } else if (this.state.fetchContactsErr) {
      this.setState({
        isOpenModal: false,
      });
    } else {
      this.setState(
        {
          modalTitle: '',
          modalMessage: '',
          isOpenModal: false,
          next: null,
        },
        () => {
          if (next) {
            next();
          }
        },
      );
    }
  }

  closeModalWithList() {
    this.setState({
      modalTitle: '',
      modalMessage: '',
      error_list: [],
      isOpenErrorWithListModal: false,
    });
  }

  onCancelProcess() {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });

    if (this.state.encodePageType === 'void') {
      const { trueTicketId } = this.state;
      API.cancelVoidUpload(this.state.memberUploadId, this.state.ticketId)
        .then(response => {
          if (response && response.error) {
            API.deleteCancelVoid(this.state.memberUploadId, this.state.ticketId)
              .then(() => {
                this.setState({
                  loading_state: false,
                  check_internet_flag: false,
                  processingStatus: {},
                  isOpenModal: true,
                  modalTitle: 'Processing of members cancelled',
                  modalMessage: `All progress has been discarded. Start over by uploading a new members list or editing the data mapping for ticket ID ${trueTicketId}.`,
                });
              })
              .catch(error => {
                console.log('cancel process fallback call err', error);
              });
          } else {
            this.setState({
              loading_state: false,
              processingStatus: {},
              isOpenModal: true,
              modalTitle: 'Processing of members cancelled',
              modalMessage: `All progress has been discarded. Start over by uploading a new members list or editing the data mapping for ticket ID ${trueTicketId}.`,
            });
          }
        })
        .catch(e => {
          if (e.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              processingStatus: {},
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState({
              loading_state: false,
            });
          }
        });
    } else {
      const { trueTicketId } = this.state;
      API.cancelMemberUpload(this.state.memberUploadId, this.state.ticketId)
        .then(response => {
          if (response && response.error) {
            API.deleteCancelEnrollment(
              this.state.memberUploadId,
              this.state.ticketId,
            )
              .then(() => {
                this.setState({
                  loading_state: false,
                  check_internet_flag: false,
                  processingStatus: {},
                  isOpenModal: true,
                  modalTitle: 'Processing of members cancelled',
                  modalMessage: `All progress has been discarded. Start over by uploading a new members list or editing the data mapping for ticket ID ${trueTicketId}.`,
                });
              })
              .catch(error => {
                console.log('cancel process fallback call err', error);
              });
          } else {
            this.setState({
              loading_state: false,
              processingStatus: {},
              isOpenModal: true,
              modalTitle: 'Processing of members cancelled',
              modalMessage: `All progress has been discarded. Start over by uploading a new members list or editing the data mapping for ticket ID ${trueTicketId}.`,
            });
          }
        })
        .catch(e => {
          if (e.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              processingStatus: {},
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState({
              loading_state: false,
            });
          }
        });
    }
  }

  handleCloseLostInternetConnectionModal = () => {
    this.setState({
      check_internet_flag: false,
    });
  };

  onBackToDashboard() {
    this.setState({
      redirectFlag: true,
      redirectInfo: {
        pathname: '/membership/',
      },
    });
  }

  onCancelEnrollment() {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });

    const statusActionError = () => {
      this.setState({
        loading_state: false,
        check_internet_flag: false,
        processingStatus: {},
        isOpenModal: true,
        modalTitle: 'Cancel Enrollment',
        modalMessage: 'Unable to cancel the enrollment.',
      });
    };

    if (this.state.encodePageType === 'void') {
      API.deleteCancelVoid(this.state.memberUploadId, this.state.ticketId)
        .then(() => {
          this.setState({
            loading_state: false,
            check_internet_flag: false,
            processingStatus: {},
          });
        })
        .catch(error => {
          console.log(error);
          statusActionError();
        });
    } else {
      API.deleteCancelEnrollment(this.state.memberUploadId, this.state.ticketId)
        .then(() => {
          this.setState({
            loading_state: false,
            check_internet_flag: false,
            processingStatus: {},
          });
        })
        .catch(error => {
          console.log(error);
          statusActionError();
        });
    }
  }

  onConfirmEnrollment() {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });
    const { details_data, uploaded_data, isAddMembersTicket } = this.state;
    const memberNos =
      details_data['number_of_members'] &&
      details_data['number_of_members'] !== '' &&
      !isNaN(details_data['number_of_members'])
        ? parseInt(details_data['number_of_members'])
        : uploaded_data['rows'].length;
    const memberProcessedReport = {
      ticket_type: ['manual', 'client-manual'].includes(
        this.state.encodePageType,
      )
        ? 'Manual Encode'
        : this.state.encodePageType === 'void'
        ? 'Upload Void Masterlist'
        : isAddMembersTicket
        ? 'Add Member'
        : 'Enroll Memberlist',
      no_of_members: `${memberNos}`,
    };

    API.createMemberProcessed(memberProcessedReport);
    if (this.state.encodePageType === 'void') {
      //confirm enrollment button void
      Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
        console.log(user_name);
        //console.log('Utils', result)
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
          API.activityVoidUpload(
            this.state.uploadMemberData['batch_name'],
            this.state.uploadMemberData,
            user_name,
          )
            .then(response => {
              if (response === undefined) {
                console.log('response', response);
              }
              console.log('activityVoidUpload', response);
            })
            .catch(e => {
              console.log('terminate error', e);
            });
        }
      });
      API.confirmVoidUpload(this.state.memberUploadId, this.state.ticketId)
        .then(response => {
          if (response && response.error !== undefined) {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
            });
          } else {
            this.setState({
              loading_state: false,
              isOpenModal: true,
              modalTitle: 'Submission Successful',
              modalMessage: 'Member file was successfully uploaded.',
              next: this.onBackToDashboard,
            });
            this.deleteSaveForNow();
          }
        })
        .catch(e => {
          if (e.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState({
              loading_state: false,
            });
          }
        });

      // } else if(this.state.encodePageType === 'default') {

      //   console.log('annnnn122', this.state)
      // Utils.StorageService('user_data').then(result => {
      //   let user_name: string = '';
      //   console.log(user_name)
      //   //console.log('Utils', result)
      //   if (result && result !== undefined) {
      //     for (const i in result) {
      //       if (result[i]['key'] === 'username') {
      //         user_name = result[i]['value'];
      //       }
      //     }
      //     console.log('activity void upload2', this.state)
      //     API.activityEnrollMemberList(this.state.trueTicketId
      //       , this.state.details_data, user_name)
      //     .then(response => {
      //       if(response === undefined){
      //         console.log('response', response)
      //       }
      //       console.log('activityEnrollMemberList11', response)
      //     }).catch(e => {
      //       console.log('terminate error', e)
      //     })
      //   };
      // });
    } else {
      const retryProps: any = {
        loading_state: false,
        isConfirmModalOpen: true,
        modalTitle: 'Submission Failed',
        modalMessage: 'Failed to submit members.',
        modalCloseText: 'Cancel',
        modalConfirmText: 'Continue Process',
        next: () => {
          this.onConfirmEnrollment();
        },
      };
      this.setState({
        loading_state: true,
        check_internet_flag: true,
        processingStatus: {
          status: 'INITIAL',
          active_index: 0,
          total_index: this.state.uploaded_data.rows
            ? this.state.uploaded_data.rows.length
            : 0,
        },
      });
      Processmaker.put('cases/' + this.state.ticketId + '/variable', {
        upload_status: 'Processing',
      });
      API.startMemberUpload(this.state.memberUploadId, this.state.ticketId)
        .then(response => {
          if (this.state.pageHeader === 'Upload Member File') {
            // console.log('ann123 upload confirm final22', this.state  )
            if (
              this.state.isAddMembersTicket === true &&
              this.state.encodePageType === 'default'
            ) {
              Utils.StorageService('user_data').then(result => {
                let user_name: string = '';
                console.log(user_name);
                //console.log('Utils', result)
                if (result && result !== undefined) {
                  for (const i in result) {
                    if (result[i]['key'] === 'username') {
                      user_name = result[i]['value'];
                    }
                  }
                  API.activityAddMembers(
                    this.state.trueTicketId,
                    this.state.uploadMemberData,
                    user_name,
                  ).catch(e => {
                    console.log('claiming error', e);
                  });
                }
              });
            } else if (
              this.state.encodePageType === 'default' &&
              this.state.isAddMembersTicket === false
            ) {
              Utils.StorageService('user_data').then(result => {
                let user_name: string = '';
                if (result && result !== undefined) {
                  for (const i in result) {
                    if (result[i]['key'] === 'username') {
                      user_name = result[i]['value'];
                    }
                  }
                  API.activityEnrollMemberList(
                    this.state.trueTicketId,
                    this.state.uploadMemberData,
                    user_name,
                  ).catch(e => {
                    console.log('claiming error', e);
                  });
                }
              });
            } else {
            }
          } else {
            if (this.state.encodePageType === 'manual') {
              Utils.StorageService('user_data').then(result => {
                let user_name: string = '';
                if (result && result !== undefined) {
                  for (const i in result) {
                    if (result[i]['key'] === 'username') {
                      user_name = result[i]['value'];
                    }
                  }
                  API.activityEnrollManualEncode(
                    this.state.trueTicketId,
                    this.state.uploadMemberData,
                    user_name,
                  ).catch(e => {
                    console.log('claiming error', e);
                  });
                }
              });
            }
          }
          const resErr = _.get(response, 'error', null);
          if (_.isNil(resErr)) {
            // console.log('aaaaaaa2')
            this.getProcessingStatus();
          } else {
            this.setState(retryProps);
          }
        })
        .catch(e => {
          if (e.message === 'USER_DATA_NOT_FOUND') {
            this.setState({
              loading_state: false,
              modalTitle: 'User Data Error',
              modalMessage: 'User data not found. Please try logging in again.',
              isOpenModal: true,
              next: () => {
                // window.location.replace('../index.html#/');
              },
            });
          } else {
            this.setState(retryProps);
          }
        });
    }
  }
  handleEncodedMemberData = (encodedData: any[]) => {
    let encodedDataArray: any[] = encodedData.map(enData => {
      enData['date_of_birth'] =
        enData['date_of_birth'] === null ? '' : enData['date_of_birth'];
      enData['hire_date'] =
        enData['hire_date'] === null ? '' : enData['hire_date'];
      enData['effectivity_date'] =
        enData['effectivity_date'] === null ? '' : enData['effectivity_date'];
      enData['regularization_date'] =
        enData['regularization_date'] === null
          ? ''
          : enData['regularization_date'];
      enData['date_printed'] =
        enData['date_printed'] === null ? '' : enData['date_printed'];
      enData['is_philhealth_rider'] =
        enData['is_philhealth_rider'] === ''
          ? false
          : enData['is_philhealth_rider'];
      enData['is_member_consent'] =
        enData['is_member_consent'] === ''
          ? false
          : enData['is_member_consent'];
      enData['is_vip'] = enData['is_vip'] === '' ? false : enData['is_vip'];

      return enData;
    });

    this.setState(prevState => ({
      ...prevState,
      uploaded_data: {
        ...prevState.uploaded_data,
        rows: encodedDataArray,
      },
    }));
  };

  handleCheckActiveStepForUnmappable = (currentStep, detailsData) => {
    if (
      currentStep === 1 &&
      detailsData.file_url &&
      this.state.handleNextCounter === 0
    ) {
      return 0;
    } else {
      return currentStep;
    }
  };

  handleSaveSuffixErrorDetails = (title: string, message: string) => {
    this.setState({
      modalTitle: title,
      modalMessage: message,
      isSuffixError: true,
    });
  };

  handleDeleteSuffixErrorDetails = () => {
    this.setState({
      modalTitle: '',
      modalMessage: '',
      isSuffixError: false,
    });
  };

  closeSuffixErrorModal = () => {
    this.setState({
      isShowSuffixErrorModal: false,
    });
  };

  handleDisableNext = data => {
    this.setState({
      disable_next: data,
    });
  };

  rowDataFixer = (corporates: any) => {
    let newCorporatesArray: any[] = [];
    let statuses = [
      'Active',
      'Suspended',
      'Terminated',
      'Expired',
      'Awaiting Activation',
    ];
    corporates.map(corporate => {
      if (statuses.includes(corporate.status))
        newCorporatesArray.push(corporate);
    });
    return newCorporatesArray;
  };
  

  // public
  public render(): JSX.Element {
    const {
      pmaker_task,
      breadcrumbs,
      pageHeader,
      steps,
      activeStep,
      clients_data,
      open,
      details_data,
      corporate_account,
      disabledNext,
      initial_load,
      loading_state,
      uploaded_data,
      columns_data,
      contract_options,
      system_names,
      clients_data_maps,
      selected_data_map,
      gender_data,
      civil_status_data,
      plan_type_data,
      type_data,
      relationship_data,
      vip_data,
      ph_rider_data,
      member_consent_data,
      site_data,
      raw_uploaded_data,
      add_new_data_map,
      rows_start_at,
      filtered_raw_data,
      client_maps,
      modalTitle,
      modalMessage,
      isOpenModal,
      isConfirmModalOpen,
      isOpenErrorWithListModal,
      error_list,
      batchnames,
      plan_type_options,
      civil_status_options,
      relationship_options,
      principals_options,
      site_assigned_options,
      uploadMemberData,
      ticketId,
      trueTicketId,
      isAddMembersTicket,
      isProcessing,
      processingStatus,
      redirectFlag,
      redirectInfo,
      encodePageType,
      // pageData,
      modalCloseText,
      modalConfirmText,
      customModalMessage,
      isShowSuffixErrorModal,
      suffix_data,
      disable_next,
      isExpiredClientModalOpen,
      selected_client_details,
      ticketVariables
    } = this.state;

    const memberLayout = ()=>{
      const isAutoRenew = this.getQueryParam('isAutoRenew') === 'true';
      if(isAutoRenew){
        const client_id = this.props.match.params.client_id;
        return <AutoRenew clientId={client_id} handleBackButton={this.handleBackButton}/>
      }

      return(
        <>
         <UploadMember
              case_id={ticketId}
              ticketId={trueTicketId}
              pmaker_task={pmaker_task}
              updateDetails={this.handleUpdateDetails}
              details_data={details_data}
              responseData={this.rowDataFixer(clients_data)}
              corporate_account={corporate_account}
              system_names={system_names}
              uploaded_data={uploaded_data}
              column_names={columns_data}
              showHideLoader={this.showHideLoader}
              updateState={this.updateState}
              updateUploadMemberData={this.updateUploadMemberData}
              data_maps={clients_data_maps}
              selected_data_map={selected_data_map}
              gender_data={gender_data}
              civil_status_data={civil_status_data}
              plan_type_data={plan_type_data}
              type_data={type_data}
              relationship_data={relationship_data}
              vip_data={vip_data}
              ph_rider_data={ph_rider_data}
              member_consent_data={member_consent_data}
              site_data={site_data}
              //  branch_options={branch_options}
              data={clients_data}
              clientData={selected_client_details}
              raw_uploaded_data={raw_uploaded_data}
              add_new_data_map={add_new_data_map}
              rows_start_at={rows_start_at}
              filtered_raw_data={filtered_raw_data}
              savingNewDataMap={this.savingNewDataMap}
              updatingDataMap={this.updatingSelectedDataMap}
              client_maps={client_maps}
              plan_type_options={plan_type_options}
              civil_status_options={civil_status_options}
              relationship_options={relationship_options}
              uploadMemberData={uploadMemberData}
              isProcessing={isProcessing}
              processingStatus={processingStatus}
              getProcessingStatus={this.getProcessingStatus}
              resumeStatusBgPolling={this.resumeStatusBgPolling}
              cancelStatusBgPolling={this.cancelStatusBgPolling}
              onBackToDashboard={this.onBackToDashboard}
              onCancelEnrollment={this.onCancelEnrollment}
              onConfirmEnrollment={this.onConfirmEnrollment}
              onCancelProcess={this.onCancelProcess}
              saveSuffixErrorMessage={(title: string, message: string) => {
                this.handleSaveSuffixErrorDetails(title, message);
              }}
              deleteSuffixErrorMessage={this.handleDeleteSuffixErrorDetails}
              page={this.state.encodePageType}
              suffix_data={suffix_data}
              ocpfile={this.state.ocp_file}
              is_ocp={this.state.is_ocp}
              default_data_mapping_save_for_now={
                this.state.default_data_mapping_save_for_now
              }
              save_for_now_system_name={this.state.save_for_now_system_name}
              system_names2={system_names}
              updatedMapping={this.state.client_maps}
              reload_table={this.state.reload_table}
              renewalType={this.state?.renewalType}
            />
        </>
      )
    }

    console.log("ticketVariables", ticketVariables)
    if (redirectFlag) {
      return <Redirect to={redirectInfo} />;
    }
    let newActiveStep = this.handleCheckActiveStepForUnmappable(
      activeStep,
      details_data,
    );   
    const onNextHowToRenewModal = type => {
      this.setState((prevState) => ({
        ...prevState,
        renewalType: type,
      }))
      const payload = {
        is_manual: type === 'manualRenewal' ? 1 : 0,
      };
     Processmaker.put(
        'cases/' + this.props.match.params.ticket_id + '/variable',
        payload,
      )
       if (type === 'manualRenewal') {
              this.setState({
                loading_state: false,
                redirectFlag: true,
                redirectInfo: {
                  pathname: `/membership/clients-profile/manual-renewal/selection/${this.props.match.params.client_id}/${this.props.match.params.ticket_id}`,
                  state: this.state
                },
              });
      }  else if(type === "uploadRenewal") {
              this.setState({
                pageHeader: 'Upload Member File',
                activeStep: 1,
              });

              onCloseHowToRenewModal()
        } 
    };
    const onCloseHowToRenewModal = () => {
      this.setState(prevState => ({
        ...prevState,
        isHowToRenewModalOpen: false,
      }));
    };
    return (
      <div className={clsx('EncodeMemberPage')}>
        {loading_state ? <Loader /> : null}
        <Grid container className={clsx('member-header')}>
          <Grid item xs={12}>
            <Components.UI.BreadcrumbsComponent items={breadcrumbs} />
          </Grid>
          <Grid item xs={12}>
            <PageHeaderComponent
              id={'upload_member_stepper'}
              label={pageHeader}
              steps={steps}
              activeStep={newActiveStep}
            />
          </Grid>
        </Grid>
        {newActiveStep === 0 && initial_load ? (
          <>
            <AddDetails
              state_data={this.state}
              responseData={this.rowDataFixer(clients_data)}
              updateDetails={this.handleUpdateDetails}
              updateState={this.updateState}
              details_data={details_data}
              contract_options={contract_options}
              batchnames={batchnames}
              isAddMembersTicket={isAddMembersTicket}
              encodePageType={this.state.encodePageType}
              hasTicketId={this.props.match.params.ticket_id ? true : false}
              handleUpdateDetailsChannelAndName={
                this.handleUpdateDetailsChannelAndName
              } //5685
              ticketVariables={this.state.ticketVariables} // 5685
            />
          </>
        ) : (encodePageType === 'default' ||
            encodePageType === 'void' ||
            encodePageType === 'terminate') &&
          newActiveStep === 1 ? (
          <>
            {memberLayout()}
          </>
        ) : ['manual', 'client-manual'].includes(this.state.encodePageType) &&
          newActiveStep === 1 &&
          initial_load ? (
          <EncodeMultipleMembers
            setEncodedData={this.handleEncodedMemberData}
            updateState={this.updateState}
            system_names={system_names}
            plan_type_options={plan_type_options}
            civil_status_options={civil_status_options}
            site_assigned_options={site_assigned_options}
            relationship_options={relationship_options}
            principals_options={principals_options}
            //site_options={site_options}
            details_data={details_data}
            column_map={
              columns_data && columns_data['rows'] ? columns_data['rows'] : []
            }
            ticketId={trueTicketId}
            uploaded_data={uploaded_data}
            isProcessing={isProcessing}
            processingStatus={processingStatus}
            getProcessingStatus={this.getProcessingStatus}
            resumeStatusBgPolling={this.resumeStatusBgPolling}
            cancelStatusBgPolling={this.cancelStatusBgPolling}
            onBackToDashboard={this.onBackToDashboard}
            onCancelEnrollment={this.onCancelEnrollment}
            onConfirmEnrollment={this.onConfirmEnrollment}
            onCancelProcess={this.onCancelProcess}
            onError={(title: string, message: string) => {
              this.setState({
                isOpenModal: true,
                modalTitle: title,
                modalMessage: message,
              });
            }}
          />
        ) : null}
        <Grid container alignItems="stretch">
          <Grid item xs={12}>
            {!(newActiveStep === 1 && this.getQueryParam('isAutoRenew') === 'true') && ( // condition to hide this button if autoRenew is true
              <FloatingButtons
                leftButtons={
                  <SnackBarButton
                    handleSaveForNow={this.onSaveForNowClicked}
                    handleNextButton={this.handleNextButton}
                    disabled={disabledNext}
                  />
                }
                rightButtons={
                  newActiveStep === 0 ? null : (
                    <SnackBarButtonBack
                      onBackPressed={this.handleBackButton}
                      disabled={false}
                    />
                  )
                }
              />
              )}
          </Grid>
        </Grid>
        <ConfirmationModal
          id={'encode-member-modal'}
          isModalOpen={open}
          title={this.state.renewalType  === "uploadRenewal" ? 'Confirm Renewal' : undefined}
          company={
            details_data && details_data['account']['label'] !== undefined
              ? details_data['account']['label']
              : ''
          } //label
          member_count={
            Object.keys(uploaded_data).length > 0
              ? uploaded_data['rows'].length
              : 0
          }
          onSubmit={this.onHandleSubmitClick}
          onClose={this.closeModal}
          disable={disable_next}
        />
        <ConfirmationModalComponent
          id="upload-member-confirm-modal"
          isModalOpen={isConfirmModalOpen}
          modalTitle={modalTitle}
          modalMessage={modalMessage}
          closeText={modalCloseText}
          confirmText={modalConfirmText}
          onClose={() => {
            this.setState({
              isConfirmModalOpen: false,
            });
          }}
          onConfirm={() => {
            const { next } = this.state;
            if (next) {
              this.setState(
                {
                  isConfirmModalOpen: false,
                },
                () => {
                  next();
                },
              );
            }
          }}
          customMessage={customModalMessage}
        />
        <ModalComponent
          id="upload_modal_error"
          isModalOpen={isOpenModal}
          title={modalTitle}
          message={modalMessage}
          onClose={this.closeModalMessage}
        />
        <ModalComponent
          id="upload_modal_error"
          isModalOpen={isShowSuffixErrorModal}
          title={modalTitle}
          message={modalMessage}
          onClose={this.closeSuffixErrorModal}
        />
        <ErrorWithListComponent
          id="upload_modal_list_error"
          isModalOpen={isOpenErrorWithListModal}
          title={modalTitle}
          message={modalMessage}
          data={error_list}
          onClose={this.closeModalWithList}
        />
        <LostInternetModal
          id="Lost Internet Modal"
          isModalOpen={this.state.check_internet_flag}
          onClose={this.handleCloseLostInternetConnectionModal}
        />
        <ModalComponent
          id="expired-client-modal"
          isModalOpen={isExpiredClientModalOpen}
          title={modalTitle}
          message={modalMessage}
          onClose={this.handleClosenModalProps}
        />
        <ModalWithChildren
          id="how-to-renew"
          isModalOpen={this.state.isHowToRenewModalOpen}
          onClose={onCloseHowToRenewModal}
          title="Renew Members"
        >
          <HowToRenewLayout
            onCancel={onCloseHowToRenewModal}
            onNext={onNextHowToRenewModal}
          />
        </ModalWithChildren>
      </div>
    );
  }
}

const mapStateToProps = (state: Store) => state.home;

const mapDispatchToProps = (dispatch: Dispatch) => ({
  Map: bindActionCreators(EncodeMemberActions.Map, dispatch),
});

export { mapStateToProps, mapDispatchToProps };
