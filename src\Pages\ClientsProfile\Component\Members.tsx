//#region Global Imports
import React, { useState, useRef, useEffect } from 'react';
import {
  Grid,
  TextField,
  InputAdornment,
  Popper,
  ClickAwayListener,
  Paper,
  Typography,
  Box,
  Link,
} from '@material-ui/core/';
import { Search } from '@material-ui/icons/';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { MemberStatusActionButtons } from './MemberStatusActionButtons';
import {
  faPlus,
  faDownload,
  faUsersCog,
  faRecycle,
} from '@fortawesome/free-solid-svg-icons';
import { bindActionCreators, Dispatch } from 'redux';
import clsx from 'clsx';
import moment from 'moment';
import {
  get,
  // isNil,
  forEach,
  map,
  // cloneDeep,
} from 'lodash';
import { makeStyles } from '@material-ui/core/styles';
import { Redirect } from 'react-router-dom';
import { saveAs } from 'file-saver';
//#endregion Global Imports

// import './style.scss';

//#region Interface Imports
import { Store } from 'Components/Stores/IStore';
import { IMemberPage } from './IMemberPage';
import { MemberActions } from './MemberActions';
import { Loader } from 'Components/UI/LoadingIndicator';
import { PageHeaderComponent } from 'Components/UI/PageHeader';
import { MembersTable } from './Table/MembersTable';
import { ModalComponent } from 'Components/UI/ModalComponent';
// import { AddSelect } from 'Components/UI/AddSelect';
import { Components } from '@hims/core';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import { API } from '../../API';
// import { DateRange } from 'Components/UI/DateRange';
import { Processmaker } from 'Pages/Processmaker';
import { LostInternetModal } from 'Components/UI';
import { MemberSuspensionModal } from './MemberSuspensionModal';
import { ConfirmMemberSuspensionModal } from './ConfirmMemberSuspensionModal';
import { ConfirmCancelSuspensionModal } from './ConfirmCancelSuspensionModal';
import { MemberTerminateBatchesModal } from './MemberTerminateBatchesModal';
import { MemberCancelTerminationModal } from './MemberCancelTerminationModal';
import { TerminateMemberConfirmationModal } from './TerminateMemberConfirmationModal';
import { CancelSuspensionModal } from './CancelSuspensionModal';
import { LiftSuspensionModal } from './LiftSuspensionModal';
import { ConfirmLiftSuspensionModal } from './ConfirmLiftSuspensionModal';
import { MemberReactivateModal } from './ReactivateMemberModal';
import { ConfirmReactivateModal } from './ConfirmReactivateModal';
import { SuccessSuspensionModal } from './SuccessSuspensionModal';
import { ReactivateMemberErrorModal } from './ReactivateMemberErrorModal';

const membershipModuleManagerGroup = [
  'APD_MANAGER GROUP',
  'APD_SUPERVISOR GROUP',
  'APD_TL GROUP',
  'APD_MANAGER',
  'APD_SUPERVISOR',
  'APD_TL',
];
const membershipPrintingGroup = [
  'DEPT_APD_PRINTING GROUP',
  'APD_PRINTING_STAFF GROUP',
  'APD_PRINTING_STAFF',
];
const membershipEncoderGroup = [
  'DEPT_APD_ENCODER GROUP',
  'APD_ENCODER GROUP',
  'APD_ENCODER',
];
const membershipVerifierGroup = [
  'DEPT_APD_VERIFIER GROUP',
  'APD_VERIFIER GROUP',
  'APD_VERIFIER',
];

const memberPageActionBtnsStyles = makeStyles(theme => ({
  change_status_link: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
    textDecoration: 'underline',
  },
  icons: {
    color: '#0D5E40',
    cursor: 'pointer',
    fontSize: '13px',
    marginRight: '4px',
  },
  popper: {
    top: '4px',
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '0 1em 1em 1em',
        borderColor: `transparent transparent ${theme.palette.background.paper} transparent`,
      },
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '1em 1em 0 1em',
        borderColor: `${theme.palette.background.paper} transparent transparent transparent`,
      },
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 1em 1em 0',
        borderColor: `transparent ${theme.palette.background.paper} transparent transparent`,
      },
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 0 1em 1em',
        borderColor: `transparent transparent transparent ${theme.palette.background.paper}`,
      },
    },
  },
  arrow: {
    position: 'absolute',
    fontSize: 7,
    width: '3em',
    height: '3em',
    '&::before': {
      content: '""',
      margin: 'auto',
      display: 'block',
      width: 0,
      height: 0,
      borderStyle: 'solid',
    },
  },
  paper: {
    padding: '15px',
    width: 150,
  },
  paper2: {
    padding: '15px',
    width: 200,
  },
  status: {
    padding: '5px',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#dddfe3',
    },
  },
  buttonredirect: {
    width: '100%',
    padding: '5px 20px',
    textAlign: 'left',
    alignItems: 'stretch',
    alignContent: 'left',
    justifyContent: 'flex-start',
    textTransform: 'none',
    fontSize: '12px',
  },
}));

interface MemberPageActionBtnsProps {
  clientId: string;
  clientStatus: string;
  memberFields?: any;
  memberData?: any;
  onExportIdPicture: (processAction: string, link: any) => void;
  onBatchProcessAction: (processAction: string) => void;
  onRenewProcessAction: (processAction: string) => void;
  showModalPopUp: (title: string, message: string) => void;
  memberTotal?: any;
}

const MemberPageActionBtns: React.FC<MemberPageActionBtnsProps> = (
  props: MemberPageActionBtnsProps,
): JSX.Element => {
  const classes = memberPageActionBtnsStyles(props);

  const anchorRef = useRef(null);
  const anchorRefRenew = useRef(null);
  const [arrowRef, setArrowRef] = useState<any>(null);
  const [arrowRefRenew, setArrowRefRenew] = useState<any>(null);
  const [redirect, setRedirect] = useState<any>({
    exportIdPicture: false,
    renew: false,
    generateReport: false,
    encodeMember: false,
  });
  const [isTL, setIsTL] = useState<boolean>(false);
  // const [isPrintingStaff, setIsPrintingStaff] = useState<boolean>(false);
  const [isPrintingStaff, setIsPrintingStaff] = useState<boolean>(false);
  const [isEncoder, setIsEncoder] = useState<boolean>(false);
  const [isVerifier, setIsVerifier] = useState<boolean>(false);
  const [isPopoverOpenGB, setPopupoverOpenGB] = useState(false);
  const [isPopoverOpenGBRenew, setPopupoverOpenGBRenew] = useState(false);
  const [downloadLink, setDonwloadLink] = React.useState<any>('');
  const [currentUserData, setCurrentUserData] = React.useState<any>({});

  function openPopoverGB() {
    setPopupoverOpenGB(true);
  }

  function openPopoverGBRenew() {
    setPopupoverOpenGBRenew(true);
  }

  function closePopoverGB() {
    setPopupoverOpenGB(false);
  }

  function closePopoverGBRenew() {
    setPopupoverOpenGBRenew(false);
  }

  const getGroup = async () => {
    await API.getUserDataFromDb().then(userData => {
      // console.log('current user', userData)
      setCurrentUserData(userData);

      if (membershipModuleManagerGroup.includes(userData.groupName)) {
        setIsTL(true);
      }
      if (membershipPrintingGroup.includes(userData.groupName)) {
        setIsPrintingStaff(true);
      }
      if (membershipEncoderGroup.includes(userData.groupName)) {
        setIsEncoder(true);
      }
      if (membershipVerifierGroup.includes(userData.groupName)) {
        setIsVerifier(true);
      }
    });
  };

  const exportIdPicture = () => {
    API.getExportIdPictureLink(props.clientId)
      .then((resp: any) => {
        setDonwloadLink(API.getDownloadLink(resp.download_link));
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  useEffect(() => {
    getGroup();
    exportIdPicture();
  }, []);

  useEffect(() => {
    console.log('PROPSSSS', props);
    console.log('currentUserData val', currentUserData);
    if (Object.keys(currentUserData).length > 0) {
      // checking of current user's rbac against a specific policy ID
      console.log(
        'has MS79?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS79', // can assign an APD ticket to a user
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS83?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS83', // can process manual encode ticket
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS85?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS85', // can process verify member ticket
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS86?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS86', // can process print card ticket
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS87?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS87', // can process upload correction ticket
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS88?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS88', // can process upload termination ticket
          currentUserData,
          false,
        ),
      );

      console.log(
        'has MS89?',
        GlobalFunction.checkUserRbacByPolicy(
          // Gitlab Issue #7587
          'MS89', // can process upload suspension ticket
          currentUserData,
          false,
        ),
      );
      //
    }
  }, [currentUserData]);

  useEffect(() => {
    console.log('isTL group?', isTL);
    console.log('isEncoder group?', isEncoder);
    console.log('isVerifier group?', isVerifier);
    console.log('isPrintingStaff group?', isPrintingStaff);
    console.log('PROPS', props);
  }, [isTL, isEncoder, isVerifier, isPrintingStaff]);

  if (redirect['encodeMember'] === true) {
    if (props.clientId && props.clientId !== '') {
      return (
        <Redirect
          to={{
            pathname: `/membership/encode/client-manual/${props.clientId}`,
          }}
        />
      );
    } else {
      return (
        <Redirect
          to={{
            pathname: `/membership/add-members`,
          }}
        />
      );
    }
  }
  const isShowActionButtons = (userData: any) => {
    if (
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS79', // can assign an APD ticket to a user (Supervisor, TL, Encoder)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS85', // can process verify member ticket (Supervisor, TL, Verifier)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS86', // can process print card ticket (Supervisor, TL, Printing Staff)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS88', // can process upload termination ticket (Supervisor, TL, Encoder, Verifier)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS89', // can process upload suspension ticket (Supervisor, TL, Encoder, Verifier)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS87', // can process upload correction ticket (Supervisor, TL, Encoder, Verifier)
        userData,
        false,
      ) ||
      GlobalFunction.checkUserRbacByPolicy(
        // Gitlab Issue #7587
        'MS83', // can process manual encode ticket (Supervisor, TL, Encoder)
        userData,
        false,
      )
    ) {
      console.log('show Client > Members action buttons');

      return true;
    }

    return false;
  };

  return (
    <>
      {isShowActionButtons(currentUserData) === true ? (
        <Grid container direction="row" justify="flex-end" spacing={2}>
          <Grid item>
            <FontAwesomeIcon icon={faDownload} className={classes.icons} />
            <Link
              //  href={downloadLink}
              className={classes.change_status_link}
              onClick={() => {
                setTimeout(() => {
                  // this.setState({
                  //   isModalOpen: true,
                  //   modalTitle: 'Export ID Pictures',
                  //   modalMessage: 'ID Pictures Successfully.',
                  // });
                  if (props.onExportIdPicture) {
                    props.onExportIdPicture('Export ID Picture', downloadLink);
                  }
                }, 1000);
              }}
            >
              Export ID Picture
            </Link>
          </Grid>
          <Grid item>
            <span
              id="client_members_process_batch"
              data-cy="client_members_process_batch"
              ref={anchorRef}
              className={classes.change_status_link}
              onClick={openPopoverGB}
              aria-describedby={'generate_batches'}
            >
              <FontAwesomeIcon icon={faUsersCog} className={classes.icons} />
              Process Batches
              <Popper
                id={'generate_batches'}
                open={isPopoverOpenGB}
                anchorEl={anchorRef.current}
                placement={'bottom-start'}
                className={classes.popper}
                disablePortal={false}
                style={{ marginTop: '4px' }}
                modifiers={{
                  flip: {
                    enabled: false,
                  },
                  preventOverflow: {
                    enabled: true,
                    boundariesElement: 'undefined',
                  },
                  arrow: {
                    enabled: true,
                    element: arrowRef,
                  },
                }}
              >
                <span className={classes.arrow} ref={setArrowRef} />
                <Paper className={classes.paper2}>
                  <ClickAwayListener onClickAway={closePopoverGB}>
                    <Grid container>
                      <Grid item xs={12}>
                        <h5 style={{ fontWeight: 600, margin: '0 0 10px' }}>
                          Choose Batch Process
                        </h5>
                        {GlobalFunction.checkUserRbacByPolicy(
                          // Gitlab Issue #7587
                          'MS86',
                          currentUserData,
                          false,
                        ) ? (
                          <>
                            <Typography
                              id="clientprofile_print_id_cards"
                              data-cy="clientprofile_print_id_cards"
                              className={classes.status}
                              onClick={() => {
                                if (props.onBatchProcessAction) {
                                  props.onBatchProcessAction('Print ID Cards');
                                }
                              }}
                            >
                              Print ID Cards
                            </Typography>

                            {/* <Typography
                              id="clientprofile_print_id_cards"
                              data-cy="clientprofile_print_id_cards"
                              className={classes.status}
                              onClick={() => {
                                if (props.onBatchProcessAction) {
                                  props.onBatchProcessAction(
                                    'Generate Transmittal Request',
                                  );
                                }
                              }}
                            >
                              Generate Transmittal Request
                            </Typography> */}
                          </>
                        ) : null}
                        {/* <Typography
                          id='clientprofile_print_id_cards'
                          data-cy='clientprofile_print_id_cards'
                          className={classes.status}
                        >
                          Upload Members List
                        </Typography>
                        <Typography
                          id='clientprofile_print_id_cards'
                          data-cy='clientprofile_print_id_cards'
                          className={classes.status}
                        >
                          Upload File Correction
                        </Typography>
                        <Typography
                          id='clientprofile_print_id_cards'
                          data-cy='clientprofile_print_id_cards'
                          className={classes.status}
                        >
                          Upload Supporting Documents
                        </Typography> */}
                        {GlobalFunction.checkUserRbacByPolicy(
                          // Gitlab Issue #7587
                          'MS88', // can process upload termination ticket (Supervisor, TL, Encoder, Verifier)
                          currentUserData,
                          false,
                        ) ? (
                          <Typography
                            id="clientprofile_terminate_members"
                            data-cy="clientprofile_terminate_members"
                            className={classes.status}
                            onClick={() => {
                              if (props.onBatchProcessAction) {
                                props.onBatchProcessAction('Terminate Members');
                              }
                            }}
                          >
                            Terminate Members
                          </Typography>
                        ) : null}

                        {GlobalFunction.checkUserRbacByPolicy(
                          // Gitlab Issue #7587
                          'MS89', // can process upload suspension ticket (Supervisor, TL, Encoder, Verifier)
                          currentUserData,
                          false,
                        ) ? (
                          <Typography
                            id="clientprofile_suspend_members"
                            data-cy="clientprofile_suspend_members"
                            className={classes.status}
                            onClick={() => {
                              if (props.onBatchProcessAction) {
                                props.onBatchProcessAction('Suspend Members');
                              }
                            }}
                          >
                            Suspend Members
                          </Typography>
                        ) : null}

                        {GlobalFunction.checkUserRbacByPolicy(
                          // Gitlab Issue #7587
                          'MS87', // can process upload correction ticket (Supervisor, TL, Encoder, Verifier)
                          currentUserData,
                          false,
                        ) ? (
                          <Typography
                            id="clientprofile_correction_members"
                            data-cy="clientprofile_correction_members"
                            className={classes.status}
                            onClick={() => {
                              if (props.onBatchProcessAction) {
                                props.onBatchProcessAction(
                                  'Upload File for Correction',
                                );
                              }
                            }}
                          >
                            Upload File for Correction
                          </Typography>
                        ) : null}

                        {GlobalFunction.checkUserRbacByPolicy(
                          // Gitlab Issue #7587
                          'MS38', //can manually renew the membership of a member account
                          currentUserData,
                          false,
                        ) ? (
                          <Typography
                            id="clientprofile_renew_members"
                            data-cy="clientprofile_renew_members"
                            className={classes.status}
                            onClick={() => {
                              if (props.onBatchProcessAction) {
                                props.onBatchProcessAction('Renew Members');
                              }
                            }}
                          >
                            Renew Members
                          </Typography>
                        ) : null}

                        {props.memberTotal !== undefined && 
                        props.memberTotal > 0 ?
                        (
                          <Typography
                            id='clientprofile_void_masterlist'
                            data-cy='clientprofile_void_masterlist'
                            className={classes.status}
                            onClick={() => {
                              if (props.onBatchProcessAction) {
                                props.onBatchProcessAction('Void Masterlist')
                              }
                            }}
                          >
                            Void Masterlist
                          </Typography>
                        ) : null}
                      </Grid>
                    </Grid>
                  </ClickAwayListener>
                </Paper>
              </Popper>
            </span>
          </Grid>

          {/*GlobalFunction.checkUserRbacByPolicy(
            // Gitlab Issue #7587
            'MS38',
            currentUserData,
            false,
          ) ? (
            <Grid item>
              <span
                id="client_members_renew"
                data-cy="client_members_renew"
                ref={anchorRefRenew}
                className={classes.change_status_link}
                onClick={openPopoverGBRenew}
                aria-describedby={'generate_renew'}
              >
                Renew
                <Popper
                  id={'generate_renew'}
                  open={isPopoverOpenGBRenew}
                  anchorEl={anchorRefRenew.current}
                  placement={'bottom-start'}
                  className={classes.popper}
                  disablePortal={false}
                  style={{ marginTop: '4px' }}
                  modifiers={{
                    flip: {
                      enabled: false,
                    },
                    preventOverflow: {
                      enabled: true,
                      boundariesElement: 'undefined',
                    },
                    arrow: {
                      enabled: true,
                      element: arrowRefRenew,
                    },
                  }}
                >
                  <span className={classes.arrow} ref={setArrowRefRenew} />
                  <Paper className={classes.paper2}>
                    <ClickAwayListener onClickAway={closePopoverGBRenew}>
                      <Grid container>
                        <Grid item xs={12}>
                          <h5 style={{ fontWeight: 600, margin: '0 0 10px' }}>
                            Choose Renew Process
                          </h5>
                          {GlobalFunction.checkUserRbacByPolicy(
                            // Gitlab Issue #7587
                            'MS38',
                            currentUserData,
                            false,
                          ) ? (
                            <>
                              <Typography
                                id="clientprofile_terminate_members"
                                data-cy="clientprofile_terminate_members"
                                className={classes.status}
                                onClick={() => {
                                  if (props.onRenewProcessAction) {
                                    props.onRenewProcessAction(
                                      'Manual Renewal',
                                    );
                                  }
                                }}
                              >
                                Manual Renewal (Selection)
                              </Typography>
                            ) : null}

                          {GlobalFunction.checkUserRbacByPolicy( // Gitlab Issue #7587
                            'MS89', // can process upload suspension ticket (Supervisor, TL, Encoder, Verifier)
                            currentUserData,
                            false
                          ) ? (
                              <Typography
                                id='clientprofile_suspend_members'
                                data-cy='clientprofile_suspend_members'
                                className={classes.status}
                                onClick={() => {
                                  if (props.onBatchProcessAction) {
                                    props.onBatchProcessAction('Suspend Members')
                                  }
                                }}
                              >
                                Suspend Members
                              </Typography>
                            ) : null}

                          {GlobalFunction.checkUserRbacByPolicy( // Gitlab Issue #7587
                            'MS87', // can process upload correction ticket (Supervisor, TL, Encoder, Verifier)
                            currentUserData,
                            false
                          ) ? (
                              <Typography
                                id='clientprofile_correction_members'
                                data-cy='clientprofile_correction_members'
                                className={classes.status}
                                onClick={() => {
                                  if (props.onBatchProcessAction) {
                                    props.onBatchProcessAction('Upload File for Correction')
                                  }
                                }}
                              >
                                Upload File for Correction
                              </Typography>
                            ) : null}

                          {GlobalFunction.checkUserRbacByPolicy( // Gitlab Issue #7587
                            'MS38', //can manually renew the membership of a member account
                            currentUserData,
                            false
                          ) ? (
                              <Typography
                                id='clientprofile_renew_members'
                                data-cy='clientprofile_renew_members'
                                className={classes.status}
                                onClick={() => {
                                  if (props.onBatchProcessAction) {
                                    props.onBatchProcessAction('Renew Members')
                                  }
                                }}
                              >
                                Renew Members
                              </Typography>
                            ) : null}

                          {props.memberTotal !== undefined && 
                          props.memberTotal > 0 ?
                          (
                            <Typography
                              id='clientprofile_void_masterlist'
                              data-cy='clientprofile_void_masterlist'
                              className={classes.status}
                              onClick={() => {
                                if (props.onBatchProcessAction) {
                                  props.onBatchProcessAction('Void Masterlist')
                                }
                              }}
                            >
                              Void Masterlist
                            </Typography>
                          ) : null}
                        </Grid>
                      </Grid>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
              </span>
            </Grid>
                              ) : null*/}

            {GlobalFunction.checkUserRbacByPolicy( // Gitlab Issue #7587
              'MS38',
              currentUserData,
              false
            ) ? (
                <Grid item>
                  <span
                    id='client_members_renew'
                    data-cy='client_members_renew'
                    ref={anchorRefRenew}
                    className={classes.change_status_link}
                    onClick={openPopoverGBRenew}
                    aria-describedby={'generate_renew'}
                  >
                    <FontAwesomeIcon icon={faRecycle} className={classes.icons} />
                Renew
              <Popper
                      id={'generate_renew'}
                      open={isPopoverOpenGBRenew}
                      anchorEl={anchorRefRenew.current}
                      placement={'bottom-start'}
                      className={classes.popper}
                      disablePortal={false}
                      style={{ marginTop: '4px' }}
                      modifiers={{
                        flip: {
                          enabled: false,
                        },
                        preventOverflow: {
                          enabled: true,
                          boundariesElement: 'undefined',
                        },
                        arrow: {
                          enabled: true,
                          element: arrowRefRenew,
                        },
                      }}
                    >
                      <span className={classes.arrow} ref={setArrowRefRenew} />
                      <Paper className={classes.paper2}>
                        <ClickAwayListener onClickAway={closePopoverGBRenew}>
                          <Grid container>
                            <Grid item xs={12}>
                              <h5 style={{ fontWeight: 600, margin: '0 0 10px' }}>
                                Choose Renew Process
                        </h5>
                              {GlobalFunction.checkUserRbacByPolicy( // Gitlab Issue #7587
                                'MS38',
                                currentUserData,
                                false
                              ) ? (
                                  <>
                                    <Typography
                                      id='clientprofile_terminate_members'
                                      data-cy='clientprofile_terminate_members'
                                      className={classes.status}
                                      onClick={() => {
                                        if (props.onRenewProcessAction) {
                                          props.onRenewProcessAction('Manual Renewal')
                                        }
                                      }}
                                    >
                                      Manual Renewal (Selection)
                          </Typography>
                                  </>) : null}
                            </Grid>
                          </Grid>
                        </ClickAwayListener>
                      </Paper>
                    </Popper>
                  </span>
                </Grid>
              ) : null}

            {GlobalFunction.checkUserRbacByPolicy(
              'MS83', // can process manual encode ticket (Supervisor, TL, Encoder)
              currentUserData,
              false
            ) ? (
                <Grid item>
                  <span
                    id='client_members_encode_member'
                    data-cy='client_members_encode_member'
                    className={classes.change_status_link}
                    onClick={() => {
                      setRedirect({ encodeMember: true })
                    }}
                    aria-describedby={'encode_members'}
                  >
                    <FontAwesomeIcon icon={faPlus} className={classes.icons} />
                Encode Member
              </span>
            </Grid>
          ) : null}
        </Grid>
      ) : null}
    </>
  );
};

type ISelectItem = Components.UI.InputSelect.ISelectItem;
//#endregion Interface Imports

declare global {
  interface Window {
    downloadFile: any;
  }
}

export class MembersComponent extends React.Component<
  IMemberPage.IProps,
  IMemberPage.IState
> {
  constructor(props: IMemberPage.IProps) {
    super(props);
    const table: any = {
      formattedColumns: {
        required_columns: ['status'],
      },
      filterExtensions: [],
      columnExtensions: [
        {
          columnName: 'person_id_label',
          width: 200,
          wordWrapEnabled: true,
        },
        {
          columnName: 'member_id',
          width: 200,
          wordWrapEnabled: true,
        },
        {
          columnName: 'member_name',
          width: 250,
          wordWrapEnabled: true,
        },
        {
          columnName: 'effectivity_date',
          wordWrapEnabled: true,
        },
        {
          columnName: 'termination_date',
          wordWrapEnabled: true,
        },
        {
          columnName: 'date_printed',
          wordWrapEnabled: true,
        },
        {
          columnName: 'status',
          wordWrapEnabled: true,
        },
      ],
      columns: [
        {
          name: 'person_id_label',
          title: 'Person ID',
        },
        {
          name: 'member_id',
          title: 'Member ID',
        },
        {
          name: 'member_name',
          title: 'Name',
        },
        {
          name: 'effectivity_date',
          title: 'Effective Date',
        },
        {
          name: 'termination_date',
          title: 'Termination Date',
        },
        {
          name: 'date_printed',
          title: 'Card Printed Date',
        },
        {
          name: 'status',
          title: 'Status',
        },
      ],
      rows: [],
    };
    let memberDataCF: any = {};

    this.state = {
      check_internet_flag: false,
      loading_state: false,
      member_list: [],
      memberDataCF: memberDataCF,
      member_data: table,
      member_data_all: table,
      selected_date_filter: {
        id: '',
        value: 'This Month',
        label: 'This Month',
      },
      selected_start_date: new Date(),
      selected_end_date: new Date(),
      global_filter: '',
      redirect_to_member: false,
      member_id: '',
      default_filters: [],
      sorting: [],
      isModalOpen: false,
      isPageSizeModal: false,
      modalTitle: '',
      modalMessage: '',
      totalCount: 0,
      dependentCount: 0,
      selectedCount: 0,
      forPrintingCount: 0,
      statusPanelCount: {},
      statusCount: {
        active: 0,
        markedForSuspension: 0,
        markedForTermination: 0,
        suspendedMarkedForTermination: 0,
        suspended: 0,
        terminated: 0,
        awaitingActivation: 0,
        forValidation: 0,
        cancelled: 0,
        others: 0,
      },
      memberStatusActionArrays: {
        forSuspension: [],
        forSuspensionDependents:[],
        forCancelSuspension: [],
        forLiftSuspension: [],
        forTermination: [],
        forReactivation: [],
        forCancelTermination: [],
      },
      memberStatusActionFlags: {
        isMemberSuspension: false,
        isMemberCancelSuspension: false,
        isMemberLiftSuspension: false,
        isMemberTermination: false,
        isMemberCancelTermination: false,
        isMemberReactivation: false,
      },
      dataFromStatusActions: {
        suspensionModal: [],
        cancelSuspensionModal: [],
        liftSuspensionModal: [],
        terminationModal: [],
        reactivationModal: [],
      },
      displayDataFromStatusActions: {
        reactivationModal: [],
      },
      memberStatusActionConfirmFlags: {
        isConfirmSuspension: false,
        isConfirmCancelSuspension: false,
        isConfirmLiftSuspension: false,
        isConfirmTermination: false,
        isConfirmCancelTermination: false,
        isConfirmReactivation: false,
      },
      selectedDependents: {
        suspensionModal: [],
      },
      memberList: {
        suspensionModal: [],
      },
      dependentSelected: [],
      applyToAllFlags: {
        suspension: false,
        cancelSuspension: false,
        liftSuspension: false,
        termination: false,
        cancelTermination: false,
        reactivation: false,
      },
      typing: false,
      typingTimeout: 0,
      page: 0,
      limit: 10,
      isResetTable: false,
      allMemberDisplay: {},
      batchProcessActions: {
        isBatchPrintIdCards: false,
        isBatchTerminateAction: false,
        isBatchSuspendAction: false,
        isBatchCorrectionAction: false,
        isBatchRenewAction: false,
        isGenerateTransmittalAction: false,
        isVoidMasterlistAction: false,
      },
      renewProcessActions: {
        isManualRenewal: false,
      },
      status_filters: [],
      currentUserData: {},
      isSuccessSuspensionModalOpen: false,
      successSuspensionMembersDisplay: [],
      isMemberReactivationError: false,
      memberReactivationErrorData: [],
      timeoutHandler: null,
      loading: false,
      openSnackbar: false,
      variant: 'success',
      snackbarMessage: '',
      memberData: {},
      memberId: '',
      id: '',
      memberCFData: {},
      memberFields: {},
      person_id: '',
      fullName: '',
      client_id: '',
      isLoaded: false,
      memberDataForCF: {},
      isExpiredClientModalOpen: false,
    };

    this.handleChangeFilter = this.handleChangeFilter.bind(this);
    this.handleDateSent = this.handleDateSent.bind(this);
    this.handleSearch = this.handleSearch.bind(this);
    this.handleChangeSorting = this.handleChangeSorting.bind(this);
    this.searchMembers = this.searchMembers.bind(this);
    this.closeModal = this.closeModal.bind(this);
    this.openUploadBatch = this.openUploadBatch.bind(this);
    this.generateMemberListReport = this.generateMemberListReport.bind(this);

    memberDataCF = this.state.member_list.data;
  }

  async componentDidMount() {
    this.getUser();
    // this.handlePreClickRow(this.props);
    this.searchMembers();
    try {
      const memberData = await API.getMemberData(
        this.props.match.params.member._id,
      );
      this.setState({ memberData: memberData });
    } catch (error) {
      console.log('async error', error);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    console.log('get member_fields data here', prevProps)
    if (prevState.isResetTable !== this.state.isResetTable) {
      // console.log('isResetTable: '+this.state.isResetTable)
    }

    if (prevState.selectedCount !== this.state.selectedCount) {
      // console.log('selected: '+this.state.selectedCount)
    }

    if (prevState.statusCount !== this.state.statusCount) {
      // console.log(this.state.statusCount)
    }

    if (
      prevState.memberStatusActionArrays !== this.state.memberStatusActionArrays
    ) {
      // console.log(this.state.memberStatusActionArrays)
    }

    if (prevState.status_filters !== this.state.status_filters) {
      console.log('update status_filters', this.state.status_filters);
    }

    if (prevState.currentUserData !== this.state.currentUserData) {
      console.log('update currentUserData', this.state.currentUserData);
    }
  }

  getUser = async () => {
    try {
      this.setState({
        loading_state: true,
        check_internet_flag: true,
      });

      await API.getUserDataFromDb().then(userData => {
        this.setState({
          currentUserData: userData,
        });
      });
    } catch (error) {
      console.log('getUserDataFromDb catch err', error);
      this.setState({
        loading_state: false,
        check_internet_flag: false,
      });
    }
  };

  generateMemberListReport() {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });

    const { global_filter, default_filters, sorting, memberId, id } = this.state;
    console.log('MEMBER API', memberId)
    //  console.log('MEMBER API', id)
    console.log('MEMBER API', this.state)
    // console.log('MEMBER API', this.props)
    // console.log('USE THIS MEMBERDATA', this.state.memberData)

    API.getMemberListReport(global_filter, default_filters, sorting, id)
      .then(response => {
        this.setState({
          loading_state: false,
        });
        if (response !== undefined) {
          if (response.type === 'application/json') {
            this.setState({
              check_internet_flag: false,
              isModalOpen: true,
              modalTitle: 'Generate Report Error',
              modalMessage: 'Failed to get list of members.',
            });
          } else {
            const filename = `LIST_OF_MEMBERS${moment().format(
              'MMM-DD-YYYY',
            )}.csv`;
            const file = new File([response], filename, {
              type: 'text/csv;charset=utf-8',
            });
            saveAs(file);
          }
        } else {
          this.setState({
            isModalOpen: true,
            modalTitle: 'Generate Report Error',
            modalMessage: 'Failed to get list of members.',
          });
        }

        console.log('statusPanelCount', response.statusCount);
      })
      .catch(() => {
        this.setState({
          loading_state: false,
          isModalOpen: true,
          modalTitle: 'Generate Report Error',
          modalMessage: 'Failed to get list of members.',
        });
      });
  }

  openUploadBatch() {
    window.location.href = '#/membership/add-members/';
  }

  isValidDate(date: string) {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  }

  isValidMomentDate(date: string) {
    const mmDdYyyy = moment(date, 'MM/DD/YYYY', true);
    const yyyyMmDd = moment(date, 'YYYY/MM/DD', true);

    return mmDdYyyy.isValid() || yyyyMmDd.isValid();
  }

  initialData = () => {
    this.setState({
      check_internet_flag: true,
    });
    API.getMembers(10, 0).then(response => {

      console.log('initialdata', response)

      // console.log('initialData table', table)
      if (response && response.error === undefined) {
        const table = this.generateDataForTable(response.members);
        this.setState({
          check_internet_flag: false,
          member_list: response,
          member_data_all: table,
          loading_state: false,
          totalCount: response.total.count,
          page: 0,
          limit: 10,
        });
        console.log('INITIAL DATA', response);
      } else {
        if (response.status === 502) {
          this.initialData();
        } else {
          const table = this.generateDataForTable([]);
          this.setState({
            member_list: [],
            member_data_all: table,
            loading_state: false,
            isModalOpen: true,
            modalTitle: 'Loading failed',
            modalMessage: 'An error occured while loading the list of members.',
            page: 0,
          });
        }
      }
    });
  };

  /******* generate data for table *******/
  generateDataForTable(response: any) {
    const predicate = () => true;
    const compare = (_column: any) => {
      return (_a: any, _b: any) => {
        return 0;
      };
    };
    const table: any = {
      formattedColumns: {
        required_columns: ['status'],
      },
      filterExtensions: [
        {
          columnName: 'recently_added',
          filteringEnabled: false,
          sortingEnabled: false,
        },
      ],
      integratedFilteringColumnExtensions: [
        {
          columnName: 'person_id_label',
          predicate: predicate,
        },
        {
          columnName: 'member_id',
          predicate: predicate,
        },
        {
          columnName: 'member_name',
          predicate: predicate,
        },
        {
          columnName: 'effectivity_date',
          predicate: predicate,
        },
        {
          columnName: 'termination_date',
          predicate: predicate,
        },
        {
          columnName: 'date_printed',
          predicate: predicate,
        },
        {
          columnName: 'status',
          predicate: predicate,
        },
      ],
      integratedSortingColumnExtensions: [
        {
          columnName: 'member_id',
          compare: compare('member_id'),
        },
        {
          columnName: 'member_name',
          compare: compare('member_name'),
        },
        {
          columnName: 'effectivity_date',
          compare: compare('effectivity_date'),
        },
        {
          columnName: 'termination_date',
          compare: compare('termination_date'),
        },
        {
          columnName: 'date_printed',
          compare: compare('date_printed'),
        },
        {
          columnName: 'status',
          compare: compare('status'),
        },
      ],
      columnExtensions: [
        {
          columnName: 'person_id_label',
          width: 200,
          wordWrapEnabled: true,
        },
        {
          columnName: 'member_id',
          width: 200,
          wordWrapEnabled: true,
        },
        {
          columnName: 'member_name',
          width: 250,
          wordWrapEnabled: true,
        },
        {
          columnName: 'effectivity_date',
          wordWrapEnabled: true,
        },
        {
          columnName: 'termination_date',
          wordWrapEnabled: true,
        },
        {
          columnName: 'date_printed',
          wordWrapEnabled: true,
        },
        {
          columnName: 'status',
          wordWrapEnabled: true,
        },
      ],
      columns: [
        {
          name: 'person_id_label',
          title: 'Person ID',
        },
        {
          name: 'member_id',
          title: 'Member ID',
        },
        {
          name: 'member_name',
          title: 'Name',
        },
        {
          name: 'effectivity_date',
          title: 'Effective Date',
        },
        {
          name: 'termination_date',
          title: 'Termination Date',
        },
        {
          name: 'date_printed',
          title: 'Date Printed',
        },
        {
          name: 'status',
          title: 'Status',
        },
      ],
      rows: [],
    };

    forEach(response, memberData => {
      const {
        _id,
        client_object,
        effectivity_date,
        first_name,
        last_name,
        member_id,
        formatted_member_status,
        member_status,
        middle_name,
        registered_name,
        date_printed,
        added_date,
        member_type,
        member_suspension,
        member_termination,
        person_id_label,
        title,
        suffix,
        date_of_birth,
        gender,
        civil_status,
        plan_type,
        relationship_to_principal,
        principal_name,
        employee_id,
        hire_date,
        site,
        side_address,
        floor,
        designation,
        job_lvl,
        supervisor,
        cost_center,
        financial_code,
        sub_company,
        department,
        email_address,
        member_card_no,
        member_account_no,
        regularization_date,
        is_philhealth_rider,
        is_member_consent,
        is_vip,
        principal_employee_id,
      } = memberData;
      this.setState({
        memberDataForCF: memberData,
      });

      const memberRow: any = {
        id: '',
        member_id: '',
        member_name: '',
        corporates: '',
        effectivity_date: '',
        termination_date: '',
        date_printed: '',
        status: '',
        recently_added: '',
      };

      memberRow['id'] = _id;
      memberRow['member_id'] = member_id;

      // MS834: Previous Status to Restore
      if (memberData && memberData['member_status_before_termination']) {
        memberRow['member_status_before_termination'] =
          memberData['member_status_before_termination'];
      }
      if (memberData && memberData['member_status_before_suspension']) {
        memberRow['member_status_before_suspension'] =
          memberData['member_status_before_suspension'];
      }
      if (added_date) {
        const effectivityInDateFormat = new Date(effectivity_date);
        const timeDiff =
          new Date().getTime() - effectivityInDateFormat.getTime();
        memberRow['recently_added'] =
          timeDiff <= 60 * 60 * 24 * 1000 ? 'Recently Added' : '';
      } else {
        memberRow['recently_added'] = '---';
      }

      if (
        effectivity_date !== undefined &&
        effectivity_date !== null &&
        (this.isValidDate(effectivity_date) ||
          this.isValidMomentDate(effectivity_date))
      ) {
        memberRow['effectivity_date'] = GlobalFunction.toShortDateString(
          GlobalFunction.getDateFromString(effectivity_date),
        );
      }

      if (
        memberData['member_termination'] &&
        memberData['member_termination']['termination_date'] &&
        moment(memberData['member_termination']['termination_date']).isValid()
      ) {
        memberRow['termination_date'] = moment(
          memberData['member_termination']['termination_date'],
        ).format('MMM D, YYYY');
      }

      if (
        date_printed !== undefined &&
        date_printed !== null &&
        (this.isValidDate(date_printed) || this.isValidMomentDate(date_printed))
      ) {
        memberRow['date_printed'] = GlobalFunction.toShortDateString(
          GlobalFunction.getDateFromString(date_printed),
        );
      }

      let memberName: string = '';
      if (first_name && first_name !== undefined && first_name !== null) {
        memberName += first_name;
      }
      if (middle_name && middle_name !== undefined && middle_name !== null) {
        /** MVP-MS998 */
        // memberName += ' ' + middle_name.substr(0, 1) + '.';
        memberName += ' ' + middle_name;
      }
      if (last_name && last_name !== undefined && last_name !== null) {
        memberName += ' ' + last_name;
      }
      /** MVP-MS998 */
      if (
        memberData['suffix'] &&
        memberData['suffix'] !== null &&
        memberData['suffix'].trim() !== ''
      ) {
        memberName += ', ' + memberData['suffix'];
      }
      memberRow['member_name'] = memberName;

      if (
        registered_name &&
        registered_name !== undefined &&
        registered_name !== null
      ) {
        memberRow['corporates'] = registered_name;
      } else if (
        client_object &&
        client_object !== undefined &&
        client_object !== null
      ) {
        const corporates: any[] = map(client_object, item => {
          return item['registered_name'];
        });
        memberRow['corporates'] = corporates.join(', ');
      }

      if (
        member_status &&
        member_status !== undefined &&
        member_status !== null
      ) {
        memberRow['status'] = formatted_member_status ? formatted_member_status : member_status;
      }

      let value = false;
      if (member_type) {
        memberRow['member_type'] = member_type;
        value = true;
      }
      console.log('is value true?1', value);
      if (member_suspension) {
        memberRow['member_suspension'] = member_suspension;
        console.log('membersusp true?', value)
      }
      if (member_termination) {
        memberRow['member_termination'] = member_termination;
      }
      if (person_id_label) {
        memberRow['person_id_label'] = person_id_label;
      }
      if (title) {
        memberRow['title'] = title;
      }
      if (suffix) {
        memberRow['suffix'] = suffix;
      }
      if (date_of_birth) {
        memberRow['date_of_birth'] = date_of_birth;
      }
      if (gender) {
        memberRow['gender'] = gender;
      }
      if (civil_status) {
        memberRow['civil_status'] = civil_status;
      }
      if (plan_type) {
        memberRow['plan_type'] = plan_type;
      }
      if (relationship_to_principal) {
        memberRow['relationship_to_principal'] = relationship_to_principal;
      }
      if (plan_type) {
        memberRow['plan_type'] = plan_type;
      }
      if (principal_name) {
        memberRow['principal_name'] = principal_name;
      }
      if (employee_id) {
        memberRow['employee_id'] = employee_id;
      }
      if (hire_date) {
        memberRow['hire_date'] = hire_date;
      }
      if (site) {
        memberRow['site'] = site;
      }
      if (side_address) {
        memberRow['side_address'] = side_address;
      }
      if (floor) {
        memberRow['floor'] = floor;
      }
      if (designation) {
        memberRow['designation'] = designation;
      }
      if (job_lvl) {
        memberRow['job_lvl'] = job_lvl;
      }
      if (supervisor) {
        memberRow['supervisor'] = supervisor;
      }
      if (cost_center) {
        memberRow['cost_center'] = cost_center;
      }
      if (financial_code) {
        memberRow['financial_code'] = financial_code;
      }
      if (sub_company) {
        memberRow['sub_company'] = sub_company;
      }
      if (department) {
        memberRow['department'] = department;
      }
      if (email_address) {
        memberRow['email_address'] = email_address;
        value = true;
      }
      console.log('is value true?2', value);
      if (member_card_no) {
        memberRow['member_card_no'] = member_card_no;
      }
      if (member_account_no) {
        memberRow['member_account_no'] = member_account_no;
      }
      if (regularization_date) {
        memberRow['regularization_date'] = regularization_date;
      }
      if (is_philhealth_rider) {
        memberRow['is_philhealth_rider'] = is_philhealth_rider;
      }
      if (is_member_consent) {
        memberRow['is_member_consent'] = is_member_consent;
        value = true;
      }
      if (is_vip) {
        memberRow['is_vip'] = is_vip;
      }
      if (principal_employee_id) {
        memberRow['principal_employee_id'] = principal_employee_id;
      }

      table['rows'].push(memberRow);
    });

    const memberDataFinal: any = table;
    console.log('MemberDataFinal', memberDataFinal);
    return table;
  }



  /******* handles click on row in the table *******/
  handleClickRow =  (row: any) => { //async
    // const member_id = row.id;
    const memberId = row.id;
    const memberFields = this.props.memberFields;

    console.log(memberId);
    console.log(memberFields);
    // // try {
    //   if (memberId) {
    //     const memberDataCF = row
    //     const clientFields = this.props.memberFields
    //     console.log('setMemberToForValidCF called', memberDataCF)
    //     console.log('clientfields', clientFields)

    //     let isRequiredMatch: any[] = [];
    //     // let CFfinalList: any[] = [];
    //     let isRequiredCF: any[] = [];
    //     let emptyField: any[] = [];
    //     let memberField: any[] = [];
    //     let clientFieldName: any[] = [];
    //     let newMemberStatus = memberDataCF.member_status
    //     // let finalCF = clientFields.field_type
    //     console.log(isRequiredMatch)
    //     console.log(isRequiredCF)
    //     console.log('clientfields final', clientFields)
    //     console.log('clientfields fieldtype0', clientFields[0].field_type)
    //     if(clientFields && clientFields.length > 2) {
    //       console.log('clientfields true', clientFields)

    //   //     isEmpty = clientFields.map(item => {
    //   //       console.log('filtered item', item)
    //   //       console.log('item is_required!', item.is_required)
    //   //       return {
    //   //         code: item.system_name,
    //   //         label: item.system_name
    //   //       }

    //           // CFfinalList =
    //           clientFields.filter(item => {
    //             console.log('filtered item', item)
    //             console.log('item is_required!', item.is_required)
    //               if(item.field_name) {
    //                 console.log('before fieldtype')

    //                 //item here should compare if member has empty
    //                 if(item.field_type !== 'Mandatory' && item.is_required === true) {
    //                   console.log('mandatory', item.is_required)
    //                   console.log('mandatory2', item)
    //                   console.log('memberdata vs req-mandatory field', memberDataCF)
    //                   // return emptyField = item
    //                 }

    //                 // return item
    //                 console.log('RETURN ONLY EMAIL!', item)
    //                 console.log('RETURN ONLY EMAIL!FINAL!', emptyField)

    //                 if(emptyField) {
    //                   console.log('empty field is true', emptyField)
    //                   console.log('empty field is true', memberDataCF)
    //                   clientFieldName = memberDataCF.field_name
    //                   console.log('get field name', clientFieldName)
    //                     // clientFieldName.filter(item => {
    //                     //   console.log('clientfieldname', item)
    //                     // })
    //                   if(emptyField === memberDataCF.field_name) {
    //                     console.log('GOT YOU!', emptyField)
    //                     console.log('GOT YOU!', memberDataCF.field_name)
    //                   } else {
    //                     console.log('stupid!', memberDataCF.field_name)
    //                   }
    //                   // memberDataCF.filter(item => {
    //                   //   console.log('memberDataCFitem', item)
    //                   //   console.log('memberDataCF item fieldName true?', item.field_name)
    //                   //   return memberField = item.field_name
    //                   //   // if(item.field_name) {
    //                   //   //   return true
    //                   //   // }
    //                   // })
    //                   console.log('almost memberField', memberField)
    //                   console.log('almost emptyField', emptyField)
    //                   // this.handleClientFieldForValid(memberDataCF)
    //                   return memberDataCF

    //                 } else {
    //                   console.log('emptyfield is null', emptyField)
    //                   console.log(' or member is for valid', newMemberStatus)
    //                 }
    //               } else {
    //                 console.log('either item is mandatory/item is not required', item.is_required)
    //               }
    //             console.log('ITEMM111', item)
    //             console.log('ITEMM222', memberDataCF)

    //             })
    //         console.log('should pass prop of member data', memberDataCF)
    //         console.log('should only return the email', emptyField)
    //         // console.log('CFfinalList!', CFfinalList)

    //     } else {
    //       console.log('FIELD TYPE IS MANDATORY', clientFields.field_type)
    //     }
    this.setState({
      redirect_to_member: true,
      member_id: memberId,
      memberFields: memberFields,
    });
    //    }
    //   // } catch (error){
    //   //   console.log('for valid member error', error)
    //   // }
  };

  handleDataFromCheckbox = (row: any[]) => {
    console.log('handleDataFromCheckbox data', row);
    if (row.length > 0) {
      let activeCounter = 0;
      let markedForSuspensionCounter = 0;
      let markedForTerminationCounter = 0;
      let suspendedMarkedForTerminationCounter = 0;
      let suspendedCounter = 0;
      let otherStatusCounter = 0;
      let terminatedCounter = 0;
      let awaitingActivationCounter = 0;
      let forValidationCounter = 0;
      let cancelledCounter = 0;

      let suspensionArray: any[] = [];
      let suspensionArrayDependents: any[] = [];
      let cancelSuspensionArray: any[] = [];
      let liftSuspensionArray: any[] = [];
      let terminationArray: any[] = [];
      let reactivateArray: any[] = [];
      let cancelTerminationArray: any[] = [];

      row.forEach(rowData => {
        console.log('rowData test', rowData);
        if (rowData.status.toString().toLowerCase() === 'active') {
          console.log('Memberlist table original', suspensionArray)
          activeCounter++;
          suspensionArray.push(rowData);
          suspensionArrayDependents.push(rowData);
          terminationArray.push(rowData);
        } else if (
          rowData.status.toString().toLowerCase() ===
          'marked for suspension - marked for termination'
        ) {
          markedForSuspensionCounter++;
          if (rowData['isSuspendedMarkedForTermination']) {
            suspendedMarkedForTerminationCounter++;
          } else {
            markedForTerminationCounter++;
          }
          cancelTerminationArray.push(rowData);
          suspensionArray.push(rowData);
          cancelSuspensionArray.push(rowData);
          terminationArray.push(rowData);
        } else if (
          rowData.status.toString().toLowerCase() ===
            'active - for suspension' ||
          rowData.status.toString().toLowerCase() === 'marked for suspension'
        ) {
          markedForSuspensionCounter++;
          cancelSuspensionArray.push(rowData);
          terminationArray.push(rowData);
          suspensionArray.push(rowData);
        } else if (rowData.status.toString().toLowerCase() === 'suspended') {
          suspendedCounter++;
          liftSuspensionArray.push(rowData);
          terminationArray.push(rowData);
        } else if (rowData.status.toString().toLowerCase() === 'terminated') {
          terminatedCounter++;
          reactivateArray.push(rowData);
        } else if (
          rowData.status.toString().toLowerCase() ===
            'active - for termination' ||
          rowData.status.toString().toLowerCase() ===
            'suspended - for termination' ||
          rowData.status.toString().toLowerCase() === 'marked for termination'
        ) {
          if (rowData['isSuspendedMarkedForTermination']) {
            suspendedMarkedForTerminationCounter++;
          } else {
            markedForTerminationCounter++;
          }
          cancelTerminationArray.push(rowData);
          suspensionArray.push(rowData);
        } else if (
          rowData.status.toString().toLowerCase() === 'awaiting activation'
        ) {
          awaitingActivationCounter++;
          terminationArray.push(rowData);
        } else if (
          rowData.status.toString().toLowerCase() === 'for validation'
        ) {
          forValidationCounter++;
          terminationArray.push(rowData);
        } else if (rowData.status.toString().toLowerCase() === 'cancelled') {
          cancelledCounter++;
          reactivateArray.push(rowData);
        } else {
          otherStatusCounter++;
        }
      });

      this.setState({
        statusCount: {
          active: activeCounter,
          markedForSuspension: markedForSuspensionCounter,
          markedForTermination: markedForTerminationCounter,
          suspendedMarkedForTermination: suspendedMarkedForTerminationCounter,
          suspended: suspendedCounter,
          terminated: terminatedCounter,
          awaitingActivation: awaitingActivationCounter,
          forValidation: forValidationCounter,
          cancelled: cancelledCounter,
          others: otherStatusCounter,
        },
        memberStatusActionArrays: {
          forSuspension: suspensionArray,
          forSuspensionDependents: suspensionArrayDependents,
          forCancelSuspension: cancelSuspensionArray,
          forLiftSuspension: liftSuspensionArray,
          forTermination: terminationArray,
          forReactivation: reactivateArray,
          forCancelTermination: cancelTerminationArray,
        },
      });
    }

    this.setState({
      selectedCount: row.length
    })

    // console.log('MEMBERS FOR Suspension', this.state)
    // console.log('OG! forSuspension', this.state.memberStatusActionArrays['forSuspension'])
    // console.log('OG! forSuspensionDependents',  this.state.memberStatusActionArrays['forSuspensionDependents'])
    // console.log('OG! memberStatusActionArrays', this.state.memberStatusActionArrays)
  }

  handleMemberStatusAction = (statusAction: string) => {
   console.log('STATUS ACTION TEST', statusAction);
    if (statusAction === 'Suspend') {
      // console.log('MEMBERS FOR Suspension', this.state.memberStatusActionArrays['forSuspension'])
      // console.log('MEMBERS FOR Suspension', this.state.memberStatusActionArrays['forSuspensionDependents'])
      this.setState({
        memberStatusActionFlags: {
          isMemberSuspension: true,
        },
      });
    } else if (statusAction === 'Cancel Suspension') {
      this.setState({
        memberStatusActionFlags: {
          isMemberCancelSuspension: true,
        },
      });
    } else if (statusAction === 'Lift Suspension') {
      this.setState({
        memberStatusActionFlags: {
          isMemberLiftSuspension: true,
        },
      });
    } else if (statusAction === 'Terminate') {
      this.setState({
        memberStatusActionFlags: {
          isMemberTermination: true,
        },
      });
    } else if (statusAction === 'Reactivate') {
      console.log(
        'MEMBERS FOR REACTIVATION',
        this.state.memberStatusActionArrays['forReactivation'],
      );

      let members = this.state.memberStatusActionArrays['forReactivation'];
      let forValidationMembers: any[] = [];
      // Check if selected members member status is previously For Validation
      members.map(member => {
        if (member.hasOwnProperty('member_status_before_termination')) {
          if (member.member_status_before_termination === 'For Validation') {
            forValidationMembers.push(member);
          }
        }
      });

      if (forValidationMembers.length > 0) {
        this.setState({
          isMemberReactivationError: true,
          memberReactivationErrorData: forValidationMembers,
          modalMessage:
            'These member(s) were previously For Validation and cannot be reactivated.',
          modalTitle: 'Member(s) Cannot be Reactivated',
        });
      } else {
        this.setState({
          memberStatusActionFlags: {
            isMemberReactivation: true,
          },
        });
      }
    } else if (statusAction === 'Cancel Termination') {
      this.setState({
        memberStatusActionFlags: {
          isMemberCancelTermination: true,
        },
      });
    }
  };

  handleMemberSuspension = (data: any, isApplyToAll: boolean) => {
    console.log('SUSPENSION DATA 2', data)
    //.length > 0
    if (data) {
      this.setState({
        dataFromStatusActions: {
          suspensionModal: data,
          dependentSelected:  data && data.selectedDependents ? data.selectedDependents : [],
          allMembers: data && data.allMembers ? data.allMembers : [],
        },
        applyToAllFlags: {
          suspension: isApplyToAll,
        },
        memberStatusActionConfirmFlags: {
          isConfirmSuspension: true
        },
       
        selectedDependents: {
          suspensionModal: data && data.selectedDependents ? data.selectedDependents : [],
        },
        memberList: {
          allMembers: data && data.allMembers ? data.allMembers : [],
        }
        
      });


    }
  };

  handleConfirmMemberSuspension = (data: any[]) => {
    // console.log(data)
    if (data.length > 0) {
      console.log('confirmmembersuspension', data)
      this.setState({
        loading_state: true,
        check_internet_flag: true,
        isResetTable: true,
      },
        () => {
          API.putMemberSuspension(data)
            .then(response => {
              console.log(response);
              if (response && response.error === undefined) {
                console.log('confirmmembersuspension', response)
                // this.setState({
                //   loading_state: false,
                //   check_internet_flag: false,
                //   isModalOpen: true,
                //   modalTitle: 'Member Suspension Successful',
                //   modalMessage: 'Member(s) have been successfully suspended.',
                //   isResetTable: false,
                // })

                this.setState({
                  loading_state: false,
                  check_internet_flag: false,
                  isSuccessSuspensionModalOpen: true,
                  modalTitle:
                    data[0].memberIds.length > 1
                      ? 'Members has been suspended'
                      : 'Member has been suspended',
                  modalMessage: 'Member(s) have been successfully suspended.',
                  isResetTable: false,
                  allMemberDisplay: data
                })

              } else {
                statusActionError();
              }
            })
            .catch(error => {
              console.log(error);
              statusActionError();
            });

          const statusActionError = () => {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
              isModalOpen: true,
              modalTitle: 'Member Suspension Failed',
              modalMessage:
                'An error occured while trying to suspend the selected member(s).',
              isResetTable: false,
            });
          };
        },
      );
    }
  };

  handleMemberReactivation = (data: any[], displayData: any[]) => {
    // console.log(data)
    // console.log(displayData)
    if (data.length > 0 && displayData.length > 0) {
      this.setState({
        dataFromStatusActions: {
          reactivationModal: data,
        },
        displayDataFromStatusActions: {
          reactivationModal: displayData,
        },
        memberStatusActionConfirmFlags: {
          isConfirmReactivation: true,
        },
      });
    }
  };

  handleConfirmReactivation = (payload: any[]) => {
    // console.log(payload)
    if (payload.length > 0) {
      this.setState(
        {
          loading_state: true,
          check_internet_flag: true,
          isResetTable: true,
        },
        () => {
          API.putMemberReactivation(payload)
            .then(response => {
              console.log(response);
              if (response && response.error === undefined) {
                this.setState({
                  loading_state: false,
                  check_internet_flag: false,
                  isModalOpen: true,
                  modalTitle: 'Member(s) have been Reactivated',
                  modalMessage:
                    'Member(s) have successfully reactivated their membership.',
                  isResetTable: false,
                });
              } else {
                statusActionError(
                  response && response.error && response.error.message
                    ? response.error.message
                    : undefined,
                );
              }
            })
            .catch(error => {
              console.log(error);
              statusActionError(
                error && error.message ? error.message : undefined,
              );
            });

          const statusActionError = (errorMsg?: string) => {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
              isModalOpen: true,
              modalTitle: 'Member Reactivation Failed',
              modalMessage: errorMsg
                ? errorMsg
                : 'An error occured while trying to reactivate the selected member(s).',
              isResetTable: false,
            });
          };
        },
      );
    }
  };

  handleMemberCancelSuspension = (data: any) => {
    // console.log(data)
    this.setState({
      dataFromStatusActions: {
        cancelSuspensionModal: data,
      },
      memberStatusActionConfirmFlags: {
        isConfirmCancelSuspension: true,
      },
    });
  };

  handleConfirmCancelSuspension = (payload: any) => {
    // console.log(payload)
    this.setState(
      {
        loading_state: true,
        check_internet_flag: true,
        isResetTable: true,
      },
      () => {
        API.deleteMemberCancelSuspension(payload)
          .then(response => {
            console.log(response);
            if (response && response.error === undefined) {
              this.setState({
                loading_state: false,
                check_internet_flag: false,
                isModalOpen: true,
                modalTitle: 'Cancelling of Member Suspension Successful',
                modalMessage:
                  'Member(s) have been successfully cancelled from suspension.',
                isResetTable: false,
              });
            } else {
              statusActionError();
            }
          })
          .catch(error => {
            console.log(error);
            statusActionError();
          });

        const statusActionError = () => {
          this.setState({
            loading_state: false,
            check_internet_flag: false,
            isModalOpen: true,
            modalTitle: 'Cancelling of Member Suspension Failed',
            modalMessage:
              'An error occured while trying to cancel the suspension of the selected member(s).',
            isResetTable: false,
          });
        };
      },
    );
  };

  handleMemberLiftSuspension = (data: any[], isApplyToAll: boolean) => {
    // console.log(data)
    if (data.length > 0) {
      this.setState({
        dataFromStatusActions: {
          liftSuspensionModal: data,
        },
        applyToAllFlags: {
          liftSuspension: isApplyToAll,
        },
        memberStatusActionConfirmFlags: {
          isConfirmLiftSuspension: true,
        },
      });
    }
  };

  handleConfirmMemberLiftSuspension = (data: any[]) => {
    // console.log(data)
    if (data.length > 0) {
      this.setState(
        {
          loading_state: true,
          check_internet_flag: true,
          isResetTable: true,
        },
        () => {
          API.patchMemberLiftSuspension(data)
            .then(response => {
              console.log(response);
              if (response && response.error === undefined) {
                this.setState({
                  loading_state: false,
                  check_internet_flag: false,
                  isModalOpen: true,
                  modalTitle: 'Lifting of Member Suspension Successful',
                  modalMessage:
                    'Member(s) have been successfully lifted from suspension.',
                  isResetTable: false,
                });
              } else {
                statusActionError();
              }
            })
            .catch(error => {
              console.log(error);
              statusActionError();
            });

          const statusActionError = () => {
            this.setState({
              loading_state: false,
              check_internet_flag: false,
              isModalOpen: true,
              modalTitle: 'Lifting of Member Suspension Failed',
              modalMessage:
                'An error occured while trying to lift the suspension of the selected member(s).',
              isResetTable: false,
            });
          };
        },
      );
    }
  };

  handleMemberCancelTermination = (payload: any) => {
    // console.log(payload)
    this.setState(
      {
        loading_state: true,
        check_internet_flag: true,
        isResetTable: true,
      },
      () => {
        API.deleteMemberCancelTermination([payload])
          .then(response => {
            console.log(response);
            if (response && response.error === undefined) {
              this.setState({
                loading_state: false,
                check_internet_flag: false,
                isModalOpen: true,
                modalTitle: 'Cancelling of Member Termination Successful',
                modalMessage:
                  'Member(s) have been successfully cancelled from termination.',
                isResetTable: false,
              });
            } else {
              statusActionError();
            }
          })
          .catch(error => {
            console.log(error);
            statusActionError();
          });

        const statusActionError = () => {
          this.setState({
            loading_state: false,
            check_internet_flag: false,
            isModalOpen: true,
            modalTitle: 'Cancelling of Member Termination Failed',
            modalMessage:
              'An error occured while trying to cancel the termination of the selected member(s).',
            isResetTable: false,
          });
        };
      },
    );
  };

  closeMemberStatusActionModal = (actionModal: string) => {
    if (actionModal === 'suspensionModal') {
      this.setState({
        memberStatusActionFlags: {
          isMemberSuspension: false,
        },
      });
    } else if (actionModal === 'cancelSuspensionModal') {
      this.setState({
        memberStatusActionFlags: {
          isMemberCancelSuspension: false,
        },
      });
    } else if (actionModal === 'liftSuspensionModal') {
      this.setState({
        memberStatusActionFlags: {
          isMemberLiftSuspension: false,
        },
      });
    } else if (actionModal === 'cancelTerminationModal') {
      this.setState({
        memberStatusActionFlags: {
          isMemberCancelTermination: false,
        },
      });
    } else if (actionModal === 'reactivationModal') {
      this.setState({
        memberStatusActionFlags: {
          isMemberReactivation: false,
        },
      });
    }
  };

  closeMemberStatusActionConfirmModal = (confirmActionModal: string) => {
    if (confirmActionModal === 'confirmSuspensionModal') {
      this.setState({
        memberStatusActionConfirmFlags: {
          isConfirmSuspension: false,
        },
      });
    } else if (confirmActionModal === 'confirmLiftSuspensionModal') {
      this.setState({
        memberStatusActionConfirmFlags: {
          isConfirmLiftSuspension: false,
        },
      });
    } else if (confirmActionModal === 'confirmReactivationModal') {
      this.setState({
        memberStatusActionConfirmFlags: {
          isConfirmReactivation: false,
        },
      });
    } else if (confirmActionModal === 'confirmCancelSuspensionModal') {
      this.setState({
        memberStatusActionConfirmFlags: {
          isConfirmCancelSuspension: false,
        },
      });
    }
  };

  handleExportIdPicture = async (processAction: string, link: any) => {
    var _self = this;

    if (processAction === 'Export ID Picture') {
      let reader = new FileReader();
      let linkFileName = link.substr(link.lastIndexOf('/') + 1);
      let fileName = linkFileName.slice(0, -4);
      await fetch(link)
        .then(res => res.blob())
        .then(function(myBlob) {
          if (window.hasOwnProperty('downloadFile')) {
            reader.readAsDataURL(myBlob);
            reader.onloadend = async function() {
              var base64data = (reader.result as string).substring(
                (reader.result as string).indexOf(',') + 1,
              );

              let args = {
                filename: fileName,
                file: base64data,
                extension: 'zip',
              };

              const download = await window.downloadFile(args);

              if (download.success) {
                _self.setState({
                  isModalOpen: true,
                  modalTitle: 'Export ID Pictures',
                  modalMessage: 'ID Pictures Exported Successfully.',
                });
              } else {
                _self.setState({
                  isModalOpen: true,
                  modalTitle: 'Export ID Pictures',
                  modalMessage: 'Exporting ID Pictures has been cancelled.',
                });
              }
            };
          } else {
            saveAs(myBlob, fileName);
          }
        });
    }
  };

  handleBatchProcessActions = (processAction: string) => {
    if (processAction === 'Print ID Cards') {
      this.setState({
        batchProcessActions: {
          isBatchPrintIdCards: true,
        },
      });
    }
    if (processAction === 'Terminate Members') {
      this.setState({
        batchProcessActions: {
          isBatchTerminateAction: true,
        },
      });
    }
    if (processAction === 'Suspend Members') {
      this.setState({
        batchProcessActions: {
          isBatchSuspendAction: true,
        },
      });
    }
    if (processAction === 'Upload File for Correction') {
      this.setState({
        batchProcessActions: {
          isBatchCorrectionAction: true,
        },
      });
    }
    if (processAction === 'Renew Members') {
      this.setState({
        batchProcessActions: {
          isBatchRenewAction: true,
        },
      });
    }
    // if (processAction === 'Export ID Picture') {
    //   this.setState({
    //     isModalOpen: true,
    //     modalTitle: 'Export ID Pictures',
    //     modalMessage: 'ID Pictures Exported Successfully.',
    //   });
    // }
    if (processAction === 'Generate Transmittal Request') {
      this.setState({
        batchProcessActions: {
          isGenerateTransmittalAction: true,
        },
      });
    }
    if (processAction === 'Void Masterlist') {
      this.setState({
        batchProcessActions: {
          isVoidMasterlistAction: true
        }
      });
    }
  }

  handleRenewProcessActions = (processAction: string) => {
    if (processAction === 'Manual Renewal') {
      this.setState({
        renewProcessActions: {
          isManualRenewal: true,
        },
      });
    }
  };

  /******* handles changes in month filter *******/
  handleChangeFilter(item: ISelectItem) {
    this.setState({
      selected_date_filter: item,
    });
  }

  /******* handles changes in date range *******/
  handleDateSent(date: Date, attr: string) {
    if (attr === 'start') {
      this.setState({
        selected_start_date: date,
      });
    } else {
      this.setState({
        selected_end_date: date,
      });
    }
  }

  /******* handles changes in global search *******/
  handleSearch(e: React.ChangeEvent<HTMLInputElement>) {
    const { value } = e.target;
    const { default_filters } = this.state;
    if (this.state.typingTimeout < -1) {
      if (value === '' && default_filters && default_filters.length === 0) {
        const table = this.generateDataForTable([]);
        this.setState({
          member_list: [],
          member_data_all: table,
          member_data: table,
          global_filter: value,
          totalCount: 0,
        });

        return;
      }
    }

    if (this.state.typingTimeout) {
      clearTimeout(this.state.typingTimeout);
    }
    this.setState({
      global_filter: value,
      page: 0,
      limit: 10,
      typing: false,
      typingTimeout: setTimeout(() => {
        this.searchMembers();
      }, 2000),
    });
  }

  handlePageChange = (currentPage: number) => {
    this.setState(
      {
        page: currentPage,
      },
      () => {
        this.searchMembers();
      },
    );
  };

  handlePageSizeChange = value => {
    const { totalCount } = this.state;
    const pageLimit = 1000;

    if (value === 0 && totalCount !== 0 && totalCount > pageLimit) {
      this.setState({
        isPageSizeModal: true,
      });
    } else {
      this.setState(
        {
          limit: value,
        },
        () => {
          this.searchMembers();
        },
      );
    }
  };

  // handle api filter by column
  handleFilterByColumn = (filter: any) => {
    const { global_filter, status_filters } = this.state;

    console.log('FILTER1', filter);

    if (filter.length > 0) {
      filter = filter
        .map(fData => {
          if (
            fData['value'] &&
            fData['columnName'] &&
            fData['columnName'] === 'status'
          ) {
            if (fData['value'].length > 0) {
              fData['columnName'] = 'member_status';
              return fData;
            } else {
              return null;
            }
          } else if (
            fData['columnName'] &&
            fData['columnName'] === 'termination_date'
          ) {
            if (fData['value'] !== '') {
              return fData;
            } else {
              return null;
            }
          } else if (
            fData['value'] &&
            typeof fData['value'] === 'string' &&
            fData['value'].trim() === ''
          ) {
            return null;
          } else if (fData['value'] === null) {
            return null;
          } else {
            return fData;
          }
        })
        .filter(currData => currData !== null);
    }

    if (filter.length === 0 && status_filters.length > 0) {
      filter = [
        {
          columnName: 'member_status',
          value: status_filters,
        },
      ];
    }

    if (
      filter.length > 0 &&
      status_filters.length > 0 &&
      !filter.map(a => a['columnName']).includes('member_status')
    ) {
      filter = [
        ...filter,
        {
          columnName: 'member_status',
          value: status_filters,
        },
      ];
    }

    console.log('filter val', filter);

    if (this.state.typingTimeout < -1) {
      if (global_filter === '' && filter && filter.length === 0) {
        const table = this.generateDataForTable([]);
        this.setState({
          member_list: [],
          member_data_all: table,
          member_data: table,
          default_filters: filter,
          totalCount: 0,
        });

        return;
      }
    }

    if (this.state.typingTimeout) {
      clearTimeout(this.state.typingTimeout);
    }

    this.setState({
      default_filters: filter,
      page: 0,
      typing: false,
      typingTimeout: setTimeout(() => {
        this.searchMembers();
      }, 2000),
    });

    // TODO add filter api call
  };

  /******* handles sorting in table columns *******/
  handleChangeSorting(sorting: any) {
    this.setState(
      {
        sorting: sorting,
      },
      () => {
        this.searchMembers();
      },
    );
  }

  searchMembers = () => {
    this.setState(
      {
        loading_state: true,
        check_internet_flag: true,
      },
      () => {
        let fltr = this.state.default_filters;
        let statusFltr = this.state.status_filters;

        console.log('client member: limit', this.state.limit);
        console.log(
          'client member: offset',
          this.state.limit * this.state.page,
        );
        console.log('client member: query', this.state.global_filter);
        console.log('client member: fltr', fltr);
        console.log('client member: sorting', this.state.sorting);

        if (fltr.length === 0) {
          API.searchMembers(
            this.state.limit,
            this.state.limit * this.state.page,
            this.state.global_filter,
            fltr,
            this.state.sorting,
            this.props.data._id,
            ).then(response => {
              console.log('search member response', response);
              if (response && response.error === undefined) {
                const table = this.generateDataForTable(response.data);
                console.log('memberlist table 1', table)

                this.setState({
                  default_filters: fltr,
                  check_internet_flag: false,
                  member_list: response,
                  member_id: response.member_id,
                  member_data_all: table,
                  member_data: table,
                  loading_state: false,
                  totalCount: response.totalCount,
                  statusPanelCount: response.statusCount,
                  //member_type: response.member_type,
                  forPrintingCount: get(response, 'forPrintingCount', 0),
                });

              } else {
                const table = this.generateDataForTable([]);
                this.setState({
                  member_list: [],
                  member_data_all: table,
                  member_data: table,
                  loading_state: false,
                  isModalOpen: true,
                  modalTitle: 'Loading failed',
                  modalMessage:
                    'An error occured while loading the list of members.',
                });
              }
              console.log(this.state.statusPanelCount['Suspended'], 'suspended');
              // console.log('STATUS COUNT', this.state.statusPanelCount)
              // console.log('MEMBER ID', memberId)
            });
          }
          else{
            console.log('garb',fltr);
            fltr.map((flt, index) => {
              console.log('index', index);

              if(flt['columnName'] === 'member_status'){
                if(!statusFltr.includes('Marked for Termination')){
                  console.log('in without Marker for Termination search');
                    API.searchMembers(
                      this.state.limit,
                      this.state.limit * this.state.page,
                      this.state.global_filter,
                      fltr,
                      this.state.sorting,
                      this.props.data._id,
                    ).then(response => {

                      if (response && response.error === undefined) {
                        let table = this.generateDataForTable(response.data);
                        if (fltr.length > 0) {
                          fltr.forEach(fltr => {
                            if (fltr && fltr['columnName'] && fltr['columnName'] === 'member_status') {
                              // REMOVED ROW FILTER => FILTERD THROUGH API ALREADY:
                              // table.rows = table.rows.map(row => {
                              //   if (
                              //     statusFltr.includes('Active') &&
                              //     (row['status'] === 'Marked for Suspension' ||
                              //       row['status'] === 'Marked for Termination')
                              //   ) {
                              //       if(row['status'] === 'Marked for Termination'){
                              //         const today = new Date();
                              //         const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                              //         const date1 = new Date(date);
                    
                              //         if(row.member_suspension && row.member_suspension.suspension_start_date){
                              //           console.log('Entered Here', row);
                              //           const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                              //           const date2 = new Date(suspensionDate);
                              //           console.log('Today:', date1, 'SuspensionDate:',date2)
                    
                              //           const difference_In_Time = date2.getTime() - date1.getTime();
                              //           const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                              //           console.log('Difference in time:',difference_In_Time);
                              //           console.log('Difference in Days',difference_In_Days);
    
                              //           if(difference_In_Days < 0) {
                              //             console.log('Real Value:', row);
                              //             console.log('Suspended - For Termination');
                              //           }
                              //         }
                    
                              //         console.log('Real Value:', row);
                              //         row['status'] = 'Active - For Termination'
                              //         return row;
                              //       }
    
                              //       if(row['status'] === 'Marked for Suspension'){
                              //         row['status'] = 'Active - For Suspension'
                              //         return row;
                              //       }
                              //       return row;
                              // }
                                
                              // if (
                              //   statusFltr.includes('Suspended') &&
                              //   row['status'] == 'Marked For Termination'
                              // ) {
                              //     const today = new Date();
                              //     const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                              //     const date1 = new Date(date);
    
                              //     if(row['status'] == 'Marked For Termination'){
                              //       if(row.member_suspension && row.member_suspension.suspension_start_date){
                              //         console.log('Entered Here', row);
                              //         const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                              //         const date2 = new Date(suspensionDate);
                              //         console.log('Today:', date1, 'SuspensionDate:',date2)
                  
                              //         const difference_In_Time = date2.getTime() - date1.getTime();
                              //         const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                              //         console.log('Difference in time:',difference_In_Time);
                              //         console.log('Difference in Days',difference_In_Days);
      
                              //         if(difference_In_Days < 0) {
                              //           row['status'] = 'Suspended - For Termination'
                              //           return row;
                              //         }
                              //       }
                              //     }
                              //   }
                                
                              //   if (statusFltr.includes('Marked for Suspension') 
                              //     && row['status'] === 'Marked for Suspension') {
                              //     row['status'] = 'Active - For Suspension'
                              //     return row;
                              //   } 
    
                              //   if (statusFltr.includes(row['status'])) {
                              //     return row;
                              //   } 
    
                              //   return null
                              // }).filter(currData => currData !== null);
                            }
                          })
                        }
                        
            
                        this.setState({
                          default_filters: fltr,
                          check_internet_flag: false,
                          member_list: response,
                          member_id: response.member_id,
                          member_data_all: table,
                          member_data: table,
                          loading_state: false,
                          totalCount: response.totalCount,
                          statusPanelCount: response.statusCount,
                          //member_type: response.member_type,
                          forPrintingCount: get(response, 'forPrintingCount', 0),
                        });
                      } else {
                        console.log('SCENE 2', statusFltr)
                      // if(item.member_status === 'Active' && getActiveSusp === '' || getActiveSusp === undefined) {
                      //   console.log('A1', item)
                      //   console.log('A2', fltr['value'])
                      //   return item
                      //   }
                      }
                      console.log('5', this.state.statusPanelCount['Suspended'], 'suspended');
                    });

                  }
                  // if statusfltr is Marked for Termination
                else{
                    console.log('in with Marked for Termination search');
                      // fltr.map((tst, index)=>{
                      //     if(tst.columnName === 'member_status'){
                      //        fltr.splice(index, 1);
                      //     }
                      // })
                      console.log('fltr', fltr);
                      console.log('gab',statusFltr)
                      API.searchMembers(
                        this.state.limit,
                        this.state.limit * this.state.page,
                        this.state.global_filter,
                        fltr,
                        this.state.sorting,
                        this.props.data._id,
                      ).then(response => {
                        if (response && response.error === undefined) {
                          let table = this.generateDataForTable(response.data);
                          console.log('memberlist table 3', table)
                                // table.rows = table.rows.map(row => {
                                //     // if (statusFltr.includes('Marked for Suspension') 
                                //     //   && row['status'] === 'Marked for Suspension') {
                                //     //   row['status'] = 'Active - For Suspension'
                                //     //   return row;
                                //     // } 
      
                                //     // if(statusFltr.includes('Marked for Termination') && 
                                //     //   row['status'] === 'Marked for Termination'){
                                //     //   const today = new Date();
                                //     //   const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                                //     //   const date1 = new Date(date);
                    
                                //     //   if(row.member_suspension && row.member_suspension.suspension_start_date){
                                //     //     console.log('Entered Here', row);
                                //     //     const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                                //     //     const date2 = new Date(suspensionDate);
                                //     //     console.log('Today:', date1, 'SuspensionDate:',date2)
                    
                                //     //     const difference_In_Time = date2.getTime() - date1.getTime();
                                //     //     const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                                //     //     console.log('Difference in time:',difference_In_Time);
                                //     //     console.log('Difference in Days',difference_In_Days);
                                //     //     if(difference_In_Days < 0) {
                                //     //       console.log('Real Value:', row);
                                //     //       row['status'] = 'Suspended - For Termination'
                                //     //       return row;
                                //     //     }
                                //     //   }
                                      
                                //     //   console.log('Real Value:', row);
                                //     //   row['status'] = 'Active - For Termination'
                                //     //   return row;
                                //     // }
  
                                //     if (statusFltr.includes(row['status'])) {
                                //       console.log('in here');
                                //       return row;
                                //     } 
  
                                //   return null
                                // }).filter(currData => currData !== null);
                          console.log('newtabledata', table)
              
                          this.setState({
                            default_filters: fltr,
                            check_internet_flag: false,
                            member_list: response,
                            member_id: response.member_id,
                            member_data_all: table,
                            member_data: table,
                            loading_state: false,
                            totalCount: response.totalCount,
                            statusPanelCount: response.statusCount,
                            //member_type: response.member_type,
                            forPrintingCount: get(response, 'forPrintingCount', 0),
                          });
                        } else {
                          const table = this.generateDataForTable([]);
                          this.setState({
                            member_list: [],
                            member_data_all: table,
                            member_data: table,
                            loading_state: false,
                            isModalOpen: true,
                            modalTitle: 'Loading failed',
                            modalMessage:
                              'An error occured while loading the list of members.',
                          });
                        }
                        console.log('9', this.state.statusPanelCount['Suspended'], 'suspended');
                        // console.log('STATUS COUNT', this.state.statusPanelCount)
                        // console.log('MEMBER ID', memberId)
                      });
                    }
                  }else{
                    API.searchMembers(
                      this.state.limit,
                      this.state.limit * this.state.page,
                      this.state.global_filter,
                      fltr,
                      this.state.sorting,
                      this.props.data._id,
                    ).then(response => {
                      console.log('search member response', response);
                      if (response && response.error === undefined) {
                        let table = this.generateDataForTable(response.data);
                        if (fltr.length > 0) {
                          fltr.forEach(fltr => {
                            if (fltr && fltr['columnName'] && fltr['columnName'] === 'member_status') {
                              table.rows = table.rows.map(row => {
                                // if (
                                //   statusFltr.includes('Active') &&
                                //   (row['status'] === 'Marked for Suspension' ||
                                //     row['status'] === 'Marked for Termination')
                                // ) {
                                //     if(row['status'] === 'Marked for Termination'){
                                //       const today = new Date();
                                //       const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                                //       const date1 = new Date(date);
      
                    
                                //       if(row.member_suspension && row.member_suspension.suspension_start_date){
                                //         console.log('Entered Here', row);
                                //         const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                                //         const date2 = new Date(suspensionDate);
                                //         console.log('Today:', date1, 'SuspensionDate:',date2)
      
                    
                                //         const difference_In_Time = date2.getTime() - date1.getTime();
                                //         const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                                //         console.log('Difference in time:',difference_In_Time);
                                //         console.log('Difference in Days',difference_In_Days);
      
                                //         if(difference_In_Days < 0) {
                                //           console.log('Real Value:', row);
                                //           console.log('Suspended - For Termination');
                                //         }
                                //       }
      
                    
                                //       console.log('Real Value:', row);
                                //       row['status'] = 'Active - For Termination'
                                //       return row;
                                //     }
      
                                //     if(row['status'] === 'Marked for Suspension'){
                                //       row['status'] = 'Active - For Suspension'
                                //       return row;
                                //     }
                                //     return row;
                                // }
      
                                  
                                // if (
                                //   statusFltr.includes('Suspended') &&
                                //   row['status'] == 'Marked For Termination'
                                // ) {
                                //     const today = new Date();
                                //     const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                                //     const date1 = new Date(date);
      
                                //     if(row['status'] == 'Marked For Termination'){
                                //       if(row.member_suspension && row.member_suspension.suspension_start_date){
                                //         console.log('Entered Here', row);
                                //         const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                                //         const date2 = new Date(suspensionDate);
                                //         console.log('Today:', date1, 'SuspensionDate:',date2)
      
                    
                                //         const difference_In_Time = date2.getTime() - date1.getTime();
                                //         const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                                //         console.log('Difference in time:',difference_In_Time);
                                //         console.log('Difference in Days',difference_In_Days);
      
                                //         if(difference_In_Days < 0) {
                                //           row['status'] = 'Suspended - For Termination'
                                //           return row;
                                //         }
                                //       }
                                //     }
                                //   }
      
                                  
                                //   if (statusFltr.includes('Marked for Suspension') 
                                //     && row['status'] === 'Marked for Suspension') {
                                //     row['status'] = 'Active - For Suspension'
                                //     return row;
                                //   } 
      
                                //   if(statusFltr.includes('Marked for Termination') && 
                                //     row['status'] === 'Marked for Termination'){
                                //     const today = new Date();
                                //     const date = (today.getMonth()+1)+'/'+today.getDate()+'/'+today.getFullYear();
                                //     const date1 = new Date(date);
      
                  
                                //     if(row.member_suspension && row.member_suspension.suspension_start_date){
                                //       console.log('Entered Here', row);
                                //       const suspensionDate = moment(row.member_suspension.suspension_start_date).format('MM/DD/YYYY');
                                //       const date2 = new Date(suspensionDate);
                                //       console.log('Today:', date1, 'SuspensionDate:',date2)
      
                  
                                //       const difference_In_Time = date2.getTime() - date1.getTime();
                                //       const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
                                //       console.log('Difference in time:',difference_In_Time);
                                //       console.log('Difference in Days',difference_In_Days);
                                //       if(difference_In_Days < 0) {
                                //         console.log('Real Value:', row);
                                //         row['status'] = 'Suspended - For Termination'
                                //         return row;
                                //       }
                                //     }
      
                                    
                                //     console.log('Real Value:', row);
                                //     row['status'] = 'Active - For Termination'
                                //     return row;
                                //   }
      
                                if (statusFltr.includes(row['status'])) {
                                  return row;
                                }
                                  if (statusFltr.includes(row['status'])) {
                                    return row;
                                  } 
      
                                return null
                                  return null
                              }).filter(currData => currData !== null);
                            }
                          })
                        }
      
                        console.log('ddd', table, response,)
      
                        
                        console.log('newtabledata', table,response,)
            
                        this.setState({
                          default_filters: fltr,
                          check_internet_flag: false,
                          member_list: response,
                          member_id: response.member_id,
                          member_data_all: table,
                          member_data: table,
                          loading_state: false,
                          totalCount: response.totalCount,
                          statusPanelCount: response.statusCount,
                          //member_type: response.member_type,
                          forPrintingCount: get(response, 'forPrintingCount', 0),
                        });
                      } else {
                        console.log('eee')
                        const table = this.generateDataForTable([]);
                        this.setState({
                          member_list: [],
                          member_data_all: table,
                          member_data: table,
                          loading_state: false,
                          isModalOpen: true,
                          modalTitle: 'Loading failed',
                          modalMessage:
                            'An error occured while loading the list of members.',
                        });
                      }
                      console.log('fff', this.state.statusPanelCount['Suspended'], 'suspended');
                      // console.log('STATUS COUNT', this.state.statusPanelCount)
                      // console.log('MEMBER ID', memberId)
                    });
                  }
                })
              }
      
      
              
              
              /**
              // TODO : Commented seems the response it not used. uneccessary endpoint
      
                //getDependents total
                let memberId = this.state.member_id;
                // let client_id = this.state.member_data.
                  API.getMemberDependentsTotal(memberId).then(response => {
                    console.log('RESPONSE111', response)
                    //const memberStatus = response.member_status;
                  if (response && response.error === undefined) {
                    console.log('Dependent total list SUCCESS!', response)
                    console.log('Dependent total count', response.length)
                  }
      
                this.setState({
                 // dependentCount: response.length
                })
                  console.log('TOTAL DEPENDENT COUNT', this.state.dependentCount)
                })
              */
      
              // //get all dependent suspended
              // API.getMemberDependentSuspended(memberId).then(response => {
              //   if (response && response.error === undefined) {
              //     console.log('Suspended Dependent list SUCCESS!!', response)
              //     console.log('Dependent suspended count', response.length)
              //   }
      
              //   // this.setState({
              //   //   suspendedDependent: response.length
              //   // })
              // })
      
              // //get all dependent active
              // API.getMemberDependentActive(memberId).then(response => {
              //   if (response && response.error === undefined) {
              //     console.log('Active Dependent list SUCCESS!!', response)
              //     console.log('Dependent Active count', response.length)
              //   }
      
              //   // this.setState({
              //   //   suspendedDependent: response.length
              //   // })
              // })
            },
          );
        };
      

  closeModal() {
    this.setState({ isModalOpen: false });
    this.searchMembers();
  }

  handlePageSizeModalClose = () => {
    this.setState({
      isPageSizeModal: false,
    });
  };

  handleCloseLostInternetConnectionModal = () => {
    this.setState({
      check_internet_flag: false,
    });
  };

  handleSubmitTerminationModal = (payload: any) => {
    console.log('handleSubmitTerminationModal', payload);
    this.setState({
      dataFromStatusActions: {
        terminationModal: payload,
      },
      memberStatusActionConfirmFlags: {
        isMemberTermination: true,
      },
    });
    console.log('handleSubmitTerminationModal', payload);
  };

  // handleTerminationPostModal = () => {
  //   console.log('handleTerminationPostModal')
  //   let payload = this.state.dataFromStatusActions['terminationModal'];
  //   console.log('terminate selection payload', payload)
  //   let membersWithFiles = payload.data.map(data => {
  //     if (data.files.length > 0) {
  //       const dataObject = {
  //         id: data.id,
  //         // file: data.files[0]
  //         file: data.files
  //       }
  //       return dataObject;
  //     } else {
  //       return '';
  //     }
  //   })
  //   console.log('terminate selection payload2', membersWithFiles)
  //   let filteredMemberData = membersWithFiles.filter(data => data !== '');
  //   let finalPayloadData = payload.data.map(data => {
  //     delete data.id;
  //     delete data.files;
  //     return data;
  //   })

  //   this.setState({
  //     loading_state: true,
  //     check_internet_flag: true,
  //     isResetTable: true,
  //   })

  //   if (filteredMemberData.length > 0) {
  //     this.uploadSupportingDocuments(filteredMemberData, finalPayloadData);
  //   } else {
  //     this.processMemberTermination(finalPayloadData);
  //   }
  // }

  handleTerminationPostModal = () => {
    console.log('handleTerminationPostModal');
    let payload = this.state.dataFromStatusActions['terminationModal'];
    console.log('PAYLOADDDDDD', payload)
    let dependentList: any[] = [];
    let membersWithFiles = payload.data.map(data => {
      if (data.files && data.files.length > 0) {
        const dataObject = {
          id: data.id,
          // file: data.files[0]
          file: data.files,
        };
        return dataObject;
      } else {
        return '';
      }
    });
    console.log('terminate selection payload2', membersWithFiles);
    let filteredMemberData = membersWithFiles.filter(data => data !== '');
    let finalPayloadData = payload.data.map(data => {

      console.log('DATTA finalPayload', data)
      // let finalDependent = payload && payload.dependents.length > 0 ? payload.dependents.map(dep => {
      //   console.log('DEPENDENTS', dep)
      //   dependentList.push(dep)
      //   return dependentList
      // }) : null

      // console.log('GET DEPENDENTS', finalDependent)
      // console.log('PRINCIPAL', data)
      // console.log('PRINCIPAL1', data.id)
      // let allIDs = [...dependentList]
      // console.log('GET ALL IDS TO TERMINATE', allIDs)
      // console.log('GET ALL IDS TO TERMINATE', ...dependentList)
     
      // if(allIDs && allIDs.length > 0) {
      //   allIDs.map(item => {
      //     console.log('GET DEPENDENT TO TERMINATE', item)
      //     // delete item
      //     delete item._id
      //     delete data.id;
      //     delete data.files;

      //     console.log('RETURN THIS', data)
      //     console.log('RETURN DEP ID', item._id)
      //     console.log('RETURN THIS DEP SHOLD TERM', item)

         
      //   })

      //   let depData = dependentList && dependentList.length > 0 ? dependentList.map(item => delete item._id ) : console.log('no dependents to terminate')
      //   console.log('WHATTTT', depData)
      //   delete data.id;
      //   delete data.files;
      //   console.log('RETURN THIS PRPINCIPAL', data.id)
      //     return data;
      // }

      let depData = dependentList && dependentList.length > 0 ? dependentList.map(item => delete item._id ) : console.log('no dependents to terminate')
      console.log('get dep', depData)
      delete data.id;
      delete data.files;
      console.log('return principalid', data.id)
      return data;
      
    })
    this.setState({
      loading_state: true,
      check_internet_flag: true,
      isResetTable: true,
    });
    if (filteredMemberData.length > 0) {
      this.uploadSupportingDocuments(filteredMemberData, finalPayloadData);
    } else {
      this.createTerminationTicket(finalPayloadData);
      // this.processMemberTermination(finalPayloadData);
    }
  };

  async uploadSupportingDocuments(memberData: any[], payload: any) {
    const mTitle = 'Supporting Document Upload Failed';
    const mMsg = 'Unable to upload the supporting document(s).';

    try {
      // let promise = await memberData.map(data =>
      //   API.uploadMemberDocument(data.id, 'termination', data.file, undefined, 'client-member', undefined, payload.endorsed_date)
      // )
      console.log('MEMBER DATAS', memberData);
      console.log('MEMBER DATAS PAYLOAD', payload);
      let promise = await this.newProcessUploadSupportingDocuments(
        memberData,
        payload,
      );
      const response = await Promise.all(promise);
      console.log('upload doc api res', response);
      if (response.length > 0 && response[0].error === undefined) {
        this.createTerminationTicket(payload);
        // this.processMemberTermination(payload);
      } else {
        this.terminationFailed(mTitle, mMsg);
      }
    } catch (error) {
      console.log(error);
      this.terminationFailed(mTitle, mMsg);
    }
  }

  newProcessUploadSupportingDocuments = async (memberData, payload) => {
    let errorFlag: any[] = [{ error: undefined }];
    console.log('FINAL MEMBER DATA', memberData);
    console.log('FINAL MEMBER PAYLOAD', payload);
    // for (const _member of payload) {
    for (const _files of memberData) {
      for (const _file of _files.file) {
        await API.uploadMemberDocument(
          _files.id,
          'termination',
          _file,
          undefined,
          'client-member',
          undefined,
          payload.endorsed_date,
        )
          .then(response => {
            if (response && response.error && response.error.message) {
              errorFlag[0].error = true;
            }
          })
          .catch(err => {
            console.log('Upload Document Err - ', err);
          });
      }
    }
    // }
    return errorFlag;
  };

  processMemberTermination = (payload: any) => {
    console.log('processMemberTermination', payload)
    API.memberTerminateBatch(payload).then((response) => {
      if (response && response.error === undefined) {
        this.terminationSuccess();
      } else {
        const mTitle = "Member Termination Failed";
        const mMsg = "An error occured while trying to terminate the selected member(s)."
        this.terminationFailed(mTitle, mMsg);
        // this.terminationSuccess();
      }
    })
  }

  terminationSuccess = () => {
    this.setState({
      loading_state: false,
      check_internet_flag: false,
      isResetTable: false,
      isModalOpen: true,
      modalTitle: 'Member(s) have been Terminated',
      modalMessage: "Member(s) successfully terminated.",
    });
  }

  createTerminationTicket = (payload: any) => {
    Processmaker.post('cases/', {
      pro_uid: '985546387637b43941733a9061380414',
      tas_uid: '758995345656eb7472c1de5058777383',
    }).then(response => {
      if (response.app_uid !== undefined) {
        const pmakerResponse = response;
        const ticketId = pmakerResponse.app_uid;
        API.memberTerminateBatch(payload,this.props.data._id,ticketId).then(response => {
          console.log('batch terminate', response);
          if (response && response.error === undefined) {
            const uploadId:string = response.member_upload_id ? response.member_upload_id : '';
            this.executeTicketTrigger(ticketId,pmakerResponse,uploadId)
            // this.setTicketVariable(ticketId,pmakerResponse,uploadId);
          } else {
            const mTitle = 'Member Termination Failed';
            const mMsg =
              'An error occured while trying to terminate the selected member(s).';
            this.terminationFailed(mTitle, mMsg);
          }
        });
      }
    }).catch((e: Error) => {
      console.log('Check Terminate Ticket Error: ', e);
      const mTitle = 'Member Termination Failed';
      const mMsg =
        'An error occured while trying to terminate the selected member(s).';
      this.terminationFailed(mTitle, mMsg);
    });
    
  };

  setTicketVariable(appUid: string, pmaker_response: any, memberUploadId:any) {

    Processmaker.put('cases/' + appUid + '/variable', {
      client_id: this.props.data._id,
      client_name: this.props.data.registered_name,
      member_upload_id: memberUploadId,
      pressed_reassign: true,
    }, false)
      .then(response => {
        Processmaker.put('cases/' + appUid + '/route-case', {})
        .then(() => {
          console.log('setTicketVariable', response);
          let payload = {
            status: 'UNASSIGNED',
            ticket_id: pmaker_response.app_number.toString(),
            pmaker_case_uid: appUid,
            ticket_type: "Verify Termination",
            client: this.props.data.registered_name,
            client_id: this.props.data._id
          };
          API.postTicket(payload, false)
            .then(() => {
              console.log('postTicket: Verify Member');
              this.setState({
                loading_state: false,
                check_internet_flag: false,
                isResetTable: false,
                isModalOpen: true,
                modalTitle: 'Member(s) have been Terminated',
                modalMessage: 'Member(s) successfully terminated.',
              });
            })
            .catch(() => { 
              const mTitle = 'Member Termination Failed';
              const mMsg =
                'An error occured while trying to terminate the selected member(s).';
              this.terminationFailed(mTitle, mMsg);
            });
        })
      })
      .catch(e => {
        console.log('setTicketVariable', e);
        const mTitle = 'Member Termination Failed';
        const mMsg =
          'An error occured while trying to terminate the selected member(s).';
        this.terminationFailed(mTitle, mMsg);
      });
  }

  executeTicketTrigger(appUid: string, pmaker_response: any, memberUploadId:any) {
    Processmaker.put(
      'cases/' + appUid + '/execute-trigger/833912817637ca2e000fc11085185718',
      {},
    )
      .then(response => {
        console.log('Check Execute Trigger: ', response);
        this.setTicketVariable(appUid,pmaker_response,memberUploadId);
      })
      .catch(e => {
        console.log('Check Execute Trigger Error: ', e);
        const mTitle = 'Member Termination Failed';
        const mMsg =
          'An error occured while trying to terminate the selected member(s).';
        this.terminationFailed(mTitle, mMsg);
      });
  }

  terminationFailed = (title: string, message: string) => {
    this.setState({
      loading_state: false,
      check_internet_flag: false,
      isResetTable: false,
      isModalOpen: true,
      modalTitle: title,
      modalMessage: message,
    });
  };

  getMemberDataCF = memberDataCF => {
    let memberId = memberDataCF.id;

    console.log('getMemberDatCF memberId', memberId);
    console.log('memberDataCF', memberDataCF);
  };
  //change member status to for valid if no data in req client memberfields
  handleClientFieldForValid = memberDataCF => {
    console.log('members memberDataCF', memberDataCF);

    let memberId = memberDataCF.id;
    console.log('handleclientfieldforvalid id', memberId);
    const payload = {
      member_status: 'For Validation',
      status: 'For Validation',
    };

    console.log('handleclientfieldforvalid payload', payload);
    this.updateMemberStatus(payload, memberDataCF);

    // console.log('func return', payload)
  };

  //main func to update status
  updateMemberStatus = (payload, memberDataCF) => {
    clearTimeout(this.state.timeoutHandler);
    let memberId = memberDataCF.id;

    if (memberId) {
      console.log('gottcha memerId', memberId);
      this.setState({
        timeoutHandler: setTimeout(() => {
          this.setState({
            loading: true,
          });
          console.log('memberid', memberId);
          console.log('updateMemberStatus11', payload);

          if (memberId) {
            console.log('memberId is true', memberId);
            this.changeMemberStatus(payload, memberDataCF);
          } else {
            console.log('memberDataCF');
          }
        }, 250),
      });
    }

    console.log('updateMemberStatus1', memberDataCF);
  };

  handleOpenModalProps = (title: string, message: string) => {
    this.setState({
      isExpiredClientModalOpen: true,
      modalTitle: title,
      modalMessage: message,
    })
  }

  handleClosenModalProps = () => {
    this.setState({
      isExpiredClientModalOpen: false,
      modalTitle: '',
      modalMessage: '',
    })
  }

  changeMemberStatus = (payload, memberDataCF) => {
    let memberId = memberDataCF.id
    this.setState({
      check_internet_flag: true,
    });
    API.postChangeMemberStatus(payload, memberId).then(response => {
      console.log('POSTCHANGESTATUS!', response);
      this.setState({
        loading: false,
        check_internet_flag: false,
      });
      if (
        response.error &&
        response.error !== undefined &&
        response.error !== null
      ) {
        this.setState({
          openSnackbar: true,
          variant: 'error',
          snackbarMessage: response.error.message,
        });
      } else {
        this.setState({
          openSnackbar: true,
          variant: 'success',
          snackbarMessage: 'Successfully updated status of member.',
        });
        console.log('GETMEMBERDATACF!', memberDataCF);
        this.getMemberDataCF(memberDataCF);
      }
    });
    // this.setState({
    //   check_internet_flag: false,
    // })
  };

  // public 
  public render(): JSX.Element {
    const {
      loading_state,
      member_data,
      global_filter,
      redirect_to_member,
      member_id,
      default_filters,
      isModalOpen,
      modalTitle,
      modalMessage,
      totalCount,
      // selectedDependents,
      // dependentSelected,
      selectedCount,
      statusCount,
      applyToAllFlags,
      memberStatusActionArrays,
      memberStatusActionFlags,
      memberStatusActionConfirmFlags,
      dataFromStatusActions,
      displayDataFromStatusActions,
      page,
      limit,
      isResetTable,
      isPageSizeModal,
      batchProcessActions,
      renewProcessActions,
      status_filters,
      isExpiredClientModalOpen,
    } = this.state;

    if (redirect_to_member === true) {
      console.log('redirect func1', this.state)
      console.log('redirect func2', this.props)
      console.log('redirect func3', this.state.member_id)

      if(this.state.member_id) {
        API.getMemberData(this.state.member_id)
        .then(res => {
          console.log('response member data', res)
          let memberData = res
          let clientId = res['client_id']
        
            console.log('memberdata test', memberData)
            console.log('clientid test', clientId)
            API.getMembersByClientId(10, 0, clientId)
            .then(response => {
              console.log('responssss1', response)
            })
        })
      } else {
        console.log('errror testing', this.props.memberData)
      }
     
        
      return (
        <Redirect
          to={{
            // pathname: '/membership/view-member/' + member_id + '/' + page + '/new',
            pathname: '/membership/view-client-member/' + member_id,
            state: { from: this.props.location },
          }}
          // memberFields={this.props.memberFields}
        />
      );
    } else if (batchProcessActions['isBatchPrintIdCards'] === true) {
      let cleanedClientName = this.props.data.registered_name
        ? this.props.data.registered_name.split('%').join('%25')
        : '';

      return (
        <Redirect
          to={`/membership/process-batches/${encodeURI(cleanedClientName)}/${
            this.props.data._id
          }`}
        />
      );
    } else if (batchProcessActions['isGenerateTransmittalAction'] === true) {
      let cleanedClientName = this.props.data.registered_name
        ? this.props.data.registered_name.split('%').join('%25')
        : '';
      return (
        <Redirect
          to={{
            pathname: `/membership/process-batches-transmittal-request/transmittal_request/${encodeURI(
              cleanedClientName,
            )}/${this.props.data._id}`,
            state: { from: this.props.location },
          }}
        />
      );
    } else if (batchProcessActions['isBatchTerminateAction'] === true) {
      return (
        <Redirect
          to={{
            pathname: `/membership/clients-profile/batch-terminate/${this.props.data._id}`,
            // `/membership/encode/terminate/${this.props.data._id}`,
            state: { from: this.props.location },
          }}
        />
      );
    } else if (batchProcessActions['isBatchSuspendAction'] === true) {
      return (
        <Redirect
          to={{
            pathname:
              '/membership/clients-profile/batch-suspend/' +
              this.props.data._id,
            state: { from: this.props.location },
          }}
        />
      );
    } else if (batchProcessActions['isBatchCorrectionAction'] === true) {
      return (
        <Redirect
          to={{
            pathname:
              '/membership/clients-profile/batch-correction/' +
              this.props.data._id,
            state: { from: this.props.location },
          }}
        />
      );
    } else if (batchProcessActions['isBatchRenewAction'] === true) {
      return (
        <Redirect
          to={{
            pathname:
              '/membership/clients-profile/batch-renewal/' +
              this.props.data._id,
            state: { from: this.props.location },
          }}
        />
      );
    } else if (batchProcessActions['isVoidMasterlistAction'] === true) {
      return (
        <Redirect
          to={{
            pathname: `/membership/encode/void/${this.props.data._id}`,
            state: { from: this.props.location },
          }}
        />
      );
    }  else if (renewProcessActions['isManualRenewal'] === true) {
      return (
        <Redirect
          to={{
            pathname:
              '/membership/clients-profile/manual-renewal/selection/' +
              this.props.data._id,
            state: { from: this.props.location },
          }}
        />
      );
    } else {
      return (
        <div className={clsx('MemberPage')}>
          {loading_state ? <Loader /> : null}
          <Grid container className={clsx('member-header')}>
            <Grid item xs={12}>
              <PageHeaderComponent
                id={'member_page_stepper'}
                label="Members"
                steps={[]}
                activeStep={0}
              />
            </Grid>
          </Grid>
          <Grid container alignItems="stretch">
            <Grid item xs={12} style={{ marginBottom: 8 }}>
              <Grid container>
                {/* <Grid item xs={12} md={6} /> */}
                <Grid item xs={12}>
                  <Grid
                    container
                    direction="row"
                    justify="flex-end"
                    alignItems="stretch"
                  >
                    <Grid item>
                      <MemberPageActionBtns
                        //clientId={this.props.data._id ? this.props.data._id : ''}
                        clientId={this.props.data._id ? this.props.data._id : ''}
                        clientStatus={this.props.data.status ? this.props.data.status : ''}
                        memberFields={this.props.memberFields ? this.props.memberFields : ''}
                        onBatchProcessAction={this.handleBatchProcessActions}
                        onRenewProcessAction={this.handleRenewProcessActions}
                        onExportIdPicture={this.handleExportIdPicture}
                        showModalPopUp = {(title: string, message: string )=> {
                          this.handleOpenModalProps(title, message)
                        }}
                        memberTotal={this.state.totalCount}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container>
                <Grid item xs={12}>
                  <TextField
                    data-cy="members_search"
                    style={{ width: '100%' }}
                    id="members_search"
                    placeholder="Search members by typing their member ID, name, etc."
                    label=""
                    margin="normal"
                    variant="outlined"
                    value={global_filter}
                    onChange={this.handleSearch}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search style={{ color: 'rgba(54, 54, 54, 0.4)' }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} style={{ paddingTop: '32px' }}>
              <Grid
                container
                md={12}
                spacing={2}
                style={{
                  backgroundColor: '#fafcfe',
                  padding: '20px 10px 12.5px',
                }}
              >
                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.totalCount !== undefined
                        ? this.state.totalCount
                        : this.state.totalCount}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      TOTAL
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.forPrintingCount}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      FOR PRINTING
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.statusPanelCount['Active'] !== undefined
                        ? this.state.statusPanelCount['Active']
                        : 0}
                      {/* {this.state.dependentCount !== undefined ? this.state.dependentCount : 0} */}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      ACTIVE
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.statusPanelCount['Awaiting Activation'] !==
                      undefined
                        ? this.state.statusPanelCount['Awaiting Activation']
                        : 0}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      AWAITING ACTIVATION
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.statusPanelCount['For Validation'] !==
                      undefined
                        ? this.state.statusPanelCount['For Validation']
                        : 0}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      FOR VALIDATION
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.statusPanelCount['Suspended'] !== undefined
                        ? this.state.statusPanelCount['Suspended']
                        : 0}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      SUSPENDED
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs>
                  <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: 20,
                        color: '#5bc393',
                        paddingRight: 8,
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      {this.state.statusPanelCount['Terminated'] !== undefined
                        ? this.state.statusPanelCount['Terminated']
                        : 0}
                    </Typography>
                    <Typography
                      align={'center'}
                      style={{
                        fontSize: '12px',
                        textTransform: 'uppercase',
                        fontWeight: 600,
                        display: 'inline-block',
                      }}
                    >
                      TERMINATED
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Grid item xs={12} style={{ paddingTop: '32px' }}>
                <MemberStatusActionButtons
                  selectedCount={selectedCount}
                  statusCount={statusCount}
                  onStatusActionClick={this.handleMemberStatusAction}
                  currentUserData={this.state.currentUserData}
                  clientsProfile={this.props.data}
                />
              </Grid>
              <MembersTable
                id="tab-all"
                data-cy="tab-all"
                isRemoteMode={true}
                totalCount={
                  member_data &&
                  member_data['rows'] &&
                  member_data['rows'].length > 0 &&
                  GlobalFunction.checkUserRbacByPolicy(
                    'MS14',
                    this.state.currentUserData,
                    this.state.loading_state,
                  )
                    ? totalCount
                    : 0
                }
                updateSorting={this.handleChangeSorting}
                rows={
                  Object.keys(member_data).length > 0 &&
                  GlobalFunction.checkUserRbacByPolicy(
                    'MS14',
                    this.state.currentUserData,
                    this.state.loading_state,
                  )
                    ? member_data['rows']
                    : []
                }
                columns={
                  Object.keys(member_data).length > 0
                    ? member_data['columns']
                    : []
                }
                message={
                  global_filter ? 'No available data' : 'No available data'
                }
                onClickRow={this.handleClickRow} // {this.handlePreClickRow} //{this.handleClickRow}
                singleSelect={false}
                disableSelect={false}
                showSelectionColumn={false}
                onDataSelection={this.handleDataFromCheckbox}
                disableSearch
                formattedColumns={
                  Object.keys(member_data).length > 0
                    ? member_data['formattedColumns']
                    : {}
                }
                columnExtensions={
                  Object.keys(member_data).length > 0
                    ? member_data['columnExtensions']
                    : []
                }
                filterExtensions={
                  Object.keys(member_data).length > 0
                    ? member_data['filterExtensions']
                    : []
                }
                integratedFilteringColumnExtensions={
                  Object.keys(member_data).length > 0
                    ? member_data['integratedFilteringColumnExtensions']
                    : []
                }
                integratedSortingColumnExtensions={
                  Object.keys(member_data).length > 0
                    ? member_data['integratedSortingColumnExtensions']
                    : []
                }
                onFilterChange={this.handleFilterByColumn}
                onSortingChange={this.handleChangeSorting}
                onPageChange={this.handlePageChange}
                onPageSizeChange={this.handlePageSizeChange}
                onStatusFilterUpdate={(data: any[]) => {
                  this.setState({
                    status_filters: data,
                  });
                }}
                defaultFilter={
                  default_filters.length > 0
                    ? default_filters
                        .map(dfData => {
                          if (
                            dfData['columnName'] &&
                            dfData['columnName'] === 'member_status'
                          ) {
                            return null;
                          } else {
                            return dfData;
                          }
                        })
                        .filter(currData => currData !== null)
                    : []
                }
                statusFilter={status_filters}
                currentPage={page}
                limit={limit}
                isResetTable={isResetTable}
                allowedStatusAction={{
                  forSuspension: GlobalFunction.checkUserRbacByPolicy(
                    'MS24',
                    this.state.currentUserData,
                    this.state.loading_state,
                  ),
                  forTermination: GlobalFunction.checkUserRbacByPolicy(
                    'MS26',
                    this.state.currentUserData,
                    this.state.loading_state,
                  ),
                  forValidation: GlobalFunction.checkUserRbacByPolicy(
                    'MS20',
                    this.state.currentUserData,
                    this.state.loading_state,
                  ),
                }}
              />
            </Grid>
          </Grid>

          {/* Member Status Action Modals -----  Client Table */}

          <MemberSuspensionModal
            id="member_suspension_modal"
            data-cy="member_suspension_modal"
            isModalOpen={memberStatusActionFlags['isMemberSuspension']}
            memberData={memberStatusActionArrays['forSuspension']}
            // dependentsData={memberStatusActionArrays['forSuspensionDependents']}
            onStatusAction={this.handleMemberSuspension}
            onClose={() => {
              this.closeMemberStatusActionModal('suspensionModal');
            }}
          />
          {/* {console.log('SELECTEDDEPENDENTS11', selectedDependents['suspensionModal'])}
          {console.log('SELECTEDDEPENDENTS22', selectedDependents)}
          {console.log('THISSSS', dataFromStatusActions['dependentSelected'])} */}

          <ConfirmMemberSuspensionModal
            id="confirm_member_suspension_modal"
            data-cy="confirm_member_suspension_modal"
            isModalOpen={memberStatusActionConfirmFlags['isConfirmSuspension']}
            isApplyToAll={applyToAllFlags['suspension']}
            memberData={dataFromStatusActions['suspensionModal']}
            // dependents={selectedDependents['suspensionModal']}
            // dependentsData={memberStatusActionArrays['forSuspensionDependents']}
            dependents={dataFromStatusActions['dependentSelected']}
            onConfirmAction={this.handleConfirmMemberSuspension}
            onClose={() => {
              this.closeMemberStatusActionConfirmModal(
                'confirmSuspensionModal',
              );
            }}
          />
          <CancelSuspensionModal
            id="cancel_suspension_modal"
            data-cy="cancel_suspension_modal"
            isModalOpen={memberStatusActionFlags['isMemberCancelSuspension']}
            memberData={memberStatusActionArrays['forCancelSuspension']}
            onStatusAction={this.handleMemberCancelSuspension}
            onClose={() => {
              this.closeMemberStatusActionModal('cancelSuspensionModal');
            }}
          />
          <ConfirmCancelSuspensionModal
            id="confirm_cancel_suspension_modal"
            data-cy="confirm_cancel_suspension_modal"
            isModalOpen={
              memberStatusActionConfirmFlags['isConfirmCancelSuspension']
            }
            memberData={dataFromStatusActions['cancelSuspensionModal']}
            clientData={this.props.data}
            onConfirmAction={this.handleConfirmCancelSuspension}
            onClose={() => {
              this.closeMemberStatusActionConfirmModal(
                'confirmCancelSuspensionModal',
              );
            }}
          />
          <LiftSuspensionModal
            id="member_lift_suspension_modal"
            data-cy="member_lift_suspension_modal"
            isModalOpen={memberStatusActionFlags['isMemberLiftSuspension']}
            memberData={memberStatusActionArrays['forLiftSuspension']}
            onStatusAction={this.handleMemberLiftSuspension}
            onClose={() => {
              this.closeMemberStatusActionModal('liftSuspensionModal');
            }}
          />
          <ConfirmLiftSuspensionModal
            id="confirm_lift_suspension_modal"
            data-cy="confirm_lift_suspension_modal"
            isModalOpen={
              memberStatusActionConfirmFlags['isConfirmLiftSuspension']
            }
            isApplyToAll={applyToAllFlags['liftSuspension']}
            memberData={dataFromStatusActions['liftSuspensionModal']}
            onConfirmAction={this.handleConfirmMemberLiftSuspension}
            onClose={() => {
              this.closeMemberStatusActionConfirmModal(
                'confirmLiftSuspensionModal',
              );
            }}
          />
          {memberStatusActionFlags['isMemberTermination'] ? (
            <MemberTerminateBatchesModal
              id="member_terminate_batches_modal"
              isModalOpen={memberStatusActionFlags['isMemberTermination']}
              memberData={memberStatusActionArrays['forTermination']}
              clientId={this.state.client_id}
              clientData={this.props}
              onSubmit={(payload: any) => {
                this.handleSubmitTerminationModal(payload);
              }}
              onClose={() => {
                this.setState({
                  memberStatusActionFlags: {
                    isMemberTermination: false,
                  },
                });
              }}
            />
          ) : null}
          <MemberReactivateModal
            id="member_reactivation_modal"
            data-cy="member_reactivation_modal"
            isModalOpen={memberStatusActionFlags['isMemberReactivation']}
            memberData={memberStatusActionArrays['forReactivation']}
            onStatusAction={this.handleMemberReactivation}
            onClose={() => {
              this.closeMemberStatusActionModal('reactivationModal');
            }}
          />
          <ConfirmReactivateModal
            id="confirm_reactivate_modal"
            isModalOpen={
              memberStatusActionConfirmFlags['isConfirmReactivation']
            }
            isApplyToAll={applyToAllFlags['reactivation']}
            displayData={displayDataFromStatusActions['reactivationModal']}
            payloadData={dataFromStatusActions['reactivationModal']}
            onConfirmAction={this.handleConfirmReactivation}
            onClose={() => {
              this.closeMemberStatusActionConfirmModal(
                'confirmReactivationModal',
              );
            }}
          />
          <TerminateMemberConfirmationModal
            id={'member_termination_confirmation_modal'}
            isModalOpen={memberStatusActionConfirmFlags['isMemberTermination']}
            terminationData={dataFromStatusActions['terminationModal']}
            clientData={memberStatusActionArrays['forTermination']}
            onTerminate={this.handleTerminationPostModal}
            onClose={() => {
              this.setState({
                memberStatusActionConfirmFlags: {
                  isMemberTermination: false,
                },
              });
            }}
          />
          <MemberCancelTerminationModal
            id="member_cancel_termination_modal"
            isModalOpen={memberStatusActionFlags['isMemberCancelTermination']}
            memberData={memberStatusActionArrays['forCancelTermination']}
            clientData={this.props.data}
            onStatusAction={this.handleMemberCancelTermination}
            onClose={() => {
              this.closeMemberStatusActionModal('cancelTerminationModal');
            }}
          />

          {/* End of Member Status Action Modals */}

          <ModalComponent
            id="member-list-modal"
            isModalOpen={isModalOpen}
            title={modalTitle}
            message={modalMessage}
            onClose={this.closeModal}
          />
          <ModalComponent
            id="expired-client-modal"
            isModalOpen={isExpiredClientModalOpen}
            title={modalTitle}
            message={modalMessage}
            onClose={this.handleClosenModalProps}
          />
          <ModalComponent
            id="member-list-modal-page-size"
            isModalOpen={isPageSizeModal}
            title={'Error'}
            message={'Too many entries to display.'}
            onClose={this.handlePageSizeModalClose}
          />
          <LostInternetModal
            id="Lost Internet Modal"
            isModalOpen={this.state.check_internet_flag}
            onClose={this.handleCloseLostInternetConnectionModal}
          />
          <SuccessSuspensionModal
            id=""
            isModalOpen={this.state.isSuccessSuspensionModalOpen}
            message={this.state.modalMessage}
            title={this.state.modalTitle}
            memberData={memberStatusActionArrays['forSuspension']}
            allMembers={this.state.allMemberDisplay}
            onClose={() => {
              this.setState({
                ...this.state,
                isSuccessSuspensionModalOpen: false,
              });
              this.searchMembers();
            }}
          />
          <ReactivateMemberErrorModal
            id=""
            isModalOpen={this.state.isMemberReactivationError}
            message={modalMessage}
            title={modalTitle}
            memberData={this.state.memberReactivationErrorData}
            onClose={() => {
              this.setState({
                isMemberReactivationError: false,
              });
            }}
          />
        </div>
      );
    }
  }
}
const mapStateToProps = (state: Store) => state.home;

const mapDispatchToProps = (dispatch: Dispatch) => ({
  Map: bindActionCreators(MemberActions.Map, dispatch),
});

export { mapStateToProps, mapDispatchToProps };
