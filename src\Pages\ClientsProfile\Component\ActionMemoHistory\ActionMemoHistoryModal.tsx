import React, { useEffect } from 'react';
import moment from 'moment';

import { makeStyles } from '@material-ui/core/styles';
// import { Tabs, Tab, Button, Grid, } from '@material-ui/core';
import {
    DialogContent,
    Dialog,
    DialogTitle,
    Divider,
    IconButton,
    Box,
    Link
} from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import * as Excel from 'exceljs';
import { saveAs } from 'file-saver';
import _ from 'lodash';


// import * as XLSX from 'xlsx';
// import { number } from 'prop-types';


const useStyles = makeStyles(theme => ({
    sectionTitle: {
        fontSize: 24,
        fontWeight: 700,
        color: '#272e4c',
        marginBottom: '1em'
    },
    emailContent: {
        marginTop: "30px",
        minHeight: "400px",
        padding: "70px",
        margin: "20px",
    },
    dialog: {
        align: 'center',
        justify: 'center',
        fontFamily: 'usual',
    },
    closeButton: {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
    dialogTitle: {
        paddingTop: theme.spacing(5),
        minWidth: 500,
    },
    dialogFirstTitle: {
        fontSize: '2rem',
        color: '#272E4C',
        fontWeight: 600,
        marginBottom: '0.8em',
    },
    dialogSecondTitle: {
        fontSize: '0.9em',
        color: '#272E4C',
        marginBottom: '0.8em',
        fontWeight: 600,
    },
    dialogRecipients: {
        fontSize: '0.9em',
        color: '#272E4C',
        fontWeight: 400
    },
    dialogAttachment: {
        fontSize: '0.9em',
        color: '#0D5E40',
        cursor: 'pointer'
    },
    dialogContent: {
        paddingTop: '15px',
    },
}));

export const ActionMemoHistoryModal = ({
    isOpenModal,
    title,
    directs,
    ccs,
    body,
    exceptionReports,
    handleCloseModal
}) => {
    const classes = useStyles();
    //@ts-ignore


    const [modalProps, setModalProps] = React.useState({
        title: '',
        open: false,
        method: () => { }
      });
      // const [modalPropsMessage, setModalPropsMessage] = React.useState<any>();

    // const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    // const fileExtension = '.xlsx';
    
    useEffect(() => {
        console.log('Action Memo Modal Props: ', isOpenModal, title, directs, ccs, exceptionReports)
            // if(directs !== undefined || directs !== '') {
            //     console.log('directs is not undefined', directs)
            //     // directs?.join(', ')
            //     directs + ', '
            // } else {
            //     console.log('directs is  undefined', directs)
            //     directs
            // }

            // if(ccs !== undefined || ccs !== '') {
            //     // ccs.join(', ')
            //      ccs + ', '
            // } else {
            //     ccs
            // }
    }, [isOpenModal]);


//ann refactor
    //partialFormatRow
    const partialFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.similar_fields ? data?.member_details?.similar_fields : data?.similar_fields,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

  //incompleteFormatRow
    const incompleteFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //conflictFormatRow
    const conflictFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //unmatchedFormatRow
    const unmatchedFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.member_details.principal_name ? data.member_details.principal_name : data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //supportingFormatRow
    const supportingFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //othervalidFormatRow
    const othervalidFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //disapprovedFormatRow
    const disapprovedFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }

    //approvedFormatRow
    const approvedFormatRow = (data:any, isPartial?:boolean, rowCount?:any, possibleMatchNames?:any, possibleMatchBatchNo?:any, possibleMatchBirthdate?:any) => {
        // console.log('action_memo1', data, isPartial, rowCount, possibleMatchNames,possibleMatchBatchNo, possibleMatchBirthdate  )
        return {
                no:rowCount ? rowCount : '',
                batch_name: data?.member_details?.batch_name ? data?.member_details?.batch_name : data?.batch_name,
                first_name: data?.member_details?.first_name ? data?.member_details?.first_name : data.first_name,
                middle_name: data?.member_details?.middle_name ? data?.member_details?.middle_name : data.middle_name,
                last_name: data?.member_details?.last_name ? data?.member_details?.last_name : data.last_name,
                member_name:data?.member_name,
                plan_type:data.plan_type,
                member_account_no: data?.member_details?.member_account_no ? data?.member_details?.member_account_no : data.member_account_no,
                date_of_birth:  data?.member_details?.date_of_birth ? moment(data?.member_details?.date_of_birth).format('MM/DD/YYYY') : moment(data.date_of_birth).format('MM/DD/YYYY'),
                member_type:  data?.member_details?.member_type ? data?.member_details?.member_type : data.member_type,
                gender:   data?.member_details?.gender ? data?.member_details?.gender : data.gender, 
                effectivity_date:  data?.member_details?.effectivity_date ? moment(data?.member_details?.effectivity_date).format('MM/DD/YYYY') : moment(data.effectivity_date).format('MM/DD/YYYY'), 
                civil_status:  data?.member_details?.civil_status ? data?.member_details?.civil_status : data.civil_status, 
                relationship_to_principal:data.relationship_to_principal,
                principal_name:data.principal_name,
                system_remarks: data?.member_details?.remarks ? data?.member_details?.remarks : data?.remarks,
                remarks: data?.member_details?.user_remarks ? data?.member_details?.user_remarks : data.user_remarks,
                similar_fields: data?.similar_fields ? data?.similar_fields : '',
                missing_fields: data?.missing_fields ? data?.missing_fields : '',
                possible_match_name: possibleMatchNames ? possibleMatchNames : '',
                possible_match_batch_no: possibleMatchBatchNo ? possibleMatchBatchNo : '',
                possible_match_birthdate: possibleMatchBirthdate ? possibleMatchBirthdate : '',
                }
    }



      // const handleCloseModalProps = () => {
      //   setModalProps({
      //     ...modalProps,
      //     open: false
      //   })
      // };

      // const getMappedPartial = (json) => {
      //   const temp = json.map((row, idx) => (
      //     {
      //     'No': idx + 1,
      //     'Batch no.': row.member_details.batch_name,
      //     'First Name': row.first_name,
      //     'Middle Name': row.middle_name,
      //     'Last Name': row.last_name,
      //     'Suffix': row.suffix,
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': row.gender,
      //     'Civil Status': row.civil_status,
      //     'Member Type': row.member_type,
      //     'Plan Type': row.plan_type,
      //     'Relationship': row.relationship_to_principal,
      //     'Name of Principal': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      //     'Similar Fields': row.similar_fields ? row.similar_fields : '',
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }
      //   ));
    
      //   let memberDetails;
      //   // if (partial !== null) {
      //   //   console.log('partial not empty', partial)
      //   //   const matchedProfileRows = partial.rows
      //   //   const matchedProfile = matchedProfileRows.member_details
      //   //   console.log('MatchedProfileRows', matchedProfileRows)
      //   //   console.log('matchedProfile', matchedProfile)
    
      //   //   matchedProfileRows.forEach(item => {
      //   //     console.log('ITEMM1', item)
      //   //     memberDetails = item.member_details
      //   //     const matchedMemberData = memberDetails.validation_status.matched_member
      //   //     console.log('Matched Profile matchedMember', memberDetails)
      //   //     console.log('matchedMemberData', matchedMemberData)
    
      //   //     return memberDetails
      //   //   })
          
      //   // console.log('getMappedPartial temp', temp)
      //   // console.log('temp2 memberDetails', memberDetails)
      //   // // } else {
      //   // //   console.log('partial is empty', partial)
      //   // }  
      //   const existingMemberBatchNo = memberDetails.match_batchnames.existing_member;
      //   const existingMemberNo = temp[0]['No'];
      //   const existingMemberSimilarFields = temp[0]['Similar Fields'];
    
      //   const temp2 = json.map((memberDetails) => ({
      //     'No': existingMemberNo,
      //     'Batch no.': existingMemberBatchNo,
      //     'First Name': memberDetails.first_name,
      //     'Middle Name': memberDetails.middle_name,
      //     'Last Name': memberDetails.last_name,
      //     'Suffix': memberDetails.suffix,
      //     'Birthdate': memberDetails && memberDetails.date_of_birth ? moment(memberDetails.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': memberDetails.gender,
      //     'Civil Status': memberDetails.civil_status,
      //     'Member Type': memberDetails.member_type,
      //     'Plan Type': memberDetails.plan_type,
      //     'Relationship': memberDetails.relationship_to_principal,
      //     'Name of Principal': memberDetails.principal_name,
      //     'Similar Fields': existingMemberSimilarFields,
      //     'System Remarks': memberDetails && memberDetails.remarks ? memberDetails.remarks : '',
      //     'Remarks': memberDetails && memberDetails.user_remarks ? memberDetails.user_remarks : ''
      //   }))
    
    
      //   const temp3 = [...temp, ...temp2]
    
      //   console.log('getMappedPartial temp1', temp)
      //   // console.log('getMappedPartial temp2', temp2)
      //   // console.log('getMappedPartial temp3', temp3)
      //   return temp3
      // }
    
      // const getMappedIncomplete = (json) => {
      //   console.log('MAPPED FILE INCOMPLETE FILE DOWNLOAD', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': `${row.first_name} ${row.middle_name} ${row.last_name}${row.suffix ? `, ${row.suffix}` : ''}`,
      //     'Member Type': row.member_type,
      //     'Plan Type': row.plan_type,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details.principal_first_name}, ${row.member_details.principal_middle_name}` : '',
      //     'Missing Fields': row.missing_fields,
      //     'System Remarks': row.missing_fields ? row.missing_fields : '', //row.incomplete_action !== '' ? row.incomplete_action : row.user_remarks,
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }));
    
      //   console.log('getMappedIncomplete temp', temp)
      //   return temp
      // }
    
      // const getMappedConflict = (json) => {
      //   console.log('MAPPED FILE CONFLICT FILE DOWNLOAD', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': row.member_name,
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': row.gender,
      //     'Member Type': row.member_type,
      //     'Civil Status': row.civil_status,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      //     'Plan Type': row.plan_type,
      //     // 'Supporting Documents Needed': '',
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }));
    
      //   console.log('getMappedConflict temp', temp)
      //   return temp
      // }
    
      // const getMappedUnmatched = (json) => {
      //   console.log('MAPPED FILE UNMATCHED FILE DOWNLOAD', json)
      //   console.log('JSON Unmatched', json)
    
      //   const temp = json.map((row, idx) => {
      //     let newObj = {
      //       'No': idx + 1,
      //       'Member Name': row.member_name,
      //       'Relationship': row.relationship_to_principal,
      //       'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details.principal_middle_name}` : '',
      //       'System Remarks': row && row.remarks ? row.remarks : '',
      //       'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //     }
    
      //     let possibleMatchNames: any[] = [];
      //     let possibleMatchBatchNo: any[] = [];
      //     let possibleMatchBirthdate: any[] = [];
    
      //     row.member_details.possible_principals.map(principal => {
      //       possibleMatchNames.push(`${principal.first_name} ${principal.middle_name} ${principal.last_name}`)
      //       possibleMatchBatchNo.push(principal.batch_name)
      //       possibleMatchBirthdate.push(principal.date_of_birth ? moment(principal.date_of_birth).format('MM/DD/YYYY') : '')
      //     })
    
      //     newObj['Possible Match Name'] = possibleMatchNames;
      //     newObj['Possible Match Batch No'] = possibleMatchBatchNo;
      //     newObj['Possible Match Birthdate'] = possibleMatchBirthdate;
    
      //     return newObj;
    
      //   })
    
      //   return temp
      // }
    
      // const getMappedValidation = (json) => {
      //   console.log('MAPPED FILE OTHER VALID FILE DOWNLOAD', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': row.member_name,
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': row.gender,
      //     'Member Type': row && row.member_type ? row.member_type : '',
      //     'Civil Status': row.civil_status,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      //     'Plan Type': row.plan_type,
      //     // 'Supporting Documents Needed': '',
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }));
      //   return temp
      // }
    
      
      // const getMappedSupporting = (json) => {
      //   console.log('MAPPED FILE SUPPORTING FILE DOWNLOAD', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': row.member_name,
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': row.gender,
      //     'Civil Status': row.civil_status,
      //     'Member Type': row.type,
      //     'Plan Type': row.plan_type,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name':row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      //     // 'Supporting Documents Needed': '',
      //     // 'Remarks': row.remarks,
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }));
      //   return temp
      // }
    
      // const getMappedDisapproved = (json) => {
      //   console.log('MAPPED FILE DISAPPROVED FILE DOWNLOAD', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': row.member_name,
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Gender': row && row.gender ? row.gender : '',
      //     'Member Type': row && row.member_type ? row.member_type : '', 
      //     'Civil Status': row.civil_status,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      //     'Plan Type': row && row.plan_type ? row.plan_type : '',
      //     // 'Supporting Documents Needed': '',
      //     // 'System Remarks': 'Disapproved by Verifier',
      //     // 'System Remarks':  row.member_details.validation_status.user_remarks, //row.member_details.validation_status.message,
      //     // 'Remarks': row.member_details.validation_status.user_remarks,
      //     // 'Remarks': row.remarks,
      //     // 'Remarks': row.user_remarks ? row.user_remarks : ''
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_validation ? row.user_validation : ''
      //   }));
      //   return temp
      // }
    
      // const getMappedApproved = (json) => {
      //   // console.log('MAPPED FILE APPROVED FILE DOWNLOAD', json)
      //   console.log('debugging getMappedApproved', json)
      //   const temp = json.map((row, idx) => ({
      //     'No': idx + 1,
      //     'Member Name': row.member_name,
      //     'Plan Type': row.plan_type,
      //     'Account Number': '',
      //     'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      //     'Member Type': row.member_type,
      //     'Gender': row.gender,
      //     'Effectivity Date': row.effectivity_date ? moment(row.effectivity_date).format('MM/DD/YYYY') : '',
      //     'Civil Status': row.civil_status,
      //     'Relationship': row.relationship_to_principal,
      //     'Principal Name': row && row.principal_name ? row.principal_name : '',
      //     'System Remarks': row && row.remarks ? row.remarks : '',
      //     'Remarks': row && row.user_remarks ? row.user_remarks : ''
      //   }));
    
      //   console.log('debugging getMappedApproved2222', temp)
      //   return temp
      // }  


      // const downloadNotificationModal = (err, success, cancelled?: boolean) => {
      //   setModalProps({
      //     title: 'Download Report',
      //     open: true,
      //     method: handleCloseModalProps,
      //   })
    
      //   if (cancelled) {
      //     setModalPropsMessage('Downloading file has been cancelled')
      //   } else {
      //     if (err.length > 0) {
      //       if (success.length > 0) {
      //         if (success.length == 7)
      //           setModalPropsMessage(
      //             <div>
      //               <span>
      //                 Successfully downloaded the file.
      //               </span>
      //             </div>
      //           )
      //         else
      //           setModalPropsMessage(
      //             <div>
      //               <span>
      //                 Successfully Downloaded table(s)
      //                 <br />
      //                 <ul>
      //                   {success.map((item: any, idx: any) => (
      //                     <>
      //                       <li key={idx}>
      //                         {item}
      //                       </li>
      //                     </>
      //                   ))
      //                   }
      //                 </ul>
      //                 <br />
      //                 <br />
      //                 No records to download from table(s)
      //                 <br />
    
      //                 <ul>
      //                   {err.map((item: any, idx: any) => (
      //                     <>
      //                       <li key={idx}>
      //                         {item}
      //                       </li>
      //                     </>
      //                   ))
      //                   }
      //                 </ul>
    
      //               </span>
      //             </div>
      //           )
      //       } else {
      //         setModalPropsMessage(
      //           <div>
      //             <span>
      //               <br />
      //               No records to download from table(s)
      //               <br />
      //               <ul>
      //                 {err.map((item: any, idx: any) => (
      //                   <>
      //                     <li key={idx}>
      //                       {item}
      //                     </li>
      //                   </>
      //                 ))
      //                 }
      //               </ul>
      //             </span>
      //           </div>
      //         )
      //       }
      //     } else {
      //       setModalProps({
      //         title: 'Download Report',
      //         open: true,
      //         method: handleCloseModalProps,
      //       })
      //       if (success.length == 7)
      //         setModalPropsMessage(
      //           <div>
      //             <span>
      //               Successfully downloaded the file.
      //             </span>
      //           </div>
      //         )
      //       else
      //         setModalPropsMessage(
      //           <div>
      //             <span>
      //               Successfully Downloaded table(s)
      //               <br />
      //               <ul>
      //                 {success.map((item: any, idx: any) => (
      //                   <>
      //                     <li key={idx}>
      //                       {item}
      //                     </li>
      //                   </>
      //                 ))
      //                 }
      //               </ul>
      //             </span>
      //           </div>
      //         )
      //     }
      //   }
    
      // }

      // function handleExportToExcel(csvData, fileName, err, success) {
      //   if (window.hasOwnProperty('downloadFile')) { 
      //     console.log('XLS single export WINDOWS')
      //     let reader = new FileReader();
      //     const ws = XLSX.utils.json_to_sheet(csvData);
      //     const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
      //     const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      //     const data = new Blob([excelBuffer], { type: fileType });
    
      //     reader.readAsDataURL(data)
      //     reader.onloadend = async function () {
      //       var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);
    
      //       let args = {
      //         filename: fileName,
      //         file: base64data,
      //         extension: 'xlsx'
      //       }
    
      //       const download = await window.downloadFile(args);
    
      //       if (download.success) {
      //         downloadNotificationModal(err, success)
      //       } else {
      //         downloadNotificationModal(err, success, true)
      //       }
      //     }
      //   } else {
      //     console.log('XLS single export WEB')
      //     const ws = XLSX.utils.json_to_sheet(csvData);
      //     const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
      //     const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      //     const data = new Blob([excelBuffer], { type: fileType });
      //     saveAs(data, fileName + fileExtension);
      //     downloadNotificationModal(err, success)
      //   }
      // }


    const generateExceptionReport = async () => {
      
        const filename = `exception-report-${new Date().toJSON().slice(0, 10)}.xlsx`;
        let workbook = new Excel.Workbook();
      
        for (let i = 0; i < exceptionReports.length; i++) {


            if (exceptionReports[i].hasOwnProperty('partial')) {
                // let arr: any = exceptionReports[i].partial!
                let arr: any = exceptionReports[i] && exceptionReports[i].partial ? exceptionReports[i].partial : []
                console.log('partial data1',arr)
             
                const partialHeaders = [
                // const partialHeaders = arr.map((row, idx) => (

                    // [
                    {header: 'No', key: 'no'},
                    {header: 'Batch no', key: 'batch_name'},
                    {header: 'First Name', key: 'first_name'},
                    {header: 'Middle Name', key: 'middle_name'},
                    {header: 'Last Name', key: 'last_name'},
                    {header: 'Suffix', key: 'suffix'},
                    {header: 'Birthdate', key: 'date_of_birth'},
                    {header: 'Gender', key: 'gender'},
                    {header: 'Civil Status', key: 'civil_status'},
                    {header: 'Member Type', key: 'member_type'},
                    {header: 'Plan Type', key: 'plan_type'},
                    {header: 'Relationship', key: 'relationship_to_principal'},
                    {header: 'Name of Principal', key: 'principal_name'},
                    {header: 'Similar Fields', key: 'similar_fields'},
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},
                    
                ]
                let memberDetails;
               
                let matchedProfileRows = arr  


                matchedProfileRows.map(item => {
                    let matchedMember = item.member_details?.validation_status?.matched_member;
                    let newArr = [item, matchedMember] 
                    memberDetails = item.member_details

                    newArr.push(newArr)
                    return newArr


                });

    
            const partialHeaders2 = [
                {header: 'Batch no', key: 'existingMemberNo'},
                {header: 'First Name', key: 'matched_first_name'},
                {header: 'Middle Name', key: 'matched_middle_name'},
                {header: 'Last Name', key: 'matched_last_name'},
                {header: 'Suffix', key: 'matched_suffix'},
                {header: 'Birthdate', key: 'matched_date_of_birth'},
                {header: 'Gender', key: 'matched_gender'},
                {header: 'Civil Status', key: 'matched_civil_status'},
                {header: 'Member Type', key: 'matched_member_type'},
                {header: 'Plan Type', key: 'matched_plan_type'},
                {header: 'Relationship', key: 'matched_relationship_to_principal'},
                {header: 'Name of Principal', key: 'matched_principal_name'},
                {header: 'Similar Fields', key: 'existingMemberSimilarFields'},
                {header: 'System Remarks', key: 'matched_remarks'},
                {header: 'Remarks', key: 'matched_user_remarks'},
                
            ]
        

                if (matchedProfileRows.length > 0) {
                    let worksheet = workbook.addWorksheet('Partial Matches');
                    worksheet.columns = partialHeaders;
                    let rowCount = 0;
                    rowCount++;
                    matchedProfileRows.map((item, idx) => {
                        let matchedMember = item.member_details?.validation_status?.matched_member;
                        //add similar fields value to duplicate item
                        matchedMember.similar_fields = item.similar_fields;
                        matchedMember.remarks = item.remarks;
                        matchedMember.user_remarks = item.user_remarks;
                        matchedMember.no =rowCount
                        let newArr = [item, matchedMember] 

                        for (let j = 0; j < newArr.length; j++) {
                           
                            worksheet.addRow(
                                partialFormatRow(
                                    newArr[j], 
                                    true,
                                    rowCount
                                )
                            );
                            // worksheet.addRow(partialHeaders3[j]);
                        }
                    })
                   
                }
            }

            if (exceptionReports[i].hasOwnProperty('incomplete')) {
                // let arr: any[] = exceptionReports[i].incomplete!;
                let arr: any = exceptionReports[i] && exceptionReports[i].incomplete ? exceptionReports[i].incomplete : []
                // console.log('incomplete data1',arr)
                let rowCount = 0;
                const incompleteHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'Missing Fields', key: 'missing_fields' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},

                ];


                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Incomplete Requirements');
                    worksheet.columns = incompleteHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        console.log('incomplete data3',arr[j])
                        worksheet.addRow(incompleteFormatRow(arr[j], false, rowCount));
                    }
                }
            }

            if (exceptionReports[i].hasOwnProperty('conflict')) {
                let arr: any[] = exceptionReports[i] && exceptionReports[i].conflict ? exceptionReports[i].conflict : []
                // console.log('conflict data1',arr)
                let rowCount = 0;
                const conflictHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Birthdate', key: 'date_of_birth' },
                    { header: 'Gender', key: 'gender' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Civil Status', key: 'civil_status' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},

                  
                ];

                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Conflict on Data');
                    worksheet.columns = conflictHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        worksheet.addRow(conflictFormatRow(arr[j], false, rowCount));
                    }
                }
            }

            if (exceptionReports[i].hasOwnProperty('unmatched')) {
                // let arr: any[] = exceptionReports[i].unmatched!;
                let arr: any[] = exceptionReports[i] && exceptionReports[i].unmatched ? exceptionReports[i].unmatched : []
                let rowCount = 0;
                // console.log('unmatched data1',arr)
                const unmatchedHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'Possible Match Name', key: 'possible_match_name' },
                    { header: 'Possible Match Batch No', key: 'possible_match_batch_no' },
                    { header: 'Possible Match Birthdate', key: 'possible_match_birthdate' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},


                ];


                if (arr.length > 0) {
                  
                    let worksheet = workbook.addWorksheet('Unmatched Dependents');
                    worksheet.columns = unmatchedHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;

                        
                  let possibleMatchNames: any = [];
                  let possibleMatchBatchNo: any = [];
                  let possibleMatchBirthdate: any = [];

                  console.log('unmatched data1',arr, arr[j])
                  arr[j].member_details.possible_principals.map(principal => {
                    possibleMatchNames.push(`${principal.first_name} ${principal.middle_name} ${principal.last_name}`)
                    possibleMatchBatchNo.push(principal.batch_name)
                    possibleMatchBirthdate.push(principal.date_of_birth ? moment(principal.date_of_birth).format('MM/DD/YYYY') : '')

                  })

                        worksheet.addRow(unmatchedFormatRow(arr[j], false, rowCount, possibleMatchNames.toString().replace(/[$@%]/g, ''), possibleMatchBatchNo.toString().replace(/[$@%]/g, ''), possibleMatchBirthdate.toString().replace(/[$@%]/g, '') ));
                    }
                }
            }
            if (exceptionReports[i].hasOwnProperty('supporting')) {
                // let arr: any[] = exceptionReports[i].unmatched!;
                let arr: any[] = exceptionReports[i] && exceptionReports[i].supporting ? exceptionReports[i].supporting : []
                // console.log('supporting data1',arr)
                const supportingHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Birthdate', key: 'date_of_birth' },
                    { header: 'Gender', key: 'gender' },
                    { header: 'Civil Status', key: 'civil_status' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},
                ];
                let rowCount = 0;

                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Requiring Supporting Documents');
                    worksheet.columns = supportingHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        worksheet.addRow(supportingFormatRow(arr[j], false, rowCount));
                    }
                }
            }

            if (exceptionReports[i].hasOwnProperty('validation')) {
                // let arr: any[] = exceptionReports[i].validation!;
                let arr: any[] = exceptionReports[i] && exceptionReports[i].validation ? exceptionReports[i].validation : []
                // console.log('others data1',arr)
                const othersHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Birthdate', key: 'date_of_birth' },
                    { header: 'Gender', key: 'gender' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Civil Status', key: 'civil_status' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},
                ];

                let rowCount = 0;
                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Other Validation Rules');
                    worksheet.columns = othersHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        worksheet.addRow(othervalidFormatRow(arr[j], false, rowCount));
                    }
                }
            }

            if (exceptionReports[i].hasOwnProperty('disapproved')) {
                // let arr: any[] = exceptionReports[i].disapproved!;
                let arr: any[] = exceptionReports[i] && exceptionReports[i].disapproved ? exceptionReports[i].disapproved : []
                // console.log('disapproved data1',arr)
                let rowCount = 0;
                const disapprovedHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Birthdate', key: 'date_of_birth' },
                    { header: 'Gender', key: 'gender' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Civil Status', key: 'civil_status' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},
                ];
                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Disapproved');
                    worksheet.columns = disapprovedHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        worksheet.addRow(disapprovedFormatRow(arr[j], false, rowCount));
                    }
                }
            }

            if (exceptionReports[i].hasOwnProperty('approved')) {
                // let arr: any[] = exceptionReports[i].approved!;
                let arr: any[] = exceptionReports[i] && exceptionReports[i].approved ? exceptionReports[i].approved : []
                // console.log('approved data1',arr)
                // const approvedData = getMappedApproved(arr)
                // success.push('Approved')
                let rowCount = 0;
                const approvedHeaders = [
                    { header: 'No', key: 'no' },
                    { header: 'Member Name', key: 'member_name' },
                    { header: 'Plan Type', key: 'plan_type' },
                    { header: 'Account Number', key: 'member_account_no' },
                    { header: 'Birthdate', key: 'date_of_birth' },
                    { header: 'Member Type', key: 'member_type' },
                    { header: 'Gender', key: 'gender' },
                    { header: 'Effectivity Date', key: 'effectivity_date' },
                    { header: 'Civil Status', key: 'civil_status' },
                    { header: 'Relationship', key: 'relationship_to_principal' },
                    { header: 'Principal Name', key: 'principal_name' },
                    { header: 'System Remarks', key: 'system_remarks'},
                    { header: 'Remarks', key: 'remarks'},
                ];
                if (arr.length > 0) {
                    let worksheet = workbook.addWorksheet('Approved');
                    worksheet.columns = approvedHeaders;
                    for (let j = 0; j < arr.length; j++) {
                        rowCount++;
                        worksheet.addRow(approvedFormatRow(arr[j], false, rowCount));
                    }

                }
            }

        }

        const buffer = await workbook.xlsx.writeBuffer();
        const data = new Blob([buffer], { type: '.xlsx' });

        let attachments = [
            {
                filename,
                content: buffer,
                contentType:
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            },
        ];


        saveAs(data, attachments[0]['filename']);
    }
    return (
        <Dialog
            id={'email-template-preview'}
            maxWidth={'lg'}
            open={isOpenModal}
            scroll={'paper'}
            aria-labelledby="modal-title"
            className={classes.dialog}
        >
            <DialogTitle
                className={classes.dialogTitle}
                disableTypography={true}
                id="modal-title"
            >
                <div className={classes.dialogFirstTitle}>{title}</div>
                <div className={classes.dialogSecondTitle}>Direct: <span className={classes.dialogRecipients}>{directs.join(', ')}</span></div>
                <div className={classes.dialogSecondTitle}>CC: <span className={classes.dialogRecipients}>{ccs.join(', ')}</span></div>
                <Link className={classes.dialogAttachment} onClick={generateExceptionReport}>Download Exception Report</Link>
                <IconButton
                    id='close_button'
                    data-cy='close_button'
                    aria-label="close"
                    className={classes.closeButton}
                    onClick={handleCloseModal}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>

            <Divider light />

            <DialogContent className={classes.dialogContent}>
                <Box
                    className="ql-editor"
                    dangerouslySetInnerHTML={{ __html: body }}
                />

            </DialogContent>
        </Dialog>
    )
}

export default ActionMemoHistoryModal;