import React, { useState, useRef, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
  Typography,
  Popper,
  FormControlLabel,
  ClickAwayListener,
  Paper,
  RadioGroup,
  Radio,
  Checkbox,
  FormGroup,
  Button,
} from '@material-ui/core/';
import CloseIcon from '@material-ui/icons/Close';
import IconButton from '@material-ui/core/IconButton';
import Snackbar from '@material-ui/core/Snackbar';
import SnackbarContent from '@material-ui/core/SnackbarContent';
import { amber } from '@material-ui/core/colors';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Redirect } from 'react-router-dom';
import {
  faDownload,
  faEnvelope
} from '@fortawesome/free-solid-svg-icons';
import { Processmaker } from '../../Processmaker';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

import clsx from 'clsx';
import PropTypes from 'prop-types';

import { TableComponent } from 'Components/UI/TableComponent';
import { FloatingButtons } from 'Components/UI/FloatingButtons';
import { SnackbarNotification } from 'Components/UI/SnackbarNotification';
import { ModalComponent } from 'Components/UI/ModalComponent';
import { ConfirmationModalComponent } from 'Components/UI/ConfirmationModalComponent';
import { Loader } from 'Components/UI/LoadingIndicator';

import { EmailReportModal } from './EmailReportModal';
import { EmailExceptionReportModal, PreviewModal, AlertModal } from './EmailExceptionReportModal';
import { API } from '../../API';
import { Utils } from '@hims/core';
import _ from 'lodash';
import moment from 'moment';

import { Alphabet } from './alphabet.const'
import { MemberInformation } from '../MemberInformation';
import { combineRemarks } from '../ActionConsts';

const useStyles1 = makeStyles(theme => ({
  success: {
    backgroundColor: '#E6F5F6',
    borderColor: '#E6F5F6',
  },
  error: {
    backgroundColor: theme.palette.error.light,
    borderColor: theme.palette.error.light,
  },
  info: {
    backgroundColor: theme.palette.primary.main,
  },
  warning: {
    backgroundColor: amber[700],
  },
  icon: {
    fontSize: 20,
    color: '#363636',
  },
  iconVariant: {
    opacity: 0.9,
    marginRight: theme.spacing(1),
  },
  message: {
    color: '#272E4C',
    display: 'flex',
    alignItems: 'center',
  },
}));

const categories: any[] = [
  { value: 'partial', label: 'Partial Matches', id: 'partial' },
  { value: 'incomplete', label: 'Incomplete Requirements', id: 'incomplete' },
  { value: 'conflict', label: 'Conflict on Data', id: 'conflict' },
  { value: 'unmatched', label: 'Unmatched Principal Dependents', id: 'unmatched' },
  { value: 'supporting', label: 'Requiring Supporting Documents', id: 'supporting' },
  { value: 'validation', label: 'Other Validation Rules', id: 'validation' },
  { value: 'disapproved', label: 'Disapproved', id: 'disapproved' },
  { value: 'approved', label: 'Approved', id: 'approved' },

];

function MySnackbarContentWrapper(props) {
  const classes = useStyles1();
  const { className, message, onClose, variant, ...other } = props;

  return (
    <SnackbarContent
      className={clsx(classes[variant], className)}
      aria-describedby="client-snackbar"
      message={
        <span id="client-snackbar" className={classes.message}>
          {message}
        </span>
      }
      action={[
        <IconButton
          key="close"
          aria-label="close"
          color="inherit"
          onClick={onClose}
        >
          <CloseIcon className={classes.icon} />
        </IconButton>,
      ]}
      {...other}
    />
  );
}

MySnackbarContentWrapper.propTypes = {
  className: PropTypes.string,
  message: PropTypes.string,
  onClose: PropTypes.func,
  variant: PropTypes.oneOf(['error', 'info', 'success', 'warning']).isRequired,
};

const useStyles = makeStyles(theme => ({
  root: {
    width: '100%',
    display: 'flex',
    height: '100%',
  },
  iconsSpanOne: {
    color: '#0D5E40',
    fontSize: '14px',
    fontWeight: 600,
    marginRight: '10px',
    cursor: 'pointer',
  },
  iconsSpanTwo: {
    color: '#0D5E40',
    fontSize: '14px',
    fontWeight: 600,
    cursor: 'pointer',
  },
  icons: {
    color: '#0D5E40',
    fontSize: '18px',
    paddingRight: '6px',
    paddingTop: '3px',
  },
  label: {
    padding: '5px 5px',
    fontSize: '14px',
  },
  approvedlabel: {
    padding: '5px 5px',
    fontSize: '16px',
    fontWeight: 700,
  },
  gridtable: {
    marginBottom: '35px',
  },
  popper: {
    top: '4px',
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '0 1em 1em 1em',
        borderColor: `transparent transparent ${theme.palette.background.paper} transparent`,
      },
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: '-0.9em',
      width: '3em',
      height: '1em',
      '&::before': {
        borderWidth: '1em 1em 0 1em',
        borderColor: `${theme.palette.background.paper} transparent transparent transparent`,
      },
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 1em 1em 0',
        borderColor: `transparent ${theme.palette.background.paper} transparent transparent`,
      },
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: '-0.9em',
      height: '3em',
      width: '1em',
      '&::before': {
        borderWidth: '1em 0 1em 1em',
        borderColor: `transparent transparent transparent ${theme.palette.background.paper}`,
      },
    },
  },
  arrow: {
    position: 'absolute',
    fontSize: 7,
    width: '3em',
    height: '3em',
    '&::before': {
      content: '""',
      margin: 'auto',
      display: 'block',
      width: 0,
      height: 0,
      borderStyle: 'solid',
    },
  },
  paper: {
    padding: '20px',
    width: 300,
  },
  grid: {
    padding: '0px 20px 15px 20px',
  },
  textLabel: {
    fontSize: '12px',
    fontWeight: 700,
  },
  downloadBtn: {
    width: 135,
    border: '1px solid #3AB77D',
    color: '#3AB77D',
    backgroundColor: '#FFFFFF',
  },
  rightButton: {
    height: '40px',
    minWidth: '99px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
  },
  leftButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    border: '2px solid #3AB77D',
  },
  backButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
    textTransform: 'none',
    '&:hover': {
      background: '#000000',
    },
  },
  buttonTitle: {
    fontWeight: 700,
    fontSize: 13,
  },
  buttonSubTitle: {
    fontWeight: 400,
    fontSize: 7,
  },
  generateButton: {
    height: '40px',
    minWidth: '150px',
  },
}));

interface ExceptionReportProps {
  id: string;
  case_id: string;
  pmaker_task: string;
  client_id: string;
  client_name: string;
  client_accNo: string;
  data: any;
  memberUploadData: any;
  email: string;
  handleBack: () => void;
  handleCloseLostInternetConnectionModal: () => void;
  handleOpenLostInternetConnectionModal: () => void;
  handlePublishToOcp: (origin: string, reportFile: any) => void;
  ocpOrigin: string;
  initApprovedMembers: any[];
  batchDetails: any;
  allValidated: any[];
  ticket_closed?: boolean;
}

declare global {
  interface Window {
    downloadFile: any
  }
}

export const ExceptionReport: React.FC<ExceptionReportProps> = (
  props: ExceptionReportProps,
): JSX.Element => {

  let { case_id, data, email, id, client_id, client_name, initApprovedMembers, batchDetails, allValidated } = props;

  const classes = useStyles();
  const [isPopoverOpen, setPopupoverOpen] = useState(false);
  const anchorRef = useRef(null);
  const [arrowRef, setArrowRef] = useState<any>(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [variant, setVariant] = useState('');
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [selectedData, setSelectedData] = useState('Include entire list');
  const [selectedFormat, setSelectedFormat] = useState('PDF');
  const [emailReportModalOpen, setEmailReportModalOpen] = useState(false);
  const [redirectToDashboard, setRedirectToDashboard] = useState(false);
  const [exportExcelErr, setExportExcelErr] = useState<any>([]);
  const [exportExcelSuccess, setExportExcelSucess] = useState<any>([]);
  const [state, setState] = React.useState({
    partial: false,
    incomplete: false,
    conflict: false,
    unmatched: false,
    supporting: false,
    validation: false,
    disapproved: false,
    approved: false,
  });
  const [modalProps, setModalProps] = React.useState({
    title: '',
    open: false,
    method: () => { }
  });
  const [modalPropsMessage, setModalPropsMessage] = React.useState<any>();

  const [isSnackbarNotifOpen, setIsSnackbarNotifOpen] = React.useState(false);
  const [snackbarNotifMsg, setSnackbarNotifMsg] = React.useState('Success.');
  const [snackbarNotifVariant, setSnackbarNotifVariant] = React.useState(
    'success',
  );
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [confirmModalTitle, setConfirmModalTitle] = useState('');
  const [confirmModalMessage, setConfirmModalMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [next, setNext] = useState<(() => void) | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalMessage, setModalMessage] = useState('');

  const [dialogModalState, setDialogModalState] = React.useState<any>({
    isOpen: false,
    title: '',
    message: '',
    tableData: null,
  });

  const [emailExceptionReportModalOpen, setEmailExceptionReportModalOpen] = useState(false);
  const [emailTemplateOption, setEmailTemplateOption] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [actionMemoRecipients, setActionMemoRecipients] = useState<any>({});
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>({});
  const [alertModalOpen, setAlertModalOpen] = useState(false);
  const [alertContent, setAlertContent] = useState('');
  // const [emailTemplateFilter, setEmailTemplateFilter] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<any>([])

  const partial = _.assign({}, data.partial);
  const incomplete = _.assign({}, data.incomplete);
  const validation = _.assign({}, data.validation);
  const othervalidation = _.assign({}, data.othervalidation);
  const conflict = _.assign({}, data.conflict);
  const disapproved = _.assign({}, data.disapprove);
  const approved = _.assign({}, data.approved);
  const supporting = _.assign({}, data.supporting);
  partial.columns.splice(3, 1);
  incomplete.columns.splice(2, 1);
  validation.columns.splice(2, 1);
  othervalidation.columns.splice(2, 1);
  conflict.columns.splice(2, 1);
  disapproved.columns.splice(2, 1);
  supporting.columns.splice(2, 1);
  //XLS constants

  const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  const fileExtension = '.xlsx';

  function closeModalMessage() {
    setIsOpenModal(false);
    if (next !== null) {
      next();
      setNext(null);
    }
  }

  function handleClickRow() {
    setSelectedData('');
  }

  function handleClose() {
    setPopupoverOpen(false);
  }

  function handleOpenPopper() {
    setPopupoverOpen(true);
  }

  function handleChange(event: any, name: string) {
    setState({ ...state, [name]: event.target.checked });
  }

  function onSnackbarClose() {
    setOpenSnackbar(false);
  }

  function handleExportToExcel(csvData, fileName, err, success) {
    if (window.hasOwnProperty('downloadFile')) {
      console.log('XLS single export WINDOWS')
      let reader = new FileReader();
      const ws = XLSX.utils.json_to_sheet(csvData);
      const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: fileType });

      reader.readAsDataURL(data)
      reader.onloadend = async function () {
        var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);

        let args = {
          filename: fileName,
          file: base64data,
          extension: 'xlsx'
        }

        const download = await window.downloadFile(args);

        if (download.success) {
          downloadNotificationModal(err, success)
        } else {
          downloadNotificationModal(err, success, true)
        }
      }
    } else {
      console.log('XLS single export WEB')
      const ws = XLSX.utils.json_to_sheet(csvData);
      const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: fileType });
      saveAs(data, fileName + fileExtension);
      downloadNotificationModal(err, success)
    }
  }

  function handleExportToExcelUnmatchedDependents(csvData, fileName, err, success) {
    let newObj = getUnmatchedCustomizedSheet(csvData, csvData.length)

    var wb = {
      SheetNames: ["Sheet1"],
      Sheets: {
        Sheet1: newObj,
      }
    };

    if (window.hasOwnProperty('downloadFile')) {
      let reader = new FileReader();

      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: fileType });

      reader.readAsDataURL(data)
      reader.onloadend = async function () {
        var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);

        let args = {
          filename: fileName,
          file: base64data,
          extension: 'xlsx'
        }

        const download = await window.downloadFile(args);

        if (download.success) {
          downloadNotificationModal(err, success);
        } else {
          downloadNotificationModal(err, success, true);
        }
      }
    } else {
      var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });
      saveAs(new Blob([s2ab(wbout)], { type: "application/octet-stream" }), `${fileName}.xlsx`);
      downloadNotificationModal(err, success);
    }
  }

  function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
  }

  const getUnmatchedCustomizedSheet = (
    prop,
    totalCount,
  ) => {
    let letter = Alphabet
    let newObj = {
      "!ref": `A1:M${totalCount + 20}`,
      A1: { t: 's', v: "No" },
      B1: { t: 's', v: "Member Name" },
      C1: { t: 's', v: "Relationship" },
      D1: { t: 's', v: "Principal Name" },
      // E1: { t: 's', v: "Supporting Documents Requested" },
      E1: { t: 's', v: "Possible Match Name" },
      F1: { t: 's', v: "Possible Match Batch No" },
      G1: { t: 's', v: "Possible Match Birthdate" },
      H1: { t: 's', v: "System Remarks" },
      I1: { t: 's', v: "Remarks" },
    }
    let counter = 1;
    let merges: any[] = [];
    prop.map((data) => {
      if (data["Possible Match Name"].length > 0) {

        if (merges.length === 0) {
          merges = [
            { s: { r: counter, c: 0 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 0 } },
            { s: { r: counter, c: 1 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 1 } },
            { s: { r: counter, c: 2 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 2 } },
            { s: { r: counter, c: 3 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 3 } },
            { s: { r: counter, c: 4 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 4 } },
            { s: { r: counter, c: 7 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 7 } },
            { s: { r: counter, c: 8 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 8 } },
          ]
        } else {
          merges.push({ s: { r: counter, c: 0 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 0 } })
          merges.push({ s: { r: counter, c: 1 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 1 } })
          merges.push({ s: { r: counter, c: 2 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 2 } })
          merges.push({ s: { r: counter, c: 3 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 3 } })
          merges.push({ s: { r: counter, c: 4 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 4 } })
          merges.push({ s: { r: counter, c: 7 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 7 } })
          merges.push({ s: { r: counter, c: 8 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 8 } })
          // merges.push({ s: { r: counter, c: 9 }, e: { r: counter + data["Possible Match Name"].length - 1, c: 9 } })
        }

        counter = counter + data["Possible Match Name"].length;
      }
    })

    newObj['!merges'] = merges
    let line = 2;
    prop.map(a => {
      newObj[`${letter[0]}${line}`] = { t: 's', v: a['No'] }
      newObj[`${letter[1]}${line}`] = { t: 's', v: a['Member Name'] }
      newObj[`${letter[2]}${line}`] = { t: 's', v: a['Relationship'] }
      newObj[`${letter[3]}${line}`] = { t: 's', v: a['Principal Name'] }
      // newObj[`${letter[4]}${line}`] = { t: 's', v: a['Supporting Documents Requested'] }

      a["Possible Match Name"].map((name, nameIdx) => {
        newObj[`${letter[4]}${line + nameIdx}`] = { t: 's', v: name }
      })

      a["Possible Match Batch No"].map((batch, batchIdx) => {
        newObj[`${letter[5]}${line + batchIdx}`] = { t: 's', v: batch }
      })

      a["Possible Match Birthdate"].map((birth, birthIdx) => {
        newObj[`${letter[6]}${line + birthIdx}`] = { t: 's', v: birth }
      })

      newObj[`${letter[7]}${line}`] = { t: 's', v: a['System Remarks'] }

      newObj[`${letter[8]}${line}`] = { t: 's', v: a['Remarks'] }

      line = line + a["Possible Match Name"].length
    })

    return newObj
  }

  function handleExportMultipleTableToExcel(arrayObjects, sheetName, fileName, err, success) {
    if (window.hasOwnProperty('downloadFile')) {
      console.log('XLS multi export WINDOWS')
      let reader = new FileReader();
      let sheets;
      let sheet_names = Object.keys(sheetName)

      arrayObjects.map((prop, i) => {
        if (sheet_names[i] !== 'Unmatched Dependents') {
          let newObj = {
            ...sheets,
            [sheet_names[i]]: XLSX.utils.json_to_sheet(prop)
          }
          sheets = newObj;
        } else {

          let newObj = getUnmatchedCustomizedSheet(prop, prop.length)

          let newObj2 = {
            ...sheets,
            [sheet_names[i]]: newObj
          }

          sheets = newObj2
        }
      })

      const wb = {
        Sheets: sheets,
        SheetNames: sheet_names
      };

      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: fileType });

      reader.readAsDataURL(data)
      reader.onloadend = async function () {
        var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);

        let args = {
          filename: fileName,
          file: base64data,
          extension: 'xlsx'
        }

        const download = await window.downloadFile(args);

        if (download.success) {
          downloadNotificationModal(err, success);
        } else {
          downloadNotificationModal(err, success, true);
        }
      }
    } else {
      console.log('XLS multi export WEB')
      let sheets;
      let sheet_names = Object.keys(sheetName)

      console.log(sheet_names)

      arrayObjects.map((prop, i) => {
        if (sheet_names[i] !== 'Unmatched Dependents') {
          let newObj = {
            ...sheets,
            [sheet_names[i]]: XLSX.utils.json_to_sheet(prop)
          }
          sheets = newObj;
        } else {

          let newObj = getUnmatchedCustomizedSheet(prop, prop.length)

          let newObj2 = {
            ...sheets,
            [sheet_names[i]]: newObj
          }

          sheets = newObj2
        }
      })
      console.log("sheets", sheets)
      const wb = {
        Sheets: sheets,
        SheetNames: sheet_names
      };
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: fileType });
      saveAs(data, fileName + fileExtension);
      console.log('err', exportExcelErr)
      console.log('sucess', exportExcelSuccess)
      downloadNotificationModal(err, success)
    }
  }

  const downloadNotificationModal = (err, success, cancelled?: boolean) => {
    setModalProps({
      title: 'Download Report',
      open: true,
      method: handleCloseModalProps,
    })

    if (cancelled) {
      setModalPropsMessage('Downloading file has been cancelled')
    } else {
      if (err.length > 0) {
        if (success.length > 0) {
          if (success.length == 7)
            setModalPropsMessage(
              <div>
                <span>
                  Successfully downloaded the file.
                </span>
              </div>
            )
          else
            setModalPropsMessage(
              <div>
                <span>
                  Successfully Downloaded table(s)
                  <br />
                  <ul>
                    {success.map((item: any, idx: any) => (
                      <>
                        <li key={idx}>
                          {item}
                        </li>
                      </>
                    ))
                    }
                  </ul>
                  <br />
                  <br />
                  No records to download from table(s)
                  <br />

                  <ul>
                    {err.map((item: any, idx: any) => (
                      <>
                        <li key={idx}>
                          {item}
                        </li>
                      </>
                    ))
                    }
                  </ul>

                </span>
              </div>
            )
        } else {
          setModalPropsMessage(
            <div>
              <span>
                <br />
                No records to download from table(s)
                <br />
                <ul>
                  {err.map((item: any, idx: any) => (
                    <>
                      <li key={idx}>
                        {item}
                      </li>
                    </>
                  ))
                  }
                </ul>
              </span>
            </div>
          )
        }
      } else {
        setModalProps({
          title: 'Download Report',
          open: true,
          method: handleCloseModalProps,
        })
        if (success.length == 7)
          setModalPropsMessage(
            <div>
              <span>
                Successfully downloaded the file.
              </span>
            </div>
          )
        else
          setModalPropsMessage(
            <div>
              <span>
                Successfully Downloaded table(s)
                <br />
                <ul>
                  {success.map((item: any, idx: any) => (
                    <>
                      <li key={idx}>
                        {item}
                      </li>
                    </>
                  ))
                  }
                </ul>
              </span>
            </div>
          )
      }
    }

  }

  function handleDownloadFile(isDefaultDownload: boolean) {
    const selectedTab: string[] = [];
    let body: any = {};
    let filename: string = '';

    if (selectedData === '') {
      setVariant('error');
      setSnackbarMessage('Please choose data to continue.');
      setOpenSnackbar(true);

      return;
    }

    if (selectedFormat === '') {
      setVariant('error');
      setSnackbarMessage('Please choose format to continue.');
      setOpenSnackbar(true);

      return;
    }

    if (selectedData === 'Custom list') {
      Object.keys(state).map(key => {
        if (state[key]) {
          selectedTab.push(key);
        }
      });

      if (selectedTab.length === 0) {
        setVariant('error');
        setSnackbarMessage('Please select data from the list to continue.');
        setOpenSnackbar(true);

        return;
      }
    } else {
      selectedTab.push('all');
    }

    if (selectedFormat === 'PDF') {
      filename = `enrollment-exception_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
        ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
          ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}.pdf`;
    } else if (selectedFormat === 'XLS') {
      filename = `enrollment-exception_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
        ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
          ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}.csv`;
    } else if (selectedFormat === 'XML') {
      filename = `enrollment-exception_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
        ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
          ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}.xml`;
    }

    if (selectedFormat !== 'XLS') {
      console.log('DOWNLOAD PDF')

      body = {
        data: selectedTab,
        format: selectedFormat,
      };

      setIsLoading(true);

      validatePDFNotification(id, body, isDefaultDownload, filename)

    } else {
      console.log('DOWNLOAD XLS')
      setIsLoading(true);
      let err: any = [];
      let success: any = [];
      let multipleTable: any[] = [];
      let sheetName: any[] = [];
      let selectedTableCount = getSelectedTableCount();
      console.log("count", selectedTableCount)
       //add activitylogs download verify member exception report
       Utils.StorageService('user_data').then(result => {
        let user_name: string = '';
        console.log(user_name)
        //console.log('Utils', result)
        if (result && result !== undefined) {
          for (const i in result) {
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }

          console.log('download excep1', props, )
          API.activityDownloadVerifyExceptionReport(props.memberUploadData['batch_name'], props.memberUploadData, user_name).then(response => {
            if(response === undefined){
              console.log('response', response)
            }
          }).catch(e => {
            console.log('terminate error', e)
          })
        };
      });



      if (state.partial === true || selectedData === 'Include entire list') {
        const json = partial.rows
        console.log('partial rows', partial.rows)
        if (json.length > 0) {
          console.log('partial json', json)
          const partialData = getMappedPartial(json)
          success.push('Partial Matches')

          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {

            handleExportToExcel(partialData, `enrollment-exception_Partial Matches_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            //multiple item selected
            multipleTable.push(partialData)
            sheetName['Partial Matches'] = '';
          }
          setExportExcelSucess(success);
        } else {
          console.log('partial json err', json)
          err.push('Partial Matches');
          setExportExcelErr(err);
        }
      }
      if (state.incomplete === true || selectedData === 'Include entire list') {
        const json = incomplete.rows
        if (json.length > 0) {
          const incompleteData = getMappedIncomplete(json)
          console.log('INCOMPLETE123', incompleteData)
          success.push('Incomplete Requirements')
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(incompleteData, `enrollment-exception_Incomplete Requirements_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(incompleteData)
            sheetName['Incomplete Requirements'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Incomplete Requirements');
          setExportExcelErr(err);
        }
      }
      if (state.conflict === true || selectedData === 'Include entire list') {
        const json = conflict.rows
        if (json.length > 0) {
          const conflictData = getMappedConflict(json)
          success.push('Conflict on Data');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(conflictData, `enrollment-exception_Conflict on Data'_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(conflictData)
            sheetName['Conflict on Data'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Conflict on Data');
          setExportExcelErr(err);
        }
      }
      if (state.unmatched === true || selectedData === 'Include entire list') {
        const json = validation.rows
        if (json.length > 0) {
          const unmatchedData = getMappedUnmatched(json)
          console.log('UNMATCHED DATA', unmatchedData)
          success.push('Unmatched Dependents');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            let fileName = `enrollment-exception_Unmatched Dependents_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
            handleExportToExcelUnmatchedDependents(unmatchedData, fileName, err, success)
          } else {
            multipleTable.push(unmatchedData)
            sheetName['Unmatched Dependents'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Unmatched Dependents');
          setExportExcelErr(err);
        }
      }
      if (state.validation === true || selectedData === 'Include entire list') {
        const json = othervalidation.rows
        if (json.length > 0) {
          const validationData = getMappedValidation(json)
          success.push('Other Validation Rules');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(validationData, `enrollment-exception_Other Validation Rules_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(validationData)
            sheetName['Other Validation Rules'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Other Validation Rules');
          setExportExcelErr(err);
        }
      }
      if (state.disapproved === true || selectedData === 'Include entire list') {
        const json = disapproved.rows
        if (json.length > 0) {
          const disapprovedData = getMappedDisapproved(json)
          success.push('Disapproved');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(disapprovedData, `enrollment-exception_Disapproved_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(disapprovedData)
            sheetName['Disapproved'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Disapproved');
          setExportExcelErr(err);
        }
      }
      if (state.approved === true || selectedData === 'Include entire list') {
        const json = approved.rows
        if (json.length > 0) {
          const approvedData = getMappedApproved(json)
          success.push('Approved');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(approvedData, `enrollment-exception_Approved_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(approvedData)
            sheetName['Approved'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Approved');
          setExportExcelErr(err);
        }
      }
      if (state.supporting === true || selectedData === 'Include entire list') {
        const json = supporting.rows
        if (json.length > 0) {
          const supportingData = getMappedSupporting(json)
          success.push('Requiring Supporting Documents');
          if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
            handleExportToExcel(supportingData, `enrollment-exception_Requiring Supporting Documents_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
              ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
                ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
          } else {
            multipleTable.push(supportingData)
            sheetName['Requiring Supporting Documents'] = '';
          }
          setExportExcelSucess(success);
        } else {
          err.push('Requiring Supporting Documents');
          setExportExcelErr(err);
        }
      }

      if (multipleTable.length > 0 && success.length > 0) {
        console.log('XLS 1st CONDITION BEING CALLED')
        console.log("XLS multipleTable", multipleTable)
        handleExportMultipleTableToExcel(multipleTable, sheetName, `enrollment-exception_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
          ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
            ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`, err, success)
      } else if (multipleTable.length > 0 && success.length === 0) {
        console.log('XLS 2nd CONDITION BEING CALLED')
        downloadNotificationModal(err, success);
      } else if (
        multipleTable.length === 0 &&
        selectedData !== 'Include entire list' &&
        err.length > 0
      ) {
        console.log('XLS 3rd CONDITION BEING CALLED')
        downloadNotificationModal(err, success);
      }


      setTimeout(() => {
        setIsLoading(false);
        setPopupoverOpen(false);
      }, 500)

    }

  }

  const downloadPdfMethod = (id, body, isDefaultDownload, filename, err, success) => {
    props.handleOpenLostInternetConnectionModal();
    console.log('downloadPdfMethod()',)

    API.downloadExceptionReport(id, body)
      .then(response => {
        console.log('download exception report res', response)
        setIsLoading(false);

        if (response !== undefined) {
          props.handleCloseLostInternetConnectionModal();
          if (response.type === 'application/json') {
            setVariant('error');
            setSnackbarMessage('Error downloading file.');
            setOpenSnackbar(true);
          } else {
            if (isDefaultDownload === true) {

              if (window.hasOwnProperty('downloadFile')) {
                let reader = new FileReader();
                reader.readAsDataURL(response)
                reader.onloadend = async function () {
                  var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);

                  let args = {
                    filename: filename,
                    file: base64data,
                    extension: 'pdf'
                  }

                  const download = await window.downloadFile(args);

                  if (download.success) {
                    // setIsOpenModal(true);
                    // setModalTitle('Download Successful')
                    // setModalMessage('Successfully downloaded the file.')
                    downloadNotificationModal(err, success)
                  } else {
                    downloadNotificationModal(err, success, true)
                  }
                }
              } else {
                const samp = new File([response], filename + '.pdf');
                saveAs(samp);
                downloadNotificationModal(err, success)
              }

              // Old download implementation
              // const url = window.URL.createObjectURL(response);
              // const a = document.createElement('a');
              // a.href = url;
              // a.download = filename;
              // a.click();
              // setIsOpenModal(true);
              // setModalTitle('Download Successful')
              // setModalMessage('Successfully downloaded the file.')
            } else {
              if (props.ocpOrigin === 'vm-ocp') {
                // @Blueming
                // 1st
                // the response from the API is already converted to Blob
                // console.log('exception report Blob type', response)
                // props.handlePublishToOcp(props.ocpOrigin, response)

                // 2nd
                // let reportFile = new File([response], filename, {type: response.type});
                // console.log('exception report File type conversion', reportFile)
                // props.handlePublishToOcp(props.ocpOrigin, reportFile)

                // 3rd
                let reader = new FileReader();
                reader.readAsDataURL(response);
                reader.onload = function () {
                  var base64data = reader.result;
                  console.log('exception report base64 type conversion', base64data)
                  props.handlePublishToOcp(props.ocpOrigin, base64data)
                }
              }
            }
          }
        }
      })
      .catch(() => {
        setIsLoading(false);
      });


        //add activitylogs download verify member exception report
        Utils.StorageService('user_data').then(result => {
          let user_name: string = '';
          console.log(user_name)
          //console.log('Utils', result)
          if (result && result !== undefined) {
            for (const i in result) {
              if (result[i]['key'] === 'username') {
                user_name = result[i]['value'];
              }
            }
            API.activityDownloadVerifyExceptionReport(props.memberUploadData['batch_name'], props.memberUploadData, user_name).then(response => {
              if(response === undefined){
                console.log('response', response)
              }
              console.log('activityDownloadVerifyExceptionReport', response)
            }).catch(e => {
              console.log('terminate error', e)
            })
          };
        });
  
  }

  const validatePDFNotification = (id, body, isDefaultDownload, fileName) => {

    let err: any = [];
    let success: any = [];
    let multipleTable: any[] = [];
    let sheetName: any[] = [];
    let selectedTableCount = getSelectedTableCount();
    console.log("count", selectedTableCount)
    if (state.partial === true || selectedData === 'Include entire list') {
      const json = partial.rows
      if (json.length > 0) {
        const partialData = getMappedPartial(json)
        // const matchedData = partial.rows[]
        success.push('Partial Matches')
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Partial Matches_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(partialData)
          sheetName['Partial Matches'] = '';
        }
        setExportExcelSucess(success);
      } else {
        console.log('partial json err', json)
        err.push('Partial Matches');
        setExportExcelErr(err);
      }
    }
    if (state.incomplete === true || selectedData === 'Include entire list') {
      const json = incomplete.rows
      if (json.length > 0) {
        const incompleteData = getMappedIncomplete(json)
        console.log('INCOMPLETE123', incompleteData)
        success.push('Incomplete Requirements')
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Incomplete Requirements_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(incompleteData)
          sheetName['Incomplete Requirements'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Incomplete Requirements');
        setExportExcelErr(err);
      }
    }
    if (state.conflict === true || selectedData === 'Include entire list') {
      const json = conflict.rows
      if (json.length > 0) {
        const conflictData = getMappedConflict(json)
        success.push('Conflict on Data');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Conflict on Data_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(conflictData)
          sheetName['Conflict on Data'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Conflict on Data');
        setExportExcelErr(err);
      }
    }
    if (state.unmatched === true || selectedData === 'Include entire list') {
      const json = validation.rows
      if (json.length > 0) {
        const unmatchedData = getMappedUnmatched(json)
        console.log('UNMATCHED DATA', unmatchedData)
        success.push('Unmatched Dependents');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Unmatched Dependents_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(unmatchedData)
          sheetName['Unmatched Dependents'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Unmatched Dependents');
        setExportExcelErr(err);
      }
    }
    if (state.validation === true || selectedData === 'Include entire list') {
      const json = othervalidation.rows
      if (json.length > 0) {
        const validationData = getMappedValidation(json)
        success.push('Other Validation Rules');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Other Validation Rules_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(validationData)
          sheetName['Other Validation Rules'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Other Validation Rules');
        setExportExcelErr(err);
      }
    }
    if (state.disapproved === true || selectedData === 'Include entire list') {
      const json = disapproved.rows
      if (json.length > 0) {
        const disapprovedData = getMappedDisapproved(json)
        success.push('Disapproved');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Disapproved_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(disapprovedData)
          sheetName['Disapproved'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Disapproved');
        setExportExcelErr(err);
      }
    }
    if (state.approved === true || selectedData === 'Include entire list') {
      const json = approved.rows
      if (json.length > 0) {
        const approvedData = getMappedApproved(json)
        success.push('Approved');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Approved_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(approvedData)
          sheetName['Approved'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Approved');
        setExportExcelErr(err);
      }
    }
    if (state.supporting === true || selectedData === 'Include entire list') {
      const json = supporting.rows
      if (json.length > 0) {
        const supportingData = getMappedApproved(json)
        success.push('Requiring Supporting Documents');
        if (selectedData !== 'Include entire list' && selectedTableCount === 1) {
          // downloadPdfMethod(id, body, isDefaultDownload, fileName);
          fileName = `enrollment-exception_Requiring Supporting Documents_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
            ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
              ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`
        } else {
          multipleTable.push(supportingData)
          sheetName['Requiring Supporting Documents'] = '';
        }
        setExportExcelSucess(success);
      } else {
        err.push('Requiring Supporting Documents');
        setExportExcelErr(err);
      }
    }

    if (multipleTable.length > 0) {
      fileName = `enrollment-exception_${moment().format('MMM-DD-YYYY')}${props.client_accNo && props.client_accNo.trim() !== ''
        ? '_' + props.client_accNo : props.client_name && props.client_name.trim() !== ''
          ? '_' + props.client_name.replace(/([~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,.\/? ])+/g, '-').replace(/^(-)+|(-)+$/g, '') : '_' + props.client_name}`;
    }

    if (success.length > 0) {
      downloadPdfMethod(id, body, isDefaultDownload, fileName, err, success);
    } else {
      setIsLoading(false);
      downloadNotificationModal(err, success)
    }
  }

  const getMappedPartial = (json) => {
    const temp = json.map((row, idx) => (
      {
      'No': idx + 1,
      'Batch no.': row.member_details.batch_name,
      'First Name': row.first_name,
      'Middle Name': row.middle_name,
      'Last Name': row.last_name,
      'Suffix': row.suffix,
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': row.gender,
      'Civil Status': row.civil_status,
      'Member Type': row.member_type,
      'Plan Type': row.plan_type,
      'Relationship': row.relationship_to_principal,
      'Name of Principal': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      'Similar Fields': row.similar_fields ? row.similar_fields : '',
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }
    ));

    let memberDetails;
    if (partial !== null) {
      console.log('partial not empty', partial)
      const matchedProfileRows = partial.rows
      const matchedProfile = matchedProfileRows.member_details
      console.log('MatchedProfileRows', matchedProfileRows)
      console.log('matchedProfile', matchedProfile)

      matchedProfileRows.forEach(item => {
        memberDetails = item.member_details
        const matchedMemberData = memberDetails.validation_status.matched_member
        console.log('Matched Profile matchedMember', memberDetails)
        console.log('matchedMemberData', matchedMemberData)

        return memberDetails
      })
      
    console.log('getMappedPartial temp', temp)
    console.log('temp2 memberDetails', memberDetails)
    // } else {
    //   console.log('partial is empty', partial)
    }  
    const existingMemberBatchNo = memberDetails.match_batchnames.existing_member;
    const existingMemberNo = temp[0]['No'];
    const existingMemberSimilarFields = temp[0]['Similar Fields'];

    const temp2 = json.map((memberDetails) => ({
      'No': existingMemberNo,
      'Batch no.': existingMemberBatchNo,
      'First Name': memberDetails.first_name,
      'Middle Name': memberDetails.middle_name,
      'Last Name': memberDetails.last_name,
      'Suffix': memberDetails.suffix,
      'Birthdate': memberDetails && memberDetails.date_of_birth ? moment(memberDetails.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': memberDetails.gender,
      'Civil Status': memberDetails.civil_status,
      'Member Type': memberDetails.member_type,
      'Plan Type': memberDetails.plan_type,
      'Relationship': memberDetails.relationship_to_principal,
      'Name of Principal': memberDetails.principal_name,
      'Similar Fields': existingMemberSimilarFields,
      'System Remarks': memberDetails && memberDetails.remarks ? memberDetails.remarks : '',
      'Remarks': memberDetails && memberDetails.user_remarks ? memberDetails.user_remarks : ''
    }))


    const temp3 = [...temp, ...temp2]

    console.log('getMappedPartial temp1', temp)
    // console.log('getMappedPartial temp2', temp2)
    // console.log('getMappedPartial temp3', temp3)
    return temp3
  }

  const getMappedIncomplete = (json) => {
    console.log('MAPPED FILE INCOMPLETE FILE DOWNLOAD', json)
    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': `${row.first_name} ${row.middle_name} ${row.last_name}${row.suffix ? `, ${row.suffix}` : ''}`,
      'Member Type': row.member_type,
      'Plan Type': row.plan_type,
      'Relationship': row.relationship_to_principal,
      'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details.principal_first_name}, ${row.member_details.principal_middle_name}` : '',
      'Missing Fields': row.missing_fields,
      'System Remarks': row.missing_fields ? row.missing_fields : '', //row.incomplete_action !== '' ? row.incomplete_action : row.user_remarks,
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));

    console.log('getMappedIncomplete temp', temp)
    return temp
  }

  const getMappedConflict = (json) => {
    console.log('MAPPED FILE CONFLICT FILE DOWNLOAD', json)
    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': row.member_name,
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': row.gender,
      'Member Type': row.member_type,
      'Civil Status': row.civil_status,
      'Relationship': row.relationship_to_principal,
      'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      'Plan Type': row.plan_type,
      // 'Supporting Documents Needed': '',
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));

    console.log('getMappedConflict temp', temp)
    return temp
  }

  const getMappedUnmatched = (json) => {
    console.log('JSON Unmatched', json)

    const temp = json.map((row, idx) => {
      let newObj = {
        'No': idx + 1,
        'Member Name': row.member_name,
        'Relationship': row.relationship_to_principal,
        'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details.principal_middle_name}` : '',
        'System Remarks': row && row.remarks ? row.remarks : '',
        'Remarks': row && row.user_remarks ? row.user_remarks : ''
      }

      let possibleMatchNames: any[] = [];
      let possibleMatchBatchNo: any[] = [];
      let possibleMatchBirthdate: any[] = [];

      row.member_details.possible_principals.map(principal => {
        possibleMatchNames.push(`${principal.first_name} ${principal.middle_name} ${principal.last_name}`)
        possibleMatchBatchNo.push(principal.batch_name)
        possibleMatchBirthdate.push(principal.date_of_birth ? moment(principal.date_of_birth).format('MM/DD/YYYY') : '')
      })

      newObj['Possible Match Name'] = possibleMatchNames;
      newObj['Possible Match Batch No'] = possibleMatchBatchNo;
      newObj['Possible Match Birthdate'] = possibleMatchBirthdate;

      return newObj;

    })

    return temp
  }

  const getMappedValidation = (json) => {

    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': row.member_name,
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': row.gender,
      'Member Type': row && row.member_type ? row.member_type : '',
      'Civil Status': row.civil_status,
      'Relationship': row.relationship_to_principal,
      'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      'Plan Type': row.plan_type,
      // 'Supporting Documents Needed': '',
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));
    return temp
  }

  
  const getMappedSupporting = (json) => {
    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': row.member_name,
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': row.gender,
      'Civil Status': row.civil_status,
      'Member Type': row.type,
      'Plan Type': row.plan_type,
      'Relationship': row.relationship_to_principal,
      'Principal Name':row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      // 'Supporting Documents Needed': '',
      // 'Remarks': row.remarks,
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));
    return temp
  }

  const getMappedDisapproved = (json) => {
    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': row.member_name,
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Gender': row && row.gender ? row.gender : '',
      'Member Type': row && row.member_type ? row.member_type : '', 
      'Civil Status': row.civil_status,
      'Relationship': row.relationship_to_principal,
      'Principal Name': row && row.member_details?.principal_last_name ? `${row.member_details?.principal_last_name}, ${row.member_details?.principal_first_name}, ${row.member_details?.principal_middle_name}` : '',
      'Plan Type': row && row.plan_type ? row.plan_type : '',
      // 'Supporting Documents Needed': '',
      // 'System Remarks': 'Disapproved by Verifier',
      // 'System Remarks':  row.member_details.validation_status.user_remarks, //row.member_details.validation_status.message,
      // 'Remarks': row.member_details.validation_status.user_remarks,
      // 'Remarks': row.remarks,
      // 'Remarks': row.user_remarks ? row.user_remarks : ''
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));
    return temp
  }

  const getMappedApproved = (json) => {
    const temp = json.map((row, idx) => ({
      'No': idx + 1,
      'Member Name': row.member_name,
      'Plan Type': row.plan_type,
      'Account Number': '',
      'Birthdate': row.date_of_birth ? moment(row.date_of_birth).format('MM/DD/YYYY') : '',
      'Member Type': row.member_type,
      'Gender': row.gender,
      'Effectivity Date': row.effectivity_date ? moment(row.effectivity_date).format('MM/DD/YYYY') : '',
      'Civil Status': row.civil_status,
      'Relationship': row.relationship_to_principal,
      'Principal Name': row && row.principal_name ? row.principal_name : '',
      'System Remarks': row && row.remarks ? row.remarks : '',
      'Remarks': row && row.user_remarks ? row.user_remarks : ''
    }));


    return temp
  }

  const getSelectedTableCount = () => {
    let counter = 0;
    if (state.partial === true) { counter += 1 }
    if (state.incomplete === true) { counter += 1 }
    if (state.conflict === true) { counter += 1 }
    if (state.unmatched === true) { counter += 1 }
    if (state.validation === true) { counter += 1 }
    if (state.disapproved === true) { counter += 1 }
    if (state.approved === true) { counter += 1 }
    if (state.supporting === true) { counter += 1 }
    return counter
  }

  const handleCloseModalProps = () => {
    setModalProps({
      ...modalProps,
      open: false
    })
  };



  function handleEmailReport(data: any, tableData: any) {
    // console.log('final report data', data)
    setEmailReportModalOpen(false);

    if (data && data['receivers'] && data['attachments']) {
      setIsLoading(true);
      API.postEmailReport(
        data['receivers'],
        data['attachments'],
        data['batch'] ? data['batch'] : undefined,
        data['filename'] ? data['filename'] : undefined,
        data['sender'] ? data['sender'] : undefined,
      ).then(res => {
        if (res && res.error && res.error.message) {
          setIsLoading(false);
          console.log('postEmailReport err', res.error)
          setDialogModalState({
            isOpen: true,
            title: 'Email Report failed',
            message: res.error.message,
            tableData: null
          })
        } else if (res) {
          setIsLoading(false);
          console.log('postEmailReport res', res)
          setDialogModalState({
            isOpen: true,
            title: 'Email Report has been sent',
            message: 'The report has been sent to the following lists.',
            tableData: {
              rows: tableData['rows'] ? tableData['rows'] : [],
              columns: tableData['columns'] ? tableData['columns'] : [],
              columnExtensions: tableData['columnExtensions'] ?
                tableData['columnExtensions'] : [],
              formattedColumns: tableData['formattedColumns'] ?
                tableData['formattedColumns'] : {}
            }
          })
        }
      })
    }
  }

  function closeEmailReportModal() {
    setEmailReportModalOpen(false);
  }

  // function openEmailReportModal() {
  //   setEmailReportModalOpen(true);
  // }

  function openEmailExceptionReportModal() {
    setEmailExceptionReportModalOpen(true);
  }

  function handleMarkAsDoneClick() {
    setConfirmModalTitle('Proceed Confirmation');
    setConfirmModalMessage('Are you sure want to mark this ticket as "Done"?');
    setIsConfirmModalOpen(true);
  }

  function handleMarkAsDone() {
    setIsConfirmModalOpen(false);
    setIsLoading(true);

    const validatedMembers = _.get(
      props.memberUploadData,
      'validated_members',
      [],
    );
    const membersArr = _.get(
      props.memberUploadData,
      'members',
      [],
    );

    // SEPT 17, 2020: MVP-MS961 (New print ID ticket for newly approved members)
    let newlyApprovedCnt: number = 0;
    let newlyApprovedMemberIds: string[] = [];
    if (validatedMembers) {
      let approvedMembers: any[] = validatedMembers.length > 0 ? validatedMembers.filter(data =>
        data['validation_status'] &&
        data['validation_status']['status'] &&
        data['validation_status']['status'] === 'APPROVED'
      ) : [];
      console.log('initApprovedMembers val', initApprovedMembers)
      console.log('approvedMembers val', approvedMembers)

      approvedMembers.forEach((data, idx) => {
        // OLD IMPLEMENTATION
        // if (initApprovedMembers.length === 0) {
        //   newlyApprovedCnt++;
        //   newlyApprovedMemberIds.push(data['temp_id']);
        // } else if (data['_id'] && initApprovedMembers.length > 0 &&
        //   initApprovedMembers.map(iData => iData['_id']).indexOf(data['_id']) === -1) {
        //   newlyApprovedCnt++;
        //   newlyApprovedMemberIds.push(data['temp_id']);
        // }

        // NEW IMPLEMENTATION
        let memberIdx: number = membersArr.map(mData => mData['temp_id']).indexOf(data['temp_id']);

        if (
          memberIdx !== -1 && membersArr[memberIdx] &&
          (
            !membersArr[memberIdx]['case_id'] ||
            (membersArr[memberIdx]['case_id'] && membersArr[memberIdx]['case_id'].length === 0)
          )
        ) {
          console.log(`[${idx}] member data`, membersArr[memberIdx])
          newlyApprovedCnt++;
          newlyApprovedMemberIds.push(data['temp_id']);
        }
      })
    }

    console.log('newlyApprovedCnt val', newlyApprovedCnt)
    if (newlyApprovedCnt > 0) {
      console.log('there are newly approved members. Proceeding to Print ID Cards ticket creation.');
      createPrintCardIDTicket((pmaker_case_uid) => {
        props.handleOpenLostInternetConnectionModal();
        API.patchValidateResolve(id, pmaker_case_uid, newlyApprovedMemberIds)
          .then(response => {
            if (
              response.error &&
              response.error !== undefined &&
              response.error === null
            ) {
              setIsLoading(false);
              setVariant('error');
              setSnackbarMessage('Error member validation.');
              setOpenSnackbar(true);
            } else {
              routeTicket();
            }
          })
          .catch(e => {
            setIsLoading(false);
            if (e.message === 'USER_DATA_NOT_FOUND') {
              setIsOpenModal(true);
              setModalTitle('User Data Error');
              setModalMessage('User data not found. Please try logging in again.');
              setNext(() => {
                return () => {
                  // window.location.replace('../index.html#/');
                };
              });
            }
          });
      });
    } else {
      console.log('there are no newly approved members');
      props.handleOpenLostInternetConnectionModal();
      API.patchValidateResolve(id, case_id)
        .then(response => {
          if (
            response.error &&
            response.error !== undefined &&
            response.error === null
          ) {
            setIsLoading(false);
            setVariant('error');
            setSnackbarMessage('Error member validation.');
            setOpenSnackbar(true);
          } else {
            routeTicket();
          }
        })
        .catch(e => {
          setIsLoading(false);
          if (e.message === 'USER_DATA_NOT_FOUND') {
            setIsOpenModal(true);
            setModalTitle('User Data Error');
            setModalMessage('User data not found. Please try logging in again.');
            setNext(() => {
              return () => {
                // window.location.replace('../index.html#/');
              };
            });
          }
        });
    }
    //
  }

  function routeTicket() {
    const validatedMembers = _.get(
      props.memberUploadData,
      'validated_members',
      [],
    );

    console.log('lodash validatedMembers', validatedMembers)
    // check if there are validated members with for confirmation status
    const hasMembersForConfirmation = _.some(
      validatedMembers,
      validatedMember => {
        const validationAction = _.get(
          validatedMember,
          'validation_status.action',
          null,
        );
        const validationStatus = _.get(
          validatedMember,
          'validation_status.status',
          null
        )
        console.log(validationStatus, validatedMember.first_name, validatedMember.last_name);
        return (
          !(validationStatus === '' || validationStatus === 'APPROVED' || validationStatus === 'DISAPPROVED')
          && (validationAction === null || (validationAction != null &&
            (validationAction === 'confirmation' ||
              validationAction === 'for confirmation')))
        );
      },
    );
    // do not route ticket
    if (hasMembersForConfirmation) {
      console.log('unable to close the ticket')
      setIsLoading(false);
      setRedirectToDashboard(true);

      return;
    }
    console.log('proceed to close the ticket')

    // API.deleteMemberUpload(member_upload_id).then(response => {
    //   console.log('deleteMemberUpload response: ', response);
    //   API.deleteMemberUploadData(member_upload_id)
    //     .then(response2 => {
    //       console.log('deleteMemberUploadData response: ', response2);
          // Route ticket of Verifier
          Processmaker.put('cases/' + case_id + '/variable', {
            pressed_reassign: false,
          }).then(() => {
            Processmaker.put('cases/' + case_id + '/route-case', {})
              .then(() => {
                setIsLoading(false);
                // Create Ticket for Print Card IDs
                let payload = {
                  status: 'CLOSED',
                };
                API.patchTicket(payload, case_id, true)
                  .then(() => {
                    setIsLoading(false);
                    setRedirectToDashboard(true);
                  })
                  .catch(() => {
                    setIsLoading(false);
                    setSnackbarNotifMsg(
                      'Something went wrong. Please try again later.',
                    );
                    setSnackbarNotifVariant('error');
                    setIsSnackbarNotifOpen(true);
                  });
              })
              .catch(e => {
                setIsLoading(false);
                if (e.message === 'USER_DATA_NOT_FOUND') {
                  setIsOpenModal(true);
                  setModalTitle('User Data Error');
                  setModalMessage(
                    'User data not found. Please try logging in again.',
                  );
                  setNext(() => {
                    return () => {
                      // window.location.replace('../index.html#/');
                    };
                  });
                } else {
                  setSnackbarNotifMsg(
                    'Something went wrong. Please try again later.',
                  );
                  setSnackbarNotifVariant('error');
                  setIsSnackbarNotifOpen(true);
                }
              });
          });
    //     })
    //     .catch(e => {
    //       setIsLoading(false);
    //       if (e.message === 'USER_DATA_NOT_FOUND') {
    //         setIsOpenModal(true);
    //         setModalTitle('User Data Error');
    //         setModalMessage(
    //           'User data not found. Please try logging in again.',
    //         );
    //         setNext(() => {
    //           return () => {
    //             // window.location.replace('../index.html#/');
    //           };
    //         });
    //       } else {
    //         setSnackbarNotifMsg(
    //           'Something went wrong. Please try again later.',
    //         );
    //         setSnackbarNotifVariant('error');
    //         setIsSnackbarNotifOpen(true);
    //       }
    //     });
    // });
  }

  function createPrintCardIDTicket(callback) {
    // setIsLoading(true);
    Processmaker.post('cases/', {
      pro_uid: '3363390215e4a46e1d4c9c3014822793',
      tas_uid: '4476339725e4a470aacd7b3087643861',
    })
      .then(response => {
        console.log('createPrintCardIDTicket', response);
        // setIsLoading(false);
        if (response.app_uid !== undefined) {
          const ticketId = response.app_uid;
          setTicketVariable(ticketId, response, response.app_uid, callback);
        }
      })
      .catch(e => {
        console.log('createAddMemberTicket', e);

        setIsLoading(false);
        if (e.message === 'USER_DATA_NOT_FOUND') {
          setIsOpenModal(true);
          setModalTitle('User Data Error');
          setModalMessage('User data not found. Please try logging in again.');
          setNext(() => {
            return () => {
              // window.location.replace('../index.html#/');
            };
          });
        }
      });
  }

  function setTicketVariable(appUid: string, pmaker_response: any, case_id: any, callback: any) {
    Processmaker.put('cases/' + appUid + '/variable', {
      client_id: client_id || '',
      client_name: client_name || '',
      from_membership: true,
      old_case_id: case_id
    })
      .then(response => {
        console.log('setTicketVariable', response);
        Processmaker.put('cases/' + appUid + '/route-case', {})
          .then(() => {
            let payload = {
              status: 'UNASSIGNED',
              ticket_id: pmaker_response.app_number.toString(),
              pmaker_case_uid: appUid,
              ticket_type: 'Print Card IDs',
              client: client_name,
              client_id: client_id,
            };
            API.postTicket(payload)
              .then(() => {
                // setIsLoading(false);
                // setRedirectToDashboard(true);
                callback(appUid);
              })
              .catch(() => {
                setIsLoading(false);
                setSnackbarNotifMsg(
                  'Something went wrong. Please try again later.',
                );
                setSnackbarNotifVariant('error');
                setIsSnackbarNotifOpen(true);
              });
          })
          .catch(e => {
            setIsLoading(false);
            if (e.message === 'USER_DATA_NOT_FOUND') {
              setIsOpenModal(true);
              setModalTitle('User Data Error');
              setModalMessage(
                'User data not found. Please try logging in again.',
              );
              setNext(() => {
                return () => {
                  // window.location.replace('../index.html#/');
                };
              });
            } else {
              setSnackbarNotifMsg(
                'Something went wrong. Please try again later.',
              );
              setSnackbarNotifVariant('error');
              setIsSnackbarNotifOpen(true);
            }
          });
      })
      .catch(e => {
        console.log('setTicketVariable', e);
        if (e.message === 'USER_DATA_NOT_FOUND') {
          setIsOpenModal(true);
          setModalTitle('User Data Error');
          setModalMessage('User data not found. Please try logging in again.');
          setNext(() => {
            return () => {
              // window.location.replace('../index.html#/');
            };
          });
        }
      });
  }

  const handleOnCloseEmailExceptionReportModal = () => {
    setSelectedTemplate({});
    setEmailExceptionReportModalOpen(false);
    setEmailTemplateOption([]);
    // setEmailTemplateFilter('');
  }

  const handleOnChangeSelect = (value) => {
    setSelectedTemplate(value);
  }

  const handleOnPreview = () => {
    if (Object.keys(selectedTemplate).length !== 0) {
      console.log(`preview:`, selectedTemplate);
      setPreviewModalOpen(!previewModalOpen);
      return;
    }
    setAlertContent('Please select an Email Template');
    setAlertModalOpen(true);
  }

  useEffect(() => {
    console.log(`selected:`, selectedTemplate);
  }, [selectedTemplate])


  useEffect(() => {
    const getEmailTemplates = () => {
      API.getEmailTemplatesWithPaging({}, 1).then(response => {
        let templates = response.emailTemplates;
        let result = templates.map((template) => {
          return { value: template._id, label: template.title, body: template.description }
        });
        setEmailTemplateOption(result);
      })
    };
    getEmailTemplates();
    console.log('called');
  }, [emailExceptionReportModalOpen]);

  useEffect(() => {
    console.log('category selected:', selectedCategories);
  }, [selectedCategories])

  useEffect(() => {
    const getData = async () => {
      setIsLoading(true)
      await API.getClient(client_id)
        .then(response => {
          setActionMemoRecipients(response.action_memo_recipient || {});
          setIsLoading(false)
        })
    }
    getData();
  }, [])

  const handleOnCheckCategory = (event, id) => {
    if (event.target.checked) {
      setSelectedCategories([...selectedCategories, id]);
    } else {
      setSelectedCategories(selectedCategories.filter((value) => value !== id));
    }
    console.log('category target:', event.target.checked);
  }

  const handleOnSendEmailExceptionReport = () => {
    let exceptionReport:any[] = [];
    let membersIncluded: number = 0;
    

    selectedCategories.map((category) => {
      if (category === "partial") {
        exceptionReport.push({ partial: partial['rows'] })
      }
      if (category === "incomplete") {
        exceptionReport.push({ incomplete: incomplete['rows'] })
      }
      if (category === "conflict") {
        exceptionReport.push({ conflict: conflict['rows'] })
      }
      if (category === "unmatched") {
        exceptionReport.push({ unmatched: validation['rows'] })
      }
      if(category === 'supporting') {
        exceptionReport.push({supporting:supporting['rows']})
      }
      if(category === "validation") {
        // exceptionReport.push({validation:validation['rows']})
        exceptionReport.push({validation:othervalidation['rows']})
      }
      if (category === "disapproved") {
        exceptionReport.push({ disapproved: disapproved['rows'] })
      }
      if (category === "approved") {
        exceptionReport.push({ approved: approved['rows'] })
      }
    });

    const payload = {
      template: selectedTemplate,
      recipients: actionMemoRecipients,
      exception_reports: exceptionReport,
    };

    //CREATE ACTION MEMO HERE

    if (Object.keys(selectedTemplate).length === 0) {
      setAlertContent('Please select an Email Template');
      setAlertModalOpen(true);
      return;
    }

    if (!actionMemoRecipients || actionMemoRecipients?.direct?.length === 0) {
      setAlertContent('Unable to send, Please add Direct Recipient(s)');
      setAlertModalOpen(true);
      return;
    }

    //SEND EMAIL REPORT 
    setIsLoading(true);
    //await
     API.sendEmailExceptionReport(payload).then(response => {
      console.log('Email payload: ', response)

      //RECORD ACTION MEMO HISTORY   
      if (props.batchDetails.data['members']) {
        if (props.batchDetails.data['members'].length > 0) {
          membersIncluded = props.batchDetails.data['members'].length
        }
      }


      Utils.StorageService('user_data').then(async result => {
        let user_name: string = '';
        if (result && result !== undefined) {
          for (const i in result) {
            console.log('memo history: ', result[i]['key']);
            if (result[i]['key'] === 'username') {
              user_name = result[i]['value'];
            }
          }
        };

        console.log('asdasdasd: ', user_name)

        const action_memo_history_payload: any = {
          ticket_id: props.batchDetails.data['batch_name'],
          date_sent: moment(new Date).format('MM/DD/YYYY hh:mm A'),
          members_included: membersIncluded,
          template: payload,
          //sent_by: `${localStorage.getItem('first_name')} ${localStorage.getItem('last_name')}`
          sent_by: user_name
        }

        //Send Action Memo History Data in Clients Profile
        await API.saveActionMemoHistory(action_memo_history_payload, client_id).then(response => {
          console.log('Memo History Payload: ', response)
          setIsLoading(false);
          handleOnCloseEmailExceptionReportModal();
          setAlertContent('Email Exception Report has been successfully sent');
          setAlertModalOpen(true);
        }).catch(error => {
          setIsLoading(false);
          handleOnCloseEmailExceptionReportModal();
          setAlertContent('Email Exception Report failed to send');
          setAlertModalOpen(true);
          console.log('Email exception report error: ', error)
        });

        //Send Action Memo Activity Data in User Management
        await API.activitySendActionMemo(props.batchDetails.data['batch_name'], action_memo_history_payload, user_name).then(response => {
          console.log('response', response)
        }).catch(e => {
          console.log('activity send action memo error: ', e)
        })
      })



    setSelectedCategories([])
    }).catch(error => {
      setIsLoading(false);
      handleOnCloseEmailExceptionReportModal();
      setAlertContent('Email Exception Report failed to send');
      setAlertModalOpen(true);
      setSelectedCategories([])
      console.log('Email exception report error: ', error)
    });
  }

  const handleOnClickGridTab = (index) => {
    setSelectedTab(index)
  }

  const rowDataFixer = (array: any) => {
    let newEntry: any = [];
    if (array && array.length > 0) {
      array.forEach((a, idx) => {
          // console.log("RowDataFixer", a)
          const partialSearch = partial['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const incompleteSearch = incomplete['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const conflictSearch = conflict['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const validationSearch = validation['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const supportingSearch = supporting['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const otherValidSearch = othervalidation['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const disapprovedSearch = disapproved['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          const approvedSearch = disapproved['rows'].findIndex(x => x.member_details.temp_id === a.member_details.temp_id)
          //If member is not in each table, other than approved, then push to approved table
          if (partialSearch === -1 && 
              incompleteSearch === -1 && 
              conflictSearch === -1 &&
              validationSearch === -1 &&
              supportingSearch === -1 &&
              otherValidSearch === -1 &&
              disapprovedSearch === -1 &&
              approvedSearch === -1
              ) {
                

            let iApprovedOrDisapprovedStatus = false
            let remarksCombined: { temp_id: string, system_remarks: string, user_remarks: string } = {
                temp_id: '',
                system_remarks: '',
                user_remarks: ''
            }

            if(a.member_details && a.member_details?.validation_status){
              if( a.member_details?.validation_status?.status.toLowerCase() === 'approved' || a.member_details?.validation_status?.status.toLowerCase() === 'diapproved' ){
                iApprovedOrDisapprovedStatus = true
                remarksCombined = combineRemarks(allValidated, a.member_details?.validation_status?.status, a.member_details.temp_id, false)
            
              }
            }

                
            let newRow = {
                no: idx+1,
                member_name: a.hasOwnProperty('member_name') ? a.member_name : '',
                plan_type: a.hasOwnProperty('plan_type') ? a.plan_type : '',
                member_id: a.member_details.hasOwnProperty('member_id') ? a.member_details.member_id : '',
                date_of_birth: a.hasOwnProperty('date_of_birth') ? a.date_of_birth : '',
                gender: a.hasOwnProperty('gender') ? a.gender : '',
                civil_status: a.hasOwnProperty('civil_status') ? a.civil_status : '',
                member_type: a.hasOwnProperty('member_type') ? a.member_type : '',
                relationship_to_principal: a.hasOwnProperty('relationship_to_principal') ? a.relationship_to_principal : '',
                principal_name: a.hasOwnProperty('principal_name') ? a.principal_name : '',
                effectivity_date: a.hasOwnProperty('effectivity_date') ? a.effectivity_date : '',
                user_remarks:  iApprovedOrDisapprovedStatus ? remarksCombined?.user_remarks : a.member_details.user_remarks, 
                remarks:  iApprovedOrDisapprovedStatus ? remarksCombined?.system_remarks : a.member_details.system_remarks, 
            }
     
            newEntry.push(newRow)
      }
    })

    return newEntry;
    }

    return [];
  }

  let testOtherTable = othervalidation['rows'].filter((ele,ind) => 
    ind === othervalidation['rows'].findIndex(elem => 
      elem.member_details.temp_id === ele.member_details.temp_id &&
      elem.member_details.validation_status.message && 
      ele.member_details.validation_status.message
    ))

    othervalidation['rows'] = testOtherTable

  if (redirectToDashboard) { //columns
    return <Redirect to={{ pathname: '/membership/' }} />;
  } else {
    return (
      <Grid container style={{ flex: 1 }}>
        <Grid item xs={12}>
          {isLoading ? <Loader /> : null}
        </Grid>
        <Grid container className={clsx('page-header-title')}>
          <Grid item xs={12} style={{ marginBottom: '30px' }}>
            <MemberInformation
              data={batchDetails.data}
              corporateAccountNo={batchDetails.corporateAccountNo}
              corporateAccountName={batchDetails.corporateAccountName}
              contractName={batchDetails.contractName}
              contractId={batchDetails.contractId} //Ann
              pageType={batchDetails.pageType}
              ocpOrigin={batchDetails.ocpOrigin}
              member_id={batchDetails.member_id}
              generateFlag={batchDetails.generateFlag}
              benefitPlanTree={batchDetails.benefitPlanTree}
              handleRedirect={() => { }}
              batch_ticket_id={batchDetails.batch_ticket_id}
            />
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={8}>
              <Typography className={clsx('sub-title')} color="textPrimary">
                Exception Report
              </Typography>
            </Grid>
            <Grid item xs={4}>
              {/* <span
                id='email_report'
                data-cy='email_report'
                className={classes.iconsSpanOne}
                onClick={openEmailReportModal}
              >
                <FontAwesomeIcon icon={faEnvelope} className={classes.icons} />{' '}
                Email Report
              </span> */}

              <span
                id='email_exception_report'
                data-cy='email_exception_report'
                className={classes.iconsSpanOne}
                onClick={openEmailExceptionReportModal}
              >
                <FontAwesomeIcon icon={faEnvelope} className={classes.icons} />{' '}
                Email Exception Report
              </span>

              <span
                id='download_report'
                data-cy='download_report'
                ref={anchorRef}
                className={classes.iconsSpanTwo}
                onClick={handleOpenPopper}
              >
                <FontAwesomeIcon icon={faDownload} className={classes.icons} />{' '}
                Download Report
                <Popper
                  open={isPopoverOpen}
                  anchorEl={anchorRef.current}
                  placement={'bottom-end'}
                  className={classes.popper}
                  disablePortal={false}
                  style={{ marginTop: '4px' }}
                  modifiers={{
                    flip: {
                      enabled: false,
                    },
                    preventOverflow: {
                      enabled: true,
                      boundariesElement: 'undefined',
                    },
                    arrow: {
                      enabled: true,
                      element: arrowRef,
                    },
                  }}
                >
                  <span className={classes.arrow} ref={setArrowRef} />
                  <Paper className={classes.paper}>
                    <ClickAwayListener onClickAway={handleClose}>
                      <Grid container>
                        <Grid item xs={12}>
                          <Typography
                            className={classes.textLabel}
                            color="textPrimary"
                          >
                            Choose Data
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <RadioGroup
                            aria-label="choose-data"
                            name="choose-data"
                            value={selectedData}
                            style={{ paddingLeft: '5px' }}
                            onChange={e => setSelectedData(e.target.value)}
                          >
                            <FormControlLabel
                              value="Include entire list"
                              control={
                                <Radio
                                  id='radio_include_entire_list'
                                  data-cy='radio_include_entire_list'
                                  style={{ padding: '5px' }}
                                  color="primary"
                                />
                              }
                              label="Include entire list"
                              labelPlacement="end"
                            />
                            <FormControlLabel
                              value="Custom list"
                              control={
                                <Radio
                                  id='radio_custom_list'
                                  data-cy='radio_custom_list'
                                  style={{ padding: '5px' }}
                                  color="primary"
                                />
                              }
                              label="Custom list"
                              labelPlacement="end"
                            />
                          </RadioGroup>
                        </Grid>
                        {selectedData === 'Custom list' ? (
                          <Grid item xs={12} className={classes.grid}>
                            <FormGroup>
                              <FormControlLabel
                                checked={state.partial}
                                control={
                                  <Checkbox
                                    id='checkbox_partial'
                                    data-cy='checkbox_partial'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Partial Matches"
                                onChange={e => handleChange(e, 'partial')}
                              />
                              <FormControlLabel
                                checked={state.incomplete}
                                control={
                                  <Checkbox
                                    id='checkbox_incomplete'
                                    data-cy='checkbox_incomplete'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Incomplete Requirements"
                                onChange={e => handleChange(e, 'incomplete')}
                              />
                              <FormControlLabel
                                checked={state.conflict}
                                control={
                                  <Checkbox
                                    id='checkbox_conflict'
                                    data-cy='checkbox_conflict'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Conflict on Data"
                                onChange={e => handleChange(e, 'conflict')}
                              />
                              <FormControlLabel
                                checked={state.unmatched}
                                control={
                                  <Checkbox
                                    id='checkbox_validation'
                                    data-cy='checkbox_validation'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Unmatched Dependents"
                                onChange={e => handleChange(e, 'unmatched')}
                              />
                              <FormControlLabel
                                checked={state.supporting}
                                control={
                                  <Checkbox
                                    id='checkbox_supporting'
                                    data-cy='checkbox_supporting'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Requiring Supporting Documents"
                                onChange={e => handleChange(e, 'supporting')}
                              />
                              <FormControlLabel
                                checked={state.validation}
                                control={
                                  <Checkbox
                                    id='checkbox_validation'
                                    data-cy='checkbox_validation'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Other Validation Rules"
                                onChange={e => handleChange(e, 'validation')}
                              />
                              <FormControlLabel
                                checked={state.disapproved}
                                control={
                                  <Checkbox
                                    id='checkbox_disapproved'
                                    data-cy='checkbox_disapproved'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Disapproved"
                                onChange={e => handleChange(e, 'disapproved')}
                              />
                              <FormControlLabel
                                checked={state.approved}
                                control={
                                  <Checkbox
                                    id='checkbox_approved'
                                    data-cy='checkbox_approved'
                                    style={{ padding: '5px' }}
                                    color="primary"
                                  />
                                }
                                label="Approved"
                                onChange={e => handleChange(e, 'approved')}
                              />
                            </FormGroup>
                          </Grid>
                        ) : null}
                        <Grid item xs={12}>
                          <Typography
                            className={classes.textLabel}
                            color="textPrimary"
                          >
                            Choose Format
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <RadioGroup
                            aria-label="choose-format"
                            name="choose-format"
                            value={selectedFormat}
                            style={{ paddingLeft: '5px' }}
                            onChange={e => setSelectedFormat(e.target.value)}
                          >
                            <FormControlLabel
                              value="PDF"
                              control={
                                <Radio
                                  id='radio_pdf'
                                  data-cy='radio_pdf'
                                  style={{ padding: '5px' }}
                                  color="primary"
                                />
                              }
                              label="PDF"
                              labelPlacement="end"
                            />
                            <FormControlLabel
                              value="XLS"
                              control={
                                <Radio
                                  style={{ padding: '5px' }}
                                  color="primary"
                                />
                              }
                              label="XLS"
                              labelPlacement="end"
                            />
                            {/* <FormControlLabel
                              value="XML"
                              control={
                                <Radio
                                  style={{ padding: '5px' }}
                                  color="primary"
                                />
                              }
                              label="XML"
                              labelPlacement="end"
                            /> */}
                          </RadioGroup>
                        </Grid>
                        <Grid
                          item
                          xs={12}
                          style={{ textAlign: 'center', marginTop: '10px' }}
                        >
                          <Button
                            id='button_download_file'
                            data-cy='button_download_file'
                            className={classes.downloadBtn}
                            variant="contained"
                            color="secondary"
                            onClick={() => { handleDownloadFile(true) }}
                          >
                            Download File
                          </Button>
                        </Grid>
                      </Grid>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
              </span>
            </Grid>
          </Grid>
        </Grid>

        {/* Partial Matches Table */}
        <Grid
          item
          xs={12}
          className={classes.gridtable}
          style={{ marginTop: '17px' }}
        >
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Partial Matches ({partial['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="partial-matches"
                rows={Object.keys(partial).length > 0 ? partial['rows'] : []}
                columns={
                  Object.keys(partial).length > 0 ? partial['columns'] : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members were partially matched"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(partial).length > 0
                    ? partial['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Incomplete Req Table */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Incomplete Requirements ({incomplete['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12} style={{flex: 1}}>
              <TableComponent
                id="incomplete-requirements"
                rows={
                  Object.keys(incomplete).length > 0 ? incomplete['rows'] : []
                }
                columns={
                  Object.keys(incomplete).length > 0
                    ? incomplete['columns2']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members with incomplete requirements"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(incomplete).length > 0
                    ? incomplete['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>
        {/* Conflict On Data Table */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Conflict on Data ({conflict['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="conflict-requirements"
                rows={Object.keys(conflict).length > 0 ? conflict['rows'] : []}
                columns={
                  Object.keys(conflict).length > 0 ? conflict['columns'] : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members with conflict on data"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(conflict).length > 0
                    ? conflict['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Unmatched DependentsTable */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Unmatched Dependents ({validation['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="for-validation"
                rows={
                  Object.keys(validation).length > 0 ? validation['rows'] : []
                }
                columns={
                  Object.keys(validation).length > 0
                    ? validation['columns']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members to be validated"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(validation).length > 0
                    ? validation['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Requiring Supporting Documents Table */}
        {/* <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{flex: 1}}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Requiring Supporting Documents ({supporting['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="for-supporting"
                rows={
                  Object.keys(supporting).length > 0 ? supporting['rows'] : []
                }
                columns={
                  Object.keys(supporting).length > 0
                    ? supporting['columns']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members requiring supporting documents"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(supporting).length > 0
                    ? supporting['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid> */}
  {/* OTHER VALIDATION RULES */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Other Validation Rules ({othervalidation['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="for-othervalidation"
                rows={
                  Object.keys(othervalidation).length > 0 ? othervalidation['rows'] : []
                }
                columns={
                  Object.keys(othervalidation).length > 0
                    ? othervalidation['columns']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members to be validated"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(othervalidation).length > 0
                    ? othervalidation['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

  {/* Req Suppporting Documents Table */}
           <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{flex: 1}}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Requiring Supporting Documents ({supporting['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="supporting"
                rows={
                  Object.keys(supporting).length > 0 ? supporting['rows'] : []
                }
                columns={
                  Object.keys(supporting).length > 0
                    ? supporting['columns']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members to be validated"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(supporting).length > 0
                    ? supporting['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

         {/* Disapproved Table */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Disapproved ({disapproved['rows'].length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="disapproved-requirements"
                rows={
                  Object.keys(disapproved).length > 0 ? disapproved['rows'] : []
                }
                columns={
                  Object.keys(disapproved).length > 0
                    ? disapproved['columns']
                    : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members were disapproved"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(disapproved).length > 0
                    ? disapproved['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Approved Table */}
        <Grid item xs={12} className={classes.gridtable}>
          <Grid container style={{ flex: 1 }}>
            <Grid item xs={12}>
              <Typography className={classes.label} color="textPrimary">
                Approved ({rowDataFixer(approved['rows']).length})
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TableComponent
                id="approved-requirements"
                rows={Object.keys(approved).length > 0 ? rowDataFixer(approved['rows']) : []}
                columns={
                  Object.keys(approved).length > 0 ? approved['columns'] : []
                }
                onClickRow={handleClickRow}
                message="No enrolled members were approved"
                disableSelect
                disableSearch
                disableFilter
                disableSort
                disablePaging
                formattedColumns={{}}
                columnExtensions={
                  Object.keys(approved).length > 0
                    ? approved['columnExtensions']
                    : []
                }
              />
            </Grid>
          </Grid>
        </Grid>
        <ModalComponent
          id="exception_report_modal"
          data-cy='exception_report_modal'
          isModalOpen={isOpenModal}
          title={modalTitle}
          message={modalMessage}
          onClose={closeModalMessage}
        />
        <EmailReportModal
          isModalOpen={emailReportModalOpen}
          id="partial-match-modal"
          data-cy='partial-match-modal'
          client_name={props.client_name}
          client_accNo={props.client_accNo}
          uploadId={id}
          uploadData={data}
          memberUploadData={props.memberUploadData}
          receiverName={props.memberUploadData ? props.memberUploadData['sender_name'] : ''}
          email={email}
          onClose={closeEmailReportModal}
          onSend={handleEmailReport}
        />

        <EmailExceptionReportModal
          isModalOpen={emailExceptionReportModalOpen}
          onClose={handleOnCloseEmailExceptionReportModal}
          optionsEmailTemplates={emailTemplateOption}
          optionsCategories={categories}
          onChangeSelect={(value) => handleOnChangeSelect(value)}
          onPreview={handleOnPreview}
          emails={selectedTab === 0 && actionMemoRecipients ? actionMemoRecipients.direct : actionMemoRecipients.cc}
          onClickGridTab={handleOnClickGridTab}
          selectedTab={selectedTab}
          gridTabs={['Direct Recipients', 'CC Recipients']}
          onCheckCategory={handleOnCheckCategory}
          onSendEmailExceptionReport={handleOnSendEmailExceptionReport}
        />

        <PreviewModal
          isModalOpen={previewModalOpen}
          onClose={() => setPreviewModalOpen(!previewModalOpen)}
          body={selectedTemplate.body}
          title={selectedTemplate.label}
        />

        <AlertModal
          isModalOpen={alertModalOpen}
          onClose={() => setAlertModalOpen(!alertModalOpen)}
          title={'Message'}
          content={alertContent}
        />

        <ConfirmationModalComponent
          id="verify-member-confirm-modal"
          data-cy='verify-member-confirm-modal'
          isModalOpen={isConfirmModalOpen}
          modalTitle={confirmModalTitle}
          modalMessage={confirmModalMessage}
          closeText="Cancel"
          confirmText="Proceed"
          onClose={() => {
            setIsConfirmModalOpen(false);
          }}
          onConfirm={handleMarkAsDone}
        />
        <Snackbar
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          open={openSnackbar}
          autoHideDuration={6000}
          onClose={onSnackbarClose}
        >
          <MySnackbarContentWrapper
            onClose={onSnackbarClose}
            variant={variant}
            message={snackbarMessage}
          />
        </Snackbar>
        <SnackbarNotification
          isOpen={isSnackbarNotifOpen}
          message={snackbarNotifMsg}
          variant={snackbarNotifVariant}
          onSnackbarClose={() => {
            setIsSnackbarNotifOpen(false);
          }}
        />
        <Grid item xs={12}>
          <FloatingButtons
            leftButtons={
              props.ticket_closed ? null :
              <div>
                <Button
                  id='button_mark_as_done'
                  data-cy='button_mark_as_done'
                  className={classes.generateButton}
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={handleMarkAsDoneClick}
                >
                  Mark as Done
                </Button>
                {props.ocpOrigin && props.ocpOrigin === 'vm-ocp' ? (
                  <Button
                    id='button_publish_to_ocp'
                    data-cy='button_publish_to_ocp'
                    style={{ marginLeft: 10 }}
                    className={classes.generateButton}
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => {
                      setSelectedData('Include entire list');
                      setSelectedFormat('PDF');
                      handleDownloadFile(false);
                    }}
                  >
                    Publish To OCP
                  </Button>
                ) : null}
              </div>
            }
            rightButtons={
              <div>
                <Button
                  id={'exception_report_back_btn'}
                  data-cy='exception_report_back_btn'
                  className={classes.backButton}
                  variant="contained"
                  size="small"
                  onClick={props.handleBack}
                >
                  Back
                </Button>
              </div>
            }
          />
        </Grid>

        <ModalComponent
          id="upload_modal_error"
          isModalOpen={modalProps.open}
          title={modalProps.title}
          message={modalPropsMessage}
          onClose={modalProps.method}
        />

        <ModalComponent
          id="print_id_cards_dialog_modal"
          isModalOpen={dialogModalState['isOpen']}
          title={dialogModalState['title']}
          message={dialogModalState['message']}
          tableData={dialogModalState['tableData']}
          isPrimary
          onClose={() => {
            setDialogModalState({
              ...dialogModalState,
              isOpen: false,
              tableData: null,
            })
          }}
        />
      </Grid>
    );
  }
};

ExceptionReport.defaultProps = {
  data: [],
  memberUploadData: null,
};
