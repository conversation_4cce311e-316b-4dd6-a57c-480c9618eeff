import React, { useState, useEffect } from 'react';
import {
    Grid,
    Typography,
    Tabs,
    Tab,
} from '@material-ui/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faDownload,
    faEnvelope
} from '@fortawesome/free-solid-svg-icons';
import { TableComponent } from 'Components/UI/TableComponent';
import {
    RENEWED_MEMBERS_COLUMNS,
    EXCLUDED_MEMBERS_COLUMNS,
    CORPORATION_MEMBERS_COLUMNS,
    RENEWED_FORMATTED_COLUMNS,
    EXCLUDED_FORMATTED_COLUMNS,
    RENEWED_COLUMN_EXTENSIONS,
    EXCLUDED_COLUMN_EXTENSIONS,
    CORPORATION_COLUMN_EXTENSIONS,
} from './ExceptionReport.const'
import moment from 'moment';
import { get } from 'lodash';
import { useStyles } from './styles';
import { ReportSelectionModal } from '../Components/ReportSelectionModal'
// import { exportToExcel } from 'Components/UI/ConvertToExcelComponent'
import { ModalComponent } from 'Components/UI/ModalComponent';
import { Loader } from 'Components/UI/LoadingIndicator';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

import { EmailReportModal } from './EmailReportModal';
import { API } from '../../../../API';

interface ExceptionReportProps {
    reportData: any;
    memberDataFields?: any[];
}

declare global {
    interface Window {
        downloadFile: any
    }
}

export const ExceptionReport: React.FC<ExceptionReportProps> = (
    props: ExceptionReportProps
): JSX.Element => {
    const classes = useStyles();
    const {
        reportData,
        memberDataFields,
    } = props;

    // Add states for dynamic columns
    const [renewedColumns, setRenewedColumns] = useState<any[]>(RENEWED_MEMBERS_COLUMNS);
    const [excludedColumns, setExcludedColumns] = useState<any[]>(EXCLUDED_MEMBERS_COLUMNS);
    const [renewedColumnExtensions, setRenewedColumnExtensions] = useState<any[]>(RENEWED_COLUMN_EXTENSIONS);
    const [excludedColumnExtensions, setExcludedColumnExtensions] = useState<any[]>(EXCLUDED_COLUMN_EXTENSIONS);

    const generateDynamicColumns = (memberDataFields: any[]) => {
        const columns: any[] = [];
        const columnExtensions: any[] = [];
        
        // add Member ID as the first column
        columns.push({ name: 'member_id', title: 'Member ID' });
        columnExtensions.push({ columnName: 'member_id', wordWrapEnabled: true, width: 200 });
        
        if (memberDataFields && memberDataFields.length > 0) {
            const requiredFields = memberDataFields
                .filter(field => 
                    field.is_required === true && 
                    (field.field_type === 'Client-required' || field.field_type === 'Mandatory')
                )
                .sort((a, b) => a.sort_order - b.sort_order);
            
            requiredFields.forEach(field => {

                if (field.field_name === 'member_id') return;
                
                columns.push({
                    name: field.field_name,
                    title: field.system_name
                });
                
                columnExtensions.push({
                    columnName: field.field_name,
                    wordWrapEnabled: true,
                    width: undefined
                });
            });
        } else {
            const defaultFields = [
                { name: 'first_name', title: 'First Name' },
                { name: 'last_name', title: 'Last Name' },
                { name: 'member_type', title: 'Member Type' },
                { name: 'member_status_before_expiration', title: 'Status' }
            ];
            
            defaultFields.forEach(field => {
                columns.push(field);
                columnExtensions.push({
                    columnName: field.name,
                    wordWrapEnabled: true,
                    width: undefined
                });
            });
        }
        
        return {
            columns,
            columnExtensions
        };
    };

    const [renewedTabs, setRenewedTabs] = useState<any[]>([
        {
            label: 'Marked for Termination',
            count: 0,
        },
        {
            label: 'All',
            count: 0,
        },
    ]);
    const [tab, setTab] = useState<any>(0);

    const [renewedTableStates, setRenewedTableStates] = useState<any>({
        rows_marked: [],
        rows_all: [],
        rows: [],
        // other object fields here
    })

    const [excludedTableStates, setExcludedTableStates] = useState<any>({
        rows: [],
        // other object fields here
    })

    const [otherCorpsTableStates, setOtherCorpsTableStates] = useState<any>({
        rows: [],
        // other object fields here
    })

    const [currentPage, setCurrentPage] = React.useState<any>({
        renewed_marked: 0,
        renewed_all: 0,
        excluded: 0,
        other_corps: 0,
    });
    const [pageSize, setPageSize] = React.useState<any>({
        renewed_marked: 5,
        renewed_all: 5,
        excluded: 5,
        other_corps: 5,
    });
    const [modalProps, setModalProps] = React.useState({
        title: '',
        message: '',
        open: false,
        method: () => { }
    });

    const [dialogModalState, setDialogModalState] = React.useState<any>({
        isOpen: false,
        title: '',
        message: '',
        tableData: null,
    });

    const [isReportSelectionModal, setIsOpenReportSelectionModal] = React.useState<boolean>(false);
    const [emailReportModalOpen, setEmailReportModalOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState(false);


    useEffect(() => {
        if (Object.keys(reportData).length > 0) {
            if (reportData['renewed'] && reportData['renewed'].length > 0) {
                let renewedMembers = reportData['renewed'];

                let markedMembers: any[] = [];
                let allMembers: any[] = [];

                renewedMembers.forEach(rmData => {
                    if (rmData['member_termination']
                        && rmData['member_termination']['termination_date']) {
                        rmData['termination_date_display'] = rmData['member_termination']['termination_date'];
                    }

                    if (
                        rmData &&
                        get(rmData, 'member_status', '') !== 'Terminated' &&
                        rmData['member_termination'] &&
                        rmData['member_termination']['termination_date'] &&
                        moment(
                            rmData['member_termination']['termination_date'],
                        ).isValid() &&
                        moment(rmData['member_termination']['termination_date']).diff(
                            new Date(),
                        ) > 0
                    ) {
                        markedMembers.push(rmData)
                    } else {
                        allMembers.push(rmData)
                    }
                })
                setRenewedTableStates({
                    ...renewedTableStates,
                    rows_marked: markedMembers,
                    rows_all: allMembers,
                })

                let updatedTabs = renewedTabs.map((tab, idx) => {
                    if (idx === 0) {
                        tab['count'] = markedMembers.length
                    } else if (idx === 1) {
                        tab['count'] = allMembers.length
                    }

                    return tab;
                })
                setRenewedTabs(updatedTabs);
            }

            if (reportData['excluded'] && reportData['excluded'].length > 0) {
                setExcludedTableStates({
                    ...excludedTableStates,
                    rows: reportData['excluded'],
                })
            }

            if (reportData['other_corps'] && reportData['other_corps'].length > 0) {
                setOtherCorpsTableStates({
                    ...otherCorpsTableStates,
                    rows: reportData['other_corps'],
                })
            }
        }
    }, [reportData])

    useEffect(() => {
        console.log('current tab', tab)
    }, [tab])

    // Generate dynamic columns when memberDataFields change
    useEffect(() => {
        if (memberDataFields) {
            const dynamicConfig = generateDynamicColumns(memberDataFields);
            
            // Set columns for both renewed and excluded tables
            setRenewedColumns(dynamicConfig.columns);
            setExcludedColumns(dynamicConfig.columns);
            setRenewedColumnExtensions(dynamicConfig.columnExtensions);
            setExcludedColumnExtensions(dynamicConfig.columnExtensions);
        }
    }, [memberDataFields]);


    const arrayDataSetter = (array: any[]) => {
        let newArray: any[] = [];
        if (array && array.length > 0) {
            newArray = array.map(a => {
                let nameData = `${a.first_name.trim()} ${a.middle_name
                    ? a.middle_name.trim() : ''} ${a.last_name.trim()}`;
                let dataObj = {
                    ...a,
                    member_name: nameData.trim()
                }

                return dataObj;
            })
        }

        return newArray;
    }


    //***EXPORT TO EXCEL HANDLING***//

    const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    const fileExtension = '.xlsx';

    const exportToExcel = (csvData, fileName) => {
        if (window.hasOwnProperty('downloadFile')) {
            let reader = new FileReader();
            const ws = XLSX.utils.json_to_sheet(csvData);
            const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            const data = new Blob([excelBuffer], { type: fileType });
            reader.readAsDataURL(data)
            reader.onloadend = async function () {
                var base64data = (reader.result as string).substring((reader.result as string).indexOf(',') + 1);

                let args = {
                    filename: fileName,
                    file: base64data,
                    extension: 'xlsx'
                }

                const download = await window.downloadFile(args);

                if (download.success) {
                    setModalProps({
                        title: 'Download Successful',
                        message: 'Successfully downloaded the file.',
                        open: true,
                        method: handleCloseModalProps,
                    })
                } else {
                    setModalProps({
                        title: 'Download Cancelled',
                        message: 'Downloading file has been cancelled.',
                        open: true,
                        method: handleCloseModalProps,
                    })
                }
            }
        } else {
            const ws = XLSX.utils.json_to_sheet(csvData);
            const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            const data = new Blob([excelBuffer], { type: fileType });
            saveAs(data, fileName + fileExtension);
        }
    }


    //***GENERATE REPORT HANDLING***//

    const handleDownloadReport = (selectedTable) => {
        handleCloseReportSelectionModal();
        if (selectedTable.id === 1) {
            if (renewedTableStates['rows_marked'].length > 0) {
                const json = arrayDataSetter(renewedTableStates['rows_marked'])
                console.log("JSON", json)

                const data = json.map(row => ({
                    'Member Name': row.member_name,
                    'Birthdate': row.date_of_birth,
                    'Termination Date': row.termination_date_display,
                    'Member ID': row.member_id,
                    'Gender': row.gender,
                    'Civil Status': row.civil_status,
                    'Relationship': row.relationship_to_principal,
                    'Principal Name': row.principal_name
                }));

                exportToExcel(data, `Exception Report - Marked for Termination Renewed Members`)
                // setTimeout(() => {
                //     setModalProps({
                //         title: 'Download Successful',
                //         message: 'Successfully downloaded the file.',
                //         open: true,
                //         method: handleCloseModalProps,
                //     })
                // }, 1000)
            } else {
                setModalProps({
                    title: 'Generate Report Failed',
                    message: 'The table you selected doesn\'t have data.',
                    open: true,
                    method: handleCloseModalProps,
                })
            }

        } else if (selectedTable.id === 2) {

            if (renewedTableStates['rows_all'].length > 0) {
                const json = arrayDataSetter(renewedTableStates['rows_all'])
                console.log("JSON", json)

                const data = json.map(row => ({
                    'Member Name': row.member_name,
                    'Birthdate': row.date_of_birth,
                    'Termination Date': row.termination_date_display,
                    'Member ID': row.member_id,
                    'Gender': row.gender,
                    'Civil Status': row.civil_status,
                    'Relationship': row.relationship_to_principal,
                    'Principal Name': row.principal_name
                }));

                exportToExcel(data, `Exception Report - All Renewed Members`)
                // setTimeout(() => {
                //     setModalProps({
                //         title: 'Download Successful',
                //         message: 'Successfully downloaded the file.',
                //         open: true,
                //         method: handleCloseModalProps,
                //     })
                // }, 1000)
            } else {
                setModalProps({
                    title: 'Generate Report Failed',
                    message: 'The table you selected doesn\'t have data.',
                    open: true,
                    method: handleCloseModalProps,
                })
            }

        } else if (selectedTable.id === 3) {

            if (excludedTableStates['rows'].length > 0) {
                const json = excludedTableStates['rows']
                console.log("JSON", json)

                const data = json.map(row => ({
                    'First Name': row.first_name,
                    'Middle Name': row.middle_name,
                    'Last Name': row.last_name,
                    'Title': row.title,
                    'Suffix': row.suffix,
                    'Birthdate': row.date_of_birth,
                    'Civil Status': row.civil_status,
                    'Gender': row.gender,
                    'Plan Type': row.plan_type,
                    'Member Type': row.member_type,
                }));

                exportToExcel(data, `Exception Report - Excluded Members from Renewal`)
                // setTimeout(() => {
                //     setModalProps({
                //         title: 'Download Successful',
                //         message: 'Successfully downloaded the file.',
                //         open: true,
                //         method: handleCloseModalProps,
                //     })
                // }, 1000)
            } else {
                setModalProps({
                    title: 'Generate Report Failed',
                    message: 'The table you selected doesn\'t have data.',
                    open: true,
                    method: handleCloseModalProps,
                })
            }

        } else {
            if (otherCorpsTableStates['rows'].length > 0) {
                const json = arrayDataSetter(otherCorpsTableStates['rows'])
                console.log("JSON", json)

                const data = json.map(row => ({
                    'Member ID': row.member_id,
                    'Member Name': row.member_name,
                    'Corporate Account': row.registered_name,
                    'Status': row.member_status,
                    'Member Type': row.member_type,
                    'Relationship': row.relationship_to_principal,
                    'Principal Name': row.principal_name,
                }));

                exportToExcel(data, `Exception Report - Members from Other Corporations`)
                // setTimeout(() => {
                //     setModalProps({
                //         title: 'Download Successful',
                //         message: 'Successfully downloaded the file.',
                //         open: true,
                //         method: handleCloseModalProps,
                //     })
                // }, 1000)
            } else {
                setModalProps({
                    title: 'Generate Report Failed',
                    message: 'The table you selected doesn\'t have data.',
                    open: true,
                    method: handleCloseModalProps,
                })
            }
        }
    }




    //***MODAL HANDLING***//

    const openReportSelectionModal = () => {
        setIsOpenReportSelectionModal(true);
    };

    const handleCloseReportSelectionModal = () => {
        setIsOpenReportSelectionModal(false);
    };

    const handleCloseModalProps = () => {
        setModalProps({
            ...modalProps,
            open: false
        })
    };


    function handleEmailReport(data: any, tableData: any) {
        // console.log('final report data', data)
        setEmailReportModalOpen(false);

        if (data && data['receivers'] && data['attachments']) {
            setIsLoading(true);
            API.postEmailReport(
                data['receivers'],
                data['attachments'],
                data['batch'] ? data['batch'] : undefined,
                data['filename'] ? data['filename'] : undefined,
                data['sender'] ? data['sender'] : undefined,
            ).then(res => {
                if (res && res.error && res.error.message) {
                    setIsLoading(false);
                    console.log('postEmailReport err', res.error)
                    setDialogModalState({
                        isOpen: true,
                        title: 'Email Report failed',
                        message: res.error.message,
                        tableData: null
                    })
                } else if (res) {
                    setIsLoading(false);
                    console.log('postEmailReport res', res)
                    setDialogModalState({
                        isOpen: true,
                        title: 'Email Report has been sent',
                        message: 'The report has been sent to the following lists.',
                        tableData: {
                            rows: tableData['rows'] ? tableData['rows'] : [],
                            columns: tableData['columns'] ? tableData['columns'] : [],
                            columnExtensions: tableData['columnExtensions'] ?
                                tableData['columnExtensions'] : [],
                            formattedColumns: tableData['formattedColumns'] ?
                                tableData['formattedColumns'] : {}
                        }
                    })
                }
            })
        }
    }

    function closeEmailReportModal() {
        setEmailReportModalOpen(false);
    }
    function openEmailReportModal() {
        setEmailReportModalOpen(true);
    }


    return (
        <Grid container spacing={6}>
            {isLoading ? <Loader /> : null}
            <Grid container item xs={12} spacing={1}>
                <Grid container item xs={12} style={{ padding: 0 }}>
                    <Grid item xs={8}></Grid>
                    {/* <Grid item xs={2} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <span
                            id='email_report'
                            data-cy='email_report'
                            className={classes.iconsSpanOne}
                            style={{ display: 'flex', alignItems: 'center' }}
                            onClick={openEmailReportModal}
                        >
                            <FontAwesomeIcon icon={faEnvelope} className={classes.icons} />{' '}
                            Email Report
                        </span>
                    </Grid>
                    <Grid item xs={2} style={{ display: 'flex', justifyContent: 'center' }}>
                        <span
                            id='download_report'
                            data-cy='download_report'
                            className={classes.iconsSpanTwo}
                            style={{ display: 'flex', alignItems: 'center' }}
                            onClick={openReportSelectionModal}
                        >
                            <FontAwesomeIcon icon={faDownload} className={classes.icons} />{' '}
                            Download Report
                        </span>
                    </Grid> */}
                </Grid>
                <Grid item xs={12}>
                    <Typography
                        className={classes.contentTitle}
                        variant="h1"
                        component="h1"
                    >
                        Renewed Members
                    </Typography>
                </Grid>
                {/* <Grid item xs={12}>
                    <Tabs
                        value={tab}
                        onChange={(_, value: any) => {
                            setTab(value)
                        }}
                    >
                        {renewedTabs.map((item, idx) => {
                            let tabLabel = item.label + ' (' + item.count + ')';
                            return (
                                <Tab
                                    id={`renewed-members-${item.label.replace(/\s+/g, '-').toLowerCase()}`}
                                    label={tabLabel}
                                    key={`renewed-members-${idx}`}
                                />
                            )
                        })}
                    </Tabs>
                </Grid> */}
                <Grid item xs={12}>
                            <TableComponent
                                id={'renew-exception-report-renewed-all'}
                                rows={arrayDataSetter(renewedTableStates['rows_all'])}
                                columns={renewedColumns}
                                columnExtensions={renewedColumnExtensions}
                                formattedColumns={RENEWED_FORMATTED_COLUMNS}
                                message="No available data"
                                onClickRow={(row: number[]) => {
                                    console.log('all renewed row data',
                                        renewedTableStates['rows_all'][row[0]])
                                }}
                                currentPage={currentPage.renewed_all}
                                setCurrentPage={(page: number) => {
                                    setCurrentPage({
                                        ...currentPage,
                                        renewed_all: page
                                    })
                                }}
                                pageSize={pageSize.renewed_all}
                                onPageSizeChange={(pSize: number) => {
                                    setPageSize({
                                        ...pageSize,
                                        renewed_all: pSize
                                    });
                                }}
                                disableSearch
                                disableFilter
                                disableSort
                                disableSelect
                            />
                </Grid>
            </Grid>
            <Grid container item xs={12} spacing={2}>
                <Grid item xs={12}>
                    <Typography
                        className={classes.contentTitle}
                        variant="h1"
                        component="h1"
                    >
                        Excluded Members from Renewal
                    </Typography>
                </Grid>
                <Grid item xs={12}>
                    <TableComponent
                        id={'renew-exception-report-excluded'}
                        rows={excludedTableStates['rows']}
                        columns={excludedColumns}
                        columnExtensions={excludedColumnExtensions}
                        formattedColumns={EXCLUDED_FORMATTED_COLUMNS}
                        message="No available data"
                        onClickRow={(row: number[]) => {
                            console.log('excluded row data',
                                excludedTableStates['rows'][row[0]])
                        }}
                        currentPage={currentPage.excluded}
                        setCurrentPage={(page: number) => {
                            setCurrentPage({
                                ...currentPage,
                                excluded: page
                            });
                        }}
                        pageSize={pageSize.excluded}
                        onPageSizeChange={(pSize: number) => {
                            setPageSize({
                                ...pageSize,
                                excluded: pSize
                            });
                        }}
                        disableSearch
                        disableFilter
                        disableSort
                        disableSelect
                    />
                </Grid>
            </Grid>
            {/* <Grid container item xs={12} spacing={2}>
                <Grid item xs={12}>
                    <Typography
                        className={classes.contentTitle}
                        variant="h1"
                        component="h1"
                    >
                        Members from Other Corporations
                    </Typography>
                </Grid>
                <Grid item xs={12}>
                    <TableComponent
                        id={'renew-exception-report-other-corps'}
                        rows={arrayDataSetter(otherCorpsTableStates['rows'])}
                        columns={CORPORATION_MEMBERS_COLUMNS}
                        columnExtensions={CORPORATION_COLUMN_EXTENSIONS}
                        formattedColumns={{}}
                        message="No available data"
                        onClickRow={(row: number[]) => {
                            console.log('other corps row data',
                                otherCorpsTableStates['rows'][row[0]])
                        }}
                        currentPage={currentPage.other_corps}
                        setCurrentPage={(page: number) => {
                            setCurrentPage({
                                ...currentPage,
                                other_corps: page
                            });
                        }}
                        pageSize={pageSize.other_corps}
                        onPageSizeChange={(pSize: number) => {
                            setPageSize({
                                ...pageSize,
                                other_corps: pSize
                            });
                        }}
                        disableSearch
                        disableFilter
                        disableSort
                        disableSelect
                    />
                </Grid>
            </Grid> */}

            <ReportSelectionModal
                id={'report_select_modal'}
                isModalOpen={isReportSelectionModal}
                selection={[
                    { table_id: 1, table_name: "Renewed Members (Marked for Termination)", },
                    { table_id: 2, table_name: "Renewed Members (All)", },
                    { table_id: 3, table_name: "Excluded Members from Renewal", },
                    { table_id: 4, table_name: "Members from Other Corporations", },
                ]}
                onClose={handleCloseReportSelectionModal}
                onSubmit={handleDownloadReport}
            />

            <ModalComponent
                id="upload_modal_error"
                isModalOpen={modalProps.open}
                title={modalProps.title}
                message={modalProps.message}
                onClose={modalProps.method}
            />

            <EmailReportModal
                isModalOpen={emailReportModalOpen}
                id="partial-match-modal"
                data-cy='partial-match-modal'
                tableData={{
                    renewed: renewedTableStates,
                    excluded: excludedTableStates,
                    other_corps: otherCorpsTableStates,
                }}
                email={''}
                onClose={closeEmailReportModal}
                onSend={handleEmailReport}
            />

            <ModalComponent
                id="print_id_cards_dialog_modal"
                isModalOpen={dialogModalState['isOpen']}
                title={dialogModalState['title']}
                message={dialogModalState['message']}
                tableData={dialogModalState['tableData']}
                isPrimary
                onClose={() => {
                    setDialogModalState({
                        ...dialogModalState,
                        isOpen: false,
                        tableData: null,
                    })
                }}
            />
        </Grid>

    )
}
