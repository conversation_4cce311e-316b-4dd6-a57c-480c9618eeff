export class LocalFunctions {
     public static dataArraySetter(filteredDataArr: any[], benefitPlanTree: any[]) {
        let defaultArr: any[] = [];

        filteredDataArr.forEach((fData) => {
            console.log('fData val', fData)
            let eligibilityArr: any = [];
            let identifierArr: any[] = [];

            if (fData['code']) {
                // Start of setting Eligibility data
                let elgTreeIds: any[] = benefitPlanTree.filter(item =>
                    item['tree_id'] &&
                    item['tree_id'].includes(`-${fData['code']}-TC-EL`) &&
                    item['level'] &&
                    (
                        item['level'] === 5 ||
                        item['level'] === 6 ||
                        item['level'] === 7
                    )
                ).map(filteredData => filteredData['tree_id']);
                if (fData['name']) {
                    console.log(`${fData['name']} member types & civil statuses`, elgTreeIds)
                }

                elgTreeIds.forEach((eData, idx) => {
                    let treeIdx: number = benefitPlanTree.map(item => item['tree_id']).indexOf(eData);

                    if (
                        treeIdx !== -1 && benefitPlanTree[treeIdx] &&
                        benefitPlanTree[treeIdx]['custom_metadata']
                    ) {
                        let dataName: string = benefitPlanTree[treeIdx]['name'];
                        let metaData: any = benefitPlanTree[treeIdx]['custom_metadata'];
                        console.log(`**index: [${idx}], elg treeId: ${metaData['title']}`, benefitPlanTree[treeIdx])

                        let tableLabel: string = metaData['title']
                            ? this.modifyString(metaData['title'], ' ') : '';
                        let columnsArr: any[] = [
                            {
                                name: 'relationship',
                                title: 'Relationship',
                            },
                            {
                                name: 'type',
                                title: 'Type',
                            },
                            {
                                name: 'hierarchy',
                                title: 'Hierarchy',
                            },
                            {
                                name: 'enrollment_period',
                                title: 'Enrollment Period (X days from)',
                            },
                            {
                                name: 'effectivity_coverage',
                                title: 'Effectivity Coverage (X days from)',
                            },
                            {
                                name: 'age_limit',
                                title: 'Age Limit (Min-Max)',
                            },
                        ];
                        let columnExtArr: any[] = [];
                        let formatExtArr: any = {};
                        let rowsArr: any[] = [];

                        if (dataName && dataName === 'Eligibility') {
                            tableLabel = 'Principal';
                            columnsArr = [
                                {
                                    name: 'effectivity_date_from',
                                    title: 'Effectivity Date (X days from)',
                                },
                                {
                                    name: 'age_limit',
                                    title: 'Age Limit (Min-Max)',
                                },
                                {
                                    name: 'no_of_dependents',
                                    title: 'No. of Dependents',
                                }
                            ]
                            columnExtArr = [
                                {
                                    columnName: 'effectivity_date_from',
                                    wordWrapEnabled: true,
                                },
                                {
                                    columnName: 'age_limit',
                                    wordWrapEnabled: true,
                                },
                                {
                                    columnName: 'no_of_dependents',
                                    wordWrapEnabled: true,
                                },
                            ]
                            rowsArr = [
                                {
                                    effectivity_date_from: `${metaData['effictivity_date'] ? metaData['effictivity_date'] : '---'} 
                                        ${metaData['effictivity_date'] && metaData['effictivity_date_unit'] ? this.modifyString(metaData['effictivity_date_unit'], ' ') : ''}`,
                                    age_limit: `${metaData['principal_age_min'] ? metaData['principal_age_min'] + 'yrs to' : '--- '} 
                                        ${metaData['principal_age_min'] && metaData['principal_age_max'] ? metaData['principal_age_max'] + 'yrs' : ''}`,
                                    no_of_dependents: `${metaData['no_of_dependents'] ? metaData['no_of_dependents'] : '---'}`,
                                }
                            ]
                        } else if (dataName && dataName === 'Dependents') {
                            columnsArr = [
                                {
                                    name: 'days_of_enrollment_deadline',
                                    title: 'Enrollment Deadline',
                                },
                                {
                                    name: 'dep_notes',
                                    title: 'Notes',
                                },
                            ]
                            columnExtArr = [
                                {
                                    columnName: 'days_of_enrollment_deadline',
                                    wordWrapEnabled: true,
                                },
                                {
                                    columnName: 'dep_notes',
                                    wordWrapEnabled: true,
                                },
                            ]
                            formatExtArr = { multi_text: ['dep_notes'] }
                            rowsArr = [
                                {
                                    days_of_enrollment_deadline: `${metaData['days_of_enrollment_deadline']
                                        ? metaData['days_of_enrollment_deadline'] + ' Days' : '---'}`,
                                    dep_notes: `${metaData['dep_note'] ? metaData['dep_note'] : '---'}
                                        @_@${metaData['dep_note'] && metaData['dep_note_2'] ? metaData['dep_note_2'] : ''}`
                                }
                            ]
                        } else {
                            let childNodes: any[] = benefitPlanTree.filter(item =>
                                item['tree_id'] &&
                                item['tree_id'].includes(eData) &&
                                (item['level'] && item['level'] === 8) &&
                                (item['type'] && item['type'] === 'Sub-Benefit')
                            ).map(filteredData => filteredData['tree_id']);;
                            if (eData['name']) {
                                console.log(`${eData['name']} relationships`, childNodes)
                            }

                            childNodes.forEach((childNode, idx) => {
                                let cnIdx: number = benefitPlanTree.map(item => item['tree_id']).indexOf(childNode);

                                if (cnIdx !== -1 && benefitPlanTree[cnIdx] &&
                                    benefitPlanTree[cnIdx]['custom_metadata'] &&
                                    benefitPlanTree[cnIdx]['custom_metadata']['title']) {
                                    let cnMetaData: any = benefitPlanTree[cnIdx]['custom_metadata'];
                                    console.log(`>index: [${idx}], elg treeId: ${benefitPlanTree[cnIdx]['custom_metadata']['title']}`, benefitPlanTree[cnIdx])
                                    const ageLimitMinUnit = cnMetaData['age_limit_min_unit'] ? cnMetaData['age_limit_min_unit'] : 'yrs';
                                    const ageLimitMaxUnit = cnMetaData['age_limit_max_unit'] ? cnMetaData['age_limit_max_unit'] : 'yrs';
                                    rowsArr.push({
                                        relationship: `${cnMetaData['title'] ? cnMetaData['title'] : '---'}`,
                                        type: `${cnMetaData['type'] ? this.modifyString(cnMetaData['type'], ' ') : '---'}`,
                                        hierarchy: `${cnMetaData['hierarchy'] ? cnMetaData['hierarchy'] : '---'}`,
                                        enrollment_period: `${cnMetaData['enrollment_period'] ? cnMetaData['enrollment_period'].toString() : '---'} 
                                            ${cnMetaData['enrollment_period'] && cnMetaData['enrollment_period_unit'] ? this.modifyString(cnMetaData['enrollment_period_unit'], ' ', '_') : ''}`,
                                        effectivity_coverage: `${cnMetaData['effectivity_coverage'] ? cnMetaData['effectivity_coverage'].toString() : '---'} 
                                            ${cnMetaData['effectivity_coverage'] && cnMetaData['effectivity_coverage_unit'] ? this.modifyString(cnMetaData['effectivity_coverage_unit'], ' ', '_') : ''}`,
                                        age_limit: `${cnMetaData['age_limit_min'] ? cnMetaData['age_limit_min'] + ' '+ 
                                        ageLimitMinUnit +' to ' : '--- '}${cnMetaData['age_limit_min'] && cnMetaData['age_limit_max'] 
                                        ? cnMetaData['age_limit_max'] + ' '+ ageLimitMaxUnit : ''}`,
                                    })
                                }
                            })

                            if (rowsArr.length > 0) {
                                rowsArr = rowsArr.sort((a, b) => (a.hierarchy > b.hierarchy ? 1 : -1)) // ASC
                            }
                        }

                        if (
                            dataName &&
                            dataName !== 'Eligibility' &&
                            dataName !== 'Dependents'
                        ) {
                            columnExtArr = this.columnExtensionSetter(columnsArr);
                        }
                        /** */

                        eligibilityArr.push({
                            label: tableLabel,
                            columns: columnsArr,
                            columnExtensions: columnExtArr,
                            formattedColumns: formatExtArr,
                            rows: rowsArr,
                            sortOrder: dataName && dataName === 'Eligibility'
                                ? 1 : dataName && dataName === 'Dependents' ? 2 : 3
                        })
                    }
                })
                // End of setting Eligibility data

                // Start of setting Identifers data
                let idnTreeIds: any[] = [
                    `MCT-CT-${fData['code']}-ID-YOF`,
                    `MCT-CT-${fData['code']}-ID-JLG`,
                    `MCT-CT-${fData['code']}-ID-DSG`,
                    `MCT-CT-${fData['code']}-ID-DPT`,
                     `MCT-CT-${fData['code']}-ID-MEM`,
                ];

                idnTreeIds.forEach((iData, idx) => {
                    let treeIdx: number = benefitPlanTree.map(item => item['tree_id']).indexOf(iData);

                    if (
                        treeIdx !== -1 && benefitPlanTree[treeIdx] &&
                        benefitPlanTree[treeIdx]['custom_metadata']
                    ) {
                        let metaData: any = benefitPlanTree[treeIdx]['custom_metadata'];
                        console.log(`**index: [${idx}], idn treeId: ${metaData['title']}`, benefitPlanTree[treeIdx])

                        identifierArr.push({
                            _id: benefitPlanTree[treeIdx]['_id'] ? benefitPlanTree[treeIdx]['_id'] : idx,
                            data: metaData['title'] ? metaData['title'] : '',
                            value: metaData['value'] ? metaData['value'] : '---',
                        });
                    }
                })
                // End of setting Identifers data
            }

            // sort Eligibility by Principal > Dependents > Others
            eligibilityArr.sort((a, b) => (a.sortOrder > b.sortOrder ? 1 : -1)) // ASC
            identifierArr = identifierArr.map(obj => {
            if (obj.data === 'Member Type' && Array.isArray(obj.value)) {
                obj.value = obj.value.join(", ");
            }
            return obj;
            });
            defaultArr.push({
                _id: fData['_id'] ? fData['_id'] : '',
                plan_type: fData['name'] ? fData['name'] : '',
                eligibility: eligibilityArr,
                identifiers: identifierArr,
            });
        })

        return defaultArr;
    }

    public static modifyString(value: string, splitValue: string, replaceValue?: string) {
        if (replaceValue) {
            value = value.replace(replaceValue, ' '); // replace any connecting character with 'space'
        }

        let splitStr = value.toLowerCase().split(splitValue);
        for (let i = 0; i < splitStr.length; i++) {
            splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
        }

        let newString: string = splitStr.join(splitValue);

        if (newString === 'Hire Date') {
            newString = newString + ' of Principal';
        }

        return newString;
    }

    public static columnExtensionSetter(columns: any[]) {
        let columnExtArr: any[] = [];

        // console.log('has long column header(s)', columns.filter(colData => colData['title'] && colData['title'].length > 18).length > 0)
        if (columns.length > 0 &&
            columns.filter(colData => colData['title'] && colData['title'].length > 18).length > 0) {
            columns.forEach(colData => {
                columnExtArr.push({
                    columnName: colData['name'],
                    wordWrapEnabled: true,
                    width: 180,
                })
            })
        }

        return columnExtArr;
    }
}