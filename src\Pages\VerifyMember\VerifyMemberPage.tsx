//#region Global Imports
import * as React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators, Dispatch } from 'redux';
import {
  Grid,
  Tabs,
  Tab,
  Typography,
  Button,
  // Avatar,
} from '@material-ui/core/';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';
import {
  assign,
  capitalize,
  cloneDeep,
  every,
  filter,
  forEach,
  get,
  includes,
  isEmpty,
  isNil,
  toLower,
} from 'lodash';

//#region Local Imports

import '@hims/core/dist/index.css';
import './style.scss';

//#endregion Local Imports

//#region Interface Imports
import { Store } from 'Components/Stores/IStore';
import { IVerifyMember } from 'Pages/VerifyMember/IVerifyMember';
import { VerifyMemberActions } from './VerifyMemberAction';
import { PageHeaderComponent } from 'Components/UI/PageHeader';
import { MemberInformation } from './MemberInformation';
import { TableComponent } from 'Components/UI/TableComponent';
import { FloatingButtons } from 'Components/UI/FloatingButtons';
import { SnackbarNotification } from 'Components/UI/SnackbarNotification';
import { PartialMatchModal } from './PartialMatchModal';
import { ApproveMemberModal } from './ApproveMemberModal';
import { DisapproveMemberModal } from './DisapproveMemberModal';
import { ForConfirmationModal } from './ForConfirmationModal';
import { MemberFilesModal } from './MemberFilesModal';
import { AddActionRemarksModal } from './AddActionRemarksModal';
import { TmpDataModal } from './TmpDataModal';
import { ExceptionReport } from './ExceptionReport';
import { ViewCorrections } from './ViewCorrections';
import { Loader } from 'Components/UI/LoadingIndicator';
import { API } from '../API';
import { Components } from '@hims/core';
import { ApproveDependentModal, ActionData } from './ApproveDependentModal';
import { EditMemberModal } from './EditMemberModal';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
//#endregion Interface Imports
import { ModalComponent } from 'Components/UI/ModalComponent';
import { ConfirmationModalComponent } from 'Components/UI/ConfirmationModalComponent';
import { LostInternetModal } from 'Components/UI';
import { MemberInformationModal } from './MemberInformationModal';
import { RemarksModal } from './RemarksModal';
import { MemberInfoModal } from './MatchPrincipalDependentModal';
import { Utils } from '@hims/core';
import {
  // Verify Void Masterlist table data
  void_partial_member,
  // void_partial_items,
  // void_partial_columns,
  void_incomplete_data,
  void_conflict_data,
  void_unmatched_data,
  void_other_validation_data,
  void_supporting_data,
  void_existing_data,
  void_voided_data,
  void_reactivated_data,
  // void_multiple_data,
  void_disapprove_data,
  void_approved_data,
  void_all_data,
  // Verify Edit table data
  verifyEdit_conflictOnData,
  verifyEdit_forValidation,
  verifyEdit_terminatedMembers,
  verifyEdit_activeMembers,
  verifyEdit_approveMembers,
  verifyEdit_disapproveMembers,
  verifyEdit_other,
  verifyEdit_supporting,
  // Verify Member table data
  partial_member,
  partial_items,
  partial_columns,
  incomplete_data,
  conflict_data,
  validation_data,
  other_validation_data,
  supporting_data,
  disapprove_data,
  approved_data,
  all_data,
  default_tab_list,
  edit_tab_list,
} from './VerifyTableData';
import { Processmaker } from 'Pages/Processmaker';
import _ from 'lodash';
import { Redirect } from 'react-router-dom'; //Link,
import { tableData } from 'Pages/TicketList/TicketListTableData';
import { columnFieldData } from 'Pages/EncodeMember/EncodeMultipleMembers/FieldConstants';
import { combineRemarks } from './ActionConsts';


const ButtonStyle = makeStyles({
  root: {
    height: '40px',
    minWidth: '150px',
  },
  rightButton: {
    height: '40px',
    minWidth: '99px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
  },
  leftButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    border: '2px solid #3AB77D',
  },
  leftText1: {
    fontSize: '13px',
  },
  leftText2: {
    fontSize: '7px',
  },
  buttonTitle: {
    fontWeight: 700,
    fontSize: 13,
  },
  buttonSubTitle: {
    fontWeight: 400,
    fontSize: 7,
  },
});

function MyFloatingButtons(props) {
  const buttonclasses = ButtonStyle();
  return (
    <div>
      <FloatingButtons
        leftButtons={
          <div>
            {props.verifyPageType === 'default' ? (
              <Button
                id="save_for_now"
                data-cy="save_for_now"
                className={buttonclasses.leftButton}
                style={{ marginRight: '10px' }}
                variant="contained"
                color="secondary"
                size="small"
                onClick={() => {
                  props.floatingButtonActions(
                    'Save For Now',
                    props.verifyPageType,
                  );
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <Typography className={buttonclasses.buttonTitle}>
                    Save For Now
                  </Typography>
                </div>
              </Button>
            ) : null}
            {props.verifyPageType === 'default' &&
            GlobalFunction.checkUserRbacByPolicy(
              'MS56',
              props.currentUserData,
              props.isLoading,
            ) ? (
             <>
              <Button
                id="generate_exception_report"
                data-cy="generate_exception_report"
                className={buttonclasses.root}
                variant="contained"
                color="primary"
                size="small"
                // data={allData}
                // data={JSON.parse(JSON.stringify(allData))}
                onClick={() => {
                  props.floatingButtonActions(
                    'Generate Exception Report',
                    props.verifyPageType,
                  );
                }}
              >
                Generate Exception Report
              </Button>
              </>
            ) : props.verifyPageType === 'edit' ||
              props.verifyPageType === 'vd' ? (
              <Button
                id="update_list"
                data-cy="update_list"
                className={buttonclasses.root}
                variant="contained"
                color="primary"
                size="small"
                onClick={() => {
                  props.floatingButtonActions(
                    'Update List',
                    props.verifyPageType,
                  );
                }}
              >
                Update List
              </Button>
            ) : null}
          </div>
        }
        rightButtons={null}
      />
    </div>
  );
}

MyFloatingButtons.propTypes = {
  verifyPageType: PropTypes.func,
  floatingButtonActions: PropTypes.func,
  currentUserData: PropTypes.func,
  isLoading: PropTypes.func,
};

export class VerifyMember extends React.Component<
  IVerifyMember.IProps,
  IVerifyMember.IState
> {
  constructor(props: IVerifyMember.IProps) {
    super(props);

    console.log('verify member props', props); //console.log('memberDataFields', this.state.memberDataFields)
    let pageType = 'default';
    if (props && props['match'] && props['match']['params']) {
      console.log('PAGE TYPE PROPS 1', props);
      if (props['match']['params']['type']) {
        console.log('PAGE TYPE PROPS PARAMS TYPE 1', props);
        pageType = props['match']['params']['type'];
      }
    }

    let ocpPathValue = '';
    if (props && props['match'] && props['match']['params']) {
      if (props['match']['params']['ocp']) {
        ocpPathValue = props['match']['params']['ocp'];
      }
    }

    const breadcrumbs_items = [
      {
        label: 'DASHBOARD',
        url: '#/membership',
      },
      {
        label: 'VERIFY',
        url: '',
      },
      {
        label: 'Veridata',
        url: '',
      },
    ];

    this.state = {
      memberDataFields: [],
      check_internet_flag: false,
      plan_types: null,
      anchorEl: null,
      memberData: null,
      memberCardNo: '',
      redirectUrl: '', 
      redirectPage: false,
      pmaker_task: 'Assign Membership Statuses',
      row: 0,
      partialRow: 0,
      // supportingRow: 0,
      validationRow: 0,
      breadcrumbs: breadcrumbs_items,
      tab: 0,
      verifyTabs: pageType === 'default' ? default_tab_list : edit_tab_list,
      partialtable: partial_member,
      partial_items: partial_items,
      incompletetable: incomplete_data,
      rowDataForEdit: {},
      dataForEdit: {},
      otherTableRow:{},
      conflicttable: conflict_data,
      validationtable: validation_data,
      disapprovetable: disapprove_data,
      othervalidationtable: other_validation_data,
      supportingtable: supporting_data,
      approvedtable: approved_data,
      alltable: all_data,
      allValidated: [],
      defaultTableCopy: {
        partialtable: partial_member,
        incompletetable: incomplete_data,
        conflicttable: conflict_data,
        validationtable: validation_data,
        othervalidationtable: other_validation_data,
        supportingtable: supporting_data,
        disapprovetable: disapprove_data,
        approvedtable: approved_data,
        alltable: all_data,
      },
      partialModalOpen: false,
      matchPrincipalModalOpen: false,
      approveModalOpen: false,
      memberFilesModalOpen: false,
      disapproveModalOpen: false,
      forConfirmationModalOpen: false,
      approveDependent: {},
      generateFlag: false,
      member_upload_data: {
        _id: '',
        client_id: '',
        client_name: '',
        contract_id: '',
        sender_name: '',
        sender_email: '',
        channel: '',
        date_sent: '',
        batch_name: '',
        no_of_members: 0,
        members: [],
      },
      loading_state: true,
      allFilter: [],
      approvedFilter: [],
      disapprovedFilter: [],
      conflictFilter: [],
      incompleteFilter: [],
      partialFilter: [],
      validationFilter: [],
      othervalidationFilter: [],
      supportingFilter: [],
      partialColumns: partial_columns,
      allData: {
        partial: partial_member,
        incomplete: incomplete_data,
        conflict: conflict_data,
        validation: validation_data,
        othervalidationtable: other_validation_data,
        supporting: supporting_data,
        disapprove: disapprove_data,
        all: all_data,
      },
      rowData: [],
      prefix: '',
      tableName: '',
      isEdit: false,
      temp_id: '',
      tmpDataModal: false,
      tmpData: {
        partial_table: {},
        incomplete_table: {},
        conflict_table: {},
        validation_table: {},
        disapprove_table: {},
        approved_table: {},
        all_table: {},
        actionHolder: [],
        remarksHolder: [],
      },
      currentTablePage: 0,
      isActionRemarksModalOpen: false,
      actionRemarksModalProps: {
        tab: '',
        row: 0,
        action: 'Approve',
      },
      pageSizes: {
        partial_table: 5,
        incomplete_table: 5,
        conflict_table: 5,
        validation_table: 5,
        other_validation_table: 5,
        supporting_table: 5,
        disapprove_table: 5,
        approved_table: 5,
        all_table: 5,
      },
      snackbarNotifProps: {
        isOpen: false,
        title: '',
        message: '',
        variant: 'success',
      },
      corporateAccountNo: '',
      corporateAccountName: '',
      contractName: '',
      contractId: '',
      personId: '',
      clientId: '',
      member_id: '',
      fullName: '',
      filterTimeout: 0,
      forValidationTab: 0,
      memberFileUploadDetails: {},
      memberDataEdit: {},
      verifyPageType: pageType,
      verifyEditAllData: {
        conflictOnData: verifyEdit_conflictOnData,
        forValidation: verifyEdit_forValidation,
        terminatedMembers: verifyEdit_terminatedMembers,
        approveMembers: verifyEdit_approveMembers, // approved changes
        disapproveMembers:  verifyEdit_disapproveMembers,  // disapproved changes
        activeMembers: verifyEdit_activeMembers,
        other: verifyEdit_other,
        supportings: verifyEdit_supporting,
        supporting: verifyEdit_supporting,
      },
      verifyEditTable: {
        conflictOnData: verifyEdit_conflictOnData,
        forValidation: verifyEdit_forValidation,
        terminatedMembers: verifyEdit_terminatedMembers,
        approveMembers: verifyEdit_approveMembers,
        disapproveMembers:  verifyEdit_disapproveMembers, 
        activeMembers: verifyEdit_activeMembers,
        other: verifyEdit_other,
        supportings: verifyEdit_supporting,
        supporting: verifyEdit_supporting,
      },
      verifyEditFilters: {
        conflictOnData: [],
        forValidation: [],
        terminatedMembers: [],
        approveMembers: [],
        disapproveMembers: [],
        activeMembers: [],
        other: [],
        supportings: [],
        supporting: [],
      },
      verifyEditPageSize: {
        conflictOnData: 5,
        forValidation: 5,
        terminatedMembers: 5,
        approveMembers: 5,
        disApproveMembers: 5,
        activeMembers: 5,
        other: 5,
        supportings: 5,
        supporting: 5,
      },
      verifyEditTableCopy: {
        conflictOnData: verifyEdit_conflictOnData,
        forValidation: verifyEdit_forValidation,
        terminatedMembers: verifyEdit_terminatedMembers,
        approveMembers: verifyEdit_approveMembers,
        disapproveMembers:  verifyEdit_disapproveMembers, 
        activeMembers: verifyEdit_activeMembers,
        other: verifyEdit_other,
        supportings: verifyEdit_supporting,
        supporting: verifyEdit_supporting,
      },
      verifyVoidAllData: {
        partial: void_partial_member,
        incomplete: void_incomplete_data,
        conflict: void_conflict_data,
        unmatched: void_unmatched_data,
        othervalidationtable: void_other_validation_data,
        supporting: void_supporting_data,
        existing: void_existing_data,
        voided: void_voided_data,
        reactivated: void_reactivated_data,
        disapprove: void_disapprove_data,
        approved: void_approved_data,
        all: void_all_data
      },
      verifyVoidTable: {
        partial: void_partial_member,
        incomplete: void_incomplete_data,
        conflict: void_conflict_data,
        unmatched: void_unmatched_data,
        othervalidationtable: void_other_validation_data,
        supporting: void_supporting_data,
        existing: void_existing_data,
        voided: void_voided_data,
        reactivated: void_reactivated_data,
        disapprove: void_disapprove_data,
        approved: void_approved_data,
        all: void_all_data
      },
      verifyVoidFilters: {
        partial: [],
        incomplete: [],
        conflict: [],
        unmatched: [],
        othervalidationtable: [],
        supporting: [],
        existing: [],
        voided: [],
        reactivated: [],
        disapprove: [],
        approved: [],
        all: [],
      },
      verifyVoidPageSize: {
        partial: 5,
        incomplete: 5,
        conflict: 5,
        unmatched: 5,
        othervalidationtable: 5,
        supporting: [],
        existing: [],
        voided: [],
        reactivated: [],
        disapprove: [],
        approved: [],
        all: [],
      },
      verifyVoidTableCopy: {
        partial: void_partial_member,
        incomplete: void_incomplete_data,
        conflict: void_conflict_data,
        unmatched: void_unmatched_data,
        othervalidationtable: void_other_validation_data,
        supporting: void_supporting_data,
        existing: void_existing_data,
        voided: void_voided_data,
        reactivated: void_reactivated_data,
        disapprove: void_disapprove_data,
        approved: void_approved_data,
        all: void_all_data
      },
      member_edit_data: [],
      isModalOpen: false,
      modalTitle: '',
      modalMessage: '',
      isConfirmModalOpen: false,
      modalCloseText: '',
      modalConfirmText: '',
      customModalMessage: null,
      next: null,
      redirectFrom: {
        verifyEdit: false,
      },
      noCancelButton: false,
      ocpPath: ocpPathValue,
      ocpPayload: {
        endorsement_date: '',
        batch_no: '',
        verified_by: '',
        client_id: '',
        corporate_account_no: '',
        corporate_name: '',
        contract: '',
        sender: '',
        sender_email: '',
        encode_method: '',
        encoded_date: '',
        encoded_by: '',
        file: {},
        members: [],
      },
      verifierData: {},
      actionHolder: [],
      remarksHolder: [],
      ticket_id: '',
      isMemberInfoModalOpen: false,
      clientData: null,
      infoData: null,
      benefitPlanTree: [],
      initApprovedMembers: [],
      isErrorModalOpen: false,
      errorTitle: '',
      errorMessage: '',
      isRemarksModal: false,
      remarksData: {},
      dateSorting: false,
      selectedPrincipal: null,
      ticket_closed: false,
      documentData: {},
      initialNumberValidatedMembers: 0,
      verifyEditMembers: [],
      user_remarks:'',
      displayUserRemarks: [],
      principalList:[],
      approvedList:[],
    };

    this.handleRedirect = this.handleRedirect.bind(this);
  }
//need api 
  normalize(val) {
    return (val || '').trim().toLowerCase();
  }
  buildKey(remarks, source, user) {
    return `${this.normalize(remarks)}|${this.normalize(source)}|${this.normalize(user || '-')}`;
  }
  dedupeRemarks(remarks) {
    const seen = new Set();
    return remarks.filter(r => {
      const key = this.buildKey(r.remarks, r.source, r.user_fullname);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  //http://localhost:3000//#/membership/verify-dependent/vd/6413622436178e3a0c67f11026104375/4cmmx25bpokv92un1z/601bae872889cb5b74216505
  //viewmemberpage1 /membership/member-contract/601bc1722889cb5b7421868e/601bae842889cb5b7421645e/contract
  handleRedirect = (url: string) => {
    if (url && url.trim()) {
      this.setState({
        redirectUrl: url,
        redirectPage: true,
      });
    }
  };

  endDateCorrector = (endDate, additionalInfo) => {
    let tmpenddate: string = endDate;
    tmpenddate = tmpenddate.substring(0, tmpenddate.indexOf('T') + 1);
    if (additionalInfo) {
      tmpenddate = `${tmpenddate}00:00:00.000Z`;
      return tmpenddate;
    }
    tmpenddate = `${tmpenddate}23:59:59.999Z`;
    return tmpenddate;
  };

  async getMemberUploaded (id:string) {
    let callGetUpload = true;
    const tmp_validated_members:any[] = [];
    const tmp_uploaded_members:any[] = [];
    let skipCount = 0;
    
    let member_upload_details:any = {};
    while(callGetUpload){
      await API.retrieveMemberUploadMembers(id,skipCount,100).then(response=>{
        let totalRetrieved = 0;
        if(member_upload_details._id===undefined){
          member_upload_details = Object.assign({},response);
        }
        if(response.validated_members && Array.isArray(response.validated_members)){
          tmp_validated_members.push(...response.validated_members);
          totalRetrieved+=response.validated_members.length;
        }
        if(response.members && Array.isArray(response.members)){
          tmp_uploaded_members.push(...response.members);
          totalRetrieved+=response.members.length;
        }
        if(totalRetrieved===0) callGetUpload = false;
      }).catch((err)=>{
        console.log("API Error: ",err)
        callGetUpload = false;
      });
      skipCount+=100;
    }
    member_upload_details.validated_members = tmp_validated_members;
    member_upload_details.members = tmp_uploaded_members;
    return member_upload_details;
  }

  async componentDidMount() {
    try {
      const userData = await API.getUserDataFromDb();
      this.setState({ verifierData: userData });

      const memberData = await API.getMemberData(
        this.props.match.params.member._id,
      );
      this.setState({ memberData: memberData });
    } catch (error) {
      console.log('async error', error);
    }

    this.setState({
      check_internet_flag: true,
    });

    // let member_id = this.props.match.params.member_id;
    // let contract_id = this.props.match.params.contract_id;
    
    // await API.getTicketData(this.props.match.params.ticket_id,true)
    // .then(response=>{
    //   console.log("Check Ticket Data Response: ",response)
    //   if(response.status && response.error===undefined){
    //     this.setState({
    //       ticket_closed: response.status === 'CLOSED'
    //     })
    //   }
    // });
    //vd = verify dependents
    if (this.state.verifyPageType === 'default') {
      await this.checkTicketClosed('Verify Member');
      const upload_id = this.props.match.params.member_upload_id;
      const member_upload_details = await this.getMemberUploaded(upload_id);
      if(member_upload_details.client_id){
        this.setState({
          clientId: member_upload_details.client_id,
          ticket_id: member_upload_details.batch_name,
        });
      }
      console.log("Check Member Upload Details: ",member_upload_details);
      API.getClientHMOInfo(
        member_upload_details.client_id ? member_upload_details.client_id : '',
      ).then(hmoclient => {
        console.log('client hmo res', hmoclient);
        let ccId = '';
        let ccNo = '';
        let ccName = '';
        let contractName = '';
        let memberDataFields: any[] = [];
        let benefitPlanTreeArr: any[] = [];

        if (hmoclient && hmoclient.error === undefined) {
          ccId =
            hmoclient.client && hmoclient.client._id
              ? hmoclient.client._id
              : '';
          memberDataFields =
            hmoclient.client && hmoclient.client.member_data_fields
              ? hmoclient.client.member_data_fields
              : [];
          ccNo =
            hmoclient.client && hmoclient.client.corporate_account_no
              ? hmoclient.client.corporate_account_no
              : '';
          ccName =
            hmoclient.client && hmoclient.client.registered_name
              ? hmoclient.client.registered_name
              : '';
          if (
            hmoclient.benefit_plan_tree &&
            hmoclient.benefit_plan_tree.length > 0
          ) {
            console.log(
              'benefit_plan_tree val 4',
              hmoclient.benefit_plan_tree,
            );
            benefitPlanTreeArr = hmoclient.benefit_plan_tree;
            hmoclient.benefit_plan_tree.forEach(item => {
              if (item.level === 1 && item.code === 'MCT') {
                console.log('targeted item of benefit plan tree', item);
                console.log('verify11', member_upload_details)
                let period = '';
                API.getClientContracts(member_upload_details.client_id)
                .then(item => 
                  console.log('item from contracts1', item)
                )
                if (
                  item.custom_metadata &&
                  item.custom_metadata.coverage_start_date &&
                  item.custom_metadata.coverage_end_date
                ) {
                  let coverage_end_date = moment(
                    this.endDateCorrector(
                      item.custom_metadata.coverage_end_date,
                      item.custom_metadata.additional_info,
                    ),
                  )
                    .subtract(1, 'days')
                    .format('MMM D, YYYY');

                  period =
                    ' ' +
                    GlobalFunction.toCommonDateString(
                      new Date(item.custom_metadata.coverage_start_date),
                    ) +
                    ' - ' +
                    GlobalFunction.toCommonDateString(
                      // new Date(item.custom_metadata.coverage_end_date),
                      new Date(coverage_end_date),
                    );
                }
              
                // let startDate2 = new Date(item.custom_metadata.coverage_start_date)
                let endDate2 = new Date(item.custom_metadata.coverage_end_date)
                let endDate3 = endDate2.setDate(endDate2.getDate() -1)
                // let period2:any =  moment(item.custom_metadata.coverage_start_date).format('MM, DD, YYYY') - moment(item.custom_metadata.coverage_end_date).format('MM, DD, YYYY')
                contractName =
                  // 'Contract ' +
                  // (item.version ? item.version : '1') 
                 member_upload_details.client_contract_id + ' ' + moment.utc(item.custom_metadata.coverage_start_date).format('MMM D,YYYY') + ' - ' + moment.utc(item.custom_metadata.coverage_end_date).format('MMM D,YYYY')  
                 // moment(startDate2).format('MMM D,YYYY') + ' - ' + moment(endDate2).subtract(1, 'days').format('MMM D,YYYY')
                  ;
              }
            });
          }
        }

        let breadcrumbs_items: any = [
          {
            label: 'DASHBOARD',
            url: '#/membership',
          },
          {
            label: 'VERIFY MEMBER',
            url: '',
          },
        ];
        if (
          member_upload_details.client_object !== undefined &&
          member_upload_details.client_object !== null &&
          member_upload_details.client_object.registered_name !== undefined &&
          member_upload_details.client_object.registered_name !== null
        ) {
          breadcrumbs_items.push({
            label: member_upload_details.client_object.registered_name,
            url: '',
          });
          breadcrumbs_items.push({
            label: this.props.match.params.ticket_number,
            url: '',
          });
        }

        if (this.state.ocpPath === 'vm-ocp') {
          this.setState(prevState => ({
            ...prevState,
            ocpPayload: {
              ...prevState.ocpPayload,
              endorsement_date: member_upload_details['date_sent']
                ? member_upload_details['date_sent']
                : '',
              batch_no: member_upload_details['batch_name']
                ? member_upload_details['batch_name']
                : '',
              client_id: ccId,
              corporate_account_no: ccNo,
              corporate_name: ccName,
              // contractName:contractName,
              // contractId: member_upload_details.client_contract_id,
              contract: contractName,
              sender: member_upload_details['sender_name']
                ? member_upload_details['sender_name']
                : '',
              sender_email: member_upload_details['sender_email']
                ? member_upload_details['sender_email']
                : '',
              encode_method: 'Upload',
              encoded_date: member_upload_details['date_sent']
                ? member_upload_details['date_sent']
                : '',
              encoded_by: member_upload_details['user_name']
                ? member_upload_details['user_name']
                : '',
            },
          }));
        }
        const approved_disapproved_members:any [] = [];
        // MS867: Plan Type > '- For Validation' concatenation
        if (
          member_upload_details.validated_members &&
          member_upload_details.validated_members.length > 0
        ) {
          member_upload_details.validated_members = member_upload_details.validated_members.map(
            item => {
              if (
                item &&
                item.validation_status &&
                item.validation_status.status
              ) {
                if(["APPROVED","DISAPPROVED"].includes(item.validation_status.status)){
                  approved_disapproved_members.push(item);
                }
                item = this.dataModifier(
                  item,
                  item.validation_status.status,
                );
              }
              return item;
            },
          );
        }
        this.setState({
          clientData: hmoclient,
          memberDataFields: memberDataFields,
          check_internet_flag: false,
          plan_types: hmoclient.plan_types,
          corporateAccountNo: ccNo,
          corporateAccountName: ccName,
          contractName: contractName,
          breadcrumbs: breadcrumbs_items,
          loading_state: false,
          member_upload_data: member_upload_details,
          benefitPlanTree: benefitPlanTreeArr,
          initialNumberValidatedMembers: approved_disapproved_members.length
        });
        if (
          member_upload_details.validated_members !== undefined &&
          member_upload_details.validated_members !== null
        ) {
          this.getInitialApprovedMembers(member_upload_details.validated_members);
          this.handleLoadTmp();
          this.getValidatedMembers(member_upload_details.validated_members);
        }
      });
    } else if (this.state.verifyPageType === 'edit') {
      console.log('PAGETYPE IS EDIT');
      await this.checkTicketClosed('Verify Edit');
      API.getVerifyEditMemberUpload(this.props.match.params.member_upload_id)
        .then(response => {
          console.log('verify edit res', response);
          if (response && response.error === undefined) {
            API.getClientHMOInfo(this.props.match.params.client_id).then(
              hmoclient => {
                console.log('verify edit client', hmoclient);
                let ccId = '';
                let ccNo = '';
                let ccName = '';
                let contractName = '';
                let contractId = '';
                let memberDataFields: any[] = [];
                let benefitPlanTreeArr: any[] = [];

                if (hmoclient && hmoclient.error === undefined) {
                  ccId =
                    hmoclient.client && hmoclient.client._id
                      ? hmoclient.client._id
                      : '';
                  memberDataFields =
                    hmoclient.client && hmoclient.client.member_data_fields
                      ? hmoclient.client.member_data_fields
                      : [];
                  ccNo =
                    hmoclient.client && hmoclient.client.corporate_account_no
                      ? hmoclient.client.corporate_account_no
                      : '';
                  ccName =
                    hmoclient.client && hmoclient.client.registered_name
                      ? hmoclient.client.registered_name
                      : '';
                  if (
                    hmoclient.benefit_plan_tree &&
                    hmoclient.benefit_plan_tree.length > 0
                  ) {
                    console.log('=========', contractName);
                    console.log('contractId', contractId);
                    console.log(
                      'benefit_plan_tree val 1',
                      hmoclient.benefit_plan_tree,
                    );
                    benefitPlanTreeArr = hmoclient.benefit_plan_tree;
                    hmoclient.benefit_plan_tree.forEach(item => {
                      if (item.level === 1 && item.code === 'MCT') {
                        console.log('targeted item of benefit plan tree', item);
                        let period = '';
                        if (
                          item.custom_metadata &&
                          item.custom_metadata.coverage_start_date &&
                          item.custom_metadata.coverage_end_date
                        ) {
                          let coverage_end_date = moment(
                            this.endDateCorrector(
                              item.custom_metadata.coverage_end_date,
                              item.custom_metadata.additional_info,
                            ),
                          )
                            .subtract(1, 'days')
                            .format('MMM D, YYYY');
                          period =
                            ' ' +
                            GlobalFunction.toCommonDateString(
                              new Date(
                                item.custom_metadata.coverage_start_date,
                              ),
                            ) +
                            ' - ' +
                            GlobalFunction.toCommonDateString(
                              new Date(coverage_end_date),
                            );

                          console.log(period);
                        }
                        contractName = hmoclient.client['contract_id'] + period
                        // contractName =
                        //   'Contract ' +
                        //   (item.version ? item.version : '1') +
                        //   period;
                        contractId = item.principal_temp_id; //item.member_id
                      }
                    });
                  }
                }

                let breadcrumbs_items: any = [
                  {
                    label: 'DASHBOARD',
                    url: '#/membership',
                  },
                  {
                    label: 'VERIFY EDIT',
                    url: '',
                  },
                ];
                if (hmoclient.client && hmoclient.client.registered_name) {
                  breadcrumbs_items.push({
                    label: hmoclient.client.registered_name,
                    url: '',
                  });
                }

                if (this.state.ocpPath === 've-ocp') {
                  this.setState(prevState => ({
                    ...prevState,
                    ocpPayload: {
                      ...prevState.ocpPayload,
                      endorsement_date: '',
                      batch_no: '',
                      client_id: ccId,
                      corporate_account_no: ccNo,
                      corporate_name: ccName,
                      contract: contractName,
                      sender: '',
                      sender_email: '',
                      encode_method: '',
                      encoded_date: '',
                      encoded_by: '',
                    },
                  }));
                }

                this.setState({
                  clientData: hmoclient,
                  memberDataFields: memberDataFields,
                  check_internet_flag: false,
                  plan_types: hmoclient.plan_types,
                  corporateAccountNo: ccNo,
                  corporateAccountName: ccName,
                  contractName: contractName,
                  breadcrumbs: breadcrumbs_items,
                  loading_state: false,
                  member_edit_data: response,
                  benefitPlanTree: benefitPlanTreeArr,
                });
                if (response !== undefined && response !== null) {
             
                  this.getValidatedMembers(response);
                }
              },
            );
          } else {
            this.setState({
              loading_state: false,
              member_edit_data: [],
            });

            if (response && response.error) {
              this.showUnauthorizedError(response.error);
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    } else if (this.state.verifyPageType === 'vd') {
      console.log('PAGETYPE IS Verify Dependent');
      await this.checkTicketClosed('Verify Dependent');
      API.getMemberPrincipal(this.props.match.params.temp_id).then(response => {
        console.log('TEMP ID RES', response);
      });
      API.getDependentData(this.props.match.params.member_upload_id).then(
        response => {
          if (response) {
            API.getClientHMOInfo(this.props.match.params.client_id).then(
              hmoclient => {
                let ccNo = '';
                let memberDataFields: any[] = [];
                let ccName = '';
                let contractName = '';
                let contractId = '';
                let benefitPlanTreeArr: any[] = [];

                console.log('8899', contractId);
                if (hmoclient && hmoclient.error === undefined) {
                  memberDataFields =
                    hmoclient.client && hmoclient.client.member_data_fields
                      ? hmoclient.client.member_data_fields
                      : [];
                  ccNo =
                    hmoclient.client && hmoclient.client.corporate_account_no
                      ? hmoclient.client.corporate_account_no
                      : '';
                  ccName =
                    hmoclient.client && hmoclient.client.registered_name
                      ? hmoclient.client.registered_name
                      : '';
                  if (
                    hmoclient.benefit_plan_tree &&
                    hmoclient.benefit_plan_tree.length > 0
                  ) {
                    benefitPlanTreeArr = hmoclient.benefit_plan_tree;
                    hmoclient.benefit_plan_tree.forEach(item => {
                      if (item.level === 1 && item.code === 'MCT') {
                        console.log('targeted item of benefit plan tree', item);
                        let period = '';
                        if (
                          item.custom_metadata &&
                          item.custom_metadata.coverage_start_date &&
                          item.custom_metadata.coverage_end_date
                        ) {
                          let coverage_end_date = moment(
                            this.endDateCorrector(
                              item.custom_metadata.coverage_end_date,
                              item.custom_metadata.additional_info,
                            ),
                          )
                            .subtract(1, 'days')
                            .format('MMM D, YYYY');
                          period =
                            ' ' +
                            GlobalFunction.toCommonDateString(
                              new Date(
                                item.custom_metadata.coverage_start_date,
                              ),
                            ) +
                            ' - ' +
                            GlobalFunction.toCommonDateString(
                              new Date(coverage_end_date),
                            );
                        }
                        contractName =
                          'Contract' +
                          (item.version ? item.version : '1') +
                          period;
                        //+
                        //period;
                        //+ item._id;
                        //contractId = '601bc172889cb5b74218690'
                        contractId = item.contracts ? item.contracts : '';
                      }

                    });
                  }
                }

                let breadcrumbs_items: any = [
                  {
                    label: 'DASHBOARD',
                    url: '#/membership',
                  },
                  {
                    label: 'VERIFY DEPENDENT',
                    url: '',
                  },
                ];
                if (hmoclient.client && hmoclient.client.registered_name) {
                  breadcrumbs_items.push({
                    label: hmoclient.client.registered_name,
                    url: '',
                  });
                }
                const approved_disapproved_members:any[] = [];
                if (response && Array.isArray(response)) {
                  for(const member of response){
                    if(member.member_type === 'Principal') continue;
                    else {
                      const member_action:string = member.validation_status && member.validation_status.action ?
                        member.validation_status.action : ''; 
                      if(['approve','disapprove'].includes(member_action)){
                        approved_disapproved_members.push(member);
                      }
                    }
                  }
                }
                this.setState({
                  clientData: hmoclient,
                  memberDataFields: memberDataFields,
                  check_internet_flag: false,
                  plan_types: hmoclient.plan_types,
                  corporateAccountNo: ccNo,
                  corporateAccountName: ccName,
                  contractName: contractName,
                  breadcrumbs: breadcrumbs_items,
                  loading_state: false,
                  member_edit_data: response,
                  benefitPlanTree: benefitPlanTreeArr,
                  initApprovedMembers: approved_disapproved_members
                });
                if (response !== undefined && response !== null) {
                   console.log('is this vefify get3', response)
                  this.getValidatedMembers(response);
                }
              },
            );
          } else {
            this.setState({
              loading_state: false,
              member_edit_data: [],
            });
          }
        },
      );
    }
   
  }

  checkTicketClosed = async (ticket_type:string) => {
    const filter = {
      limit:10,
      skip:0,
      where:{
        and:[
          {
            pmaker_case_uid:this.props.match.params.ticket_id
          },
          {
            ticket_type: ticket_type
          }
        ]
      }
    }

    await API.getTicketList(filter)
    .then(response=>{
      if(response.tickets && response.tickets.length>0){
        this.setState({
          ticket_closed: response.tickets[0].status === 'CLOSED'
        })
      }
    }).catch(error=>{
      console.log("Check Ticket List Error: ",error);
    })
  }

  fetchPrincipals = async (id: string) => {
    try {
      // Fetch data asynchronously
      const data = await API.getClientPrincipals(id);
      console.log(" jacobdata:", data)
  
      // Safely update the state
      this.setState((prevState) => ({
        ...prevState,
        principalList: data, // Update the principalList with fetched data
      }));
    } catch (error) {
      console.error("Error fetching principals:", error);
      // You might want to handle errors here, such as showing an error message
    }
  };

  componentDidUpdate(_, prevState) {
    if (prevState.member_edit_data !== this.state.member_edit_data) {
      // console.log('update member edit data', this.state.member_edit_data)
      if (this.state.verifyPageType == 'vd') {

        this.getValidatedMembers(this.state.member_edit_data);
      }
    }

    if (prevState.partialtable !== this.state.partialtable) {
      // console.log('componentDidUpdate partial table', this.state.partialtable)
    }
    if (prevState.incompletetable !== this.state.incompletetable) {
      // console.log('componentDidUpdate incomplete table', this.state.incompletetable)
    }
    if (prevState.conflicttable !== this.state.conflicttable) {
      // console.log('update conflict table', this.state.conflicttable);
      // console.log('update prev conflict table', prevState.conflicttable);
    }
    if (prevState.validationtable !== this.state.validationtable) {
      // console.log('update validation table', this.state.validationtable)
    }
    if (prevState.othervalidationtable !== this.state.othervalidationtable) {
      // console.log('update other validation table', this.state.othervalidationtable)
    }
    if (prevState.supportingtable !== this.state.supportingtable) {
      // console.log('update supporting table', this.state.supportingtable)
    }
    if (prevState.disapprovetable !== this.state.disapprovetable) {
      // console.log('update disapprove table', this.state.disapprovetable)
    }
    if (prevState.approvedtable !== this.state.approvedtable) {
      // console.log('update approved table', this.state.approvedtable) //prevtable data to approvetable
    }
    if (prevState.alltable !== this.state.alltable) {
      // console.log('update all table', this.state.alltable)
    }
    if (prevState.verifyEditTable !== this.state.verifyEditTable) {
      // console.log('update verify edit table', this.state.verifyEditTable)
    }
    if (prevState.verifyEditAllData !== this.state.verifyEditAllData) {
      // console.log('update verify edit alldata', this.state.verifyEditAllData)
    }
    if (prevState.verifyEditTableCopy !== this.state.verifyEditTableCopy) {
      // console.log('update verify edit table copy', this.state.verifyEditTableCopy)
    }
    if (prevState.actionHolder !== this.state.actionHolder) {
      console.log('update actionHolder', this.state.actionHolder);
    }
    if (prevState.remarksHolder !== this.state.remarksHolder) {
      console.log('update remarksHolder', this.state.remarksHolder);
    }
    if (prevState.ocpPayload !== this.state.ocpPayload) {
      console.log('update ocp payload', this.state.ocpPayload);
    }
    if (prevState.verifierData !== this.state.verifierData) {
      console.log('update verifierData', this.state.verifierData);
    }
    if (prevState.member_upload_data !== this.state.member_upload_data) {
      console.log('update member_upload_data', this.state.member_upload_data);
    }
    if (prevState.initApprovedMembers !== this.state.initApprovedMembers) {
      console.log('update initApprovedMembers', this.state.initApprovedMembers);
    }

    if(this.state?.tab !== prevState?.tab) { 
     if(this.state?.validationtable?.rows.length > 0 && this.state?.tab === 3)  {
       const {clientId} = prevState
       const isHasEmpPossiblePrincipal = prevState.validationtable.rows.some(row => row?.member_details?.possible_principals.length === 0);
       if(isHasEmpPossiblePrincipal )  {
         this.fetchPrincipals(clientId)
      }
     }
    }
  }

  dataModifier = (data: any, validationStatus: string) => {
    let newData: any = data;
   
    // console.log("DATAMODIFIER()")
    if (data) {
      switch (validationStatus) {
        case 'FOR_VALIDATION': // Verify Member > Other Validation Rules tab
          if (
            data['plan_type'] &&
            !data['plan_type'].includes(' - For Validation') &&
            data['validation_status'] &&
            data['validation_status']['message'] &&
            data['validation_status']['message'].includes(
              'Plan Type [For Validation]',
            )
          ) {
            newData['plan_type'] =
              newData['plan_type'].trim() + ' - For Validation';
          }
          if (
            data['gender'] &&
            !data['gender'].includes(' - For Validation') &&
            data['validation_status'] &&
            data['validation_status']['message'] &&
            data['validation_status']['message'].includes(
              'Gender [For Validation]',
            )
          ) {
            newData['gender'] =
              newData['gender'].trim() + ' - For Validation';
          }
          if (
            data['civil_status'] &&
            !data['civil_status'].includes(' - For Validation') &&
            data['validation_status'] &&
            data['validation_status']['message'] &&
            data['validation_status']['message'].includes(
              'Civil Status [For Validation]',
            )
          ) {
            newData['civil_status'] =
              newData['civil_status'].trim() + ' - For Validation';
          }
          break;
        case 'REQUIRING_SUPPORTING_DOCUMENTS' : //Verify Member >Req Supporting Docs
        if (
          data['validation_status'] &&
          data['validation_status']['message'] &&
          data['validation_status']['message'].includes(
            'Existing member is now married',
          )
        ) {
          newData['civil_status'] =
            newData['civil_status'].trim() + ' - Requiring Supporting Document';
        }

        break;
      }
    }

    return newData;
  };

  stringMask = (strValue: string, validationStatus: string) => {
    let newString: string = strValue;
    if (strValue) {
      switch (validationStatus) {
        case 'FOR_VALIDATION':
          if (strValue.includes(' - For Validation')) {
            newString = newString.replace(' - For Validation', '');
          }
          break;
        case 'REQUIRING_SUPPORTING_DOCUMENTS':
          if (strValue.includes(' - Requiring Supporting Document')) {
            newString = newString.replace(' - Requiring Supporting Document', '');
          }
      }
    }

    return newString;
  };

  getInitialApprovedMembers = (data: any[]) => {
    let approvedMembersArr: any[] =
      data.length > 0
        ? data.filter(
            item =>
              item['validation_status'] &&
              item['validation_status']['status'] &&
              item['validation_status']['status'] === 'APPROVED',
          )
        : [];

    console.log('initially approved members', approvedMembersArr);
    this.setState({
      initApprovedMembers: approvedMembersArr,
    });
  };

  async getUpdatedMemberUploadData(isGenerate?: boolean) {
    this.setState(
      {
        check_internet_flag: true,
        loading_state: true,
      },
      () => {
        this.resetFilters();
      },
    );

    if (this.state.verifyPageType === 'default') {
      console.log(
        'new getUploadMember mem_up_id',
        this.props.match.params.member_upload_id,
      );
      await API.getUploadMember(this.props.match.params.member_upload_id) // 4942
        .then(response => {
          if (response && isNil(response.error)) {
            // MS867: Plan Type > '- For Validation' concatenation


        
            if (
              response.validated_members &&
              response.validated_members.length > 0
            ) {
              response.validated_members = response.validated_members.map(
                item => {
                  if (
                    item &&
                    item.validation_status &&
                    item.validation_status.status
                  ) {
                    item = this.dataModifier(
                      item,
                      item.validation_status.status,
                    );
                  }
                  return item;
                },
              );
            }
            //

            if (!isNil(response.validated_members)) {
              console.log(
                'new getUploadMember members',
                response.validated_members,
              );

              console.log('is this vefify get2', response.validated_members)
              this.getValidatedMembers(response.validated_members);
            }

            this.setState(
              {
                check_internet_flag: false,
                loading_state: false,
                member_upload_data: response,
              },
              () => {
                if (isGenerate === true) {
                  /** logging of initially and newly approved members. Can be removed. */
                  if (this.state.member_upload_data['validated_members']) {
                    let data: any[] = this.state.member_upload_data[
                      'validated_members'
                    ];
                    let approvedMembersArr: any[] =
                      data.length > 0
                        ? data.filter(
                            item =>
                              item['validation_status'] &&
                              item['validation_status']['status'] &&
                              item['validation_status']['status'] ===
                                'APPROVED',
                          )
                        : [];

                    console.log(
                      'isGenerate approved members2',
                      approvedMembersArr, //new approvedmembers added
                    );
                  }
                  /** */

                  this.setState({
                    generateFlag: true,
                  });
                }
              },
            );
          } else {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              member_upload_data: {},
            });

            if (isGenerate === true) {
              this.setState({
                generateFlag: true,
              });
            }
          }
        })
        .catch(() => {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            member_upload_data: {},
          });

          if (isGenerate === true) {
            this.setState({
              generateFlag: true,
            });
          }
        });
    } else if (this.state.verifyPageType === 'edit') {
      API.getVerifyEditMemberUpload(this.props.match.params.member_upload_id)
        .then(response => {
          console.log('VERIFYEDITRESP', response);
          if (response && response.error === undefined) {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              member_edit_data: response,
              noCancelButton: false,
            });
   
            this.getValidatedMembers(response);

            if (isGenerate === true) {
              this.setState({
                generateFlag: true,
              });
            }
          } else {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              member_edit_data: [],
              noCancelButton: false,
            });

            if (isGenerate === true) {
              this.setState({
                generateFlag: true,
              });
            }
          }
        })
        .catch(() => {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            member_edit_data: [],
            noCancelButton: false,
          });

          if (isGenerate === true) {
            this.setState({
              generateFlag: true,
            });
          }
        });
    } else if (this.state.verifyPageType === 'vd') {
      API.getVerifyDependentMemberUpload(
        this.props.match.params.member_upload_id,
      )
        .then(response => {
          if (response && response.error === undefined) {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              member_edit_data: response,
              noCancelButton: false,
            });
         
            this.getValidatedMembers(response);

            if (isGenerate === true) {
              const hasMembersForConfirmation = _.some(
                response,
                validatedMember => {
                  const validationAction = _.get(
                    validatedMember,
                    'validation_status.status',
                    '',
                  );
                  const validationStatus = _.get(
                    validatedMember,
                    'validation_status.action',
                    '',
                  );
                  return (
                    ['','confirmation','for confirmation'].includes(validationAction) && //For Confirmation Action
                    validationStatus==='MEMBER_STATUS_FOR_VALIDATION' //Restricted only for members with For Validation Status
                  );
                },
              );

              if (hasMembersForConfirmation) {
                this.setState({
                  redirectFrom: {
                    verifyEdit: true,
                  },
                });
              } else {
                this.routeCase(() => {
                  this.setState({
                    isModalOpen: true,
                    modalTitle: '',
                    modalMessage: 'Success',
                    redirectFrom: {
                      verifyEdit: true,
                    },
                  });
                });
              }
            }
          } else {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              member_edit_data: [],
              noCancelButton: false,
            });
          }
        })
        .catch(() => {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            member_edit_data: [],
            noCancelButton: false,
          });
        });
    }
  }




  getValidatedMembers(data: any[]) {

    //getting updated/latest plan type(5755)
    const { plan_types } = this.state;
    data.map(item => {
      if (plan_types && plan_types.length > 0 && item.plan_type) {
        const matchingPlanType = plan_types.find(pt => 
          pt.plan_type_code === item.plan_type || 
          pt.plan_type_name === item.plan_type
        );
        
        if (matchingPlanType) {
          item.plan_type = matchingPlanType.plan_type_name;
        }
      }
      
      return item;
    });
    
    const getPrincipalName = (item: any) => {
      const principalMember = item.principal_temp_id
        ? data.find(member => member.temp_id === item.principal_temp_id)
        : undefined;
      if (principalMember) {
        let principalNameVal: string = `${principalMember.first_name} ${
          principalMember.middle_name
        } ${principalMember.last_name} ${
          principalMember.suffix &&
          principalMember.suffix !== null &&
          principalMember.suffix.trim() !== ''
            ? ', ' + principalMember.suffix
            : ''
        }`;

        return principalNameVal.trim();
      }

      return '';
    };

    if (this.state.verifyPageType === 'default') {
      // const data: any[] = [];
      const partialColumns = Object.assign({}, this.state.partialColumns);
      const partialTable = Object.assign({}, this.state.partialtable);
      const incompleteTable = Object.assign({}, this.state.incompletetable);
      const conflictTable = Object.assign({}, this.state.conflicttable);
      const validationTable = Object.assign({}, this.state.validationtable);
      const othervalidationTable = Object.assign(
        {},
        this.state.othervalidationtable,
      );
      const supportingTable = Object.assign({}, this.state.supportingtable);
      const disapprovedTable = Object.assign({}, this.state.disapprovetable);
      const approvedTable = Object.assign({}, this.state.approvedtable);
      const allTable = Object.assign({}, this.state.alltable);

      let partial: any[] = [];
      let incomplete: any[] = [];
      let conflict: any[] = [];
      let validation: any[] = [];
      let othervalidation: any[] = [];
      let supporting: any[] = [];
      let disapproved: any[] = [];
      let approved: any[] = [];
      let all: any[] = [];

      const { remarksHolder } = this.state;
      let batch_Name :string = '';
      /** REMOVE STORED ACTION FOR A MEMBER WHEN MEMBER HAS BEEN TRANSFERRED TO ANOTHER TAB */
      let currentActionArr: any[] = this.state.actionHolder;
      if (currentActionArr && currentActionArr.length > 0) {
        data.forEach(mData => {
          currentActionArr = currentActionArr
          .map(ahItem => {
            if(
                ahItem &&
                ahItem['_id'] &&
                ahItem['validation_status'] &&
                mData &&
                mData['_id'] &&
                mData['validation_status'] &&
                mData['validation_status']['status'] &&
                ahItem['_id'] === mData['_id']
            ) {

                if (ahItem['action'] === 'approve' &&  mData['validation_status']['status'] === 'APPROVED' ) {
                  ahItem['validation_status'] = 'APPROVED';
                  mData['validation_status']['status'] = 'APPROVED'; /// 4942 ISSUE when upload done
                }
                if (ahItem['action'] === 'disapprove' && mData['validation_status']['status'] === 'DISAPPROVED') {
                  ahItem['validation_status'] = 'DISAPPROVED';
                  mData['validation_status']['status'] = 'DISAPPROVED';
                }
                // if (ahItem['action'] === 'for confirmation') {
                //   ahItem['validation_status'] = 'FOR_VALIDATION';
                //   mData['validation_status']['status'] = 'FOR_VALIDATION';
                // }

                return ahItem;
            } else {
              return ahItem;
            }
          })
          .filter(currData => currData !== null);
        });
        this.setState({ actionHolder: currentActionArr });
      }
      /** */
//testing: find member validation_status to trace where Member Remarks 

      this.setState({ allValidated: data });
      data.forEach((item, idx) => {
        if (item.validation_status) {
          batch_Name = item.batch_name;
          let actionIdx = currentActionArr
            .map(ahData => {
              return ahData._id;
            })
            .indexOf(item['_id']);
          let remarksIdx = remarksHolder
            .map(rhData => {
              return rhData._id;
            })
            .indexOf(item['_id']);
          const validation_status = item.validation_status;
          const member_added = `${item.first_name.trim()} ${item.middle_name.trim()} ${item.last_name.trim()}${
            item.suffix && item.suffix !== null && item.suffix.trim() !== ''
              ? ', ' + item.suffix
              : ''
          }`;
          let action: string = '';
          // if (item.validation_status.action !== undefined) {
          //   action = item.validation_status.action;
          // }
          let tempAll: any = {
            temp_id: item.temp_id,
            member_name: member_added.trim(),
            member_issue: '',
            status: item.validation_status.status
          };
          let tmpPrincipalName = '';
          if(item.member_type==='Dependent'){
            tmpPrincipalName = `${item.principal_first_name ?? ""} ${item.principal_middle_name ?? ""} ${item.principal_last_name ?? ""}`
            if(item.principal_suffix && item.principal_suffix.trim().length>0){
              tmpPrincipalName +=`, ${item.principal_suffix}` 
            }
          }
          const tempMemberData: any = {
            member_name: member_added.trim(),
            batch_name: item.batch_name,
            first_name: item.first_name,
            middle_name: item.middle_name,
            last_name: item.last_name,
            suffix: item.suffix,
            civil_status: item.civil_status,
            _id: item._id,
            gender: item.gender,
            date_of_birth: item.date_of_birth
              ? moment(item.date_of_birth).format('MM/DD/YYYY')
              : '',
            member_type: item.member_type,
            plan_type: item.plan_type,
            principal_name: tmpPrincipalName,
            relationship_to_principal: item.relationship_to_principal,
            effectivity_date: item.effectivity_date
              ? moment(item.effectivity_date).format('MM/DD/YYYY')
              : '',
          };


          switch (item.validation_status.status) {
            case 'PARTIAL_MATCH':
              // JULY 24, 2020: GET BATCH NAMES FOR MEMBER ADDED & EXISTING MEMBER
              let existingMember_batchName: string = 'N/A';
              let memberAdded_batchName: string = 'N/A';
              console.log('Partial Match Tab');
              if (
                validation_status['matched_member'] &&
                validation_status['matched_member']['batch_name']
              ) {
                existingMember_batchName =
                  validation_status['matched_member']['batch_name'];
              }
              if (item['batch_name']) {
                memberAdded_batchName = item['batch_name'];
              }

              const matched_member = validation_status.matched_member;
              const existing_member = `
              ${matched_member.first_name}
              ${matched_member.middle_name ? matched_member.middle_name : ''}
              ${matched_member.last_name}
              ${
                matched_member.suffix &&
                matched_member.suffix !== null &&
                matched_member.suffix.trim() !== ''
                  ? ', ' + matched_member.suffix
                  : ''
              }`;

              let person_profiles: any[] = [];
              let member_profiles: any[] = [];

              // let filtered_emptyFields: any[] = [];
              let filtered_sameFields: any[] = [];

              // for testing
              let PP: any[] = [];
              let MP: any[] = [];

              validation_status.person_profiles.forEach(item => {
                let temp = item;
                PP.push(item);

                // for fields that are empty
                if (temp.existing_member === '' && temp.added_member === '' || temp.existing_member === undefined && temp.added_member === undefined || temp.existing_member === null && temp.added_member === null) {
                  temp.className = 'same-field';
                  temp.compare_status = 'same';
                }


                // for same fields
                if (temp.compare_status === 'same' && temp.system_name !== 'Birthdate') {
                  temp.className = 'same-field';
                  filtered_sameFields.push(temp);
                  person_profiles.push(temp);
                }

                // for different fields
                if (temp.compare_status === 'different'  && temp.system_name !== 'Birthdate') {
                  temp.className = '';
                  person_profiles.push(temp);
                }

                // for same birthday but different formats
                if (temp.system_name === 'Birthdate') {
                  const dob_existing = moment(temp.existing_member).format('MM/DD/YYYY',).trim();
                  const dob_added = moment(temp.added_member).format('MM/DD/YYYY',).trim()

                  if (dob_existing === dob_added) {
                    temp.className = 'same-field';
                    temp.compare_status = 'same';
                    filtered_sameFields.push(temp);
                    person_profiles.push(temp);
                  }
                  else {
                    temp.className = '';
                    temp.compare_status = 'different';
                    person_profiles.push(temp);
                  }
                }



              });

              validation_status.member_profiles.forEach(item => {
                let temp = item;
                MP.push(item);

                if (temp.compare_status === 'same') {
                  temp.className = 'same-field';
                }

                //Find index of specific object
                let objIndex = person_profiles.findIndex((obj => obj.system_name == temp.system_name));

                // reassigning of data from member_profile to empty person_profile data
                if (objIndex >= 0 && person_profiles[objIndex] !== undefined) {
                  if (
                      person_profiles[objIndex].added_member === '' &&
                      person_profiles[objIndex].existing_member === '' &&
                      person_profiles[objIndex].className !== '' &&
                      person_profiles[objIndex].compare_status !== ''
                      ) {
                    person_profiles[objIndex].added_member = temp.added_member;
                    person_profiles[objIndex].className = temp.className;
                    person_profiles[objIndex].compare_status = temp.compare_status;
                    person_profiles[objIndex].existing_member = temp.existing_member;
                  } else {
                    console.log('Error')
                  }
                } else {
                  person_profiles.push(temp);
                }

                member_profiles.push(temp);

              });

              let temp1: any = {
                _id:item._id,
                member_details: item,
                member_added: member_added.trim(),
                existing_member: existing_member,
                similar_fields: validation_status.message,
                remarks: item && item.validation_status['message'] ? item.validation_status['message'] : '',
                //item && item.user_remarks ? item.user_remarks : '',
                //validation_status.message,
                user_remarks: item && item.validation_status['user_remarks'] ? item.validation_status['user_remarks'] : '',
                action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                person_info: {
                  columnExtensions: partialColumns.columnExtensions,
                  columns: partialColumns.columns,
                  rows: person_profiles,
                  batchNames: {
                    existing_member: existingMember_batchName,
                    member_added: memberAdded_batchName,
                  },
                },
                member_info: {
                  columnExtensions: partialColumns.columnExtensions,
                  columns: partialColumns.columns,
                  rows: member_profiles,
                },
                temp_id:item.temp_id,
              };

              // if (
              //   remarksIdx !== -1 &&
              //   remarksHolder[remarksIdx] &&
              //   remarksHolder[remarksIdx]['remarks']
              // ) {
              //   temp1['user_remarks'] = remarksHolder[remarksIdx]['remarks'];
              // }
              assign(temp1, tempMemberData);
              partial.push(temp1);
              tempAll.member_issue = 'Partial Matches';

              break;
            case 'INCOMPLETE_REQUIREMENTS':
              let temp2: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                remarks: item && item.validation_status['message'] ? item.validation_status['message'] : '',
                //validation_status.message,
                user_remarks: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                // item && item.user_remarks ? item.user_remarks : '',
                user_validation: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                missing_fields: validation_status.message,
                incomplete_action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                temp_id:item.temp_id,
              };
              if (
                remarksIdx !== -1 &&
                remarksHolder[remarksIdx] &&
                remarksHolder[remarksIdx]['remarks']
              ) {
                temp2['user_remarks'] = remarksHolder[remarksIdx]['remarks'];
              }

              assign(temp2, tempMemberData);

              incomplete.push(temp2);
              tempAll.member_issue = `Incomplete Requirements: ${validation_status.message}`;
              break;
             case 'CONFLICT_ON_DATA': 
              let temp3: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                remarks: item && item.validation_status['message'] ? item.validation_status['message'] : '',
                user_remarks: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                user_validation: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                conflict_data_action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                  ? currentActionArr[actionIdx]['action']
                  : action,
                  temp_id:item.temp_id, 
                };
                if (
                  remarksIdx !== -1 &&
                  remarksHolder[remarksIdx] &&
                  remarksHolder[remarksIdx]['remarks']
                ) {
                  temp3['user_remarks'] = remarksHolder[remarksIdx]['remarks'];
                }
                assign(temp3, tempMemberData);
                conflict.push(temp3);

                if(tempAll && tempAll.status === 'APPROVED') {
                  tempAll.member_issue = 'Approved';
                } else {
                tempAll.member_issue = `Conflict on Data: ${validation_status.message}`;
                }
                //  tempAll.member_issue = `Conflict on Data: ${validation_status.message}`;
                break;
            // case 'UNMATCHED_PRINCIPAL_DEPENDENTS':
            case 'FOR_VALIDATION':
              let temp4: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                // remarks: validation_status.message,
                user_validation: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                remarks: item && item.validation_status['message'] ? item.validation_status['message'] : '',
                //validation_status.message,
                user_remarks: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                other_validation_action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                temp_id:item.temp_id,
              };

              if (
                remarksIdx !== -1 &&
                remarksHolder[remarksIdx] &&
                remarksHolder[remarksIdx]['remarks']
              ) {
                temp4['user_remarks'] = remarksHolder[remarksIdx]['remarks'];
              }

              // MS867: Plan Type > '- For Validation' masking
              if (tempMemberData && tempMemberData['plan_type']) {
                tempMemberData['plan_type'] = this.stringMask(
                  tempMemberData['plan_type'],
                  item.validation_status.status,
                );
              }
              //

              assign(temp4, tempMemberData);
              othervalidation.push(temp4);
              tempAll.member_issue = `For Other Validation: ${validation_status.message}`;
              break;
            case 'REQUIRING_SUPPORTING_DOCUMENTS':
              // let remarks: string = 'N/A';
              // if(validation_status.message) {
              //   remarks = validation_status.message
              // }
              // console.log('remarks', remarks)
              let temp8: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                civil_status: item.civil_status,
                date_of_birth: item && item.date_of_birth ? moment(item.date_of_birth).format('MM/DD/YYYY') : '',
                gender: item.gender,
                type: item.member_type,
                plan_type: item.plan_type,
                principal_name: item.principal_name,
                relationship_to_principal: item.relationship_to_principal,
                // remarks: remarks,
                remarks: validation_status.message ? validation_status.message : '',
                user_remarks: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                user_validation: validation_status.user_remarks ? validation_status.user_remarks : '',
                action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                temp_id:item.temp_id
              };
              tempAll.member_issue = `Requiring Supporting Documents: ${validation_status.message}`;
              supporting.push(temp8);

              break;
            case 'UNMATCHED_PRINCIPAL_DEPENDENT':
              let temp5: any = {};
              let batchName: any = '';
              let defaultPrincipal: any = '';
              let atSameBatchPrincipal: boolean = false;

              if (
                item.possible_principals &&
                item.possible_principals.length > 0
              ) {
                const basedBatch =
                  item.possible_principals[0] &&
                  item.possible_principals[0].batch_name
                    ? item.possible_principals[0].batch_name
                    : '';
                const isSameBatch = item.possible_principals.every(
                  principal => principal.batch_name === basedBatch,
                );

                // console.log("BASED BATCH", basedBatch)
                // console.log("isSameBatch", isSameBatch)
                if (isSameBatch === true) {
                  batchName = basedBatch;
                } else {
                  batchName = '---';
                }

                defaultPrincipal =
                  item.possible_principals[0] && item.possible_principals[0].temp_id
                    ? item.possible_principals[0].temp_id
                    : '';
              }

              if(batch_Name === item.batch_name){
                atSameBatchPrincipal = true;
              }

              temp5 = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                // remarks: validation_status.message,
                remarks: validation_status.message,
                user_remarks: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                user_validation: validation_status && validation_status.user_remarks ? validation_status.user_remarks : '',
                member_principal: getPrincipalName(item),
                validation_action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                batch_name: batchName,
                selected_principal: defaultPrincipal,
                civil_status: item.civil_status,
                gender: item.gender,
                date_of_birth: item && item.date_of_birth ? moment(item.date_of_birth).format('MM/DD/YYYY') : '',
                member_type: item.member_type,
                relationship_to_principal: item.relationship_to_principal,
                temp_id:item.temp_id,
              };

              if (
                remarksIdx !== -1 &&
                remarksHolder[remarksIdx] &&
                remarksHolder[remarksIdx]['remarks']
              ) {
                temp5['user_remarks'] = remarksHolder[remarksIdx]['remarks'];
              }

              console.log("atSameBatchPrincipal Condition")
              if(atSameBatchPrincipal == false){
                assign(temp5, tempMemberData);
              // console.log('for_validation',temp4)
              // validation.push(temp5);
              console.log("VALIDATION ENTRY")
              tempAll.member_issue = `Unmatched Dependents: ${validation_status.message}`;
              }
               tempAll.member_issue = `Unmatched Dependents: ${validation_status.message}`;
              validation.push(temp5);
              break;
            case 'DISAPPROVED':
              let combinedRemarksDisapproved = combineRemarks(data, 'DISAPPROVED', item.temp_id)
              let temp6: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                civil_status: item.civil_status,
                gender: item.gender,
                date_of_birth: item.date_of_birth
                  ? moment(item.date_of_birth).format('MM/DD/YYYY')
                  : '',
                plan_type: item.plan_type,
                principal_name:
                  item.principal_first_name && item.principal_last_name
                    ? item.principal_first_name + ' ' + item.principal_last_name
                    : '',
                relationship_to_principal: item.relationship_to_principal,
                user_validation: validation_status.user_remarks,
                disapprove_action:
                  actionIdx !== -1 &&
                  currentActionArr[actionIdx] &&
                  currentActionArr[actionIdx]['action']
                    ? currentActionArr[actionIdx]['action']
                    : action,
                temp_id:item.temp_id,
                 user_remarks: combinedRemarksDisapproved && combinedRemarksDisapproved.user_remarks ? combinedRemarksDisapproved.user_remarks : '',
                remarks: combinedRemarksDisapproved && combinedRemarksDisapproved.system_remarks ? combinedRemarksDisapproved.system_remarks : '',
              };

              assign(temp6, tempMemberData);

               if (
                disapproved.findIndex(
                  item2 =>
                    item2.member_details.temp_id ===
                    temp6.member_details.temp_id,
                ) === -1
              ) {
                disapproved.push(temp6); 
               
              }
               tempAll.member_issue = `Disapproved: ${combinedRemarksDisapproved && combinedRemarksDisapproved.system_remarks ? combinedRemarksDisapproved.system_remarks : ''}`;
              break;
            case 'APPROVED':
              let combinedRemarks = combineRemarks(data, 'APPROVED', item.temp_id)
              let approvedPrincipalName = '';
              if(item.member_type==='Dependent'){
                approvedPrincipalName = `${item.principal_first_name} ${item.principal_middle_name}, ${item.principal_last_name}`
                if(item.principal_suffix && item.principal_suffix.trim().length>0){
                  approvedPrincipalName +=`, ${item.principal_suffix}`
                }
              }
              let temp7: any = {
                _id:item._id,
                member_details: item,
                member_name: member_added.trim(),
                member_added: member_added.trim(),
                existing_member: member_added.trim(),
                plan_type: item.plan_type,
                account_no: item.account_number,
                date_of_birth: item.date_of_birth
                  ? moment(item.date_of_birth).format('MM/DD/YYYY')
                  : '',
                member_type: item.member_type,
                civil_status: item.civil_status,
                gender: item.gender,
                effectivity_date: item.effectivity_date
                  ? moment(item.effectivity_date).format('MM/DD/YYYY')
                  : '',
                principal_name: approvedPrincipalName,
                relationship_to_principal: item.relationship_to_principal,
                temp_id:item.temp_id,
                user_remarks: combinedRemarks && combinedRemarks.user_remarks ? combinedRemarks.user_remarks : '',
                remarks: combinedRemarks && combinedRemarks.system_remarks ? combinedRemarks.system_remarks : '',
              };
              assign(temp7, tempMemberData);
              if (
                approved.findIndex(
                  item2 =>
                    item2.member_details.temp_id ===
                    temp7.member_details.temp_id,
                ) === -1
              ) {
                approved.push(temp7); 
              }
              //jaye 5851
              const allApproved = this.isAllApprovedForTempId(tempAll.temp_id);
              if (allApproved) {
                tempAll.member_issue = 'Approved';
              }
              break;
          }
        let foundInAll = false;
          for (const allIdx in all) {
            if (!isNil(all[allIdx])) {
              const memberInAll = all[allIdx];
                if (memberInAll.temp_id !== null || tempAll.temp_id !== null) {
                if (
                  memberInAll.temp_id === tempAll.temp_id &&
                  !includes(memberInAll.member_issue, tempAll.member_issue)
                ) {
                  memberInAll.member_issue = `${memberInAll.member_issue}${
                    memberInAll.member_issue && tempAll.member_issue ? ', ' : ''
                  }${tempAll.member_issue}`;
                  all[allIdx] = memberInAll;
                  foundInAll = true;
                  break;
                } else if (memberInAll.temp_id === tempAll.temp_id) {
                  foundInAll = true;

                  break;
                }
              } else {
                if (
                  tempAll.member_name === memberInAll.member_name &&
                  !includes(memberInAll.member_issue, tempAll.member_issue)
                ) {
                  memberInAll.member_issue = `${memberInAll.member_issue}${
                    memberInAll.member_issue && tempAll.member_issue ? ', ' : ''
                  }${tempAll.member_issue}`;
                  all[allIdx] = memberInAll;
                  foundInAll = true;
                  break;
                } else if (tempAll.member_name === memberInAll.member_name) {
                  foundInAll = true;
                  break;
                }
              }
            }
          }
          if (!foundInAll) {
            all.push(tempAll);
          }
        }
      });
 //filter Approved
      approved = approved.filter(item => item.temp_id && 
        partial.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        incomplete.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        conflict.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        validation.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        othervalidation.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        supporting.findIndex(item2=>item2.temp_id === item.temp_id)===-1 &&
        disapproved.findIndex(item2=>item2.temp_id === item.temp_id)===-1 );

      partialTable.rows = partial;
      incompleteTable.rows = incomplete;
      conflictTable.rows = conflict;
      validationTable.rows = validation;
      othervalidationTable.rows = othervalidation;
      supportingTable.rows = supporting;
      disapprovedTable.rows = disapproved;
      approvedTable.rows = approved;
      allTable.rows = all;
      //the table row values are already set here
      const tabs_verify_default: any[] = [
        {
          label: 'Partial Matches',
          count: partial.length,
        },
        {
          label: 'Incomplete Requirements',
          count: incomplete.length,
        },
        {
          label: 'Conflict on Data',
          count: conflict.length,
        },
        {
          label: 'Unmatched Dependents',
          count: validation.length,
        },
        {
          label: 'Other Validation Rules',
          count: othervalidation.length,
        },
        {
          label: 'Requiring Supporting Documents',
          count: supporting.length,
        },
        {
          label: 'Disapproved',
          count: disapproved.length,
        },
        {
          label: 'Approved',
          count: approved.length,
        },
        {
          label: 'All',
          count: all.length,
        },
      ];
      const allData = {
        partial: partialTable,
        incomplete: incompleteTable,
        conflict: conflictTable,
        validation: validationTable,
        othervalidation: othervalidationTable,
        supporting: supportingTable,
        disapprove: disapprovedTable,
        approved: approvedTable,
        all: allTable,
        data: data
      };

      this.setState({
        defaultTableCopy: {
          partialtable: partialTable,
          incompletetable: incompleteTable,
          conflicttable: conflictTable,
          validationtable: validationTable,
          othervalidationtable: othervalidationTable,
          supportingtable: supportingTable,
          disapprovetable: disapprovedTable,
          approvedtable: approvedTable,
          alltable: allTable,
        },
      });

      //These are the title and the values for the columns in each tab

      this.setFieldValue('verifyTabs', tabs_verify_default);
      this.setFieldValue('partialtable', partialTable);
      this.setFieldValue('incompletetable', incompleteTable);
      this.setFieldValue('conflicttable', conflictTable);
      this.setFieldValue('validationtable', validationTable);
      this.setFieldValue('othervalidationtable', othervalidationTable);
      this.setFieldValue('supportingtable', supportingTable);
      this.setFieldValue('disapprovetable', disapprovedTable);
      this.setFieldValue('approvedtable', approvedTable);
      this.setFieldValue('alltable', allTable);
      this.setFieldValue('allData', allData);
    } else if (this.state.verifyPageType === 'edit') {
    
      let conflictOnDataTable = Object.assign(
        {},
        this.state.verifyEditTable['conflictOnData'],
      );
      let forValidationTable = Object.assign(
        {},
        this.state.verifyEditTable['forValidation'],
      );
      let terminatedMembersTable = Object.assign(
        {},
        this.state.verifyEditTable['terminatedMembers'],
      );
      let activeMembersTable = Object.assign(
        {},
        this.state.verifyEditTable['activeMembers'],
      );
      let approveMembersTable = Object.assign(
        {},
        this.state.verifyEditTable['approveMembers'],
      );
      let disapproveMembersTable = Object.assign(
        {},
        this.state.verifyEditTable['disapproveMembers'],
      );
      let otherTable = Object.assign({}, this.state.verifyEditTable['other']);
      let supportingsTable = Object.assign({}, this.state.verifyEditTable['supportings']);
      let supportingTable = Object.assign({}, this.state.verifyEditTable['supporting']);
      
      let conflictRowArray: any[] = [];
      let validationRowArray: any[] = [];
      let terminatedRowArray: any[] = [];
      let activeRowArray: any[] = [];
      let approveRowArray: any[] = [];
      let disapproveRowArray: any[] = [];
      let otherRowArray: any[] = [];
      let supportingsRowArray: any[] = [];
      let supportingRowArray: any[] = [];
      const duplicatedRowArray: any[] = [];
      data.forEach((item, key) => {
        item['principal_name'] =
          item.member_type !== undefined &&
          item.member_type.toString().toLowerCase() === 'principal'
            ? ' '
            : item.principal_name === undefined ||
              item.principal_name.trim() === ''
            ? ''
            : item.principal_name;
        item['relationship_to_principal'] =
          item.member_type !== undefined &&
          item.member_type.toString().toLowerCase() === 'principal'
            ? 'N/A'
            : item.relationship_to_principal === undefined ||
              item.relationship_to_principal.trim() === ''
            ? ''
            : item.relationship_to_principal;


        item.date_of_birth = moment(item.date_of_birth).format('MM/DD/YYYY');
        item.effectivity_date = moment(item.effectivity_date).format('MM/DD/YYYY');
 
     
        if(item?.validations){
          let conflictNo = 1;
          let forValidationNo = 1;
          let supportingNo = 1;
          let approvedNo = 1;
          let disapproveNo = 1;
          let otherNo = 1;

          item.validations.forEach((itm) => {
            let itemClone = JSON.parse(JSON.stringify(item)); // Deep clone
            

            switch (itm.status) {
              case 'APPROVED':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                
                }
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }
                itemClone['validation_status'] = itm;
                itemClone['no'] = approvedNo++;
                itemClone['full_name'] = `${itemClone.first_name} ${itemClone.middle_name} ${itemClone.last_name} ${itemClone.suffix}`
                approveRowArray.push(itemClone);
                break

              case 'DISAPPROVED':
                if (itm.message) {
                    itemClone['remarks'] = itm.message;
                }
                itemClone['no'] = disapproveNo++;
                itemClone['validation_status'] = itm;
                itemClone['full_name'] = `${itemClone.first_name} ${itemClone.middle_name} ${itemClone.last_name} ${itemClone.suffix}`
                disapproveRowArray.push(itemClone);
                break
              case 'CONFLICT_ON_DATA':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                }
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }
                itemClone['no'] = conflictNo++;
                itemClone['validation_status'] = itm;
                conflictRowArray.push(itemClone);
                break;
          
              case 'MEMBER_STATUS_FOR_VALIDATION':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                }
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }
                itemClone['no'] = forValidationNo++;
                itemClone['validation_status'] = itm;
                validationRowArray.push(itemClone);
                break;
          
              case 'REQUIRING_SUPPORTING_DOCUMENTS':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                }
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }
                itemClone['no'] = supportingNo++;
                itemClone['validation_status'] = itm;
                supportingsRowArray.push(itemClone);
                break;
          
              case 'MEMBER_STATUS_ACTIVE':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                }
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }

                itemClone['validation_status'] = itm.status;
                activeRowArray.push(itemClone);
                break;
          
              case 'MEMBER_STATUS_OTHERS':
                if (itm.message) {
                  itemClone['remarks'] = itm.message;
                }
           
                
                if (itm?.user_remarks){
                  itemClone['user_remarks'] = itm.user_remarks;
                }
                itemClone['no'] = otherNo++;
                itemClone['validation_status'] = itm.status;
                otherRowArray.push(itemClone);
                break;
            }
          });
            if(key === 1){
              console.log("conflictRowArray", conflictRowArray)
            }
            duplicatedRowArray.push(JSON.parse(JSON.stringify(item)));
        
        }else{
     
          if (item.validation_status) {
            switch (item.validation_status.status) {
              case 'CONFLICT_ON_DATA':
                if (item.validation_status.message) {
                  item['remarks'] = item.validation_status.message;
                }
                conflictRowArray.push(item);
                break;
              case 'MEMBER_STATUS_FOR_VALIDATION':
                if (item.validation_status.message) {
                  item['remarks'] = item.validation_status.message;
                }
                validationRowArray.push(item);
                break;
              case 'REQUIRING_SUPPORTING_DOCUMENTS':
                if (item.validation_status.message) {
                    item['remarks'] = item.validation_status.message;
                  }
                  if (item['user_remarks']) {
                    item.validation_status.user_remarks = item['user_remarks'];
                  }
                  supportingsRowArray.push(item);
                  break;
              case 'MEMBER_STATUS_TERMINATED':
                if (item.validation_status.message) {
                  item['remarks'] = item.validation_status.message;
                }
                terminatedRowArray.push(item);
                break;
              case 'MEMBER_STATUS_ACTIVE':
                if (item.validation_status.message) {
                  item['remarks'] = item.validation_status.message;
                }
                activeRowArray.push(item);
                break;
              case 'MEMBER_STATUS_OTHERS':
                if (item.validation_status.message) {
                  item['remarks'] = item.validation_status.message;
                
                }
                otherRowArray.push(item);
                break;
              // case
            }
            duplicatedRowArray.push(JSON.parse(JSON.stringify(item)));
          }
        }
       
      });

      conflictOnDataTable.rows = conflictRowArray;
      forValidationTable.rows = validationRowArray;
      approveMembersTable.rows = approveRowArray;
      disapproveMembersTable.rows = disapproveRowArray
      terminatedMembersTable.rows = terminatedRowArray;
      activeMembersTable.rows = activeRowArray;
      otherTable.rows = otherRowArray;
      supportingsTable.rows = supportingsRowArray;
      supportingTable.rows = supportingRowArray;

      const tabs_verify_edit: any[] = [
        {
          label: 'Conflict on Data',
          count: conflictRowArray.length,
        },
        {
          label: 'For Validation',
          count: validationRowArray.length,
        },
        {
          label: 'Requiring Supporting Documents',
          count:  supportingsRowArray.length,
        },
    
        {
          label: 'Approved Changes',
          count: approveRowArray.length,
        },
        
        {
          label: 'Disapproved Changes',
          count: disapproveRowArray.length,
        },
        {
          label: 'Others',
          count: otherRowArray.length,
        },
      ];

      this.setFieldValue('verifyTabs', tabs_verify_edit);
      console.log("Check Active Row Array: ");

      this.setState({
        verifyEditMembers:[...duplicatedRowArray],
        verifyEditTableCopy: {
          //original unsorted array used to reset applied sorting
          approveMembers: approveMembersTable,
          disapproveMembers: disapproveMembersTable,
          conflictOnData: conflictOnDataTable,
          forValidation: forValidationTable,
          supportings: supportingsTable,
          terminatedMembers: terminatedMembersTable,
          activeMembers: activeMembersTable,
          other: otherTable,
        },
        verifyEditTable: {
          //table data for display
          disapproveMembers: disapproveMembersTable,
          approveMembers: approveMembersTable,
          conflictOnData: conflictOnDataTable,
          forValidation: forValidationTable,
          supportings: supportingsTable,
          terminatedMembers: terminatedMembersTable,
          activeMembers: activeMembersTable,
          other: otherTable,
          data: data
        },
        verifyEditAllData: {
          //table data for filtering
          disapproveMembers: disapproveMembersTable,
          approveMembers: approveMembersTable,
          conflictOnData: conflictOnDataTable,
          forValidation: forValidationTable,
          supportings: supportingsTable,
          terminatedMembers: terminatedMembersTable,
          activeMembers: activeMembersTable,
          other: otherTable,
        },
      });
    } else if (this.state.verifyPageType === 'vd') {
      console.log('PAGE TYPE === VD', data)
      let forValidationTable = Object.assign({}, this.state.verifyEditTable['forValidation']);
      let terminatedMembersTable = Object.assign({}, this.state.verifyEditTable['terminatedMembers']);
      let activeMembersTable = Object.assign({}, this.state.verifyEditTable['activeMembers']);
      // let supportingsTable = Object.assign({}, this.state.verifyEditTable['supportings']);

      let validationRowArray: any[] = [];
      let terminatedRowArray: any[] = [];
      let activeRowArray: any[] = [];


      data.forEach(item => {
        item['principal_name'] =
          item.member_type.toString().toLowerCase() === 'principal'
            ? 'N/A'
            : item.principal_name === undefined ||
              item.principal_name.trim() === ''
            ? ''
            : item.principal_name;
        item['relationship_to_principal'] =
          item.member_type.toString().toLowerCase() === 'principal'
            ? 'N/A'
            : item.relationship_to_principal === undefined ||
              item.relationship_to_principal.trim() === ''
            ? ''
            : item.relationship_to_principal;

        if (item.validation_status) {
          switch (item.validation_status.status) {
            case 'MEMBER_STATUS_FOR_VALIDATION':
              if (item.validation_status.message) {
                item['remarks'] = item.validation_status.message;
              }
              validationRowArray.push(item);
              break;
            case 'MEMBER_STATUS_TERMINATED':
              if (item.validation_status.message) {
                item['remarks'] = item.validation_status.message;
              }
              terminatedRowArray.push(item);
              break;
            case 'MEMBER_STATUS_ACTIVE':
              if (item.validation_status.message) {
                item['remarks'] = item.validation_status.message;
              }
              
              activeRowArray.push(item);
              break;
            // case 'REQUIRING_SUPPORTING_DOCUMENTS':
            //   if (item.validation_status.message) {
            //     item['remarks'] = item.validation_status.message;
            //   }
            //   supportingsRowArray.push(item);
            //   break;
            // case 'MEMBER_STATUS_OTHERS':
            //   if (item.validation_status.message) {
            //     item['remarks'] = item.validation_status.message;
            //   }
            //   otherRowArray.push(item);
            //   break;
          }
        }
      });

      forValidationTable.rows = validationRowArray;
      terminatedMembersTable.rows = terminatedRowArray;
      activeMembersTable.rows = activeRowArray;
      // supportingsTable.rows = supportingsRowArray;


      const tabs_verify_vd: any[] = [
        {
          label: 'For Validation',
          count: validationRowArray.length,
        },
        // {
        //   label: 'Requiring Supporting Documents',
        //   count: supportingsRowArray.length,
        // },
        {
          label: 'Terminated Members',
          count: terminatedRowArray.length,
        },
        {
          label: 'Active Members',
          count: activeRowArray.length,
        },
      ];

      this.setState({
        verifyEditTableCopy: {
          forValidation: forValidationTable,
          terminatedMembers: terminatedMembersTable,
          activeMembers: activeMembersTable,
          // supportings : supportingsTable,
        }
      })

      this.setFieldValue('verifyTabs', tabs_verify_vd);
      this.setState({
        verifyEditTable: {
          forValidation: forValidationTable,
          terminatedMembers: terminatedMembersTable,
          activeMembers: activeMembersTable,
          // supportings : supportingsTable,
        },
        // rowData: 
      });
    }
  }

  setFieldValue = (fieldName: string, value: any) => {
    this.setState(state => ({
      ...state,
      [fieldName]: value,
    }));
  };

  handleChange = (_event: any, newValue: number) => {
    console.log('handleChange()')

    if (this.state.verifyPageType === 'default') {
      if (newValue === 0) {
        this.setState({
          partialtable: this.state.defaultTableCopy['partialtable'],
        });
      } else if (newValue === 1) {
        this.setState({
          incompletetable: this.state.defaultTableCopy['incompletetable'],
        });
      } else if (newValue === 2) {
        this.setState({
          conflicttable: this.state.defaultTableCopy['conflicttable'],
        });
      } else if (newValue === 3) {
        this.setState({
          validationtable: this.state.defaultTableCopy['validationtable'],
        });
      } else if (newValue === 4) {
        this.setState({
          othervalidationtable: this.state.defaultTableCopy[
            'othervalidationtable'
          ],
        });
      } else if (newValue === 5) {
        this.setState({
          supportingtable: this.state.defaultTableCopy['supportingtable'],
        });
      } else if (newValue === 6) {
        this.setState({
          disapprovetable: this.state.defaultTableCopy['disapprovetable'],
        });
      } else if (newValue === 7) {
        this.setState({
          approvedtable: this.state.defaultTableCopy['approvedtable'],
        });
      } else if (newValue === 8) {
        this.setState({ alltable: this.state.defaultTableCopy['alltable'] });
      }
    } else if (this.state.verifyPageType === 'edit') {
      console.log('EDIT>VERIFY DEPENDENTS', this.state)
   
      if (newValue === 0) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            conflictOnData: this.state.verifyEditTableCopy['conflictOnData'],
          },
        }));
      } else if (newValue === 1) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            forValidation: this.state.verifyEditTableCopy['forValidation'],
          },
        }));
      } else if (newValue === 2) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            supportings: this.state.verifyEditTableCopy['supportings']
          }
        }))

      } else if (newValue === 3) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            approveMembers: this.state.verifyEditTableCopy['approveMembers']
          }
        }))
      } 
      else if (newValue === 4) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            disapproveMembers: this.state.verifyEditTableCopy['disapproveMembers']
          }
        }))
      }
      else if (newValue === 5) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            other: this.state.verifyEditTableCopy['other'],
          },
        }));
      }
    } else if (this.state.verifyPageType === 'vd') {
      if (newValue === 0) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            forValidation: this.state.verifyEditTableCopy['forValidation'],
          },
        }));
      } else if (newValue === 1) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            terminatedMembers: this.state.verifyEditTableCopy[
              'terminatedMembers'
            ],
          },
        }));
      } else if (newValue === 2) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            activeMembers: this.state.verifyEditTableCopy['activeMembers']
          }
        }))
      } else if (newValue === 3) {
        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            supportings: this.state.verifyEditTableCopy['supportings']
          }
        }))
      }
    }

    this.setFieldValue('tab', newValue);
    this.setState({ currentTablePage: 0 });
  };

  handlePageSizeChange = (table: string, pageSize: number) => {
    if (this.state.verifyPageType === 'default') {
      this.setState(prevState => ({
        ...prevState,
        pageSizes: {
          ...prevState.pageSizes,
          [table]: pageSize,
        },
      }));
    } else if (this.state.verifyPageType === 'edit') {

      this.setState(prevState => ({
        ...prevState,
        verifyEditPageSize: {
          ...prevState.verifyEditPageSize,
          [table]: pageSize,
        },
      }));
    } else if (this.state.verifyPageType === 'vd') {
      this.setState(prevState => ({
        ...prevState,
        verifyEditPageSize: {
          ...prevState.verifyEditPageSize,
          [table]: pageSize,
        },
      }));
    }
  };
  generateExeptionReport = async () => {
    console.log('generateexceptionreport()')
    const partialTable = Object.assign({}, this.state.partialtable);
    const incompleteTable = Object.assign({}, this.state.incompletetable);
    const conflictTable = Object.assign({}, this.state.conflicttable);
    const validationTable = Object.assign({}, this.state.validationtable);
    const othervalidationTable = Object.assign(
      {},
      this.state.othervalidationtable,
    );
    const supportingTable = Object.assign({}, this.state.supportingtable);
    const disapprovedTable = Object.assign({}, this.state.disapprovetable);
    const approvedTable = Object.assign({}, this.state.approvedtable);
    const allData = {
      partial: partialTable,
      incomplete: incompleteTable,
      conflict: conflictTable,
      validation: validationTable,
      othervalidation: othervalidationTable,
      supporting: supportingTable,
      disapprove: disapprovedTable,
      approved: approvedTable,
    };


    console.log('duplicates100', this.state.member_upload_data)
     console.log('duplicates101', this.state.member_upload_data.validated_members)
    let memberUploadData = Object.assign({}, this.state.member_upload_data);
    let userData: any = '';

    try {
      userData = await API.getUserDataFromDb();
      // payload['terminated_by'] = userData.userName;
   } catch (e) {
      console.log('Error getting user from IndexedDB.');
   }
    const { actionHolder, initialNumberValidatedMembers } = this.state;
    const current_approved_disapproved:any[] = [];
    if(memberUploadData && memberUploadData.validated_members) {
      for(const table of Object.keys(allData)){

        console.log('CONFLICT EXCEPTION REPORT TABLE: ', allData[table].rows);
        if(allData[table] && allData[table].rows){
          allData[table].rows.forEach(item=>{
            if(item._id){
              // console.log("Check Row: ",item);
              
              const index = memberUploadData.validated_members.findIndex(item2=>item2._id===item._id);
              if(index>-1){
                let action = '';

                if(table==='partial'){
                  action = item.action;
                  action = item.action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }
  
                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
  
                  memberUploadData.validated_members[
                    index
                  ].validation_status.action = action;
                  const { user_remarks } = item;
                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;
                  }
                } else if(table==='incomplete') {
                  const { files, user_remarks } = item;
                  if (files) {
                    const documents: any[] = [];
                    Object.keys(files).map(key => {
                      const { uploaded_file } = files[key];
                      if (uploaded_file) {
                        documents.push(uploaded_file);
                      }
                    });
  
                    memberUploadData.validated_members[
                      index
                    ].documents = documents;
                  }
                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;


                    API.activityVerifyMember(item._id, item.member_details, userData.loginUsername).then(response => {
                      if(response == undefined){
                      }
                    }).catch(e => {
                      console.log('terminate error', e)
                    })
                  }
                  action = item.incomplete_action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }

                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
  
                  if (action === 'approve') {
                    delete memberUploadData.validated_members[index]
                      .validation_status.action;
  
                    memberUploadData.validated_members[
                      index
                    ].validation_status.status = 'APPROVED';
                    API.activityVerifyMember(item._id, item, userData.loginUsername).then(response => {
                      if(response == undefined){
                      }
                    }).catch(e => {
                      console.log('terminate error', e)
                    })
                  } else {
                    memberUploadData.validated_members[
                      index 
                    ].validation_status.action = action;

                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;

                  }
                } else if(table==='conflict') {
                
                  const { files, user_remarks } = item; 
                  if (files) {
                    const documents: any[] = [];
                    Object.keys(files).map(key => {
                      const { uploaded_file } = files[key];
                      if (uploaded_file) {
                        documents.push(uploaded_file);
                      }
                    });
  
                    memberUploadData.validated_members[
                      index
                    ].documents = documents;
                  }

                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;
                  }
                  action = item.conflict_data_action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }
                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
                  if (action === 'approve') {
                 //clean up validated_members
 
                  delete memberUploadData.validated_members[index].validation_status.action;
                  memberUploadData.validated_members[index].validation_status.status = 'APPROVED';
                  memberUploadData.validated_members[index].autoapproved = true;
                  
                 } else {
                  memberUploadData.validated_members[index].validation_status.action = action;
memberUploadData.validated_members[index].validation_status.user_remarks = user_remarks;
                  }
                } else if(table==='othervalidation') {
                  action = item.other_validation_action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }
                  const { user_remarks } = item
                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;

                    item.member_details['user_remarks'] = user_remarks;
                    API.activityVerifyMember(item._id, item, userData.loginUsername).then(response => {

                      if(response == undefined){
                        console.log('response', response)
                      }
                    }).catch(e => {
                      console.log('terminate error', e)
                    })

                  }
  
                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
  
                  if (action === 'approve') {
                 
  
                    delete memberUploadData.validated_members[index]
                      .validation_status.action;
  
                    memberUploadData.validated_members[
                      index
                    ].validation_status.status = 'APPROVED';
                  } else {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.action = action;
                  }
                } else if(table==='validation') {
                  action = item.validation_action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }
                  
                  const { user_remarks } = item;

                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;
                  }

                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
  
                  if (action === 'approve') {
                      if(item.selected_principal){
                        const tgtPrincipal = memberUploadData.members.find((prin)=>prin.temp_id===item.selected_principal);
                        if(tgtPrincipal){
                          memberUploadData.validated_members[index].principal_temp_id = tgtPrincipal.temp_id;
                          memberUploadData.validated_members[index].principal_name = `${tgtPrincipal.last_name}, ${tgtPrincipal.first_name}, ${tgtPrincipal.middle_name}`;
                          if(tgtPrincipal.suffix && tgtPrincipal.suffix.trim().length>0){
                            memberUploadData.validated_members[index].principal_name+=`, ${tgtPrincipal.suffix}`;
                          }
                          
                          const idx2 = memberUploadData.members.findIndex(item3=>item3.temp_id===item.temp_id);
                          
                          if(idx2>-1){
                            memberUploadData.members[idx2].principal_temp_id = tgtPrincipal.temp_id;
                            memberUploadData.members[idx2].principal_name = `${tgtPrincipal.last_name}, ${tgtPrincipal.first_name}, ${tgtPrincipal.middle_name}`;
                            if(tgtPrincipal.suffix && tgtPrincipal.suffix.trim().length>0){
                              memberUploadData.members[idx2].principal_name+=`, ${tgtPrincipal.suffix}`;
                            }
                          }
                        }
                      }
                    
  
                      memberUploadData.validated_members[
                        index
                      ].validation_status.status = 'APPROVED';
                  } else {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.action = action;
                  }
                } else if(table==='supporting') {
                  action = item.supporting_data_action;
                  if (!action || action === '') {
                    action = 'for confirmation';
                  }

                  const { user_remarks } = item;
  
                  if (user_remarks) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.user_remarks = user_remarks;
                  }
                  
                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
  
                  if (action === 'approve') {
                   
  
                    delete memberUploadData.validated_members[index]
                      .validation_status.action;
  
                    memberUploadData.validated_members[
                      index
                    ].validation_status.status = 'APPROVED';
                  } else {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.action = action;
                  }
                } else if(table==='disapprove') {
                  action = item.disapprove_action;
                  if (!action || action === '') {
                    action = 'disapprove';
                  }
                  
                  const { files } = item;
             
  
                  if (files) {
                    memberUploadData.validated_members[
                      index
                    ].validation_status.documents = files;
                  }
  
                  if (item['_id']) {
                    let obj_id = item['_id'];
                    let actionIdx = actionHolder
                      .map(ahData => {
                        return ahData._id;
                      })
                      .indexOf(obj_id);
  
                    if (actionIdx !== -1) {
                      action =
                        actionHolder[actionIdx] &&
                        actionHolder[actionIdx]['action']
                          ? actionHolder[actionIdx]['action']
                          : action;
                    }
                  }
                  memberUploadData.validated_members[
                    index
                  ].validation_status.action = action;
                }
                if(action && ['not','match','approve','disapprove'].includes(action)){

                  current_approved_disapproved.push(memberUploadData.validated_members[index]);
 
                } else if(table==='approved'){
                  current_approved_disapproved.push(memberUploadData.validated_members[index]);
                }
              }
              
            }
            
          })
        }
      }

    } else {
      console.log('validated is undefined',  memberUploadData.validated_members)
    }
    this.setFieldValue('member_upload_data', memberUploadData);
    this.setFieldValue('allData', allData);
    this.setDefaultActions();
    Processmaker.get(
      'cases/' + this.props.match.params.ticket_id,
    ).then((presponse)=>{
      
      if(presponse.current_task && presponse.current_task.length>0){
        const no_of_members = current_approved_disapproved.length - initialNumberValidatedMembers;
        if(no_of_members>=0){
          let tas_title = '';
          if(presponse.current_task[0].tas_title === 'Claimed Verify Member'){
            tas_title = "Verify Member";
          } else if(presponse.current_task[0].tas_title === "Claimed Verify Memberlist"){
            tas_title = "Verify Memberlist";
          }
          const memberProcessedReport = {
            ticket_type: tas_title,
            no_of_members: `${no_of_members}`
          }
          API.createMemberProcessed(memberProcessedReport);
          this.setState({
            initialNumberValidatedMembers: current_approved_disapproved.length
          })
        }
      }
      this.patchUploadMember(memberUploadData);
    }).catch((err)=>{
      console.log("Check Error Member Processed: ",err);
    })
    
  };

  patchUploadMember = (data: any) => {
    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });
    if(this.state.ticket_closed){
      this.setState(
        {
          check_internet_flag: false,
          loading_state: false,
        },
        () => {
          
          this.getUpdatedMemberUploadData(true);
        },
      );
    } else {
       //approved disapproved manual API
      API.patchUploadMember(
        this.props.match.params.member_upload_id,
        data,
        this.props.match.params.ticket_id,
      )
        .then(() => {
          this.setState(
            {
              check_internet_flag: false,
              loading_state: false,
            },
            () => {
              
              this.getUpdatedMemberUploadData(true);
              this.deleteSaveForNow();
            },
          );
        })
        .catch(() => {
          this.setState({
            loading_state: false,
            generateFlag: true,
          });
        });
    }
    // console.log('clicked patchUploadMember()', data)
    return;
  };

  setDefaultActions = () => {
    const tables = [
      'partialtable',
      'incompletetable',
      'conflicttable',
      'validationtable',
      'othervalidationtable',
      'supportingtable',
      'disapprovetable',
    ];

    forEach(tables, table => {
      let data = {};
      switch (table) {
        case 'partialtable':
          data = this.state.partialtable;
          forEach(data['rows'], (rowData, row) => {
            let action = rowData['action'];
            if (!action || action === '') {
              action = 'for confirmation';
              data['rows'][row]['action'] = action;
              const temp = this.state.partial_items;
              if (temp && !isNil(temp[row])) {
                temp[row]['status'] = action;
                this.setFieldValue('partial_items', temp);
              }
            }
          });
         
          break;
        case 'incompletetable':
          data = this.state.incompletetable;

          // console.log('FINALLL0001', data)
          forEach(data['rows'], (rowData, row) => {
            // console.log('FINALLL100', rowData)
            const action = rowData['incomplete_action'];
            if (!action || action === '') {
              data['rows'][row]['incomplete_action'] = 'for confirmation';   
            }
          });
          break;
        case 'conflicttable':
          data = this.state.conflicttable;
          forEach(data['rows'], (rowData, row) => {
            const action = rowData['conflict_data_action'];
            if (!action || action === '') {
              data['rows'][row]['conflict_data_action'] = 'for confirmation';
            }
          });
          break;

        case 'validationtable':
          data = this.state.validationtable;
          console.log('Validation Table 1', data);
          forEach(data['rows'], (rowData, row) => {
            const action = rowData['validation_action'];
            if (!action || action === '') {
              data['rows'][row]['validation_action'] = 'for confirmation';
            }
          });
          break;

        case 'othervalidationtable':
          data = this.state.othervalidationtable;
          console.log('Other Validation 1', data);
          forEach(data['rows'], (rowData, row) => {
            const action = rowData['other_validation_action'];
            if (!action || action === '') {
              data['rows'][row]['other_validation_action'] = 'for confirmation';
            }
          });
          break;
        case 'supportingtable':
          data = this.state.supportingtable;
          forEach(data['rows'], (rowData, row) => {
            const action = rowData['supporting_data_action'];
            if (!action || action === '') {
              data['rows'][row]['supporting_validation_action'] =
                'for confirmation';
            }
          });
          break;

        case 'disapprovetable':
          data = this.state.disapprovetable;
          break;
//         case 'approvetable':
//           data = this.state.approvedtable;
//           break;
      }
      this.setFieldValue(table, data);
    });
  };
  //TODO:@marc apply to mark as done when closing the ticket
  routeCase = success => {
    Processmaker.put(
      'cases/' + this.props.match.params.ticket_id + '/route-case',
      {},
    )
      .then(() => {
        this.patchTicket(success);
      })
      .catch((e: Error) => {
        console.log(e);
      });
  };
  patchTicket = success => {
    let payload = {
      status: 'CLOSED',
    };
    API.patchTicket(payload, this.props.match.params.ticket_id, true)
      .then(response => {
        console.log(response);
        success();
      })
      .catch(err => {
        console.log(err);
      });
  };

  handleVerifyDependentUpdateList = () => {
    const {member_edit_data} = this.state;
    let displayData = {
      forValidation: Object.assign({}, this.state.verifyEditTable['forValidation']),
      terminatedMembers: Object.assign({}, this.state.verifyEditTable['terminatedMembers']),
      activeMembers: Object.assign({}, this.state.verifyEditTable['activeMembers']),
      supporings: Object.assign({}, this.state.verifyEditTable['supportings']),
    }
    this.setState({
      loading_state: true,
      check_internet_flag: true,
      verifyEditAllData: displayData
    });
    
    const filteredMemberData:any[] = [];
    for(const emData of member_edit_data){
      if (
        emData &&
        emData['validation_status'] &&
        emData['validation_status']['status']
      ) {
        let mData = cloneDeep(emData) ;
        let validationStatus = emData['validation_status']['status'];
        let tgtTable:any = undefined;
        switch (validationStatus) {
          case 'MEMBER_STATUS_FOR_VALIDATION':
            tgtTable = 'forValidation';
            break;
          case 'REQUIRING_SUPPORTING_DOCUMENTS':
            tgtTable = 'supporings';
            break;
          case 'MEMBER_STATUS_TERMINATED':
            tgtTable = 'terminatedMembers';
            break;
          case 'MEMBER_STATUS_ACTIVE':
            tgtTable = 'activeMembers';
            break;
        }
        if(tgtTable && displayData[tgtTable] && displayData[tgtTable].rows){
          const tableData:any = displayData[tgtTable].rows.find(tgtData=>tgtData._id===emData._id);
          if(tableData){
            mData = tableData;
          }
        }
        const { user_remarks } = mData;
        if (user_remarks) {
          mData.validation_status.user_remarks = user_remarks;
        }
        filteredMemberData.push(mData);
      }
    }

    const actioned_member:any[] = [];
    for(const member of filteredMemberData){
      if(member.member_type === 'Principal') continue;
      else {
        const action:string = member.validation_status && member.validation_status.action ?
          member.validation_status.action : ''; 
        const status = member.validation_status && member.validation_status.status ? 
          member.validation_status.status : '';
        if(status==='MEMBER_STATUS_FOR_VALIDATION' || member.validation_status.previous_action){
          const previous_action:string = member.validation_status && member.validation_status.previous_action ?
            member.validation_status.previous_action : ''; 
          if(action!==previous_action){
            actioned_member.push(member);
          }
        } else {
          actioned_member.push(member);
        }
        
      }
    }
    const memberProcessedReport = {
      ticket_type: 'Verify Dependents',
      no_of_members: `${actioned_member.length}`
    }
    // return;
    API.createMemberProcessed(memberProcessedReport);
    API.putVerifyDependentUpdateList(
      this.props.match.params.member_upload_id,
      filteredMemberData,
    )
      .then(response => {
        console.log('update list res', response);
        if (response && response.error !== undefined) {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isModalOpen: true,
            modalTitle: 'Error Updating List',
            modalMessage: response.error.message,
          });
        } else {
          console.log('update list res', response.status);
          if (response.status) {
            
            this.getUpdatedMemberUploadData(true);
          }
        }
      })
      .catch(error => {
        console.log(error);
      });
  };

  handleVerifyEditUpdateList = () => {
    console.log("this.state.member_edit_data lawrence", JSON.parse(JSON.stringify(this.state.member_edit_data)))


    let conflictOnDataTable = Object.assign(
      {},
      this.state.verifyEditTable['conflictOnData'],
    );
    let forValidationTable = Object.assign(
      {},
      this.state.verifyEditTable['forValidation'],
    );
    let terminatedMembersTable = Object.assign(
      {},
      this.state.verifyEditTable['terminatedMembers'],
    );
    let activeMembersTable = Object.assign(
      {},
      this.state.verifyEditTable['activeMembers'],
    );
     let approveMembersTable = Object.assign(
      {},
      this.state.verifyEditTable['approveMembers'],
    );
    let disapproveMembersTable = Object.assign(
      {},
      this.state.verifyEditTable['disapproveMembers'],
    );
    let otherTable = Object.assign({}, this.state.verifyEditTable['other']);
    let supportingsTable = Object.assign({}, this.state.verifyEditTable['supportings']);
    let supportingTable = Object.assign({}, this.state.verifyEditTable['supporting']);


    let displayData = {
      conflictOnData: conflictOnDataTable,
      forValidation: forValidationTable,
      terminatedMembers: terminatedMembersTable,
      approveMembers: approveMembersTable,
      disapproveMembers: disapproveMembersTable,
      activeMembers: activeMembersTable,
      other: otherTable,
      supportings: supportingsTable,
      supporting: supportingTable,
    };

    this.setState({ verifyEditAllData: displayData });
    console.log("this.state.member_edit_data lawrence", JSON.parse(JSON.stringify(this.state.member_edit_data)))
    console.log("lawrence displayData", JSON.parse(JSON.stringify(displayData)))
    let editedMemberData = this.state.member_edit_data;

    let newMemberData = editedMemberData.map(emData => {

        let mData = emData;
        
        delete mData.tmp_documents;
        return mData;
     
    });

    let filteredMemberData = newMemberData.filter(nmData => nmData !== null);
    console.log('update list payload', filteredMemberData);

    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });

    //TEMP COMMENT
    API.putVerifyEditUpdateList(
      this.props.match.params.member_upload_id,
      filteredMemberData,
    )
      .then(response => {
        console.log('update list res', response);
        API.createMemberProcessed({
          ticket_type:"Verify Edit",
          no_of_members:`${filteredMemberData.length}`,
        })
        if (response && response.error !== undefined) {
          this.setState(
            {
              isModalOpen: true,
              modalTitle: 'Update List Failed',
              modalMessage: response.error.message
                ? response.error.message
                : '',
            },
            () => {
              
              this.getUpdatedMemberUploadData();
            },
          );
        } else {
     
          this.getUpdatedMemberUploadData(true);
        }
      })
      .catch(error => {
        console.log(error);
      });

  };

  openMatchPrincipalModal = () => {
    this.setFieldValue('matchPrincipalModalOpen', true);
  };

  closeMatchPrincipalModal = () => {
    let data = this.state.validationtable;
    this.setFieldValue('matchPrincipalModalOpen', false);
  };

  openPartialModal = () => {
    this.setFieldValue('partialModalOpen', true);
  };

  openSupportingModal = () => {
    this.setFieldValue('supportingModalOpen', true);
  };

  closePartialModal = () => {
    let data = this.state.partialtable;
    console.log('data rows', data.rows);
    // console.log('partialData', partialData)

    // for (let i = 0; i < partialData; i++) {
    //   if (partialData[i] && partialData[i]['user_remarks']) {
    //     data['rows'][i]['user_remarks'] = partialData[i]['user_remarks'];
    //   }
    // }
    // this.setFieldValue('partialtable', data)
    this.setFieldValue('partialModalOpen', false);
  };

  savePartialModal = (rowData: any, row: number) => {
    console.log('savePartialModal rowData', rowData);
    if (
      rowData &&
      rowData['action'] &&
      rowData['member_details'] &&
      rowData['member_details']['_id']
    ) {
      let obj_id = rowData['member_details']['_id'];
      let val_status = undefined;
      if (
        rowData['member_details']['validation_status'] &&
        rowData['member_details']['validation_status']['status']
      ) {
        val_status = rowData['member_details']['validation_status']['status'];
      }
      this.addToActionHolder(obj_id, rowData['action'], val_status);
    }

    let data = this.state.partialtable;
    data['rows'][row] = rowData;
    this.setFieldValue('partialtable', data);
  };

  saveSupportingModal = (rowData: any, row: number) => {
    console.log('saveSupportingModal rowData', rowData);
    if (
      rowData &&
      rowData['action'] &&
      rowData['member_details'] &&
      rowData['member_details']['_id']
    ) {
      let obj_id = rowData['member_details']['_id'];
      let val_status = undefined;
      if (
        rowData['member_details']['validation_status'] &&
        rowData['member_details']['validation_status']['status']
      ) {
        val_status = rowData['member_details']['validation_status']['status'];
      }
      this.addToActionHolder(obj_id, rowData['action'], val_status);
    }

    let data = this.state.supportingtable;
    data['rows'][row] = rowData;
    this.setFieldValue('supportingtable', data);
  };

  openApproveModal = (files:any) => {
    this.setFieldValue('documentData', files);
    this.setFieldValue('approveModalOpen', true);
  };

  closeApproveModal = (row?: number, details?: any, remarks?:any, userRemarks?:any) => {
    console.log('closeApproveModal()', row, remarks, userRemarks)
   
    this.setFieldValue('approveModalOpen', false);

    if (details && details['pageType'] === 'default') {
    }
  };

  saveApproveModal = async  (
    row: number, 
    files: any, 
    remarks: string, 
    user_remarks?: any, 
    details?:any,
    rowData?:any,
    uploadedDocuments?:any,
  
  ) => {
    console.log("saveApproveModal() ",);

    let userData: any = '';

    try {
      userData = await API.getUserDataFromDb();
      // payload['terminated_by'] = userData.userName;
   } catch (e) {
      console.log('Error getting user from IndexedDB.');
   }
    let data =
      this.state.tab === 1
        ? this.state.incompletetable
        : this.state.tab === 4
        ? this.state.othervalidationtable
        : this.state.tab === 5
        ? this.state.supportingtable
        : '';
    let currentField =
      this.state.tab === 1
        ? 'incompletetable'
        : this.state.tab === 2
        ? 'conflicttable'
        : this.state.tab === 3
        ? 'validationtable'
        : this.state.tab === 4
        ? 'othervalidationtable'
        : this.state.tab === 5
        ? 'supportingtable'
        : 'disapprovetable';
    //  let newFiles: any[] = [];
    if (
      data['rows'] &&
      data['rows'][row] &&
      data['rows'][row]['member_details'] &&
      data['rows'][row]['member_details']['_id']
    ) {
      console.log("CONDITION 11")
      let obj_id = data['rows'][row]['member_details']['_id'];
      let val_status = undefined;
      if (
        data['rows'][row]['member_details']['validation_status'] &&
        data['rows'][row]['member_details']['validation_status']['status']
      ) {
        val_status =
          data['rows'][row]['member_details']['validation_status']['status'];
      }
      this.addToActionHolder(obj_id, 'approve', val_status);
      if (remarks.trim() !== '') {
        this.addToRemarksHolder(obj_id, remarks);
       API.activityVerifyMember(obj_id, data['rows'][row]['member_details'], userData.loginUsername).then(response => {
        if(response == undefined){
          console.log('response2', response)
        }
          }).catch(e => {
            console.log('terminate error', e)
          })
 
      }


    }

    // if (files) {
    //   newFiles = await this.supportingDocumentChecker(files);
    //   console.log('newFiles', newFiles);
    //   if (newFiles && newFiles.length > 0) {
    //     newFiles.map(a => {
    //       if (
    //         data.rows[row].member_details.documents &&
    //         data.rows[row].member_details.documents.length > 0
    //       )
    //         data.rows[row].member_details.documents.push(a);
    //       else data['rows'][row]['member_details']['documents'] = [a];
    //     });
    //   }
    // }
    console.log('')
    data['rows'][row]['files'] = files;
    data['rows'][row]['user_remarks'] = remarks;
    this.setFieldValue(currentField, data);
    let newFiles: any[] = [];
    if (files) {
      newFiles = await this.supportingDocumentChecker(files);
      console.log('newFiles1', newFiles);
      if (newFiles && newFiles.length > 0) {
        newFiles.map(a => {
          if (
            data.rows[row].member_details.documents &&
            data.rows[row].member_details.documents.length > 0
          )
            data.rows[row].member_details.documents.push(a);
          else data['rows'][row]['member_details']['documents'] = [a];
        });
      }
    }
    this.setFieldValue(currentField, data);
    this.setFieldValue('approveModalOpen', false);

  };

  // Start of code for MemberFilesModal
  closeMemberFilesModal = (row: number, details: any) => {
    if (details && details['pageType'] === 'default') {
      let data;
      if (this.state.tab === 5) {
        data = this.state.supportingtable;
        const row = this.state.row;
        data['rows'][row]['supporting_data_action'] = '';
        data['rows'][row]['supporting_action'] = '';
        data['rows'][row]['user_remarks'] = '';
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          let obj_id = data['rows'][row]['member_details']['_id'];
          let newRemarksHolder =
            this.state.remarksHolder.length > 1
              ? this.state.remarksHolder.filter(
                  rhData => rhData['_id'] !== obj_id,
                )
              : [];

          this.setState({
            remarksHolder: newRemarksHolder,
          });
        }
        this.setFieldValue('supportingtable', data);
      } else if (this.state.tab === 4) {
        data = this.state.othervalidationtable;
        const row = this.state.row;
        data['rows'][row]['other_validation_action'] = '';
        data['rows'][row]['user_remarks'] = '';
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          let obj_id = data['rows'][row]['member_details']['_id'];
          let newRemarksHolder =
            this.state.remarksHolder.length > 1
              ? this.state.remarksHolder.filter(
                  rhData => rhData['_id'] !== obj_id,
                )
              : [];
          this.setState({
            remarksHolder: newRemarksHolder,
          });
        }
        this.setFieldValue('othervalidationtable', data);
        // this.setState({ memberFilesModalOpen: false })
      } else if (this.state.tab === 6) {
        // console.log('closeMemberFilesModal() TAB disapprovetable', this.state.disapprovetable)
        data = this.state.disapprovetable;
        // SEPT 08, 2020: FIX FOR MS731
        // data['rows'][row]['disapprove_action'] = '';
        delete data['rows'][row]['files'];
        this.setFieldValue('disapprovetable', data);
        // console.log('closeMemberFilesModal() TAB disapprovetable', newRemarksHolder)  
        // this.setState({ memberFilesModalOpen: false })

      }
    } else if (details && details['pageType'] === 'edit') {
      // if (details['tableName']) {
      //   console.log('close member modal table', details['tableName']);
      //   let data = this.state.verifyEditTable[details['tableName']];
      //   switch (details['tableName']) {
      //     case 'conflictOnData':
      //       data['rows'][row]['conflict_data_action'] = '';
      //       break;
      //     case 'forValidation':
      //       data['rows'][row]['for_validation_action'] = '';
      //       break;
      //     case 'supportings':
      //     case 'supportings':
      //       data['rows'][row]['supporting_action'] = '';
      //       break;
      //     case 'terminatedMembers':
      //       data['rows'][row]['terminated_members_action'] = '';
      //       break;
      //     case 'activeMembers':
      //       data['rows'][row]['active_members_action'] = '';
      //       break;
      //     case 'other':
      //       data['rows'][row]['other_members_action'] = '';
      //       break;
      //   }
      //   delete data['rows'][row]['files'];

      //   this.setState(prevState => ({
      //     ...prevState,
      //     verifyEditTable: {
      //       ...prevState.verifyEditTable,
      //       [details['tableName']]: data,
      //     },
      //   }));
      // }
    }

    this.setFieldValue('memberFilesModalOpen', false);
  };

  supportingDocumentChecker = (files: any) => {
    console.log('supportingDocumentChecker()');
    let newFiles: any[] = [];
    if (
      files.annulment_papers &&
      files.annulment_papers.checked === true &&
      files.annulment_papers.uploaded === true
    ) {
      let newArr = {
        name: files.annulment_papers.uploaded_file && files.annulment_papers.uploaded_file?.name !== undefined ? files.annulment_papers.uploaded_file?.name : files.annulment_papers.uploaded_filename,
        purpose: files.annulment_papers.uploaded_file?.purpose ? files.annulment_papers.uploaded_file?.purpose : "",
        type: files.annulment_papers.uploaded_type,
        url: files.annulment_papers.uploaded_file?.url ? files.annulment_papers.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.baptismal_certificate &&
      files.baptismal_certificate.checked === true &&
      files.baptismal_certificate.uploaded === true
    ) {
      let newArr = {
        name: files && files.baptismal_certificate.uploaded_file?.name
        !== undefined ? files.baptismal_certificate.uploaded_file.name : files.baptismal_certificate.uploaded_filename ,
        purpose: files && files.baptismal_certificate.purpose ? files.baptismal_certificate?.purpose : '',
        type:  files && files.baptismal_certificate.uploaded_type,
        url:  files && files.baptismal_certificate.uploaded_file?.url ? files.baptismal_certificate.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.birth_certificate &&
      files.birth_certificate.checked === true &&
      files.birth_certificate.uploaded === true
    ) {
      let newArr = {
        name: files.birth_certificate.name ? files.birth_certificate?.name : files.birth_certificate?.uploaded_filename,
        // purpose: files.birth_certificate.uploaded_file.purpose ? files.birth_certificate.uploaded_file.purpose : '',
        purpose: files && files.birth_certificate.purpose ? files.birth_certificate?.purpose : '',
        type:  files && files.birth_certificate?.uploaded_type,
        url:  files && files.birth_certificate?.uploaded_url ? files.birth_certificate?.uploaded_url : '',
      };
      newFiles.push(newArr);
    }
    if (
      files.certificate_no_marriage &&
      files.certificate_no_marriage.checked === true &&
      files.certificate_no_marriage.uploaded === true
    ) {
      let newArr = {
        name:  files && files.certificate_no_marriage.uploaded_file?.name ? files.certificate_no_marriage.uploaded_file?.name :  files.certificate_no_marriage?.uploaded_filename,
        purpose: files && files.certificate_no_marriage?.purpose ? files.certificate_no_marriage?.purpose : '',
        type:  files && files.certificate_no_marriage.uploaded_type,
        url:  files && files.certificate_no_marriage.uploaded_file?.url ? files.certificate_no_marriage.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.death_certificate &&
      files.death_certificate.checked === true &&
      files.death_certificate.uploaded === true
    ) {
      let newArr = {
        name:  files && files.death_certificate.uploaded_file?.name ? files.death_certificate.uploaded_file?.name : files.death_certificate?.uploaded_filename,
        purpose: files && files.death_certificate?.purpose ? files.death_certificate?.purpose : '',
        type: files &&  files.death_certificate.uploaded_type,
        url:  files && files.death_certificate.uploaded_file?.url ? files.death_certificate.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.employee_id &&
      files.employee_id.checked === true &&
      files.employee_id.uploaded === true
    ) {
      let newArr = {
        name:  files && files.employee_id.uploaded_file?.name ? files.employee_id.uploaded_file?.name : files.employee_id.uploaded_filename,
        purpose: files && files.employee_id?.purpose ? files.employee_id?.purpose : '',
        type:  files && files.employee_id.uploaded_type,
        url:  files && files.employee_id.uploaded_file?.url ? files.employee_id.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.marriage_certificate &&
      files.marriage_certificate.checked === true &&
      files.marriage_certificate.uploaded === true
    ) {
      let newArr = {
        name:  files && files.marriage_certificate?.uploaded_file?.name ? files.marriage_certificate?.uploaded_file?.name : files.marriage_certificate?.uploaded_filename,
        purpose: files && files.marriage_certificate?.purpose ? files.marriage_certificate?.purpose : '',
        type:  files && files.marriage_certificate.uploaded_type,
        url:  files && files.marriage_certificate.uploaded_file?.url ? files.marriage_certificate.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.others &&
      files.others.checked === true &&
      files.others.uploaded === true
    ) {
      let newArr = {
        name:  files && files.others.uploaded_file.name ?  files.others.uploaded_file?.name :  files.others?.uploaded_filename,
        purpose: files && files.others.purpose ? files.others?.purpose : '',
        type:  files && files.others.uploaded_type,
        url:  files && files.others.uploaded_file?.url ? files.others.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.passport &&
      files.passport.checked === true &&
      files.passport.uploaded === true
    ) {
      let newArr = {
        name:  files && files.passport.uploaded_file?.name,
        purpose: files && files.passport?.purpose ? files.passport?.purpose : '',
        type:  files && files.passport?.uploaded_type,
        url:  files && files.passport.uploaded_file?.url ? files.passport.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.proof_of_parentage &&
      files.proof_of_parentage.checked === true &&
      files.proof_of_parentage.uploaded === true
    ) {
      let newArr = {
        name:  files && files.proof_of_parentage.uploaded_file?.name,
        purpose: files && files.proof_of_parentage?.purpose ? files.proof_of_parentage?.purpose : '',
        type: files &&  files.proof_of_parentage.uploaded_type,
        url:  files && files.proof_of_parentage.uploaded_file?.url ? files.proof_of_parentage.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.proof_overseas_employment &&
      files.proof_overseas_employment.checked === true &&
      files.proof_overseas_employment.uploaded === true
    ) {
      let newArr = {
        name: files &&  files.proof_overseas_employment.uploaded_file?.name,
        purpose: files && files.proof_overseas_employment?.purpose ? files.proof_overseas_employment?.purpose : '',
        type:  files && files.proof_overseas_employment.uploaded_type,
        url:  files && files.proof_overseas_employment.uploaded_file?.url ? files.proof_overseas_employment.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.scanned_hmo_card &&
      files.scanned_hmo_card.checked === true &&
      files.scanned_hmo_card.uploaded === true
    ) {
      let newArr = {
        name:  files && files.scanned_hmo_card.uploaded_file?.name,
        purpose: files && files.scanned_hmo_card?.purpose ? files.scanned_hmo_card?.purpose : '',
        type: files.scanned_hmo_card.uploaded_type,
        url:  files && files.scanned_hmo_card.uploaded_file?.url ? files.scanned_hmo_card.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    if (
      files.solo_parent_id &&
      files.solo_parent_id.checked === true &&
      files.solo_parent_id.uploaded === true
    ) {
      let newArr = {
        name:  files && files.solo_parent_id.uploaded_file?.name,
        purpose: files && files.solo_parent_id.purpose ? files.solo_parent_id?.purpose : '',
        type: files &&  files.solo_parent_id?.uploaded_type,
        url: files &&  files.solo_parent_id.uploaded_file?.url ? files.solo_parent_id.uploaded_file?.url : "",
      };
      newFiles.push(newArr);
    }
    return newFiles;
  };

  saveMemberFilesModal = async (
    row: number,
    remarks: string,
    user_remarks?: string,
    details?: any,
    rowData?:any,
    files?: any, 
    openFileModal?:boolean,
    uploadedDocuments?: any
    // row, remarks, user_remarks, details, data, openFileModal
  ) => {
    if (details && details['pageType'] === 'default' || openFileModal && openFileModal['pageType'] === 'default') {

      let data =
        this.state.tab === 1
          ? this.state.incompletetable
          : this.state.tab === 2
          ? this.state.conflicttable
          : this.state.tab === 3
          ? this.state.validationtable
          : this.state.tab === 4
          ? this.state.othervalidationtable
          : this.state.tab === 5
          ? this.state.supportingtable
          : this.state.disapprovetable;

      let currentField =
        this.state.tab === 1
          ? 'incompletetable'
          : this.state.tab === 2
          ? 'conflicttable'
          : this.state.tab === 3
          ? 'validationtable'
          : this.state.tab === 4
          ? 'othervalidationtable'
          : this.state.tab === 5
          ? 'supportingtable'
          : 'disapprovetable';
      if (this.state.tab === 5) {
        data['rows'][row]['files'] = files;
        data['rows'][row]['user_remarks'] = remarks;
        console.log('if files0', files)
        let newFiles: any[] = [];
        // API.getMemberData(item)
        if (files) {
          newFiles = await this.supportingDocumentChecker(files);
          console.log('newFiles', newFiles);
          if (newFiles && newFiles.length > 0) {
            newFiles.map(a => {
              if (
                data.rows[row].member_details.documents &&
                data.rows[row].member_details.documents.length > 0
              )
                data.rows[row].member_details.documents.push(a);
              else data['rows'][row]['member_details']['documents'] = [a];
            });
          }
        }
        this.setFieldValue(currentField, data);

      } else if (
        this.state.tab === 1 ||
        this.state.tab === 2 ||
        this.state.tab === 3 ||
        this.state.tab === 4 ||
        this.state.tab === 5
      ) {
        let newFiles: any[] = [];
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          let obj_id = data['rows'][row]['member_details']['_id'];
          let val_status = undefined;
          if (
            data['rows'][row]['member_details']['validation_status'] &&
            data['rows'][row]['member_details']['validation_status']['status']
          ) {
            val_status =
              data['rows'][row]['member_details']['validation_status'][
                'status'
              ];
          }
          this.addToActionHolder(obj_id, 'approve', val_status);
          if (remarks && remarks.trim() !== '') {
            this.addToRemarksHolder(obj_id, remarks);
            // data['rows'][row]['member_details']['validation_status']['user_remarks'] = remarks
          }
        }

        //uploadedDocuments files
        if (uploadedDocuments) {
          console.log('check if file is not empty')
          newFiles = await this.supportingDocumentChecker(uploadedDocuments);
          console.log('newFiles', newFiles);
          if (newFiles && newFiles.length > 0) {
            newFiles.map(a => {
              if (
                data.rows[row].member_details.documents &&
                data.rows[row].member_details.documents.length > 0
              )
                data.rows[row].member_details.documents.push(a);
              else data['rows'][row]['member_details']['documents'] = [a];
            });
          }
        }
        data['rows'][row]['files'] = files;
        data['rows'][row]['user_remarks'] = remarks;
        this.setFieldValue(currentField, data);
        // this.setFieldValue(`data['rows'][row]['member_details']['validation_status']['user_remarks']`, remarks);

      }

    } else if (details && details['pageType'] === 'edit' || details['pageType'] === 'vd') {
      // if (details['tableName']) {
      //   console.log('verifyedit files', files)
      //   let data = this.state.verifyEditTable[details['tableName']];
      //   data['rows'][row]['files'] = files;
      //   data['rows'][row]['user_remarks'] = remarks;

      //   this.setState(prevState => ({
      //     ...prevState,
      //     verifyEditTable: {
      //       ...prevState.verifyEditTable,
      //       [details['tableName']]: data
      //     }
      //   }))
      // }
      if (details['tableName']) {
        console.log('verifyedit files', files);
        let data = this.state.verifyEditTable[details['tableName']];
        data['rows'][row]['files'] = files;
        data['rows'][row]['user_remarks'] = remarks;

        this.setState(prevState => ({
          ...prevState,
          verifyEditTable: {
            ...prevState.verifyEditTable,
            [details['tableName']]: data,
          },
        }));

        let newFiles: any[] = [];
        if (files) {
          newFiles = this.supportingDocumentChecker(files);
          console.log('newFiles', newFiles);
          if (newFiles && newFiles.length > 0) {
            newFiles.map(a => {
              if (
                data.rows[row].tmp_documents &&
                data.rows[row].tmp_documents.length > 0
              )
                data.rows[row].tmp_documents.push(a);
              else data['rows'][row]['tmp_documents'] = [a];
              // data['rows'][row]['tmp_documents'] = [a]
            });
          }
        }
      }
    }
    
    this.setFieldValue('dataForEdit', this.state.data);
    this.setFieldValue('rowDataForEdit', this.state.rowData);
    this.setFieldValue('memberFilesModalOpen', false);


  };

  deleteMemberFiles = (row: number, name: string, details: any) => {
    if (details && details['pageType'] === 'default') {
      let data =
        this.state.tab === 1
          ? this.state.incompletetable
          : this.state.tab === 2
          ? this.state.conflicttable
          : this.state.tab === 3
          ? this.state.validationtable
          : this.state.tab === 4
          ? this.state.othervalidationtable
          : this.state.disapprovetable;
      let currentField =
        this.state.tab === 1
          ? 'incompletetable'
          : this.state.tab === 2
          ? 'conflicttable'
          : this.state.tab === 3
          ? 'validationtable'
          : this.state.tab === 4
          ? 'othervalidationtable'
          : this.state.tab === 5
          ? 'supportingtable'
          : 'disapprovetable';
      if (
        data.rows[row].member_details.documents &&
        data.rows[row].member_details.documents.length > 0
      ) {
        console.log('INSIDE');
        for (const file of data.rows[row].member_details.documents) {
          let newArray: any[] = [];
          if (file.type !== name) {
            newArray.push(file);
          }
          data.rows[row].member_details.documents = newArray;
          this.setFieldValue(currentField, data);
        }
      } else {
        console.log('OUTSIDE');
      }
    }
  };
  // End of code for MemberFilesModal

  openDisapproveModal = (files:any) => {
    this.setFieldValue('documentData', files);
    this.setFieldValue('disapproveModalOpen', true);
  };

  openForConfirmationModal = (files:any) => {
    this.setFieldValue('documentData', files);
    this.setFieldValue('forConfirmationModalOpen', true);
  };

  closeDisapproveModal = () => {
    // console.log('DEBUG CLOSEDISAPPROVEMODAL0', this.state)
    if (this.state.tab === 1) {
      const data = this.state.incompletetable;
      const row = this.state.row;
      data['rows'][row]['incomplete_action'] = '';
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
      }
      this.setFieldValue('incompletetable', data);
    } else if (this.state.tab === 4) {
      const data = this.state.othervalidationtable;
      const row = this.state.row;
      data['rows'][row]['other_validation_action'] = '';
      // delete data['rows'][row]['remarks'];
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('othervalidationtable', data);
    }
    this.setFieldValue('disapproveModalOpen', false);
  };

  closeForConfirmationModal = () => {
    if (this.state.tab === 1) {
      let data = this.state.incompletetable;
      let row = this.state.row;
      data['rows'][row]['incomplete_action'] = '';
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('incompletetable', data);
    } else if (this.state.tab === 2) {
      // console.log('closeconfirmation dialog conflict tab')
      let data = this.state.conflicttable;
      let row = this.state.row;
      data['rows'][row]['conflict_data_action'] = '';
      // delete data['rows'][row]['remarks'];
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('conflicttable', data);

    } else if (this.state.tab === 3) {
      // console.log('closeconfirmation dialog validation tab')
      let data = this.state.validationtable;
      let row = this.state.row;
      data['rows'][row]['validation_action'] = '';
      // delete data['rows'][row]['remarks'];
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('validationtable', data);

    
    } else if (this.state.tab === 4) {
      let data = this.state.othervalidationtable;
      let row = this.state.row;
      data['rows'][row]['other_validation_action'] = '';
      // delete data['rows'][row]['remarks'];
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('othervalidationtable', data);
    } else if (this.state.tab === 5) {
      // console.log('closeconfirmation dialog supporting tab')
      let data = this.state.supportingtable;
      let row = this.state.row;
      // data['rows'][row]['supporting_action'] = '';
      data['rows'][row]['supporting_action'] = '';
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('supportingtable', data);
    } else if (this.state.tab === 6) {
      // console.log('closeconfirmation dialog disapproved tab')
      let data = this.state.disapprovetable;
      let row = this.state.row;
      data['rows'][row]['disapprove_action'] = '';
      // delete data['rows'][row]['remarks'];
      // data['rows'][row]['user_remarks'] = '';
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        // let obj_id = data['rows'][row]['member_details']['_id'];
        // let newRemarksHolder =
        //   this.state.remarksHolder.length > 1
        //     ? this.state.remarksHolder.filter(
        //         rhData => rhData['_id'] !== obj_id,
        //       )
        //     : [];
        // this.setState({
        //   remarksHolder: newRemarksHolder,
        // });
      }
      this.setFieldValue('disapprovetable', data);
    }
    this.setFieldValue('forConfirmationModalOpen', false);
  };

  onAddActionRemarksModalSubmit = (
    row: number,
    tab: string,
    action: any,
    remarks: string,
    userRemarks?:any,
    // newValidation?:any
  ) => {
    let data: any = {};
    console.log('addactionremarksmodal', userRemarks)

    if (this.state.verifyPageType === 'default') {
      switch (tab) {
        case 'Disapproved':
          data = this.state.disapprovetable;
          // console.log('inside onaddactionremarksmodalsubmit disapproved', data)
          if (
            data['rows'] &&
            data['rows'][row] &&
            data['rows'][row]['member_details'] &&
            data['rows'][row]['member_details']['_id']
          ) {
            let obj_id = data['rows'][row]['member_details']['_id'];
            let val_status = undefined;
            if (
              data['rows'][row]['member_details']['validation_status'] &&
              data['rows'][row]['member_details']['validation_status']['status']
            ) {
              val_status =
                data['rows'][row]['member_details']['validation_status'][
                  'status'
                ];
            }
            this.addToActionHolder(obj_id, action, val_status);
            if (remarks.trim() !== '') {
              this.addToRemarksHolder(obj_id, remarks);
            }
          }

          data['rows'][row]['disapprove_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          this.setFieldValue('disapprovetable', data);
          break;
        case 'Validation':
          data = this.state.validationtable;
          if (
            data['rows'] &&
            data['rows'][row] &&
            data['rows'][row]['member_details'] &&
            data['rows'][row]['member_details']['_id']
          ) {
            let obj_id = data['rows'][row]['member_details']['_id'];
            let val_status = undefined;
            if (
              data['rows'][row]['member_details']['validation_status'] &&
              data['rows'][row]['member_details']['validation_status']['status']
            ) {
              val_status =
                data['rows'][row]['member_details']['validation_status'][
                  'status'
                ];
            }
            this.addToActionHolder(obj_id, action, val_status);
            if (remarks.trim() !== '') {
              this.addToRemarksHolder(obj_id, remarks);
            }
          }

          data['rows'][row]['validation_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;

          if (action !== 'approve') {
            data['rows'][row]['member_principal'] = '';
            const targetMembers = this.state.member_upload_data[
              'validated_members'
            ];
            const targetMemberIndex = targetMembers.findIndex(member => {
              return (
                member['temp_id'] ===
                data['rows'][row]['member_details']['temp_id']
              );
            });
            targetMembers[targetMemberIndex]['principal_temp_id'] = undefined;
            this.setState({
              member_upload_data: {
                ...this.state.member_upload_data,
                validated_members: targetMembers,
              },
            });
          }

          this.setFieldValue('validationtable', data);
          break;
        case 'Other Validation':
          data = this.state.othervalidationtable;
          if (
            data['rows'] &&
            data['rows'][row] &&
            data['rows'][row]['member_details'] &&
            data['rows'][row]['member_details']['_id']
          ) {
            let obj_id = data['rows'][row]['member_details']['_id'];
            let val_status = undefined;
            if (
              data['rows'][row]['member_details']['validation_status'] &&
              data['rows'][row]['member_details']['validation_status']['status']
            ) {
              val_status =
                data['rows'][row]['member_details']['validation_status'][
                  'status'
                ];
            }
            this.addToActionHolder(obj_id, action, val_status);
            if (remarks.trim() !== '') {
              this.addToRemarksHolder(obj_id, remarks);
            }
          }

          data['rows'][row]['other_validation_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;

          this.setFieldValue('othervalidationtable', data);
          break;
        case 'Conflict':
          data = this.state.conflicttable;
          if (
            data['rows'] &&
            data['rows'][row] &&
            data['rows'][row]['member_details'] &&
            data['rows'][row]['member_details']['_id']
          ) {
            let obj_id = data['rows'][row]['member_details']['_id'];
            let val_status = undefined;
            if (
              data['rows'][row]['member_details']['validation_status'] &&
              data['rows'][row]['member_details']['validation_status']['status']
            ) {
                val_status =
                data['rows'][row]['member_details']['validation_status'][
                  'status'
                ];
            }
            this.addToActionHolder(obj_id, action, val_status);
            if (remarks.trim() !== '') {
              this.addToRemarksHolder(obj_id, remarks);
            }
          }

          data['rows'][row]['conflict_data_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          this.setFieldValue('conflicttable', data);
          break;

        case 'Approved' :
          data = this.state.approvedtable;
          console.log('onaddactionremarksmodalsubmit approved', data)

          if (
            data['rows'] &&
            data['rows'][row] &&
            data['rows'][row]['member_details'] &&
            data['rows'][row]['member_details']['_id']
          ) {
            let obj_id = data['rows'][row]['member_details']['_id'];
            let val_status = undefined;
            if (
              data['rows'][row]['member_details']['validation_status'] &&
              data['rows'][row]['member_details']['validation_status']['status']
            ) {
              val_status =
                data['rows'][row]['member_details']['validation_status'][
                  'status'
                ];
            }
            this.addToActionHolder(obj_id, action, val_status);
            if (remarks.trim() !== '') {
              this.addToRemarksHolder(obj_id, remarks);
            }
          }

          data['rows'][row]['conflict_data_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          this.setFieldValue('approvedtable', data);
          break;
        default:
          break;
      }
    } else if (this.state.verifyPageType === 'edit') {
      // here
      data = this.state.verifyEditTable[tab];
      data['rows'][row]['validation_status']['action'] = action;
      data['rows'][row]['validation_status']['user_remarks'] = remarks;
      console.log('on add remarks submit table', data);
      switch (tab) {
        case 'conflictOnData':
          data['rows'][row]['conflict_data_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
         
          data['rows'][row]['validations'] = data['rows'][row]['validations'].map((item) => {
            if (item.status === 'CONFLICT_ON_DATA') {
                item.user_remarks = remarks;
                item.action = action;
            }
            return item;
        });
          break;
        case 'forValidation':
          data['rows'][row]['for_validation_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'supportings':
            data['rows'][row]['supporting_action'] = action;
            data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'terminatedMembers':
          data['rows'][row]['terminated_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'activeMembers':
          data['rows'][row]['active_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'approveMembers':
          data['rows'][row]['active_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'dispproveMembers':
          data['rows'][row]['active_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'other':
          data['rows'][row]['other_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
      }
      this.setState(prevState => ({
        ...prevState,
        verifyEditTable: {
          ...prevState.verifyEditTable,
          [tab]: data,
        },
      }));
    } else if (this.state.verifyPageType === 'vd') {
      data = this.state.verifyEditTable[tab];

      data['rows'][row]['validation_status']['action'] = action;
      switch (tab) {
        case 'forValidation':
          data['rows'][row]['for_validation_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'terminatedMembers':
          data['rows'][row]['terminated_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'supportings':
          data['rows'][row]['supporting_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
        case 'activeMembers':
          data['rows'][row]['active_members_action'] = action;
          data['rows'][row]['user_remarks'] = remarks;
          break;
      }

      this.setState(prevState => ({
        ...prevState,
        verifyEditTable: {
          ...prevState.verifyEditTable,
          [tab]: data,
        },
      }));
    }

    this.setFieldValue('isActionRemarksModalOpen', false);
  };

  closeActionRemarksModal = (row: number) => {
    if(this.state.tab === 0 && this.state.verifyPageType === 'default') {
      // console.log('closeactionremarksmodal0', this.state.tab)
      let data = this.state.partialtable;

      console.log('closeactionremarksmodal partial state', data)
    } else if(this.state.tab === 1 && this.state.verifyPageType === 'default') {
      let data = this.state.incompletetable;

    } else if (this.state.tab === 2 && this.state.verifyPageType === 'default') {
      let data = this.state.conflicttable;
      this.setFieldValue('conflicttable', data);
    } else if (
      this.state.tab === 3 &&
      this.state.verifyPageType === 'default'
    ) {
      let data = this.state.validationtable;
      this.setFieldValue('validationtable', data);
    } else if (
      this.state.tab === 4 &&
      this.state.verifyPageType === 'default'
    ) {
      let data = this.state.othervalidationtable;

      console.log('closeactionremarksmodal partial state', data)
    } else if (
      this.state.tab === 5 &&
      this.state.verifyPageType === 'default'
    ) {
      let data = this.state.disapprovetable;
      this.setFieldValue('disapprovetable', data);
    } else if( 
      this.state.tab === 6 &&
      this.state.verifyPageType === 'default') {


    } else if(
      this.state.tab === 7 &&
      this.state.verifyPageType === 'default'
    ) {
    }
    this.setFieldValue('isActionRemarksModalOpen', false);
  };

  closeApproveDependentModal = (row: number) => {
    const data = this.state.validationtable;
    data['rows'][row]['member_principal'] = '';
    console.log("Check Close Approve: ",row);
    const targetMembers = this.state.member_upload_data['validated_members'];
    const targetMemberIndex = targetMembers.findIndex(member => {
      return (
        member['temp_id'] === data['rows'][row]['member_details']['temp_id']
      );
    });
    targetMembers[targetMemberIndex]['principal_temp_id'] = undefined;
    this.setState({
      member_upload_data: {
        ...this.state.member_upload_data,
        validated_members: targetMembers,
      },
    });

    data['rows'][row]['user_remarks'] = '';
    if (
      data['rows'] &&
      data['rows'][row] &&
      data['rows'][row]['member_details'] &&
      data['rows'][row]['member_details']['_id']
    ) {
      let obj_id = data['rows'][row]['member_details']['_id'];
      let newRemarksHolder =
        this.state.remarksHolder.length > 1
          ? this.state.remarksHolder.filter(rhData => rhData['_id'] !== obj_id)
          : [];
      this.setState({
        remarksHolder: newRemarksHolder,
      });
    }

    this.setFieldValue('validationtable', data);
    this.setState({
      approveDependent: {
        isModalOpen: false,
        possiblePrincipals: undefined,
      },
    });
  };

  saveApproveDependentModal = (
    row: number,
    selectedTempId: string,
    selectedMemberName: string,
    action: ActionData,
    remarks: string,
  ) => {
    const data = this.state.validationtable;
    // console.log('DEBUG saveApproveDependentModal()', data)
    if (
      data['rows'] &&
      data['rows'][row] &&
      data['rows'][row]['member_details'] &&
      data['rows'][row]['member_details']['_id']
    ) {
      let obj_id = data['rows'][row]['member_details']['_id'];
      let val_status = undefined;
      if (
        data['rows'][row]['member_details']['validation_status'] &&
        data['rows'][row]['member_details']['validation_status']['status']
      ) {
        val_status =
          data['rows'][row]['member_details']['validation_status']['status'];
      }
      this.addToActionHolder(obj_id, action, val_status);
      if (remarks.trim() !== '') {
        this.addToRemarksHolder(obj_id, remarks);
      }
    }

    // if (data['rows'] && data['rows'][row] && data['rows'][row]['member_details'] && data['rows'][row]['member_details']['temp_id']) {
    //   console.log('target member temp_id', data['rows'][row]['member_details']['temp_id'])
    // }
    
    data['rows'][row]['validation_action'] = action.toLowerCase();
    data['rows'][row]['user_remarks'] = remarks;

    const targetMembers = this.state.member_upload_data['members'];
    console.log("Check Target Members: ",targetMembers);
    const targetMemberIndex = targetMembers.findIndex(member => {
      return (
        member['temp_id'] === data['rows'][row]['member_details']['temp_id']
      );
    });

    const targetValMembers = this.state.member_upload_data['validated_members'];
    const targetValMemberIndex = targetValMembers.findIndex(member => {
      return (
        member['temp_id'] === data['rows'][row]['member_details']['temp_id']
      );
    });
    

    targetMembers[targetMemberIndex]['principal_temp_id'] = selectedTempId;
    targetMembers[targetMemberIndex]['principal_name'] = selectedMemberName;
    console.log('target member data', targetMembers[targetMemberIndex]);

    targetValMembers[targetValMemberIndex][
      'principal_temp_id'
    ] = selectedTempId;
    targetValMembers[targetValMemberIndex][
      'principal_name'
    ] = selectedMemberName;
    console.log(
      'target val member data',
      targetValMembers[targetValMemberIndex],
    );
    console.log("Check Target Members: ",targetMembers,targetMembers);
    this.setState(
      {
        member_upload_data: {
          ...this.state.member_upload_data,
          members: targetMembers,
          validated_members: targetValMembers,
        },
      },
      () => {
        console.log(
          'new member_upload_data val',
          this.state.member_upload_data,
        );
        const getPrincipalName = () => {
          const principalMember = this.state.member_upload_data.validated_members.find(
            member => member.temp_id === selectedTempId,
          );
          if (principalMember) {
            let principalNameVal: string = `${principalMember.first_name} ${
              principalMember.middle_name
            } ${principalMember.last_name}${
              principalMember.suffix &&
              principalMember.suffix !== null &&
              principalMember.suffix.trim() !== ''
                ? ', ' + principalMember.suffix
                : ''
            }`;

            return principalNameVal.trim();
          }
          return '';
        };

        data['rows'][row]['member_principal'] = getPrincipalName();
      },
    );
    // data['rows'][row]['member_details']['principal_temp_id'] = selectedTempId;
    // console.log('test', row, targetValMembers);

    this.setFieldValue('validationtable', data);
    this.setState({
      approveDependent: {
        isModalOpen: false,
        possiblePrincipals: undefined,
        row: -1,
      },
    });
  };

  saveDisapproveModal = (row: number, remarks: string, userRemarks?:string) => {
    console.log('saveDisapprovedModal()')
    if (this.state.tab === 1) {
      const data = this.state.incompletetable;
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        let obj_id = data['rows'][row]['member_details']['_id'];
        let val_status = undefined;
        if (
          data['rows'][row]['member_details']['validation_status'] &&
          data['rows'][row]['member_details']['validation_status']['status']
        ) {
          val_status =
            data['rows'][row]['member_details']['validation_status']['status'];
        }
        this.addToActionHolder(obj_id, 'disapprove', val_status);
        if (remarks.trim() !== '') {
          this.addToRemarksHolder(obj_id, remarks);
        }
      }

      data['rows'][row]['user_remarks'] = userRemarks;
      this.setFieldValue('incompletetable', data);
    } else if (this.state.tab === 4) {
      const data = this.state.othervalidationtable;
     
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        let obj_id = data['rows'][row]['member_details']['_id'];
        let val_status = undefined;
        if (
          data['rows'][row]['member_details']['validation_status'] &&
          data['rows'][row]['member_details']['validation_status']['status']
        ) {
          // console.log('DATTAAA othervalidationtable1', data['rows'][row])
          val_status =
            data['rows'][row]['member_details']['validation_status']['status'];
        }
        this.addToActionHolder(obj_id, 'disapprove', val_status);
        if (remarks.trim() !== '') {
          this.addToRemarksHolder(obj_id, remarks);
        }
      }
      
      data['rows'][row]['user_remarks'] = userRemarks;
      this.setFieldValue('othervalidationtable', data);
    } else if (this.state.tab === 5) {
      const data = this.state.supportingtable;
      if (
        data['rows'] &&
        data['rows'][row] &&
        data['rows'][row]['member_details'] &&
        data['rows'][row]['member_details']['_id']
      ) {
        let obj_id = data['rows'][row]['member_details']['_id'];
        let val_status = undefined;
        if (
          data['rows'][row]['member_details']['validation_status'] &&
          data['rows'][row]['member_details']['validation_status']['status']
        ) {
          val_status =
            data['rows'][row]['member_details']['validation_status']['status'];
        }
        this.addToActionHolder(obj_id, 'disapprove', val_status);
        if (remarks.trim() !== '') {
          this.addToRemarksHolder(obj_id, remarks);
        }
      }

      data['rows'][row]['user_remarks'] = userRemarks;
      this.setFieldValue('supportingtable', data);
    }
    this.setFieldValue('disapproveModalOpen', false);
  };

  saveForConfirmationModal = (row: number, remarks: string, userRemarks?:any) => {
     console.log('saveForConfirmationModal1()')
      
    if (this.state.verifyPageType === 'edit') {
      if (this.state.tab === 0) {
      const data = this.state.conflicttable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          let obj_id = data['rows'][row]['member_details']['_id'];
          if (remarks.trim() !== '') {
            this.addToRemarksHolder(obj_id, remarks);
          }
        }
        data['rows'][row]['user_remarks'] = remarks;
        this.setFieldValue('conflicttable', data);
      }
    } else {
      if (this.state.tab === 1) {
        const data = this.state.incompletetable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          // let obj_id = data['rows'][row]['member_details']['_id'];
          // if (remarks.trim() !== '') {
          //   this.addToRemarksHolder(obj_id, remarks);
          // }
        }
        data['rows'][row]['user_remarks'] = userRemarks;        
        this.setFieldValue('incompletetable', data);
       
      } else if (this.state.tab === 2) {
        const data = this.state.conflicttable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          // let obj_id = data['rows'][row]['member_details']['_id'];
          // if (remarks.trim() !== '') {
          //   this.addToRemarksHolder(obj_id, remarks);
          // }
        }
        data['rows'][row]['user_remarks'] = userRemarks;
        this.setFieldValue('conflicttable', data);
      } else if (this.state.tab === 3) {
        const data = this.state.validationtable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          // let obj_id = data['rows'][row]['member_details']['_id'];
          // if (remarks.trim() !== '') {
          //   this.addToRemarksHolder(obj_id, remarks);
          // }
        }
        data['rows'][row]['user_remarks'] = userRemarks;        
        this.setFieldValue('validationtable', data);
      } else if (this.state.tab === 4) {
        const data = this.state.othervalidationtable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          // let obj_id = data['rows'][row]['member_details']['_id'];
          // if (remarks.trim() !== '') {
          //   this.addToRemarksHolder(obj_id, remarks);
          // }
        }
        data['rows'][row]['user_remarks'] = userRemarks;        
        this.setFieldValue('othervalidationtable', data);
      } else if (this.state.tab === 5) {
        const data = this.state.supportingtable;
        if (
          data['rows'] &&
          data['rows'][row] &&
          data['rows'][row]['member_details'] &&
          data['rows'][row]['member_details']['_id']
        ) {
          // let obj_id = data['rows'][row]['member_details']['_id'];
          // if (remarks.trim() !== '') {
          //   this.addToRemarksHolder(obj_id, remarks);
          // }
        }
        data['rows'][row]['user_remarks'] = userRemarks;        
        this.setFieldValue('supportingtable', data);
      }
    }

    //  else if (this.state.tab === 4) {
    //   const data = this.state.othervalidationtable;
    //   data['rows'][row]['user_remarks'] = remarks;
    //   this.setFieldValue('othervalidationtable', data);
    // }
    this.setFieldValue('forConfirmationModalOpen', false);
  };

  handleClickRow = (row: number, table: string, rowData?: any) => {
    console.log('handleClickRow()',);
    if (this.state.verifyPageType === 'default') {
      if (table === 'partialtable') {
        let data = this.state.partialtable;

        const temp = this.state.partial_items;
        if (temp && !isNil(temp[row])) {
          temp[row]['status'] = rowData.action;
          this.setFieldValue('partial_items', temp);
        }

        this.setFieldValue('partialRow', row);
        this.openPartialModal();
        this.setFieldValue(table, data);
      } else if (table === 'supportingtable') {
        let data = this.state.supportingtable;

        const temp = this.state.supportingtable;
        if (temp && !isNil(temp[row])) {
          temp[row]['status'] = rowData.action;
          this.setFieldValue('supportingTable 11', temp);
        }

        this.setFieldValue('supportingRow', row);
        this.openSupportingModal();
        this.setFieldValue(table, data);
      } else if (table === 'validationtable') {
        let data = this.state.validationtable;

        if (data && !isNil(data[row])) {
          data[row]['status'] = rowData.action;
          this.setFieldValue('validationtable', data);
        }

        this.setFieldValue('validationRow', row);
        this.setFieldValue(table, data);
      } else if (table === 'disapprovetable') {
        let data = this.state.disapprovetable;
        let rowData = data['rows'][row];
        let action = 'disapprove';
        // SEPT 08, 2020: FIX FOR MS731 (Untoggle selected action)
        data['rows'][row]['disapprove_action'] = action;
        //
        if (
          rowData &&
          rowData['member_details'] &&
          rowData['member_details']['_id']
        ) {
          console.log(`select radio ${table}`, rowData);
          let obj_id = rowData['member_details']['_id'];
          let val_status = undefined;
          if (
            rowData['member_details']['validation_status'] &&
            rowData['member_details']['validation_status']['status']
          ) {
            val_status =
              rowData['member_details']['validation_status']['status'];
          }
          this.addToActionHolder(obj_id, action, val_status);
        }

        this.setFieldValue(table, data);

      }
    } else if (this.state.verifyPageType === 'edit') {

    } else if (this.state.verifyPageType === 'vd') {

    }
  };

  resetFilters = () => {
    this.setState({
      allFilter: [],
      approvedFilter: [],
      disapprovedFilter: [],
      conflictFilter: [],
      incompleteFilter: [],
      partialFilter: [],
      validationFilter: [],
    });

    this.setState({
      verifyEditFilters: {
        conflictOnData: [],
        forValidation: [],
        terminatedMembers: [],
        activeMembers: [],
        supportings: [],
      },
    });
  };

  handleFilterChange = (
    stateFilterName:
      | 'partialFilter'
      | 'incompleteFilter'
      | 'approveFilter'
      | 'conflictFilter'
      | 'validationFilter'
      | 'othervalidationFilter'
      | 'supportingFilter'
      | 'disapprovedFilter'
      | 'approvedFilter'
      | 'allFilter'
      | 'conflictOnData'
      | 'forValidation'
      | 'terminatedMembers'
      | 'activeMembers'
      | 'approveMembers'
      | 'disapproveMembers'
      | 'supportings'
      | 'other'
  ) => (columnFilters: any[]) => {
    const { allData, verifyEditAllData } = this.state;
    let dataHolder;

    if (this.state.verifyPageType === 'default') {
      dataHolder = allData;
    } else if (this.state.verifyPageType === 'edit') {
      dataHolder = verifyEditAllData;
    } else if (this.state.verifyPageType === 'vd') {
      dataHolder = verifyEditAllData;
    }

    const getFilteredData = (tableRows, filters) => {
      if (
        !isNil(tableRows) &&
        tableRows.length > 0 &&
        !isNil(filters) &&
        filters.length > 0
      ) {
        return filter(tableRows, tableRow => {
          return every(filters, filter => {
            const tableRowValue = get(tableRow, filter.columnName, '');

            return includes(toLower(tableRowValue), toLower(filter.value));
          });
        });
      }
      return tableRows;
    };

    const getFilteredTable = tableName => {
      const table = cloneDeep(get(dataHolder, tableName, {}));
      const tableRows = get(table, 'rows', []);

      table.rows = getFilteredData(tableRows, columnFilters);

      return table;
    };

    switch (stateFilterName) {
      case 'partialFilter':
        this.setState(
          {
            partialFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  partialtable: getFilteredTable('partial'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'incompleteFilter':
        this.setState(
          {
            incompleteFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  incompletetable: getFilteredTable('incomplete'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'conflictFilter':
        this.setState(
          {
            conflictFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  conflicttable: getFilteredTable('conflict'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'validationFilter':
        this.setState(
          {
            validationFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  validationtable: getFilteredTable('validation'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'othervalidationFilter':
        this.setState(
          {
            othervalidationFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  othervalidationtable: getFilteredTable('othervalidation'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'supportingFilter':
        this.setState(
          {
            supportingFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  supportingtable: getFilteredTable('supporting'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'disapprovedFilter':
        this.setState(
          {
            disapprovedFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  disapprovetable: getFilteredTable('disapprove'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'approvedFilter':
        this.setState(
          {
            approvedFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  approvedtable: getFilteredTable('approved'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'allFilter':
        this.setState(
          {
            allFilter: columnFilters,
          },
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState({
                  alltable: getFilteredTable('all'),
                });
              }, 500),
            });
          },
        );
        break;
      case 'conflictOnData':
      case 'forValidation':
      case 'supportings':
      case 'other':
      case 'terminatedMembers':
      case 'activeMembers':
        console.log('verify edit active filter', stateFilterName);
        this.setState(
          prevState => ({
            ...prevState,
            verifyEditFilters: {
              ...prevState.verifyEditFilters,
              [stateFilterName]: columnFilters,
            },
          }),
          () => {
            clearTimeout(this.state.filterTimeout);
            this.setState({
              filterTimeout: setTimeout(() => {
                this.setState(prevState => ({
                  ...prevState,
                  verifyEditTable: {
                    ...prevState.verifyEditTable,
                    [stateFilterName]: getFilteredTable(stateFilterName),
                  },
                }));
              }, 500),
            });
          },
        );
        break;
        case 'approveMembers':
        case 'disapproveMembers':
          console.log('verify edit active filter', stateFilterName);
          this.setState(
            prevState => ({
              ...prevState,
              verifyEditFilters: {
                ...prevState.verifyEditFilters,
                [stateFilterName]: columnFilters,
              },
            }),
            () => {
              clearTimeout(this.state.filterTimeout);
              this.setState({
                filterTimeout: setTimeout(() => {
                  this.setState(prevState => ({
                    ...prevState,
                    verifyEditTable: {
                      ...prevState.verifyEditTable,
                      [stateFilterName]: getFilteredTable(stateFilterName),
                    },
                  }));
                }, 500),
              });
            },
          );
          break;
          
    }
  };

  handleSortingChange = (tableName: string) => (sorting: any[]) => {

    let isSortingDate = false;
    if (sorting[0] && sorting[0].columnName) {
      let findDateArr = sorting[0].columnName.split('_');
      isSortingDate = findDateArr.includes('date');
    }

    console.log('isSortingDate', isSortingDate);

    if (isSortingDate) {
      this.setState({
        ...this.state,
        dateSorting: true,
      });
    }

    let data;
    let sortDirection = '';
    let colName = '';

    if (this.state.verifyPageType === 'default') {
      data = Object.assign({}, this.state.defaultTableCopy[tableName]);
    } else if (this.state.verifyPageType === 'edit') {
      data = Object.assign({}, this.state.verifyEditTableCopy[tableName]);
    } else if (this.state.verifyPageType === 'vd') {
      data = Object.assign({}, this.state.verifyEditTableCopy[tableName]);
    }

    sorting.forEach(i => {
      sortDirection = i.direction;
      colName = i.columnName;
    });

    if (sortDirection === 'asc') {
      if (isSortingDate === true) {
        data.rows.sort((a, b) => {
          let aData = a[colName];
          let bData = b[colName];
          return moment(aData).isAfter(bData, 'day') ? 1 : -1;
        });
      } else {
        data.rows.sort((a, b) => (a[colName] > b[colName] ? 1 : -1));
      }
    } else {
      if (isSortingDate === true) {
        data.rows.sort((a, b) => {
          let aData = a[colName];
          let bData = b[colName];
          return moment(aData).isAfter(bData, 'day') ? -1 : 1;
        });
      } else {
        data.rows.sort((a, b) => (a[colName] > b[colName] ? -1 : 1));
      }
    }

    console.log('sorted rows', data.rows);
    console.log('sorted data', data);

    if (this.state.verifyPageType === 'default') {
      this.setFieldValue(tableName, data);
    } else if (this.state.verifyPageType === 'edit') {
      this.setState(state => ({
        ...state,
        verifyEditTable: {
          ...state.verifyEditTable,
          [tableName]: data,
        },
      }));
    } else if (this.state.verifyPageType === 'vd') {
      this.setState(state => ({
        ...state,
        verifyEditTable: {
          ...state.verifyEditTable,
          [tableName]: data,
        },
      }));
    }
  };
//verify choose action -approved/dis/forconfirm/edit
  handleSelectedRadio = (
    row: number,
    value: string,
    tableName: string,
    rowData?: any,
    pageType?: any,
    prefix?: any,
  ) => {
    console.log('handleSelectedRadio', pageType);
    console.log(prefix);
    let data = {};
    console.log("Check handle selected radio: ",tableName,' - ',row,' - ',value)
    if (this.state.verifyPageType === 'default') {
      const memberFileUploadDetails = {
        pageType: 'default',
        tableName: tableName,
        prefix: prefix,
        rowData: rowData,
      };

    this.setState({
          memberFileUploadDetails: memberFileUploadDetails
        });
      switch (tableName) {
        case 'partialtable':
          if (
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          data = this.state.partialtable;
          data['rows'][row]['action'] = value;

          const temp = this.state.partial_items;
          if (temp && !isNil(temp[row])) {
            temp[row]['status'] = value;
            this.setFieldValue('partial_items', temp);
          }
          this.setFieldValue('partialRow', row);
          this.openPartialModal();
          break;
        case 'incompletetable':
          if (
            value === 'for confirmation' &&
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          this.setFieldValue('row', row)
          data = this.state.incompletetable;
          if (
            value === 'approve' &&
            data['rows'][row]['incomplete_action'] !== 'approve'
          ) {
            this.openApproveModal(data['rows'][row]['files']);
          } else if (
            value === 'disapprove' &&
            data['rows'][row]['incomplete_action'] !== 'disapprove'
          ) {
            this.openDisapproveModal(data['rows'][row]['user_remarks']);
          } else if (
            value === 'for confirmation' &&
            data['rows'][row]['incomplete_action'] !== 'for confirmation'
          ) {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
          }

          // if (value !== 'disapprove' && value !== 'for confirmation') {
          //   delete data['rows'][row]['remarks'];
          // }

          data['rows'][row]['incomplete_action'] = value;
          break;
        case 'conflicttable':
          this.setFieldValue('row', row);
          if (
            value === 'for confirmation' &&
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          data = this.state.conflicttable;
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: 'Conflict',
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              // action: capitalize(value),
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else if (value === 'for confirmation') {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
            data['rows'][row]['conflict_data_action'] = value;
          } else {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
          }
          break;
        case 'validationtable':
          this.setFieldValue('row', row);
          console.log('Validation Table/Tab');
          if (
            value === 'for confirmation' &&
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          data = this.state.validationtable;

          if (
            rowData &&
            rowData['remarks'] &&
            (rowData['remarks']
              .toLowerCase()
              .includes('no principal member found') ||
              rowData['remarks']
                .toLowerCase()
                .includes('multiple principal members found')) &&
            (value === 'approve' || value === 'disapprove')
          ) {
            /*
              MS465 -> show only 1 possible principal if they have the same name
              to avoid duplicates
            */
            let possiblePrincipals: any[] = [];
            rowData['member_details']['possible_principals'].forEach(
              principal => {
                // APPLIED ON JUNE 29, 2020: REMOVED VALIDATION OF DUPLICATE PRINCIPALS FOR MS613 & MS614
                // const fName = principal.first_name;
                // const lName = principal.last_name;

                // if (possiblePrincipals.findIndex(pData => pData.first_name === fName &&
                //   pData.last_name === lName) === -1) {
                possiblePrincipals.push(principal);
                // }
              },
            );

            this.setState({
              approveDependent: {
                isModalOpen: true,
                possiblePrincipals:
                  // rowData['member_details']['possible_principals'],
                  possiblePrincipals,
                action: value === 'approve' ? 'Approve' : 'Disapprove',
                row: row,
              },
            });
          } else {
            if (value === 'approve' || value === 'disapprove') {
              this.setFieldValue('actionRemarksModalProps', {
                row: row,
                tab: 'Validation',
                action: value === 'approve' ? 'Approve' : 'Disapprove',
                actionValue: value,
              });
              this.setFieldValue('isActionRemarksModalOpen', true);
            } else {
              this.openForConfirmationModal(data['rows'][row]['user_remarks']);
              data['rows'][row]['validation_action'] = value;
              if (value !== 'approve') {
                data['rows'][row]['member_principal'] = '';
                const targetMembers = this.state.member_upload_data[
                  'validated_members'
                ];
                const targetMemberIndex = targetMembers.findIndex(member => {
                  return (
                    member['temp_id'] ===
                    data['rows'][row]['member_details']['temp_id']
                  );
                });
                targetMembers[targetMemberIndex][
                  'principal_temp_id'
                ] = undefined;
                this.setState({
                  member_upload_data: {
                    ...this.state.member_upload_data,
                    validated_members: targetMembers,
                  },
                });
                this.setFieldValue('validationtable', data);
              }
            }
          }
          this.setFieldValue('validationRow', row);
          break;
        case 'othervalidationtable':
          console.log('OtherValidation case/tab');
          if (
            value === 'for confirmation' &&
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          this.setFieldValue('row', row);

          data = this.state.othervalidationtable;
          // if (value === 'approve' || value === 'disapprove') {
          //   this.setFieldValue('actionRemarksModalProps', {
          //     row: row,
          //     tab: 'Other Validation',
          //     action: value === 'approve' ? 'Approve' : 'Disapprove',
          //     actionValue: value,
          //   });
          //   this.setFieldValue('isActionRemarksModalOpen', true);
          // } else {
          //   data['rows'[row]['other_validation_action'] = value;
          // }
          if (
            value === 'approve' &&
            data['rows'][row]['other_validation_action'] !== 'approve'
          ) {
            const memberFileUploadDetails = {
              pageType: pageType,
              tableName: tableName,
              prefix: prefix,
              rowData: rowData,
              temp_id: rowData.member_details.temp_id,
            };


            this.setFieldValue(
              'memberFileUploadDetails',
              memberFileUploadDetails,
            );
            this.setFieldValue('row', row);
    
            this.setFieldValue('memberFilesModalOpen', true);
            // this.openApproveModal();
          } else if (
            value === 'disapprove' &&
            data['rows'][row]['other_validation_action'] !== 'disapprove'
          ) {
            this.openDisapproveModal(data['rows'][row]['user_remarks']);
          } else if (
            value === 'for confirmation' &&
            data['rows'][row]['other_validation_action'] !== 'for confirmation'
          ) {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
          }

          // if (value !== 'approve') {
          //   delete data['rows'][row]['files'];
          // }

          // if (value !== 'disapprove') {
          //   delete data['rows'][row]['remarks'];
          // }

          data['rows'][row]['other_validation_action'] = value;
          break;
        case 'supportingtable':
          // console.log('supporting 4', supporting_items)
          if (
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }
          this.setFieldValue('row', row);
          data = this.state.supportingtable;
          if (
            value === 'approve' &&
            data['rows'][row]['supporting_data_action'] !== 'approve'
          ) {
            this.openApproveModal(data['rows'][row]['files']);
          } else if (
            value === 'disapprove' &&
            data['rows'][row]['supporting_data_action'] !== 'disapprove'
          ) {
            this.openDisapproveModal(data['rows'][row]['user_remarks']);
          } else if (
            value === 'for confirmation' &&
            data['rows'][row]['supporting_data_action'] !== 'for confirmation'
          ) {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
          }
          data['rows'][row]['action'] = value;
          data['rows'][row]['supporting_data_action'] = value;
          const temp8 = this.state.supportingtable;
          if (temp8 && !isNil(temp8[row])) {
            temp8[row]['status'] = value;
            this.setFieldValue('supportingtable', temp8);
          }
          this.setFieldValue('supportingRow', row);
          this.openSupportingModal();
          break;
        case 'disapprovetable':
          console.log('handleSelectedRadio val', value);
          this.setFieldValue('row', row);
          if (
            value === 'for confirmation' &&
            rowData &&
            rowData['member_details'] &&
            rowData['member_details']['_id']
          ) {
            console.log(`select radio ${tableName}`, rowData);
            let obj_id = rowData['member_details']['_id'];
            let val_status = undefined;
            if (
              rowData['member_details']['validation_status'] &&
              rowData['member_details']['validation_status']['status']
            ) {
              val_status =
                rowData['member_details']['validation_status']['status'];
            }
            this.addToActionHolder(obj_id, value, val_status);
          }

          data = this.state.disapprovetable;

          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: 'Disapproved',
              action: capitalize(value),
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            this.openForConfirmationModal(data['rows'][row]['user_remarks']);
            data['rows'][row]['disapprove_action'] = value;
          }

          break;
      }

      this.setFieldValue(tableName, data);
    } else if (this.state.verifyPageType === 'edit') {
      const memberFileUploadDetails = {
        pageType: 'edit',
        tableName: tableName,
        prefix: prefix,
        rowData: rowData,
      };

      this.setState({
        memberFileUploadDetails: memberFileUploadDetails
      });
      data = this.state.verifyEditTable[tableName];
      console.log("Check Rows: ",row,value);
      switch (tableName) {
        case 'conflictOnData':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else if (value === 'for confirmation') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: "For Confirmation",
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
            data['rows'][row]['conflict_data_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          } else {
            data['rows'][row]['conflict_data_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'forValidation':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else if (value === 'for confirmation') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: "For Confirmation",
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
            data['rows'][row]['for_validation_action'] = value;
            data['rows'][row]['validation_status']['action'] = value
          } else {
            data['rows'][row]['for_validation_action'] = value;
            data['rows'][row]['validation_status']['action'] = value
          }
          break;
        case 'supportings':
            console.log('selected radio for ' + tableName)
            if (value === 'approve' || value === 'disapprove') {
              this.setFieldValue('actionRemarksModalProps', {
                row: row,
                tab: tableName,
                action: value === 'approve' ? 'Approve' : 'Disapprove',
                actionValue: value,
              });
              this.setFieldValue('isActionRemarksModalOpen', true);
            } else if (value === 'for confirmation') {
              this.setFieldValue('actionRemarksModalProps', {
                row: row,
                tab: tableName,
                action: "For Confirmation",
                actionValue: value,
              });
              this.setFieldValue('isActionRemarksModalOpen', true);
              data['rows'][row]['supporting_action'] = value;
              data['rows'][row]['validation_status']['action'] = value;
            } else {
              data['rows'][row]['supporting_action'] = value;
              data['rows'][row]['validation_status']['action'] = value;
            }
          break;
        case 'other':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['other_members_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'terminatedMembers':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['terminated_members_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'activeMembers':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['active_members_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
      }

      this.setState(prevState => ({
        ...prevState,
        verifyEditTable: {
          ...prevState.verifyEditTable,
          [tableName]: data,
        },
      }));
    } else if (this.state.verifyPageType === 'vd') {
      const memberFileUploadDetails = {
        pageType: 'vd',
        tableName: tableName,
        prefix: prefix,
        rowData: rowData,
      };

      this.setState({
        memberFileUploadDetails: memberFileUploadDetails
      });
      data = this.state.verifyEditTable[tableName];
      switch (tableName) {
        case 'forValidation':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['for_validation_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'terminatedMembers':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['terminated_members_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'activeMembers':
          console.log('selected radio for ' + tableName);
          if (value === 'approve' || value === 'disapprove') {
            this.setFieldValue('actionRemarksModalProps', {
              row: row,
              tab: tableName,
              action: value === 'approve' ? 'Approve' : 'Disapprove',
              actionValue: value,
            });
            this.setFieldValue('isActionRemarksModalOpen', true);
          } else {
            data['rows'][row]['active_members_action'] = value;
            data['rows'][row]['validation_status']['action'] = value;
          }
          break;
        case 'supportings':
            console.log('selected radio for ' + tableName)
            if (value === 'approve' || value === 'disapprove') {
              this.setFieldValue('actionRemarksModalProps', {
                row: row,
                tab: tableName,
                action: value === 'approve' ? 'Approve' : 'Disapprove',
                actionValue: value,
              });
              this.setFieldValue('isActionRemarksModalOpen', true);
            } else {
              data['rows'][row]['supporting_action'] = value;
              data['rows'][row]['validation_status']['action'] = value;
            }
           break;
      }

      this.setState(prevState => ({
        ...prevState,
        verifyEditTable: {
          ...prevState.verifyEditTable,
          [tableName]: data,
        },
      }));
    }
  };

  

  handleBack = async () => {
    //for user remarks
    console.log('handleBack()', )
   if (this.state.verifyPageType === 'edit') {
    this.setState({
      loading_state: true,
      
    });
      console.log('PAGETYPE IS EDIT');
      await this.checkTicketClosed('Verify Edit');
      API.getVerifyEditMemberUpload(this.props.match.params.member_upload_id)
        .then(response => {
          console.log('verify edit res', response);
          if (response && response.error === undefined) {
            API.getClientHMOInfo(this.props.match.params.client_id).then(
              hmoclient => {
                console.log('verify edit client', hmoclient);
                let ccId = '';
                let ccNo = '';
                let ccName = '';
                let contractName = '';
                let contractId = '';
                let memberDataFields: any[] = [];
                let benefitPlanTreeArr: any[] = [];

                if (hmoclient && hmoclient.error === undefined) {
                  ccId =
                    hmoclient.client && hmoclient.client._id
                      ? hmoclient.client._id
                      : '';
                  memberDataFields =
                    hmoclient.client && hmoclient.client.member_data_fields
                      ? hmoclient.client.member_data_fields
                      : [];
                  ccNo =
                    hmoclient.client && hmoclient.client.corporate_account_no
                      ? hmoclient.client.corporate_account_no
                      : '';
                  ccName =
                    hmoclient.client && hmoclient.client.registered_name
                      ? hmoclient.client.registered_name
                      : '';
                  if (
                    hmoclient.benefit_plan_tree &&
                    hmoclient.benefit_plan_tree.length > 0
                  ) {
                    console.log('=========', contractName);
                    console.log('contractId', contractId);
                    console.log(
                      'benefit_plan_tree val 1',
                      hmoclient.benefit_plan_tree,
                    );
                    benefitPlanTreeArr = hmoclient.benefit_plan_tree;
                    hmoclient.benefit_plan_tree.forEach(item => {
                      if (item.level === 1 && item.code === 'MCT') {
                        console.log('targeted item of benefit plan tree', item);
                        let period = '';
                        if (
                          item.custom_metadata &&
                          item.custom_metadata.coverage_start_date &&
                          item.custom_metadata.coverage_end_date
                        ) {
                          let coverage_end_date = moment(
                            this.endDateCorrector(
                              item.custom_metadata.coverage_end_date,
                              item.custom_metadata.additional_info,
                            ),
                          )
                            .subtract(1, 'days')
                            .format('MMM D, YYYY');
                          period =
                            ' ' +
                            GlobalFunction.toCommonDateString(
                              new Date(
                                item.custom_metadata.coverage_start_date,
                              ),
                            ) +
                            ' - ' +
                            GlobalFunction.toCommonDateString(
                              new Date(coverage_end_date),
                            );

                          console.log(period);
                        }
                        contractName = hmoclient.client['contract_id'] + period
                        // contractName =
                        //   'Contract ' +
                        //   (item.version ? item.version : '1') +
                        //   period;
                        contractId = item.principal_temp_id; //item.member_id
                      }
                    });
                  }
                }

                let breadcrumbs_items: any = [
                  {
                    label: 'DASHBOARD',
                    url: '#/membership',
                  },
                  {
                    label: 'VERIFY EDIT',
                    url: '',
                  },
                ];
                if (hmoclient.client && hmoclient.client.registered_name) {
                  breadcrumbs_items.push({
                    label: hmoclient.client.registered_name,
                    url: '',
                  });
                }

                if (this.state.ocpPath === 've-ocp') {
                  this.setState(prevState => ({
                    ...prevState,
                    ocpPayload: {
                      ...prevState.ocpPayload,
                      endorsement_date: '',
                      batch_no: '',
                      client_id: ccId,
                      corporate_account_no: ccNo,
                      corporate_name: ccName,
                      contract: contractName,
                      sender: '',
                      sender_email: '',
                      encode_method: '',
                      encoded_date: '',
                      encoded_by: '',
                    },
                  }));
                }

                this.setState({
                  clientData: hmoclient,
                  memberDataFields: memberDataFields,
                  check_internet_flag: false,
                  plan_types: hmoclient.plan_types,
                  corporateAccountNo: ccNo,
                  corporateAccountName: ccName,
                  contractName: contractName,
                  breadcrumbs: breadcrumbs_items,
                  loading_state: false,
                  member_edit_data: response,
                  benefitPlanTree: benefitPlanTreeArr,
                });
                if (response !== undefined && response !== null) {
             
                  this.getValidatedMembers(response);
                }
              },
            );
          } else {
            this.setState({
              loading_state: false,
              member_edit_data: [],
            });

            if (response && response.error) {
              this.showUnauthorizedError(response.error);
            }
          }
        })
        .catch(error => {
          this.setState({
            loading_state: false,
            
          });
          console.log(error);
        });
     }else{
      API.getUploadMember(this.props.match.params.member_upload_id).then(
        response => {
          console.log('getUploadMember', response);
          if (response && response.error === undefined) {
            if (response.client_id) {
              this.setState({
                clientId: response.client_id,
                ticket_id: response.batch_name,
              });
            }
            API.getClientHMOInfo(
              response.client_id ? response.client_id : '',
            ).then(() => {
              if (
                response.validated_members !== undefined &&
                response.validated_members !== null
              ) {
                console.log('this');
                //  response.validated_members = response.validated_members.map(member => {
                //   if (member.member_remarks) {
                //     member.member_remarks = this.dedupeRemarks(member.member_remarks);
                //   }
                //   return member;
                // });
                response.validated_members.map(member => {
                  if (member.member_remarks) {
                    member.member_remarks.map(remarks => {
                      if (remarks.source === 'Verify Member List') {
                        member.validation_status['user_remarks'] = remarks.remarks;
                        // member.validation_status['user_remarks'] =
                        //   remarks.remarks;
                      }
                    });
                  }
                });
  
                this.getInitialApprovedMembers(response.validated_members);
                this.handleLoadTmp();
                this.getValidatedMembers(response.validated_members);
              }
            });
          } else {
            this.setState({
              loading_state: false,
              member_upload_data: {},
            });
  
            if (response && response.error) {
              this.showUnauthorizedError(response.error);
              console.log('Response', response);
              //console.log('Response Error', response.error)
            }
          }
  
          console.log('response1', response);
          setTimeout(() => {
            console.log('no response timeout set');
          }, 5000);
        },
      );
     }
    

    this.handleLoadTmp();
    this.setFieldValue('generateFlag', false);
  };

  handleViewCorrectionsMarkAsDone = (data: any) => {
    console.log('mark as done data', data);
    this.setState({
      isConfirmModalOpen: true,
      modalTitle: 'Confirm Action',
      modalMessage:
        'Are you sure you want to mark this process as done? You can still view the members on this batch by going to the Members or Clients pages.',
      modalCloseText: 'Cancel',
      modalConfirmText: 'Mark as Done',
      next: () => {

        const hasMembersForConfirmation = Array.isArray(data) && data.some(itm => itm.member_status === 'For Validation');

        if (hasMembersForConfirmation) {
          this.setState({
            redirectFrom: {
              verifyEdit: true,
            },
          });
        } else {
          this.routeCase(() => {
            this.setState({
              isModalOpen: true,
              modalTitle: 'Member Correction Successful',
              modalMessage: 'Successfully corrected members.',
              redirectFrom: {
                verifyEdit: true,
              },
            });
          });
        }
      },
    });
  };

  handlePublishToOcp = (origin: string, reportFile: any) => {
    if (origin === 'vm-ocp') {
      //verify member OCP
      if (
        this.state.member_upload_data['validated_members'] &&
        this.state.member_upload_data['validated_members'].length > 0
      ) {
        let verifierUser = this.state.verifierData;
        let validatedMembers = this.state.member_upload_data[
          'validated_members'
        ];

        let currentOcpPayload = { ...this.state.ocpPayload };
        let newOcpPayload = {
          ...currentOcpPayload,
          verified_by: verifierUser['userName'] ? verifierUser['userName'] : '',
          file: reportFile,
          members: validatedMembers,
        };
        console.log('verify member ocp payload', newOcpPayload);

        this.setState({
          check_internet_flag: true,
          loading_state: true,
        });

        // @Blueming
        API.postOcpLogin({
          username: 'membership_user_1',
          password: '1234567890Aa!',
        })
          .then(loginRes => {
            if (loginRes && loginRes.error) {
              console.log('post vm-ocp login err', loginRes.error);
              let errorMsg = loginRes.error.message
                ? loginRes.error.message
                : '';
              this.setState({
                check_internet_flag: false, //
                loading_state: false, //
                isModalOpen: true,
                modalTitle: 'OCP Login Failed',
                modalMessage: errorMsg,
              });
            } else {
              console.log('post vm-ocp login res', loginRes);
              if (
                loginRes &&
                loginRes['auth_data'] &&
                loginRes['auth_data']['data'] &&
                loginRes['auth_data']['data']['token']
              ) {
                let loginToken = loginRes['auth_data']['data']['token'];
                API.postOcpExceptionReport(newOcpPayload, loginToken)
                  .then(response => {
                    if (response && response.error) {
                      let reportErrorMsg = response.error.message
                        ? response.error.message
                        : '';
                      this.setState({
                        check_internet_flag: false,
                        loading_state: false,
                        isModalOpen: true,
                        modalTitle: 'Publish to OCP Failed',
                        modalMessage: reportErrorMsg,
                      });
                      console.log(
                        'post vm-ocp exception report err',
                        reportErrorMsg,
                      );
                    } else {
                      this.setState({
                        check_internet_flag: false,
                        loading_state: false,
                        isModalOpen: true,
                        modalTitle: 'Publish to OCP Successful',
                        modalMessage: 'Successfully published to OCP.',
                      });
                      console.log('post vm-ocp exception report res', response);
                    }
                  })
                  .catch(error => {
                    console.log('post vm-ocp exception report err', error);
                  });
              } else {
                this.setState({
                  check_internet_flag: false, //
                  loading_state: false, //
                  isModalOpen: true,
                  modalTitle: 'OCP Login Failed',
                  modalMessage: 'No user access token.',
                });
              }
            }
          })
          .catch(loginErr => {
            console.log('post vm-ocp login err', loginErr);
          });
      }
    } else if (origin === 've-ocp') {
      //verify edit OCP
      if (this.state.member_edit_data.length > 0) {
        let verifierUser = this.state.verifierData;
        let validatedMembers = this.state.member_edit_data;

        let currentOcpPayload = { ...this.state.ocpPayload };
        let newOcpPayload = {
          ...currentOcpPayload,
          verified_by: verifierUser['userName'] ? verifierUser['userName'] : '',
          file: reportFile,
          members: validatedMembers,
          endorsement_date: moment(
            validatedMembers[0]['ocp_correction_endorsment']['endorsed_date'],
          ).format('YYYY-MM-DD'),
          batch_no: parseInt(validatedMembers[0]['batch_name']),
          sender:
            validatedMembers[0]['ocp_correction_endorsment'][
              'person_who_endorsed'
            ],
          sender_email:
            validatedMembers[0]['ocp_correction_endorsment']['email'],
          encoded_by:
            validatedMembers[0]['ocp_correction_endorsment']['endorsed_by'],
          encoded_date: moment(
            validatedMembers[0]['ocp_correction_endorsment']['endorsed_date'],
          ).format('YYYY-MM-DD'),
          encode_method: 'Upload',
        };
        console.log('verify edit ocp payload', newOcpPayload);

        this.setState({
          check_internet_flag: true,
          loading_state: true,
        });

        API.postOcpLogin({
          username: 'membership_user_1',
          password: '1234567890Aa!',
        })
          .then(loginRes => {
            if (loginRes && loginRes.error) {
              console.log('post ve-ocp login err', loginRes.error);
              let errorMsg = loginRes.error.message
                ? loginRes.error.message
                : '';
              this.setState({
                check_internet_flag: false, //
                loading_state: false, //
                isModalOpen: true,
                modalTitle: 'OCP Login Failed',
                modalMessage: errorMsg,
              });
            } else {
              console.log('post ve-ocp login res', loginRes);
              this.setState({
                check_internet_flag: false, //
                loading_state: false, //
                isModalOpen: true,
                modalTitle: 'Publish to OCP Successful', // Fix for MS979
                modalMessage: 'Successfully published to OCP.',
              });
              // JULY 2, 2020: PREPARED OCP EXCEPTION REPORT API CALL. UNCOMMENT WHEN READY FOR USE.
              if (
                loginRes &&
                loginRes['auth_data'] &&
                loginRes['auth_data']['data'] &&
                loginRes['auth_data']['data']['token']
              ) {
                let loginToken = loginRes['auth_data']['data']['token'];
                API.postOcpExceptionReport(newOcpPayload, loginToken)
                  .then(response => {
                    if (response && response.error) {
                      let reportErrorMsg = response.error.message
                        ? response.error.message
                        : '';
                      this.setState({
                        check_internet_flag: false,
                        loading_state: false,
                      });
                      console.log(
                        'post ve-ocp exception report err',
                        reportErrorMsg,
                      );
                    } else {
                      this.setState({
                        check_internet_flag: false,
                        loading_state: false,
                      });
                      console.log('post ve-ocp exception report res', response);
                    }
                  })
                  .catch(error => {
                    console.log('post ve-ocp exception report err', error);
                  });
              } else {
                this.setState({
                  check_internet_flag: false, //
                  loading_state: false, //
                  isModalOpen: true,
                  modalTitle: 'OCP Login Failed',
                  modalMessage: 'No user access token.',
                });
              }
            }
          })
          .catch(loginErr => {
            console.log('post ve-ocp login err', loginErr);
          });

        // OLD CODE
        // this.setState(prevState => ({
        //   ...prevState,
        //   ocpPayload: {
        //     ...prevState.ocpPayload,
        //     verified_by: verifierUser['userName'] ? verifierUser['userName'] : '',
        //     file: reportFile,
        //     members: validatedMembers,
        //   }
        // }))
      }
    }
  };

  handleSaveTmp = () => {
    const payload: any = {
      user_id: 'verify-member',
      ticket_no: this.props.match.params.ticket_id,
      module: 'Membership',
      process: 'Verify Member',
      timestamp: new Date(),
      data: {
        partial_table: this.state.partialtable,
        incomplete_table: this.state.incompletetable,
        conflict_table: this.state.conflicttable,
        validation_table: this.state.validationtable,
        other_validation_table: this.state.othervalidationtable,
        supporting_table: this.state.supportingtable,
        disapprove_table: this.state.disapprovetable,
        approved_table: this.state.approvedtable,
        all_table: this.state.alltable,
        actionHolder: this.state.actionHolder,
        remarksHolder: this.state.remarksHolder,
      },
    };

    this.setState({
      loading_state: true,
      check_internet_flag: true,
    });

    const savedTmpId = localStorage.getItem('tmp_id');
    if (savedTmpId === null) {
      API.verifyMemberSaveToTmp(payload)
        .then(response => {
          const responseError = get(response, 'error', null);
          if (isNil(responseError)) {
            localStorage.setItem('tmp_id', response._id);
            this.setState({
              loading_state: false,
              snackbarNotifProps: {
                isOpen: true,
                title: '',
                message: 'Details are saved successfully.',
                variant: 'success',
              },
            });
          } else {
            this.setState({
              loading_state: false,
              snackbarNotifProps: {
                isOpen: true,
                title: '',
                message: 'Error saving information',
                variant: 'error',
              },
            });
          }
        })
        .catch(() => {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            snackbarNotifProps: {
              isOpen: true,
              title: '',
              message: 'Error saving information',
              variant: 'error',
            },
          });
        });
    } else {
      // Add _id to payload
      payload._id = savedTmpId;
      API.verifyMemberPatchTmp(savedTmpId, payload)
        .then(response => {
          const responseError = get(response, 'error', null);
          if (isNil(responseError)) {
            this.setState({
              loading_state: false,
              snackbarNotifProps: {
                isOpen: true,
                title: '',
                message: 'Details are saved successfully.',
                variant: 'success',
              },
            });
          } else {
            this.setState({
              loading_state: false,
              snackbarNotifProps: {
                isOpen: true,
                title: '',
                message: 'Error saving information',
                variant: 'error',
              },
            });
          }
        })
        .catch(() => {
          this.setState({
            loading_state: false,
            snackbarNotifProps: {
              isOpen: true,
              title: '',
              message: 'Error saving information',
              variant: 'error',
            },
          });
        });
    }
  };

  deleteSaveForNow = () => {
    const savedTmpId = localStorage.getItem('tmp_id');
    if (!isNil(savedTmpId)) {
      API.deleteSaveForNow(savedTmpId)
        .then(res => {
          console.log('deleteSaveForNow', res);
        })
        .catch(err => {
          console.log('deleteSaveForNow Error', err);
        });
    }
  };

  handleLoadTmp = async () => {
    console.log('handleLoadTmp', this.props);
    this.setState({
      check_internet_flag: true,
    });
    console.log('loadVerifyDataFromTmp location', this.props.location);
    await API.loadVerifyDataFromTmp(this.props.match.params.ticket_id).then(
      res => {
        console.log('loadVerifyDataFromTmp res', res);
        if (res && res.length > 0) {
          const tmpData = res[res.length - 1];
          console.log('tempData', tmpData);

          localStorage.setItem('tmp_id', tmpData._id);

          if (
            this.props.location &&
            this.props.location.state &&
            this.props.location.state.from_dashboard
          ) {
            // MS811 - fix for Verify Member > Save For Now - "Load Previous data" from Dashboard
            this.setState(
              {
                tmpData: tmpData.data,
                check_internet_flag: false,
              },
              () => {
                this.setTmpValue(); // automatic loading of previous data
              },
            );
          } else {
            this.setState({
              tmpDataModal: true,
              tmpData: tmpData.data,
              check_internet_flag: false,
            });
          }
        } else {
          localStorage.removeItem('tmp_id');
        }
      },
    );
    return 'done';
  };

  handleCloseLostInternetConnectionModal = () => {
    this.setState({
      check_internet_flag: false,
    });
  };

  handleOpenLostInternetConnectionModal = () => {
    this.setState({
      check_internet_flag: true,
    });
  };

  setTmpValue = () => {
    const data = this.state.tmpData;

    if (!isNil(data) && !isEmpty(data)) {
      this.setState({
        partialtable: data.partial_table,
        incompletetable: data.incomplete_table,
        conflicttable: data.conflict_table,
        validationtable: data.validation_table,
        othervalidationtable: data.other_validation_table,
        supportingtable: data.supporting_table,
        disapprovetable: data.disapprove_table,
        approvedtable: data.approved_table,
        alltable: data.all_table,
        tmpDataModal: false,
        actionHolder: data.actionHolder ? data.actionHolder : [],
        remarksHolder: data.remarksHolder ? data.remarksHolder : [],
        defaultTableCopy: {
          partialtable: data.partial_table,
          incompletetable: data.incomplete_table,
          conflicttable: data.conflict_table,
          validationtable: data.validation_table,
          othervalidationtable: data.other_validation_table,
          supportingtable: data.supporting_table,
          disapprovetable: data.disapprove_table,
          approvedtable: data.approved_table,
          alltable: data.all_table,
        },
      });

    } else {
      this.setState({
        tmpDataModal: false,
      });

      // console.log('setTmpValue()3', this.state)
    }
  };

  handleTmpModalClose = () => {
    this.setState({
      tmpDataModal: false,
    });
  };

  closeSnackbarNotif = () => {
    this.setState({
      snackbarNotifProps: {
        isOpen: false,
        title: '',
        message: '',
        variant: 'success',
      },
    });
  };

  fetchMemberDependents = async (
    tempId: string,
    callBack: any,
    member_status?,
  ) => {
    await API.getMemberDependents(tempId).then(res => {
      if (res && res.error) {
        console.log('getMemberDependents err', res.error);
        callBack([]);
        console.log('member status', member_status);
      } else if (res) {
        console.log('getMemberDependents res', res);
        callBack(res);
      }
    });
  };

  fetchMemberPrincipal = async (principalTempId: string, callBack: any) => {
    await API.getMemberPrincipal(principalTempId).then(res => {
      if (res && res.error) {
        console.log('getMemberPrincipal err', res.error);
        callBack([]);
      } else if (res) {
        console.log('getMemberPrincipal res', res);
        callBack(res);
      }
    });
  };

  handlePrincipalSelected = async (id: any, row: any) => {
    this.setFieldValue('loading_state',true);
    console.log("Check Row: ",row);
    // Fetch Member Validate as Principal by id
    await API.getSelectedPrincipal(id).then(res => {
        const data = this.state.validationtable;
        const rows:any[] = this.state.validationtable.rows && this.state.validationtable.rows.length>0 ? 
        this.state.validationtable.rows : [] ;
        console.log("Check Rows: ",id,rows,data);
        const rowNum = rows.findIndex(item=>item.temp_id===row.temp_id);
        data['rows'][rowNum]['selected_principal'] = id;
        this.setState({
          ...this.state,
          selectedPrincipal: res
        })
        this.setFieldValue('validationtable', data);
        this.setFieldValue('loading_state',false);
        this.openMatchPrincipalModal();
      // }
    });
  };

  handleBubbleChat = (data: any) => {
    let name = data.member_details.validation_status.file_urls
      ? data.member_details.validation_status.file_urls[0].split('/')
      : '';

    let newObj = {
      remarks: data.member_details.validation_status.user_remarks,
      files: {
        url: data.member_details.validation_status['file_urls']
          ? data.member_details.validation_status.file_urls[0]
          : '',
        name: name !== '' ? name[name.length - 1] : '',
      },
      // files: {
      //   url: './public/uploads/process_exception_report/prcss-excr-1589353232803.jpg',
      //   name: 'prcss-excr-1589353232803.jpg'
      // }
    };

    console.log('newObj', newObj);
    this.setState({
      ...this.state,
      isRemarksModal: true,
      remarksData: newObj,
    });
  };
  

  handleClickInfo = async (data: any) => {
    console.log('handleClickInfo()', )
    let userData;
    try {
       userData = await API.getUserDataFromDb();
        } catch (error) {
            console.log(error)
        }
      if(data && data.temp_id !== undefined) {
        await API.getVerifyMemberInfo(data.temp_id)
        .then(response => {
        console.log('daaaaay1', response)
        let infoData = [data, response]
        console.log('verifyusersviewinfo3', data)

        this.setState({
          memberCardNo: response.member_card_no,
          displayUserRemarks: data && data.member_details.member_remarks
        });


        })
      }

      //setting up member remarks
      if(data && data.user_remarks !== '') {
        // console.log('memberinfo3', data.user_remarks)
        let newUserRemarks= {
          remarks:  data.user_remarks,
          user_fullname: userData.userName,
          date_created: Date.now()

      }

        this.setState({
          user_remarks: data.user_remarks,
          displayUserRemarks: [...this.state.displayUserRemarks, newUserRemarks]
        });

      } else {
        console.log(' memberinfo4', data.user_remarks)
      }

    // LOCAL FUNCTIONS
    const callForPrincipalMember = (
      tempId: string,
      dataObj: any,
      callBack: any,
    ) => {
      this.setState({
        check_internet_flag: true,
        loading_state: true,
      });

      this.fetchMemberDependents(tempId, (resData: any) => {
        dataObj['dependents'] = resData;

        callBack(dataObj);

        this.setState({
          check_internet_flag: false,
          loading_state: false,
        });
      });
    };
    const callForDependentMember = (
      principalTempId: string,
      dataObj: any,
      callBack: any,
    ) => {
      this.setState({
        check_internet_flag: true,
        loading_state: true,
      });
      this.fetchMemberPrincipal(principalTempId, (resData: any) => {
        dataObj['principals'] = resData;
      });
      this.fetchMemberDependents(principalTempId, (resData: any) => {
        dataObj['dependents'] = resData;

        callBack(dataObj);

        this.setState({
          check_internet_flag: false,
          loading_state: false,
        });
      });
    };
    //

    let targetData: any = undefined;

    this.setState({
      check_internet_flag: true,
      loading_state: true,
    });

    /** NEW IMPLEMENTATION */
    if (data && data['member_details'] && data['member_details']['temp_id']) {
      /**
       * Update Implementation to fetch from member-upload-members
       */
      // await API.getMemberData(data['member_details']['temp_id'])
      await API.getMemberUploadMemberData(
        data['member_details']['temp_id'],
      ).then(res => {
        if (
          res &&
          res.error &&
          res.error.name &&
          res.error.message &&
          res.error.statusCode
        ) {
          console.log('getMemberData err', res.error);
          this.setState({
            check_internet_flag: false,
            loading_state: false,
          });

          let errorTitle: string = res.error.name
            .match(/[A-Z][a-z]+/g)
            .join(' ');
          this.showMessageModal(
            `Error ${res.error.statusCode}: ${errorTitle}`,
            res.error.message,
          );
        } else if (res) {
          console.log('getMemberData res', res);
          targetData = res;
        }
      });
    } else if (data['temp_id']) { 

      await API.getMemberByTempId(
        data['temp_id'],
      ).then(res => {
        if (
          res &&
          res.error &&
          res.error.name &&
          res.error.message &&
          res.error.statusCode
        ) {
          console.log('getMemberData err', res.error);
          this.setState({
            check_internet_flag: false,
            loading_state: false,
          });

          let errorTitle: string = res.error.name
            .match(/[A-Z][a-z]+/g)
            .join(' ');
          this.showMessageModal(
            `Error ${res.error.statusCode}: ${errorTitle}`,
            res.error.message,
          );
        } else if (res) {
          console.log('getMemberData res', res);
          targetData = res[0];
        }
      });
    }
    /** */

    console.log('handleClickInfo data', targetData);
    if (targetData) {
      if (this.state.verifyPageType === 'default') {
        if (targetData['member_type']) {
          let memberType: string = targetData['member_type'];
          let dataObj: any = {};

          if (memberType === 'Principal') {
            if (targetData['temp_id']) {
              callForPrincipalMember(
                targetData['temp_id'],
                dataObj,
                (newDataObj: any) => {
                  dataObj = newDataObj;
                },
              );
            }
          } else {
            if (targetData['principal_temp_id']) {
              callForDependentMember(
                targetData['principal_temp_id'],
                dataObj,
                (newDataObj: any) => {
                  dataObj = newDataObj;
                },
              );
            }
          }

          console.log('dataObj val', dataObj);
          this.setState({
            loading_state: false,
            isMemberInfoModalOpen: true,
            memberData: targetData,
            infoData: dataObj,
          });
        }
      } else if (this.state.verifyPageType === 'edit' || this.state.verifyPageType === 'vd') {
        if (targetData['member_type']) {
          let memberType: string = targetData['member_type'];
          let dataObj: any = {};

          if (memberType === 'Principal') {
            if (targetData['temp_id']) {
              callForPrincipalMember(
                targetData['temp_id'],
                dataObj,
                (newDataObj: any) => {
                  dataObj = newDataObj;
                },
              );
            }
          } else {
            if (targetData['principal_temp_id']) {
              callForDependentMember(
                targetData['principal_temp_id'],
                dataObj,
                (newDataObj: any) => {
                  dataObj = newDataObj;
                },
              );
            }
          }

          console.log('dataObj val', dataObj);
          this.setState({
            loading_state: false,
            isMemberInfoModalOpen: true,
            memberData: targetData,
            infoData: dataObj,
          });
        }
      }
    } else {
      console.log('handleClickInfo member not found in "members" array');
      this.setState({
        check_internet_flag: false,
        loading_state: false,
      });
    }
  };


    handleClickEdit = (anchorEl: HTMLAnchorElement | null, data: any, pageType?: string, tableName?: string, prefix?: string,  isEdit?: boolean, row?: any, rowData?: any | undefined, memberFileUploadDetails?: any, memberDataEdit?: any) => {
    console.log('isEdit', isEdit)
     if (this.state.verifyPageType === 'default') {
        if (data && data['member_details']) {
          this.setState({
            anchorEl: anchorEl,
            memberData: Object.assign({}, data['member_details']),
            // memberDetails: Object.assign({}, )
          });
        }
         const memberFileUploadDetails = {
           pageType: 'default',
           tableName: tableName,
           prefix: prefix,
           rowData: rowData,
         };        
       this.setState({
             anchorEl: anchorEl,

             pageType: pageType,
             tableName: tableName,
             prefix: prefix,
            rowData: row,
             memberFileUploadDetails: memberFileUploadDetails,
             memberData: Object.assign({}, data),
            //  selectedMemberData: row.member_details
           });

         if(pageType === 'default') {
           this.setFieldValue('memberFileUploadDetails', memberFileUploadDetails);
          this.setFieldValue('memberDataEdit', memberDataEdit);
          this.setFieldValue('memberData', this.state.memberData);

         }
      } else if (
        this.state.verifyPageType === 'edit' ||
        this.state.verifyPageType === 'vd'
      ) {
        console.log("lawrence datae", data)
        // console.log('verify edit table tab terminated23', rowData)
          if (data) {
            this.setState({
              anchorEl: anchorEl,
              memberData: Object.assign({}, data),
            });
          }

          // console.log('verify edit table tab terminated24', rowData)
          const memberFileUploadDetails = {
           pageType: 'edit',
           tableName: tableName,
           prefix: prefix,
           rowData: rowData,
         };

         this.setState({
           anchorEl: anchorEl,
           pageType: pageType,
           tableName: tableName,
           prefix: prefix,
           rowData: row,
           memberFileUploadDetails: memberFileUploadDetails,
           memberData: Object.assign({}, data),
         });

         this.setFieldValue('memberFileUploadDetails', memberFileUploadDetails);
         this.setFieldValue('memberDataEdit', memberDataEdit);
         this.setFieldValue('memberData', this.state.memberData);
     } else if (this.state.verifyPageType === 'vd') {

       if (data) {
         this.setState({
           anchorEl: anchorEl,
           memberData: Object.assign({}, data),
         });

       }
     }
  };

  handleClickUploadNew = (
    pageType: string,
    tableName: string,
    prefix: string,
    isUpload: boolean,
    row: number,
    rowData: any,
  ) => {
    console.log({ log: 'Handle Click Upload New page type', pageType, row, rowData});
    const memberFileUploadDetails = {
      pageType: pageType,
      tableName: tableName,
      prefix: prefix,
      rowData: rowData,
      // temp_id: rowData.member_details.temp_id
    };

    // Check if the member is disapproved from unmatched dependents tab
    // if (
    //   rowData.member_details.validation_status.previous_status === 'UNMATCHED_PRINCIPAL_DEPENDENT' &&
    //   rowData.member_details.validation_status.previous_action === 'disapprove'
    // ) {
    //   // Check if the disapproved member has a unresolved partial matches issue
    //   let uploadedMembers = this.state.member_upload_data.validated_members.filter(item => {
    //     return (
    //       item.first_name === rowData.first_name &&
    //       item.last_name === rowData.last_name &&
    //       item.middle_name === rowData.middle_name &&
    //       item.validation_status.previous_status === 'PARTIAL_MATCH' &&
    //       item.validation_status.previous_action === 'for confirmation'
    //     )
    //   })

    //   if (uploadedMembers.length > 0) {
    //     disable = true;
    //   }
    // }

    // if (!disable) {
    if (pageType === 'default') {
      console.log('prefix-default');
      if (prefix === 'Disapproved') {
        console.log('prefix-disapproved');
        if (
          rowData.member_details.validation_status.hasOwnProperty(
            'matched_member',
          )
        ) {
          console.log('validatoin status matched member');
          memberFileUploadDetails['temp_id'] =
            rowData.member_details.validation_status.matched_member.temp_id;
        } else {
          memberFileUploadDetails['temp_id'] = rowData.member_details.temp_id;
        }
      } else {
        memberFileUploadDetails['temp_id'] = rowData.member_details.temp_id;
      }
    } else if (pageType === 'edit' || pageType === 'vd') {
      memberFileUploadDetails['temp_id'] = rowData.temp_id
    }

    if (isUpload) {
      console.log('isUpload');
      this.setFieldValue('memberFileUploadDetails', memberFileUploadDetails);
      this.setFieldValue('row', row);
      
      this.setFieldValue('memberFilesModalOpen', true);
    }
    // }
  };

  handleClickUpload = (pageType: string, tableName: string, prefix: string) => (
    isUpload: boolean,
  ) => {
    console.log('Handle Click Upload page type', pageType);
    const memberFileUploadDetails = {
      pageType: pageType,
      tableName: tableName,
      prefix: prefix,
    };

    if (isUpload) {
      this.setFieldValue('memberFileUploadDetails', memberFileUploadDetails);
      this.setFieldValue('memberFilesModalOpen', true);
    }
  };

  handlePopoverClose = () => {
    this.setState({
      anchorEl: null,
    });
  };

  handleChangeMemberData = (key: string, value: any) => {
   
    console.log('save func verify handlechangememberdata', this.state.rowData, this.state.memberData)
    // if(this.state.memberData) {
    //   this.setState({
    //     memberData: Object.assign(this.state.memberData, { [key]: value }),
    //   });
    // } else {
    //   console.log('memberdata empty')
    // }
    if(this.state.rowData) {
      if (this.state.verifyPageType === 'default') {
        this.setState({
          memberData: Object.assign(this.state.rowData['member_details'], { [key]: value }),
        });
      } else {
        this.setState({
          memberData: Object.assign(this.state.rowData, { [key]: value }),
        });
      }

    } else {
      console.log('memberdata empty')
    }

  };

  callEditPatch = async (id: string, payload: any) => {
    console.log('callEditPatch()', payload);
    // console.log('patch edit payload JSON', JSON.stringify(payload))
    await API.patchUploadMember(id, payload)
      .then(response => {
        this.setState({
          loading_state: false,
        });

        if (response && response.error) {
          console.log('edit modal save err', response.error);
        } else {
          console.log('edit modal save res', response);

          Utils.StorageService('user_data').then(result => {
            let user_name: string = '';
            console.log(user_name)
            //console.log('Utils', result)
            if (result && result !== undefined) {
              for (const i in result) {
                if (result[i]['key'] === 'username') {
                  user_name = result[i]['value'];
                }
              }
              API.activityEditMemberVerifyEditTicket(id, payload, user_name).then(response => {
                if(response === undefined){
                  console.log('response', response)
                }
                console.log('verifyeditticketupdatememberdata', response)
              }).catch(e => {
                console.log('terminate error', e)
              })
            };
          });

          

        }
      })
      .catch(error => {
        console.log('edit modal save catch err', error);
        this.setState({
          loading_state: false,
        });
      });
    this.getUpdatedMemberUploadData();
  };

  handlePatchMemberData = (data: any) => {
  
    console.log('edit member modal save1', data);
    this.setState({
      check_internet_flag: true,
      loading_state: true,
    });
    if (this.state.verifyPageType === 'default') {
      console.log('edit member modal save2', data);
      let originalMembers = JSON.stringify(
        this.state.member_upload_data['members'],
      );
      // console.log('validated_members',originalMembers)
      let originalValidatedMembers = JSON.stringify(
        this.state.member_upload_data['validated_members'],
      );

      let member_data = JSON.parse(originalMembers);
      let validated_members_data = JSON.parse(originalValidatedMembers);
      let temp_id = this.state && this.state?.memberData?.temp_id !== undefined ? this.state?.memberData?.temp_id : (this.state.rowData as any)?.temp_id;

      let obj_id = this.state && this.state?.memberData?._id !== undefined ? this.state?.memberData?._id : this.state.rowData['_id'];


      for (let i = 0; i < member_data.length; i = i + 1) {
        if (member_data[i]['temp_id'] === temp_id) {
          delete data['_id'];
          delete data['validation_status'];

          member_data[i] = Object.assign(member_data[i], data);
        }
      }
      for (let i = 0; i < validated_members_data.length; i = i + 1) {
        // Check member's member type was editted and update all of its validated_members_data that is not equal with obj_id
        if (
          validated_members_data[i]['temp_id'] === temp_id &&
          validated_members_data[i]['_id'] !== obj_id &&
          validated_members_data[i]['member_type'] === 'Dependent' &&
          data['member_type'] === 'Principal'
        ) {
          validated_members_data[i]['member_type'] = 'Principal';
        }

        if (validated_members_data[i]['_id'] === obj_id) {
          delete data['_id'];
          delete data['validation_status'];

          validated_members_data[i] = Object.assign(
            validated_members_data[i],
            data,
          );

        }

         if(validated_members_data[i].validation_status &&
          validated_members_data[i].validation_status.status === undefined){
          validated_members_data[i].validation_status.status = '';
        } else {
          validated_members_data[i].validation_status.status = validated_members_data[i].validation_status.status;
        }

       
      }

      let id = this.state.member_upload_data['_id'];
      let payload = this.state.member_upload_data;
      payload['members'] = member_data;
      payload['validated_members'] = validated_members_data;

      this.callEditPatch(id, payload);
    } else if (this.state.verifyPageType === 'edit') {
      console.log('handlePatchMemberData edit', data);
  
      this.finalizeSaveMemberEdit(data);
    } else if (this.state.verifyPageType === 'vd') {
      // console.log('handlePatchMemberData()1 vd', data);
      API.putSaveDependentMemberEdit(data._id, false, data).then(res1 => {
        console.log('1st save edit', res1);
        if (res1 && res1.error === undefined) {
          if (
            res1.validation_status &&
            res1.validation_status.previous_status &&
            res1.validation_status.previous_status !== ''
          ) {
            let newStatus;

            if (res1.validation_status && res1.validation_status.status) {
              switch (res1.validation_status.status) {
                case 'CONFLICT_ON_DATA':
                  newStatus = 'Conflict on Data';
                  break;
                case 'MEMBER_STATUS_FOR_VALIDATION':
                  newStatus = 'For Validation';
                  break;
                case 'REQUIRING_SUPPORTING_DOCUMENTS':
                    newStatus = 'For Validation';
                  break;
                case 'MEMBER_STATUS_TERMINATED':
                  newStatus = 'Terminated';
                  break;
                case 'MEMBER_STATUS_ACTIVE':
                  newStatus = 'Active';
                  break;
                case 'MEMBER_STATUS_OTHERS':
                  newStatus = 'Others';
                  break;
              }
            }

            this.setState({
              check_internet_flag: false,
              loading_state: false,
              isConfirmModalOpen: true,
              modalTitle: 'Confirm Action',
              modalMessage: '',
              customModalMessage: () => {
                return (
                  <>
                    The user will be transferred to the{' '}
                    <b>{newStatus ? newStatus : '---'}</b>. Are you sure to
                    continue?
                  </>
                );
              },
              modalCloseText: 'Cancel',
              modalConfirmText: 'Continue',
              next: () => {
                this.setState({
                  customModalMessage: null,
                  next: null,
                  check_internet_flag: true,
                  loading_state: true,
                });
                this.finalizeSaveMemberEdit(res1);
              },
            });
          } else {
            this.setState({
              check_internet_flag: false,
              loading_state: false,
              isConfirmModalOpen: true,
              modalTitle: 'Confirm Action',
              modalMessage: '',
              customModalMessage: () => {
                return <>Are you sure to continue?</>;
              },
              modalCloseText: 'Cancel',
              modalConfirmText: 'Continue',
              next: () => {
                this.setState({
                  customModalMessage: null,
                  next: null,
                  check_internet_flag: true,
                  loading_state: true,
                });
                this.finalizeSaveMemberEdit(res1);
              },
            });
          }
        } else {
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isModalOpen: true,
            modalTitle: 'Save Member Edit Failed',
            modalMessage: res1.error.message
              ? res1.error.message
              : 'Unable to save changes for the edited member.',
          });
        }
      });
    }
  };

  finalizeSaveMemberEdit = (res1: any) => {
    if (this.state.verifyPageType === 'edit') {
      const upload_id = this.props.match.params.member_upload_id;
      const case_id = this.props.match.params.ticket_id;
      API.putSaveMemberEdit(res1._id, true, res1, upload_id, case_id ).then(res2 => {
        console.log('2nd save edit', res2);
        if (res2 && res2.error !== undefined) {
          // error
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isModalOpen: true,
            modalTitle: 'Save Member Edit Failed',
            modalMessage: res2.error.message
              ? res2.error.message
              : 'Unable to save changes for the edited member.',
          });
        } else {
          // success
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isConfirmModalOpen: true,
            modalTitle: 'Save Member Edit Successful',
            modalMessage: 'Changes to the member were applied.',
            modalCloseText: '',
            noCancelButton: true,
            modalConfirmText: 'Okay',
            next: () => {
              this.setState({
                next: null,
              });
     
              this.getUpdatedMemberUploadData();
            },
          }); 

          Utils.StorageService('user_data').then(result => {
            let user_name: string = '';
            console.log(user_name)
            //console.log('Utils', result)
            if (result && result !== undefined) {
              for (const i in result) {
                if (result[i]['key'] === 'username') {
                  user_name = result[i]['value'];
                }
              }

              console.log('verify edit > edit member')
              API.activityEditMemberVerifyEditTicket(res1.temp_id, res1, user_name).then(response => {
                if(response === undefined){
                  console.log('res undf', response)
                }
              }).catch(e => {
                console.log('terminate error', e)
              })
            };
          });

        }
      });
    } else if (this.state.verifyPageType === 'vd') {
      API.putSaveDependentMemberEdit(res1._id, true, res1).then(res2 => {
        console.log('2nd save edit', res2);
        if (res2 && res2.error !== undefined) {
          // error
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isModalOpen: true,
            modalTitle: 'Save Member Edit Failed',
            modalMessage: res2.error.message
              ? res2.error.message
              : 'Unable to save changes for the edited member.',
          });
        } else {
          // success
          this.setState({
            check_internet_flag: false,
            loading_state: false,
            isConfirmModalOpen: true,
            modalTitle: 'Save Member Edit Successful',
            modalMessage: 'Changes to the member were applied.',
            modalCloseText: '',
            noCancelButton: true,
            modalConfirmText: 'Okay',
            next: () => {
              this.setState({
                next: null,
              });
             
              this.getUpdatedMemberUploadData();
            },
          });
        }
      });
    }
  };

  handleFloatingButtonActions = (action: string, pageType: string) => {
    if (pageType === 'default' && action.toLowerCase() === 'save for now') {
      this.handleSaveTmp();
    } else if (
      pageType === 'default' &&
      action.toLowerCase() === 'generate exception report'
    ) {
      this.generateExeptionReport();
    } else if (pageType === 'edit' && action.toLowerCase() === 'save for now') {
      console.log('save for now edit');
    } else if (pageType === 'edit' && action.toLowerCase() === 'update list') {
      console.log('update list');
      this.handleVerifyEditUpdateList();
    } else if (pageType === 'vd' && action.toLowerCase() === 'update list') {
      console.log('update list');
      this.handleVerifyDependentUpdateList();
    }
  };

  closeModal = () => {
    this.setState({ isModalOpen: false });
  };

  addToActionHolder = (
    obj_id: string,
    actionValue: string,
    validationStatus?: string,
  ) => {
    const { actionHolder } = this.state;
    if (
      actionHolder
        .map(ahData => {
          return ahData._id;
        })
        .indexOf(obj_id) !== -1
    ) {
      let currentArr = actionHolder.map(ahData => {
        if (ahData['_id'] && obj_id === ahData['_id']) {
          let newObj = {
            ...ahData,
            action: actionValue.toLowerCase(),
          };
          if (validationStatus) {
            newObj['validation_status'] = validationStatus;
          }

          return newObj;
        } else {
          return ahData;
        }
      });

      this.setState({
        actionHolder: currentArr,
      });
    } else {
      let currentArr = [...actionHolder];
      let newObj = {
        _id: obj_id,
        action: actionValue.toLowerCase(),
      };
      if (validationStatus) {
        newObj['validation_status'] = validationStatus;
      }
      currentArr.push(newObj);
      this.setState({
        actionHolder: currentArr,
      });
    }
  };

  addToRemarksHolder = (obj_id: string, remarksValue: string) => {
    const { remarksHolder } = this.state;
    if (
      remarksHolder
        .map(rhData => {
          return rhData._id;
        })
        .indexOf(obj_id) !== -1
    ) {
     
      let currentArr = remarksHolder.map(rhData => {
        if (rhData['_id'] && obj_id === rhData['_id']) {
          let newObj = {
            ...rhData,
            remarks: remarksValue,
          };
          return newObj;
        } else {
          return rhData;
        }
      });
      this.setState({
        remarksHolder: currentArr,
      });
    } else {
      let currentArr = [...remarksHolder];
      currentArr.push({
        _id: obj_id,
        remarks: remarksValue,
      });
      this.setState({
        remarksHolder: currentArr,
      });
    }
  };

  showUnauthorizedError = (errorData: any) => {
    console.log('showUnauthorizedError err data', errorData);
    if (
      errorData &&
      errorData['message'] &&
      errorData['statusCode'] &&
      errorData['statusCode'] === 401 &&
      errorData['name'] &&
      errorData['name'] === 'UnauthorizedError'
    ) {
      let errorTitle: string = errorData['name']
        .match(/[A-Z][a-z]+/g)
        .join(' ');
      console.log('showUnauthorizedError error title', errorTitle);
      // this.showMessageModal(
      //   `Error ${errorData['statusCode']}: ${errorTitle}`,
      //   errorData['message'],
      // );
    }
  };

  showMessageModal = (modalTitle: string, modalMessage: string) => {
    this.setState({
      isErrorModalOpen: true,
      errorTitle: modalTitle,
      errorMessage: modalMessage,
    });
  };

  // jaye 5851 check if all members are approved for a specific member temp_id
  isAllApprovedForTempId = (temp_id: string) => {
    const { allValidated } = this.state;
    if (!Array.isArray(allValidated)) return false;

    const members = allValidated.filter(
      m => m.temp_id === temp_id
    );

    if (members.length === 0) return false;
    return members.every(
      m => m.validation_status && (m.validation_status.status === 'APPROVED' || m.validation_status.status === '')
    );
  };


  // public 
  public render(): JSX.Element {
    // console.log("verify/edit",this.props.match.params)
    const {
      pmaker_task,
      row,
      partialRow,
      // supportingRow,
      validationRow,
      breadcrumbs,
      tab,
      verifyTabs,
      partialtable,
      incompletetable,
      conflicttable,
      validationtable,
      othervalidationtable,
      supportingtable,
      disapprovetable,
      approvedtable,
      alltable,
      partialModalOpen,
      matchPrincipalModalOpen,
      approveModalOpen,
      memberFilesModalOpen,
      disapproveModalOpen,
      forConfirmationModalOpen,
      approveDependent,
      generateFlag,
      loading_state,
      member_upload_data,
      allData,
      tmpDataModal,
      isActionRemarksModalOpen,
      actionRemarksModalProps,
      pageSizes,
      snackbarNotifProps,
      corporateAccountNo,
      memberCardNo,
      corporateAccountName,
      contractName,
      contractId,
      clientId,
      // forValidationTab,
      verifyPageType,
      verifyEditTable,
      verifyEditFilters,
      verifyEditPageSize,
      isModalOpen,
      modalTitle,
      modalMessage,
      isConfirmModalOpen,
      modalCloseText,
      modalConfirmText,
      customModalMessage,
      member_edit_data,
      redirectFrom,
      noCancelButton,
      ocpPath,
      isErrorModalOpen,
      errorTitle,
      errorMessage,
      member_id,
      ticket_closed,
      documentData,
      user_remarks,
      displayUserRemarks
    } = this.state;

    if(ticket_closed){
      if(partialtable.columns){
        partialtable.columns = partialtable.columns.filter(col=>col.name!=='action');
      }
      if(incompletetable.columns){
        incompletetable.columns = incompletetable.columns.filter(col=>col.name!=='incomplete_action');
      }
      if(conflicttable.columns){
        conflicttable.columns = conflicttable.columns.filter(col=>col.name!=='incomplete_action');
      }
      if(validationtable.columns){
        validationtable.columns = validationtable.columns.filter(col=>col.name!=='incomplete_action');
      }
      if(othervalidationtable.columns){
        othervalidationtable.columns = othervalidationtable.columns.filter(col=>col.name!=='incomplete_action');
      }
      if(supportingtable.columns){
        supportingtable.columns = supportingtable.columns.filter(col=>col.name!=='incomplete_action');
      }
    }
    const batch_ticket_id = localStorage.getItem('TICKET_ID');
    const renderClientDetails = () => {
      // batch_ticket_no is stored in local storage
    
      return (
        <MemberInformation
          data={
            verifyPageType === 'edit' || verifyPageType === 'vd'
              ? member_edit_data
              : member_upload_data
          }
          corporateAccountNo={corporateAccountNo}
          memberCardNo={memberCardNo}
          corporateAccountName={corporateAccountName}
          contractName={contractName}
          // contractId={contractId} //Ann
          contractId={this.state.member_upload_data.client_contract_id} //Ann
          // contractNo={this.state.member_upload_data.client_contract_no} //Ann
          pageType={verifyPageType}
          ocpOrigin={ocpPath}
          member_id={member_id}
          generateFlag={generateFlag}
          benefitPlanTree={this.state.benefitPlanTree}
          handleRedirect={this.handleRedirect}
          batch_ticket_id={batch_ticket_id || ''}
        />
      );
    };

    if (redirectFrom['verifyEdit']) {
      console.log('REDIRECT TRIGGERED');
      return <Redirect to={{ pathname: '/membership/dashboard/' }} />;
    } else {
      return (
        <div className={clsx('VerifyMemberPage')}>
          {loading_state ? <Loader /> : null}
          <Grid container className={clsx('page-header-title')}>
            <Grid item xs={12}>
              <Grid item xs={12}>
                <Components.UI.BreadcrumbsComponent items={breadcrumbs} />
              </Grid>
              <Grid item xs={12}>
                <PageHeaderComponent
                  id={'verify_member_stepper'}
                  label={
                    verifyPageType === 'default'
                      ? 'Verify Member List'
                      : !generateFlag && verifyPageType === 'edit'
                      ? 'Verify Edit'
                      : verifyPageType === 'vd'
                      ? 'Verify Dependents'
                      : generateFlag && verifyPageType === 'edit'
                      ? 'View Corrections'
                      : ''
                  }
                  steps={[]}
                  activeStep={0}
                />
              </Grid>
            </Grid>
          </Grid>
          {!generateFlag ? (
            <>
              {/* {console.log(
                console.log(
                  'VerifyMemberPage1',
                  JSON.parse(JSON.stringify(allData)),
                ),
              )} */}
              <Grid container className={clsx('page-header-title')}>
                <Grid item xs={12}>
                  {renderClientDetails()}
                </Grid>
                {verifyPageType === 'default' ? (
                  <Grid item xs={12} className={clsx('member-list-section')}>
                    <Grid container>
                      <Grid item xs={10}>
                        <Typography
                          className={clsx('sub-title')}
                          color="textPrimary"
                        >
                          Member List
                        </Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                ) : null}
                <Grid
                  item
                  xs={12}
                  style={{
                    marginTop: '21px',
                    backgroundColor: '#F5F7FB',
                    boxShadow: '4px 1px 6px #00000029',
                  }}
                >
                  <Tabs
                    value={tab}
                    onChange={this.handleChange}
                    aria-label="simple tabs example"
                  >
                    {verifyTabs.map((data, idx) => {
                      let tabLabel = data.label;
                      if (verifyPageType === 'default') {
                        tabLabel = data.label + ' (' + data.count + ')';
                      } else if (verifyPageType === 'edit') {
                        tabLabel = data.label  + ' (' + data.count + ')';
                      }

                      return (
                        <Tab
                          id={
                            'verify-member-tab-' +
                            data.label.replace(/ /g, '-').toLowerCase()
                          }
                          data-cy={
                            'verify-member-tab-' +
                            data.label.replace(/ /g, '-').toLowerCase()
                          }
                          label={tabLabel}
                          key={'verify-member-tab-' + idx}
                          aria-controls={'verify-member-tabpanel-' + data.count}
                          style={{
                            fontSize: '10px',
                          }}
                        />
                      );
                    })}
                  </Tabs>
                </Grid>
                <Grid item xs={12} style={{ paddingTop: '5px' }}>
                {/* {console.log('edit debug1', verifyPageType)} */}
                
                  {verifyPageType === 'default' && tab === 0 ? (
                    <TableComponent
                      id="tab-partial"
                      data-cy="tab-partial"
                      rows={
                        Object.keys(partialtable).length > 0
                          ? partialtable['rows']
                            ? partialtable['rows']
                            : []
                          : []
                      }
                      columns={
                        Object.keys(partialtable).length > 0
                          ? partialtable['columns']
                            ? partialtable['columns']
                            : []
                          : []
                      }
                      intgFltrColumnExtensions={get(
                        partialtable,
                        'intgFltrColumnExtensions',
                        [],
                      )}
                      message="No available data"
                      showSelectionColumn={false}
                      onClickRow={(row: number[]) =>
                        this.handleClickRow(
                          row[0],
                          'partialtable',
                          partialtable['rows'][row[0]],
                        )
                      }
                      onSelectedRadio={(row: number, value: string) =>
                        this.handleSelectedRadio(
                          row,
                          value,
                          'partialtable',
                          partialtable['rows'][row],
                        )
                      }
                      disableSelect
                      disableSearch
                      singleSelect={true}
                      formattedColumns={
                        Object.keys(partialtable).length > 0
                          ? partialtable['formattedColumns']
                            ? partialtable['formattedColumns']
                            : {}
                          : {}
                      }
                      columnExtensions={
                        Object.keys(partialtable).length > 0
                          ? partialtable['columnExtensions']
                            ? partialtable['columnExtensions']
                            : []
                          : []
                      }
                      filterExtensions={
                        Object.keys(partialtable).length > 0
                          ? partialtable['filterExtensions']
                            ? partialtable['filterExtensions']
                            : []
                          : []
                      }
                      defaultFilter={this.state.partialFilter}
                      onFilterChange={this.handleFilterChange('partialFilter')}
                      onSortingChange={this.handleSortingChange('partialtable')}
                      currentPage={this.state.currentTablePage}
                      setCurrentPage={(page: number) => {
                        this.setState({ currentTablePage: page });
                      }}
                      pageSize={pageSizes.partial_table}
                      onPageSizeChange={(pageSize: number) => {
                        this.handlePageSizeChange('partial_table', pageSize);
                      }}
                      handleClickInfo={this.handleClickInfo}
                      ocp={ocpPath}
                      onBubbleChatSelected={this.handleBubbleChat}
                      currentTab={'partial'}
                      dateSorting={this.state.dateSorting}
                    />
                  ) : verifyPageType === 'default' && tab === 1 ? (
                    <>
                      <TableComponent
                        id="tab-incomplete"
                        rows={
                          Object.keys(incompletetable).length > 0
                            ? incompletetable['rows']
                              ? incompletetable['rows']
                              : []
                            : []
                        }
                        columns={
                          Object.keys(incompletetable).length > 0
                            ? incompletetable['columns']
                              ? incompletetable['columns']
                              : []
                            : []
                        }
                        intgFltrColumnExtensions={get(
                          incompletetable,
                          'intgFltrColumnExtensions',
                          [],
                        )}
                        message="No available data"
                        onClickRow={(row: number[]) =>
                          this.handleClickRow(
                            row[0],
                            'incompletetable',
                            incompletetable['rows'][row[0]],
                          )
                        }
                        onSelectedRadio={(row: number, value: string) =>
                          this.handleSelectedRadio(
                            row,
                            value,
                            'incompletetable',
                            incompletetable['rows'][row],
                          )
                        }
                        disableSelect
                        disableSearch
                        formattedColumns={
                          Object.keys(incompletetable).length > 0
                            ? incompletetable['formattedColumns']
                              ? incompletetable['formattedColumns']
                              : {}
                            : {}
                        }
                        columnExtensions={
                          Object.keys(incompletetable).length > 0
                            ? incompletetable['columnExtensions']
                              ? incompletetable['columnExtensions']
                              : []
                            : []
                        }
                        filterExtensions={
                          Object.keys(incompletetable).length > 0
                            ? incompletetable['filterExtensions']
                              ? incompletetable['filterExtensions']
                              : []
                            : []
                        }
                        defaultFilter={this.state.incompleteFilter}
                        onFilterChange={this.handleFilterChange(
                          'incompleteFilter',
                        )}
                        onSortingChange={this.handleSortingChange(
                          'incompletetable',
                        )}
                        currentPage={this.state.currentTablePage}
                        setCurrentPage={(page: number) => {
                          this.setState({ currentTablePage: page });
                        }}
                        pageSize={pageSizes.incomplete_table}
                        onPageSizeChange={(pageSize: number) => {
                          this.handlePageSizeChange(
                            'incomplete_table',
                            pageSize,
                          );
                        }}
                      handleClickEdit={
                        (
                        // isEdit?: boolean,
                        anchorEl: HTMLAnchorElement | null,
                        row: number,
                        memberFileUploadDetails: any,
                        // memberDataEdit:any
                      ) => {

                        this.handleClickEdit(
                            anchorEl,
                           this.state.rowDataForEdit,
                            verifyPageType,
                            'incomplete',
                            'Incomplete',
                            true,
                            row,
                            incompletetable['rows'][row[0]],
                            memberFileUploadDetails,
                            // memberDataEdit //just now
                        )
                       }
                      }
                        handleClickInfo={this.handleClickInfo}
                        handleClickUpload={(
                          isUpload: boolean,
                          row: number[],
                        ) => {
                          // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                          // added comment for rebuild purposes
                          this.handleClickUploadNew(
                            verifyPageType,
                            'incomplete',
                            'Incomplete',
                            isUpload,
                            row[0],
                            incompletetable['rows'][row[0]],
                          );
                        }}
                        allowedVerifyActions={{
                          approve: GlobalFunction.checkUserRbacByPolicy(
                            'MS41',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                          disapprove: GlobalFunction.checkUserRbacByPolicy(
                            'MS42',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                          forConfirm: GlobalFunction.checkUserRbacByPolicy(
                            'MS43',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                        }}
                        ocp={ocpPath}
                        onBubbleChatSelected={this.handleBubbleChat}
                        currentTab={'incomplete'}
                        dateSorting={this.state.dateSorting}
                      />
                    </>
                  ) : verifyPageType === 'default' && tab === 2 ? (
                    <>
                      <TableComponent
                        id="tab-conflict"
                        data-cy="tab-conflict"
                        rows={
                          Object.keys(conflicttable).length > 0
                            ? conflicttable['rows']
                              ? conflicttable['rows']
                              : []
                            : []
                        }
                        columns={
                          Object.keys(conflicttable).length > 0
                            ? conflicttable['columns']
                              ? conflicttable['columns']
                              : []
                            : []
                        }
                        intgFltrColumnExtensions={get(
                          conflicttable,
                          'intgFltrColumnExtensions',
                          [],
                        )}
                        message="No available data"
                        onClickRow={(row: number[]) =>
                          this.handleClickRow(
                            row[0],
                            'conflicttable',
                            conflicttable['rows'][row[0]],
                          )
                        }
                        onSelectedRadio={(row: number, value: string) =>
                          this.handleSelectedRadio(
                            row,
                            value,
                            'conflicttable',
                            conflicttable['rows'][row],
                          )
                        }
                        disableSelect
                        disableSearch
                        formattedColumns={
                          Object.keys(conflicttable).length > 0
                            ? conflicttable['formattedColumns']
                              ? conflicttable['formattedColumns']
                              : {}
                            : {}
                        }
                        columnExtensions={
                          Object.keys(conflicttable).length > 0
                            ? conflicttable['columnExtensions']
                              ? conflicttable['columnExtensions']
                              : []
                            : []
                        }
                        filterExtensions={
                          Object.keys(conflicttable).length > 0
                            ? conflicttable['filterExtensions']
                              ? conflicttable['filterExtensions']
                              : []
                            : []
                        }
                        defaultFilter={this.state.conflictFilter}
                        onFilterChange={this.handleFilterChange(
                          'conflictFilter',
                        )}
                        onSortingChange={this.handleSortingChange(
                          'conflicttable',
                        )}
                        currentPage={this.state.currentTablePage}
                        setCurrentPage={(page: number) => {
                          this.setState({ currentTablePage: page });
                        }}
                        pageSize={pageSizes.conflict_table}
                        onPageSizeChange={(pageSize: number) => {
                          this.handlePageSizeChange('conflict_table', pageSize);
                        }}
                        // handleClickEdit={this.handleClickEdit}
                      handleClickEdit={
                        (
                        // isEdit?: boolean,
                        anchorEl: HTMLAnchorElement | null,
                        row: number,
                        memberFileUploadDetails: any
                      ) => {

                        this.handleClickEdit(
                            anchorEl,
                           this.state.rowDataForEdit,
                            verifyPageType,
                            'conflict',
                            'Conflict',
                            true,
                            row,
                            // conflicttable['rows'],
                            conflicttable['rows'][row[0]],
                            memberFileUploadDetails,
                        )
                       }
                      }
                        handleClickInfo={this.handleClickInfo}
                        handleClickUpload={(
                          isUpload: boolean,
                          row: number[],
                        ) => {
                          // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                          this.handleClickUploadNew(
                            verifyPageType,
                            'conflict',
                            'Conflict',
                            isUpload,
                            row[0],
                            conflicttable['rows'][row[0]],
                          );
                        }}
                        allowedVerifyActions={{
                          approve: GlobalFunction.checkUserRbacByPolicy(
                            'MS41',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                          disapprove: GlobalFunction.checkUserRbacByPolicy(
                            'MS42',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                          forConfirm: GlobalFunction.checkUserRbacByPolicy(
                            'MS43',
                            this.state.verifierData,
                            this.state.loading_state,
                          ),
                        }}
                        ocp={ocpPath}
                        onBubbleChatSelected={this.handleBubbleChat}
                        currentTab={'conflict'}
                        dateSorting={this.state.dateSorting}
                      />
                    </>
                  ) : verifyPageType === 'default' && tab === 3 ? (
                    <>
                      <Grid container style={{ backgroundColor: '#F5F7FB' }}>
                        <Grid item xs={12}>
                          <TableComponent
                            id="tab-validation"
                            data-cy="tab-validation"
                            principalItem={[{ name: 'check', value: 'check' }]}
                            rows={
                              Object.keys(validationtable).length > 0
                                ? validationtable['rows']
                                  ? validationtable['rows']
                                  : []
                                : []
                            }
                            columns={
                              Object.keys(validationtable).length > 0
                                ? validationtable['columns']
                                  ? validationtable['columns']
                                  : []
                                : []
                            }
                            intgFltrColumnExtensions={get(
                              validationtable,
                              'intgFltrColumnExtensions',
                              [],
                            )}
                            message="No available data"
                            onClickRow={(row: number[]) =>
                              this.handleClickRow(
                                row[0],
                                'validationtable',
                                validationtable['rows'][row[0]],
                              )
                            }
                            onSelectedRadio={(row: number, value: string) => {
                              console.log('change', row, value);
                              this.handleSelectedRadio(
                                row,
                                value,
                                'validationtable',
                                validationtable['rows'][row],
                              );
                            }}
                            disableSelect
                            disableSearch
                            formattedColumns={
                              Object.keys(validationtable).length > 0
                                ? validationtable['formattedColumns']
                                  ? validationtable['formattedColumns']
                                  : {}
                                : {}
                            }
                            columnExtensions={
                              Object.keys(validationtable).length > 0
                                ? validationtable['columnExtensions']
                                  ? validationtable['columnExtensions']
                                  : []
                                : []
                            }
                            filterExtensions={
                              Object.keys(validationtable).length > 0
                                ? validationtable['filterExtensions']
                                  ? validationtable['filterExtensions']
                                  : []
                                : []
                            }
                            defaultFilter={this.state.validationFilter}
                            onFilterChange={this.handleFilterChange(
                              'validationFilter',
                            )}
                            onSortingChange={this.handleSortingChange(
                              'validationtable',
                            )}
                            currentPage={this.state.currentTablePage}
                            setCurrentPage={(page: number) => {
                              this.setState({ currentTablePage: page });
                            }}
                            pageSize={pageSizes.validation_table}
                            onPageSizeChange={(pageSize: number) => {
                              this.handlePageSizeChange(
                                'validation_table',
                                pageSize,
                              );
                            }}
                            // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {

                            this.handleClickEdit(
                                anchorEl,
                               this.state.rowDataForEdit,
                                verifyPageType,
                                'unmatched',
                                'Unmatched',
                                true,
                                row,
                                // validationtable['rows'],
                                validationtable['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }
                            handleClickInfo={this.handleClickInfo}
                            handleClickUpload={(
                              isUpload: boolean,
                              row: number[],
                            ) => {
                              // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                              this.handleClickUploadNew(
                                verifyPageType,
                                'validation',
                                'Unmatched',
                                isUpload,
                                row[0],
                                validationtable['rows'][row[0]],
                              );
                            }}
                            onPrincipalSelected={(
                              id: any,
                              row: number,
                            ) => {
                              this.handlePrincipalSelected(
                                id,
                                validationtable['rows'][row],
                              );
                            }}
                            allowedVerifyActions={{
                              approve: GlobalFunction.checkUserRbacByPolicy(
                                'MS41',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              disapprove: GlobalFunction.checkUserRbacByPolicy(
                                'MS42',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              forConfirm: GlobalFunction.checkUserRbacByPolicy(
                                'MS43',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                            }}
                            ocp={ocpPath}
                            currentTab={'validation'}
                            dateSorting={this.state.dateSorting}
                            principalList = {this.state.principalList}
                          />
                        </Grid>
                      </Grid>
                    </>
                  ) : verifyPageType === 'default' && tab === 4 ? (
                    <>
                      <Grid container style={{ backgroundColor: '#F5F7FB' }}>
                        <Grid item xs={12}>
                          <TableComponent
                            id="tab-other-validation"
                            data-cy="tab-other-validation"
                            // principalItem={[{ name: 'check', value: 'check' }]}
                            rows={
                              Object.keys(othervalidationtable).length > 0
                                ? othervalidationtable['rows']
                                  ? othervalidationtable['rows']
                                  : []
                                : []
                            }
                            columns={
                              Object.keys(othervalidationtable).length > 0
                                ? othervalidationtable['columns']
                                  ? othervalidationtable['columns']
                                  : []
                                : []
                            }
                            intgFltrColumnExtensions={get(
                              othervalidationtable,
                              'intgFltrColumnExtensions',
                              [],
                            )}
                            message="No available data"
                            onClickRow={(row: number[]) =>
                              this.handleClickRow(
                                row[0],
                                'othervalidationtable',
                                othervalidationtable['rows'][row[0]],
                              )
                            }
                            onSelectedRadio={(row: number, value: string) => {
                              console.log('change', row, value);
                              this.handleSelectedRadio(
                                row,
                                value,
                                'othervalidationtable',
                                othervalidationtable['rows'][row],
                                verifyPageType,
                                'Other Validation Rules',
                              );
                            }}
                            disableSelect
                            disableSearch
                            formattedColumns={
                              Object.keys(othervalidationtable).length > 0
                                ? othervalidationtable['formattedColumns']
                                  ? othervalidationtable['formattedColumns']
                                  : {}
                                : {}
                            }
                            columnExtensions={
                              Object.keys(othervalidationtable).length > 0
                                ? othervalidationtable['columnExtensions']
                                  ? othervalidationtable['columnExtensions']
                                  : []
                                : []
                            }
                            filterExtensions={
                              Object.keys(othervalidationtable).length > 0
                                ? othervalidationtable['filterExtensions']
                                  ? othervalidationtable['filterExtensions']
                                  : []
                                : []
                            }
                            defaultFilter={this.state.othervalidationFilter}
                            onFilterChange={this.handleFilterChange(
                              'othervalidationFilter',
                            )}
                            onSortingChange={this.handleSortingChange(
                              'othervalidationtable',
                            )}
                            currentPage={this.state.currentTablePage}
                            setCurrentPage={(page: number) => {
                              this.setState({ currentTablePage: page });
                            }}
                            pageSize={pageSizes.other_validation_table}
                            onPageSizeChange={(pageSize: number) => {
                              this.handlePageSizeChange(
                                'other_validation_table',
                                pageSize,
                              );
                            }}
                            // handleClickEdit={this.handleClickEdit}
                            handleClickEdit={
                              (
                              // isEdit?: boolean,
                              anchorEl: HTMLAnchorElement | null,
                              row: number,
                              memberFileUploadDetails: any
                            ) => {

                              this.handleClickEdit(
                                  anchorEl,
                                  // null,
                                 this.state.rowDataForEdit,
                                  verifyPageType,
                                  'othervalidation',
                                  'Other Validation',
                                  true,
                                  row,
                                  // othervalidationtable['rows'],
                                  othervalidationtable['rows'][row[0]],
                                  memberFileUploadDetails,
                              )
                             }
                            }
                            handleClickInfo={this.handleClickInfo}
                            handleClickUpload={(
                              isUpload: boolean,
                              row: number[],
                            ) => {
                              console.log('verifymember/table/handleclickupload', isUpload)
                              // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                              this.handleClickUploadNew(
                                verifyPageType,
                                'othervalidation',
                                'Other Validation',
                                isUpload,
                                row[0],
                                othervalidationtable['rows'][row[0]],
                              );
                            }}
                            allowedVerifyActions={{
                              approve: GlobalFunction.checkUserRbacByPolicy(
                                'MS41',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              disapprove: GlobalFunction.checkUserRbacByPolicy(
                                'MS42',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              forConfirm: GlobalFunction.checkUserRbacByPolicy(
                                'MS43',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                            }}
                            ocp={ocpPath}
                            onBubbleChatSelected={this.handleBubbleChat}
                            currentTab={'othervalidation'}
                            dateSorting={this.state.dateSorting}
                          />
                        </Grid>
                      </Grid>
                    </>
                  ) : verifyPageType === 'default' && tab === 5 ? (
                    <>
                      <Grid container style={{ backgroundColor: '#F5F7FB' }}>
                        <Grid item xs={12}>
                          <TableComponent
                            id="tab-supporting"
                            data-cy="tab-supporting"
                            // principalItem={[{ name: 'check', value: 'check' }]}
                            rows={
                              Object.keys(supportingtable).length > 0
                                ? supportingtable['rows']
                                  ? supportingtable['rows']
                                  : []
                                : []
                            }
                            columns={
                              Object.keys(supportingtable).length > 0
                                ? supportingtable['columns']
                                  ? supportingtable['columns']
                                  : []
                                : []
                            }
                            intgFltrColumnExtensions={get(
                              supportingtable,
                              'intgFltrColumnExtensions',
                              [],
                            )}
                            message="No available data"
                            onClickRow={(row: number[]) =>
                              this.handleClickRow(
                                row[0],
                                'supportingtable',
                                supportingtable['rows'][row[0]],
                              )
                            }
                            onSelectedRadio={(row: number, value: string) => {
                              console.log('change', row, value);
                              this.handleSelectedRadio(
                                row,
                                value,
                                'supportingtable',
                                supportingtable['rows'][row],
                                verifyPageType,
                                'Requiring Supporting Documents',
                              );
                            }}
                            disableSelect
                            disableSearch
                            formattedColumns={
                              Object.keys(supportingtable).length > 0
                                ? supportingtable['formattedColumns']
                                  ? supportingtable['formattedColumns']
                                  : {}
                                : {}
                            }
                            columnExtensions={
                              Object.keys(supportingtable).length > 0
                                ? supportingtable['columnExtensions']
                                  ? supportingtable['columnExtensions']
                                  : []
                                : []
                            }
                            filterExtensions={
                              Object.keys(supportingtable).length > 0
                                ? supportingtable['filterExtensions']
                                  ? supportingtable['filterExtensions']
                                  : []
                                : []
                            }
                            defaultFilter={this.state.othervalidationFilter}
                            onFilterChange={this.handleFilterChange(
                              'supportingFilter',
                            )}
                            onSortingChange={this.handleSortingChange(
                              'supportingtable',
                            )}
                            currentPage={this.state.currentTablePage}
                            setCurrentPage={(page: number) => {
                              this.setState({ currentTablePage: page });
                            }}
                            pageSize={pageSizes.supportingtable}
                            onPageSizeChange={(pageSize: number) => {
                              this.handlePageSizeChange(
                                'other_validation_table',
                                pageSize,
                              );
                            }}
                            // handleClickEdit={this.handleClickEdit}
                            handleClickEdit={
                              (
                              // isEdit?: boolean,
                              anchorEl: HTMLAnchorElement | null,
                              row: number,
                              memberFileUploadDetails: any
                            ) => {

                              this.handleClickEdit(
                                anchorEl,
                                this.state.rowDataForEdit,
                                 verifyPageType,
                                  'supporting',
                                  'Supporting',
                                  true,
                                  row,
                                  // supportingtable['rows'],
                                  supportingtable['rows'][row[0]],
                                  memberFileUploadDetails,
                              )
                             }
                            }
                            handleClickInfo={this.handleClickInfo}
                            handleClickUpload={(
                              isUpload: boolean,
                              row: number[],
                            ) => {
                              // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                              this.handleClickUploadNew(
                                verifyPageType,
                                'supporting',
                                'Supporting',
                                isUpload,
                                row[0],
                                supportingtable['rows'][row[0]],
                              );
                            }}
                            allowedVerifyActions={{
                              approve: GlobalFunction.checkUserRbacByPolicy(
                                'MS41',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              disapprove: GlobalFunction.checkUserRbacByPolicy(
                                'MS42',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              forConfirm: GlobalFunction.checkUserRbacByPolicy(
                                'MS43',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                            }}
                            ocp={ocpPath}
                            onBubbleChatSelected={this.handleBubbleChat}
                            currentTab={'supporting'}
                            dateSorting={this.state.dateSorting}
                          />
                        </Grid>
                      </Grid>
                    </>
                  ) : verifyPageType === 'default' && tab === 6 ? (
                    <>
                      <Grid container style={{ backgroundColor: '#F5F7FB' }}>
                        <Grid item xs={12}>
                          <TableComponent
                            id="tab-disapprove"
                            data-cy="tab-disapprove"
                            rows={
                              Object.keys(disapprovetable).length > 0
                                ? disapprovetable['rows']
                                  ? disapprovetable['rows']
                                  : []
                                : []
                            }
                            columns={
                              Object.keys(disapprovetable).length > 0
                                ? disapprovetable['columns']
                                  ? disapprovetable['columns']
                                  : []
                                : []
                            }
                            intgFltrColumnExtensions={get(
                              disapprovetable,
                              'intgFltrColumnExtensions',
                              [],
                            )}
                            message="No available data"
                            onClickRow={(row: number[]) =>
                              this.handleClickRow(
                                row[0],
                                'disapprovetable',
                                disapprovetable['rows'][row[0]],
                              )
                            }
                            onSelectedRadio={(row: number, value: string) => {
                              console.log('disapproved', row, value);
                              this.handleSelectedRadio(
                                row,
                                value,
                                'disapprovetable',
                                disapprovetable['rows'][row],
                              );
                            }}
                            disableSelect
                            disableSearch
                            formattedColumns={
                              Object.keys(disapprovetable).length > 0
                                ? disapprovetable['formattedColumns']
                                  ? disapprovetable['formattedColumns']
                                  : {}
                                : {}
                            }
                            columnExtensions={
                              Object.keys(disapprovetable).length > 0
                                ? disapprovetable['columnExtensions']
                                  ? disapprovetable['columnExtensions']
                                  : []
                                : []
                            }
                            filterExtensions={
                              Object.keys(disapprovetable).length > 0
                                ? disapprovetable['filterExtensions']
                                  ? disapprovetable['filterExtensions']
                                  : []
                                : []
                            }
                            defaultFilter={this.state.disapprovedFilter}
                            onFilterChange={this.handleFilterChange(
                              'disapprovedFilter',
                            )}
                            onSortingChange={this.handleSortingChange(
                              'disapprovetable',
                            )}
                            currentPage={this.state.currentTablePage}
                            setCurrentPage={(page: number) => {
                              this.setState({ currentTablePage: page });
                            }}
                            pageSize={pageSizes.disapprove_table}
                            onPageSizeChange={(pageSize: number) => {
                              this.handlePageSizeChange(
                                'disapprove_table',
                                pageSize,
                              );
                            }}
                            // handleClickEdit={this.handleClickEdit}
                            handleClickEdit={
                              (
                              // isEdit?: boolean,
                              anchorEl: HTMLAnchorElement | null,
                              row: number,
                              memberFileUploadDetails: any
                            ) => {

                              this.handleClickEdit(
                                  anchorEl,
                                 this.state.rowDataForEdit,
                                  verifyPageType,
                                  'disapprove',
                                  'Disapproved',
                                  true,
                                  row,
                                  disapprovetable['rows'][row[0]],
                                  memberFileUploadDetails,
                              )
                             }
                            }
                            handleClickUpload={(
                              isUpload: boolean,
                              row: number[],
                            ) => {
                              // this.handleClickUpload(verifyPageType, 'disapprove', 'Disapproved')
                              this.handleClickUploadNew(
                                verifyPageType,
                                'disapprove',
                                'Disapproved',
                                isUpload,
                                row[0],
                                disapprovetable['rows'][row[0]],
                              );
                            }}
                            handleClickInfo={this.handleClickInfo}
                            allowedVerifyActions={{
                              approve: GlobalFunction.checkUserRbacByPolicy(
                                'MS41',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              disapprove: GlobalFunction.checkUserRbacByPolicy(
                                'MS42',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                              forConfirm: GlobalFunction.checkUserRbacByPolicy(
                                'MS43',
                                this.state.verifierData,
                                this.state.loading_state,
                              ),
                            }}
                            memberUploadDetails={this.state.member_upload_data}
                            ocp={ocpPath}
                            onBubbleChatSelected={this.handleBubbleChat}
                            currentTab={'disapproved'}
                            dateSorting={this.state.dateSorting}
                          />
                        </Grid>
                      </Grid>
                    </>
                  ) : verifyPageType === 'default' && tab === 7 ? (
                    <>
                      {/* {console.log('edit debug1', verifyPageType)} */}
                      <TableComponent
                        id="tab-approve"
                        data-cy="tab-approve"
                        rows={
                          Object.keys(approvedtable).length > 0
                            ? approvedtable['rows']
                              ? approvedtable['rows']
                              : []
                            : []
                        }
                        columns={
                          Object.keys(approvedtable).length > 0
                            ? approvedtable['columns']
                              ? approvedtable['columns']
                              : []
                            : []
                        }
                        intgFltrColumnExtensions={get(
                          approvedtable,
                          'intgFltrColumnExtensions',
                          [],
                        )}
                        message="No available data"
                        onClickRow={(row: number[]) =>
                          this.handleClickRow(
                            row[0],
                            'approvedtable',
                            approvedtable['rows'][row[0]],
                          )
                        }
                        disableSelect
                        disableSearch
                        formattedColumns={
                          Object.keys(approvedtable).length > 0
                            ? approvedtable['formattedColumns']
                              ? approvedtable['formattedColumns']
                              : {}
                            : {}
                        }
                        columnExtensions={
                          Object.keys(approvedtable).length > 0
                            ? approvedtable['columnExtensions']
                              ? approvedtable['columnExtensions']
                              : []
                            : []
                        }
                        filterExtensions={
                          Object.keys(approvedtable).length > 0
                            ? approvedtable['filterExtensions']
                              ? approvedtable['filterExtensions']
                              : []
                            : []
                        }
                        defaultFilter={this.state.approvedFilter}
                        onFilterChange={this.handleFilterChange(
                          'approvedFilter',
                        )}
                        onSortingChange={this.handleSortingChange(
                          'approvedtable',
                        )}
                        currentPage={this.state.currentTablePage}
                        setCurrentPage={(page: number) => {
                          this.setState({ currentTablePage: page });
                        }}
                        pageSize={pageSizes.approved_table}
                        onPageSizeChange={(pageSize: number) => {
                          this.handlePageSizeChange('approved_table', pageSize);
                        }}
                        // handleClickEdit={this.handleClickEdit}
                      handleClickEdit={
                        (
                        // isEdit?: boolean,
                        anchorEl: HTMLAnchorElement | null,
                        row: number,
                        memberFileUploadDetails: any
                      ) => {

                        this.handleClickEdit(
                            anchorEl,
                           this.state.rowDataForEdit,
                            verifyPageType,
                            'othervalidation',
                            'Other Validation',
                            true,
                            row,
                            approvedtable['rows'][row[0]],
                            memberFileUploadDetails,
                        )
                       }
                      }
                        currentTab={'approved'}
                        dateSorting={this.state.dateSorting}
                      />
                    </>
                  ) : verifyPageType === 'default' && tab === 8 ? (
                    <>
                      {console.log('Default Tab 8 - ALL verify')}
                      <TableComponent
                        id="tab-all"
                        rows={
                          Object.keys(alltable).length > 0
                            ? alltable['rows']
                              ? alltable['rows']
                              : []
                            : []
                        }
                        columns={
                          Object.keys(alltable).length > 0
                            ? alltable['columns']
                              ? alltable['columns']
                              : []
                            : []
                        }
                        intgFltrColumnExtensions={get(
                          alltable,
                          'intgFltrColumnExtensions',
                          [],
                        )}
                        message="No available data"
                        onClickRow={(row: number[]) =>
                          this.handleClickRow(
                            row[0],
                            'alltable',
                            alltable['rows'][row[0]],
                          )
                        }
                        disableSelect
                        disableSearch
                        formattedColumns={
                          Object.keys(alltable).length > 0
                            ? alltable['formattedColumns']
                              ? alltable['formattedColumns']
                              : {}
                            : {}
                        }
                        columnExtensions={
                          Object.keys(alltable).length > 0
                            ? alltable['columnExtensions']
                              ? alltable['columnExtensions']
                              : []
                            : []
                        }
                        defaultFilter={this.state.allFilter}
                        onFilterChange={this.handleFilterChange('allFilter')}
                        onSortingChange={this.handleSortingChange('alltable')}
                        currentPage={this.state.currentTablePage}
                        setCurrentPage={(page: number) => {
                          this.setState({ currentTablePage: page });
                        }}
                        pageSize={pageSizes.all_table}
                        onPageSizeChange={(pageSize: number) => {
                          this.handlePageSizeChange('all_table', pageSize);
                        }}
                      />
                    </>
                  ) : //~ TABLES FOR VERIFY EDIT STARTS HERE
                  verifyPageType === 'edit' && tab === 0 ? (
                    
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      {console.log('verify edit tab0', )}
                      {/* <Grid item xs={12} style={{ marginBottom: '30px' }}>
                      <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          1
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Members with Conflict on Data ({verifyEditTable['conflictOnData']
                          && verifyEditTable['conflictOnData']['rows']
                          ? verifyEditTable['conflictOnData']['rows'].length : 0})
                      </Typography>
                    </Grid> */}
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-conflictOnData"
                          data-cy="tab-conflictOnData"
                          rows={
                            verifyEditTable['conflictOnData']
                              ? Object.keys(verifyEditTable['conflictOnData'])
                                  .length > 0
                                ? verifyEditTable['conflictOnData']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['conflictOnData']
                              ? Object.keys(verifyEditTable['conflictOnData'])
                                  .length > 0
                                ? verifyEditTable['conflictOnData']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['conflictOnData']
                              ? get(
                                  verifyEditTable['conflictOnData'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'conflictOnData',
                              verifyEditTable['conflictOnData']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'conflictOnData',
                              verifyEditTable['conflictOnData']['rows'][row]
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['conflictOnData']
                              ? Object.keys(
                                  verifyEditTable['conflictOnData'],
                                ).length > 0
                                ? verifyEditTable['conflictOnData'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['conflictOnData']
                              ? Object.keys(
                                  verifyEditTable['conflictOnData'],
                                ).length > 0
                                ? verifyEditTable['conflictOnData'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['conflictOnData']
                              ? Object.keys(
                                  verifyEditTable['conflictOnData'],
                                ).length > 0
                                ? verifyEditTable['conflictOnData'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['conflictOnData']}
                          onFilterChange={this.handleFilterChange(
                            'conflictOnData',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'conflictOnData',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['conflictOnData']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'conflictOnData',
                              pageSize,
                            );
                          }}
                          // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {
                            console.log("verifyEditTable['conflictOnData']", verifyEditTable['conflictOnData'])
                            this.handleClickEdit(
                                anchorEl,
                              //  this.state.rowDataForEdit,
                                verifyEditTable['conflictOnData']['rows'][row[0]],
                                verifyPageType,
                                'conflictOnData',
                                'Conflict on Data',
                                true,
                                row,
                                verifyEditTable['conflictOnData']['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }
                          // handleClickUpload={this.handleClickUpload(verifyPageType, 'conflictOnData', 'Conflict on Data')}
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'conflictOnData',
                              'Conflict on Data',
                              isUpload,
                              row[0],
                              verifyEditTable['conflictOnData']['rows'][row[0]],
                            );
                          }}
                          handleClickInfo={this.handleClickInfo}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'edit' && tab === 1 ? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      {/* <Grid item xs={12} style={{ marginBottom: '30px' }}>
                      <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          2
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Members for Validation ({verifyEditTable['forValidation']
                          && verifyEditTable['forValidation']['rows']
                          ? verifyEditTable['forValidation']['rows'].length : 0})
                      </Typography>
                    </Grid> */}
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-forValidation"
                          data-cy="tab-forValidation"
                          rows={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['forValidation']
                              ? get(
                                  verifyEditTable['forValidation'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'forValidation',
                              verifyEditTable['forValidation']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'forValidation',
                              verifyEditTable['forValidation']['rows'][row]
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['forValidation']}
                          onFilterChange={this.handleFilterChange(
                            'forValidation',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'forValidation',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['forValidation']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'forValidation',
                              pageSize,
                            );
                          }}
                          // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {

                            this.handleClickEdit(
                                anchorEl,
                              //  this.state.rowDataForEdit,
                                verifyEditTable['forValidation']['rows'][row[0]],
                                verifyPageType,
                                'forValidation',
                                'For Validation',
                                true,
                                row,
                                verifyEditTable['forValidation']['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }
                          // handleClickUpload={this.handleClickUpload(verifyPageType, 'forValidation', 'For Validation')}
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'forValidation',
                              'For Validation',
                              isUpload,
                              row[0],
                              verifyEditTable['forValidation']['rows'][row[0]],
                            );
                          }}
                          handleClickInfo={this.handleClickInfo}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'edit' && tab === 2 ? (

                    <Grid container style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}>
                    {/* <Grid item xs={12} style={{ marginBottom: '30px' }}>
                      <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          3
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Terminated Members ({verifyEditTable['terminatedMembers']
                          && verifyEditTable['terminatedMembers']['rows']
                          ? verifyEditTable['terminatedMembers']['rows'].length : 0})
                      </Typography>
                    </Grid> */}
                    <Grid item xs={12}>
                      <TableComponent
                        id="tab-supportings"
                        data-cy='tab-supportings'
                        rows={
                          verifyEditTable['supportings'] ?
                            Object.keys(verifyEditTable['supportings']).length > 0
                              ? verifyEditTable['supportings']['rows']
                              : [] : []
                        }
                        columns={
                          verifyEditTable['supportings'] ?
                            Object.keys(verifyEditTable['supportings']).length > 0
                              ? verifyEditTable['supportings']['columns']
                              : [] : []
                        }
                        intgFltrColumnExtensions={
                          verifyEditTable['supportings'] ?
                            get(
                              verifyEditTable['supportings'],
                              'intgFltrColumnExtensions',
                              [],
                            ) : []
                        }
                        message="No available data"
                        onClickRow={(row: number[]) =>
                          this.handleClickRow(row[0], 'supportings', verifyEditTable['supportings']['rows'][row[0]])}
                        onSelectedRadio={(row: number, value: string) =>
                          this.handleSelectedRadio(row, 
                            value, 
                            'supportings',
                            verifyEditTable['supportings']['rows'][row]
                            )
                        }
                        disableSelect
                        disableSearch
                        formattedColumns={
                          verifyEditTable['supportings'] ?
                            Object.keys(verifyEditTable['supportings']).length > 0
                              ? verifyEditTable['supportings']['formattedColumns']
                              : {} : {}
                        }
                        columnExtensions={
                          verifyEditTable['supportings'] ?
                            Object.keys(verifyEditTable['supportings']).length > 0
                              ? verifyEditTable['supportings']['columnExtensions']
                              : [] : []
                        }
                        filterExtensions={
                          verifyEditTable['supportings'] ?
                            Object.keys(verifyEditTable['supportings']).length > 0
                              ? verifyEditTable['supportings']['filterExtensions']
                              : [] : []
                        }
                        defaultFilter={verifyEditFilters['supportings']}
                        onFilterChange={this.handleFilterChange('supportings')}
                        onSortingChange={this.handleSortingChange('supportings')}
                        currentPage={this.state.currentTablePage}
                        setCurrentPage={(page: number) => {
                          this.setState({ currentTablePage: page });
                        }}
                        pageSize={verifyEditPageSize['supportings']}
                        onPageSizeChange={(pageSize: number) => {
                          this.handlePageSizeChange('supportings', pageSize);
                        }}
                        // handleClickEdit={this.handleClickEdit}
                        handleClickEdit={
                          (
                          // isEdit?: boolean,
                          anchorEl: HTMLAnchorElement | null,
                          row: number,
                          memberFileUploadDetails: any
                        ) => {

                          this.handleClickEdit(
                              anchorEl,
                            //  this.state.rowDataForEdit,
                              verifyEditTable['supportings']['rows'][row[0]],
                              verifyPageType,
                              'supportings',
                              'Supportings',
                              true,
                              row,
                              verifyEditTable['supportings']['rows'][row[0]],
                              memberFileUploadDetails,
                          )
                         }
                        }
                        handleClickUpload={(isUpload: boolean, row: number[]) => {
                          this.handleClickUploadNew(verifyPageType, 'supportings', 'Supporting', isUpload, row[0], verifyEditTable['supportings']['rows'][row[0]])
                        }}
                        handleClickInfo={this.handleClickInfo}
                        selectedTab={tab}
                        allowedVerifyActions={{
                          approve: GlobalFunction.checkUserRbacByPolicy('MS41', this.state.verifierData, this.state.loading_state),
                          disapprove: GlobalFunction.checkUserRbacByPolicy('MS42', this.state.verifierData, this.state.loading_state),
                          forConfirm: GlobalFunction.checkUserRbacByPolicy('MS43', this.state.verifierData, this.state.loading_state),
                        }}
                      />
                    </Grid>
                  </Grid>
                ) : verifyPageType === 'edit' && tab === 3 ? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      {/* <Grid item xs={12} style={{ marginBottom: '30px' }}>
                      <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          3
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Terminated Members ({verifyEditTable['terminatedMembers']
                          && verifyEditTable['terminatedMembers']['rows']
                          ? verifyEditTable['terminatedMembers']['rows'].length : 0})
                      </Typography>
                    </Grid> */}
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-approveMembers"
                          data-cy="tab-approveMembers"
                          rows={
                            verifyEditTable['approveMembers']
                              ? Object.keys(
                                  verifyEditTable['approveMembers'],
                                ).length > 0
                                ? verifyEditTable['approveMembers']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['approveMembers']
                              ? Object.keys(
                                  verifyEditTable['approveMembers'],
                                ).length > 0
                                ? verifyEditTable['approveMembers'][
                                    'columns'
                                  ]
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['approveMembers']
                              ? get(
                                  verifyEditTable['approveMembers'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'approveMembers',
                              verifyEditTable['approveMembers']['rows'][
                                row[0]
                              ],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'approveMembers',
                              verifyEditTable['approveMembers']['rows'][row]
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['approveMembers']
                              ? Object.keys(
                                  verifyEditTable['approveMembers'],
                                ).length > 0
                                ? verifyEditTable['approveMembers'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['approveMembers']
                              ? Object.keys(
                                  verifyEditTable['approveMembers'],
                                ).length > 0
                                ? verifyEditTable['approveMembers'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['approveMembers']
                              ? Object.keys(
                                  verifyEditTable['approveMembers'],
                                ).length > 0
                                ? verifyEditTable['approveMembers'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['approveMembers']}
                          onFilterChange={this.handleFilterChange(
                            'approveMembers',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'approveMembers',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['approveMembers']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'approveMembers',
                              pageSize,
                            );
                          }}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {

                            this.handleClickEdit(
                                anchorEl,
                              //  this.state.rowDataForEdit,
                                verifyEditTable['approveMembers']['rows'][row[0]],
                                verifyPageType,
                                'approveMembers',
                                'approveMembers',
                                true,
                                row,
                                verifyEditTable['approveMembers']['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'approveMembers',
                              'approveMembers',
                              isUpload,
                              row[0],
                              verifyEditTable['approveMembers']['rows'][
                                row[0]
                              ],
                            );
                          }}
                          handleClickInfo={this.handleClickInfo}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'edit' && tab === 4? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                 
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-activeMembers"
                          data-cy="tab-activeMembers"
                          rows={
                            verifyEditTable['disapproveMembers']
                              ? Object.keys(verifyEditTable['disapproveMembers'])
                                  .length > 0
                                ? verifyEditTable['disapproveMembers']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['disapproveMembers']
                              ? Object.keys(verifyEditTable['disapproveMembers'])
                                  .length > 0
                                ? verifyEditTable['disapproveMembers']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['disapproveMembers']
                              ? get(
                                  verifyEditTable['disapproveMembers'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'activeMembers',
                              verifyEditTable['disapproveMembers']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'disapproveMembers',
                              verifyEditTable['disapproveMembers']['rows'][row]
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['disapproveMembers']
                              ? Object.keys(verifyEditTable['disapproveMembers'])
                                  .length > 0
                                ? verifyEditTable['disapproveMembers'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }

                        
                          columnExtensions={
                            verifyEditTable['disapproveMembers']
                              ? Object.keys(verifyEditTable['disapproveMembers'])
                                  .length > 0
                                ? verifyEditTable['disapproveMembers'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['disapproveMembers']
                              ? Object.keys(verifyEditTable['disapproveMembers'])
                                  .length > 0
                                ? verifyEditTable['disapproveMembers'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['activeMembers']}
                          onFilterChange={this.handleFilterChange(
                            'activeMembers',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'activeMembers',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['activeMembers']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'activeMembers',
                              pageSize,
                            );
                          }}
                          // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {

                            this.handleClickEdit(
                                anchorEl,
                              //  this.state.rowDataForEdit,
                                verifyEditTable['activeMembers']['rows'][row[0]],
                                verifyPageType,
                                'activeMembers',
                                'Active',
                                true,
                                row,
                                verifyEditTable['activeMembers']['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }

                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'activeMembers',
                              'Active',
                              isUpload,
                              row[0],
                              verifyEditTable['activeMembers']['rows'][row[0]],
                            );
                          }}
                          handleClickInfo={this.handleClickInfo}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'edit' && tab === 5? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
               
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-other"
                          data-cy="tab-other"
                          rows={
                            verifyEditTable['other']
                              ? Object.keys(verifyEditTable['other']).length > 0
                                ? verifyEditTable['other']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['other']
                              ? Object.keys(verifyEditTable['other']).length > 0
                                ? verifyEditTable['other']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['other']
                              ? get(
                                  verifyEditTable['other'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'other',
                              verifyEditTable['other']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(row, value, 'other',verifyEditTable['other']['rows'][row])
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['other']
                              ? Object.keys(verifyEditTable['other']).length > 0
                                ? verifyEditTable['other']['formattedColumns']
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['other']
                              ? Object.keys(verifyEditTable['other']).length > 0
                                ? verifyEditTable['other']['columnExtensions']
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['other']
                              ? Object.keys(verifyEditTable['other']).length > 0
                                ? verifyEditTable['other']['filterExtensions']
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['other']}
                          onFilterChange={this.handleFilterChange('other')}
                          onSortingChange={this.handleSortingChange('other')}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['other']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange('other', pageSize);
                          }}
                          // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any
                          ) => {

                            this.handleClickEdit(
                                anchorEl,
                              //  this.state.rowDataForEdit,
                                verifyEditTable['other']['rows'][row[0]],
                                verifyPageType,
                                'other',
                                'Other',
                                true,
                                row,
                                verifyEditTable['other']['rows'][row[0]],
                                memberFileUploadDetails,
                            )
                           }
                          }
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'other',
                              'Other',
                              isUpload,
                              row[0],
                              verifyEditTable['other']['rows'][row[0]],
                            );
                          }}
                          handleClickInfo={this.handleClickInfo}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'vd' && tab === 0 ? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      <Grid item xs={12} style={{ marginBottom: '30px' }}>
                        {/* <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          1
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Members with Incomplete Requirements ({verifyEditTable['forValidation']
                          && verifyEditTable['forValidation']['rows']
                          ? verifyEditTable['forValidation']['rows'].length : 0})
                      </Typography> */}
                      </Grid>
                      {console.log('verify dependent tab0', this.state )}
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-forValidation"
                          data-cy="tab-forValidation"
                          rows={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['forValidation']
                              ? get(
                                  verifyEditTable['forValidation'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'forValidation',
                              verifyEditTable['forValidation']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'forValidation',
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['forValidation']
                              ? Object.keys(verifyEditTable['forValidation'])
                                  .length > 0
                                ? verifyEditTable['forValidation'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['forValidation']}
                          onFilterChange={this.handleFilterChange(
                            'forValidation',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'forValidation',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['forValidation']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'forValidation',
                              pageSize,
                            );
                          }}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any,
                            // memberDataEdit:any
                          ) => {
                            this.handleClickEdit(
                                anchorEl,
                               this.state.verifyEditTable                               ,
                                verifyPageType,
                                'terminatedMembers',
                                'Terminated',
                                true,
                                row,
                                row,
                                // this.state.verifyEditTable['terminatedMembers']['rows'][row[0]],
                                memberFileUploadDetails,
                                // memberDataEdit //just now
                            )
                           }
                          }
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'forValidation',
                              'For Validation',
                              isUpload,
                              row[0],
                              verifyEditTable['forValidation']['rows'][row[0]],
                            );
                          }}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'vd' && tab === 1 ? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      <Grid item xs={12} style={{ marginBottom: '30px' }}>
                        {/* <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          2
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Terminated Members ({verifyEditTable['terminatedMembers']
                          && verifyEditTable['terminatedMembers']['rows']
                          ? verifyEditTable['terminatedMembers']['rows'].length : 0})
                      </Typography> */}
                      </Grid>
                      {/* {console.log('verify edit table tab terminated1', verifyPageType)} */}
                      <Grid item xs={12}>
                      {/* {console.log('verify dependent tab1', this.state )} */}
                        <TableComponent
                          id="tab-terminatedMembers"
                          data-cy="tab-terminatedMembers"
                          rows={
                            verifyEditTable['terminatedMembers']
                              ? Object.keys(
                                  verifyEditTable['terminatedMembers'],
                                ).length > 0
                                ? verifyEditTable['terminatedMembers']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['terminatedMembers']
                              ? Object.keys(
                                  verifyEditTable['terminatedMembers'],
                                ).length > 0
                                ? verifyEditTable['terminatedMembers'][
                                    'columns'
                                  ]
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['terminatedMembers']
                              ? get(
                                  verifyEditTable['terminatedMembers'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'terminatedMembers',
                              verifyEditTable['terminatedMembers']['rows'][
                                row[0]
                              ],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'terminatedMembers',
                              verifyEditTable['terminatedMembers']['rows'][row]
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['terminatedMembers']
                              ? Object.keys(
                                  verifyEditTable['terminatedMembers'],
                                ).length > 0
                                ? verifyEditTable['terminatedMembers'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['terminatedMembers']
                              ? Object.keys(
                                  verifyEditTable['terminatedMembers'],
                                ).length > 0
                                ? verifyEditTable['terminatedMembers'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['terminatedMembers']
                              ? Object.keys(
                                  verifyEditTable['terminatedMembers'],
                                ).length > 0
                                ? verifyEditTable['terminatedMembers'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['terminatedMembers']}
                          onFilterChange={this.handleFilterChange(
                            'terminatedMembers',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'terminatedMembers',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['terminatedMembers']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'terminatedMembers',
                              pageSize,
                            );
                          }}
                          // handleClickEdit={this.handleClickEdit}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any,
                            // memberDataEdit:any
                          ) => {
                            this.handleClickEdit(
                                anchorEl,
                               this.state.verifyEditTable                               ,
                                verifyPageType,
                                'terminatedMembers',
                                'Terminated',
                                true,
                                row,
                                row,
                                // this.state.verifyEditTable['terminatedMembers']['rows'][row[0]],
                                memberFileUploadDetails,
                                // memberDataEdit //just now
                            )
                           }
                          }
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'terminatedMembers',
                              'Terminated',
                              isUpload,
                              row[0],
                              verifyEditTable['terminatedMembers']['rows'][row[0]],
                            );
                          }}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  ) : verifyPageType === 'vd' && tab === 2 ? (
                    <Grid
                      container
                      style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}
                    >
                      <Grid item xs={12} style={{ marginBottom: '30px' }}>
                        {/* <div style={{ marginRight: '10px', display: 'inline-block' }}>
                        <Avatar style={{
                          backgroundColor: '#3AB77D',
                          width: '30px', height: '30px'
                        }}>
                          3
                        </Avatar>
                      </div>
                      <Typography className={clsx('sub-title')}
                        style={{ display: 'inline-block' }}
                        color="textPrimary">
                        Verify Active Members ({verifyEditTable['activeMembers']
                          && verifyEditTable['activeMembers']['rows']
                          ? verifyEditTable['activeMembers']['rows'].length : 0})
                      </Typography> */}
                      </Grid>
                      <Grid item xs={12}>
                        <TableComponent
                          id="tab-activeMembers"
                          data-cy="tab-activeMembers"
                          rows={
                            verifyEditTable['activeMembers']
                              ? Object.keys(verifyEditTable['activeMembers'])
                                  .length > 0
                                ? verifyEditTable['activeMembers']['rows']
                                : []
                              : []
                          }
                          columns={
                            verifyEditTable['activeMembers']
                              ? Object.keys(verifyEditTable['activeMembers'])
                                  .length > 0
                                ? verifyEditTable['activeMembers']['columns']
                                : []
                              : []
                          }
                          intgFltrColumnExtensions={
                            verifyEditTable['activeMembers']
                              ? get(
                                  verifyEditTable['activeMembers'],
                                  'intgFltrColumnExtensions',
                                  [],
                                )
                              : []
                          }
                          message="No available data"
                          onClickRow={(row: number[]) =>
                            this.handleClickRow(
                              row[0],
                              'activeMembers',
                              verifyEditTable['activeMembers']['rows'][row[0]],
                            )
                          }
                          onSelectedRadio={(row: number, value: string) =>
                            this.handleSelectedRadio(
                              row,
                              value,
                              'activeMembers',
                            )
                          }
                          disableSelect
                          disableSearch
                          formattedColumns={
                            verifyEditTable['activeMembers']
                              ? Object.keys(verifyEditTable['activeMembers'])
                                  .length > 0
                                ? verifyEditTable['activeMembers'][
                                    'formattedColumns'
                                  ]
                                : {}
                              : {}
                          }
                          columnExtensions={
                            verifyEditTable['activeMembers']
                              ? Object.keys(verifyEditTable['activeMembers'])
                                  .length > 0
                                ? verifyEditTable['activeMembers'][
                                    'columnExtensions'
                                  ]
                                : []
                              : []
                          }
                          filterExtensions={
                            verifyEditTable['activeMembers']
                              ? Object.keys(verifyEditTable['activeMembers'])
                                  .length > 0
                                ? verifyEditTable['activeMembers'][
                                    'filterExtensions'
                                  ]
                                : []
                              : []
                          }
                          defaultFilter={verifyEditFilters['activeMembers']}
                          onFilterChange={this.handleFilterChange(
                            'activeMembers',
                          )}
                          onSortingChange={this.handleSortingChange(
                            'activeMembers',
                          )}
                          currentPage={this.state.currentTablePage}
                          setCurrentPage={(page: number) => {
                            this.setState({ currentTablePage: page });
                          }}
                          pageSize={verifyEditPageSize['activeMembers']}
                          onPageSizeChange={(pageSize: number) => {
                            this.handlePageSizeChange(
                              'activeMembers',
                              pageSize,
                            );
                          }}
                          handleClickEdit={
                            (
                            // isEdit?: boolean,
                            anchorEl: HTMLAnchorElement | null,
                            row: number,
                            memberFileUploadDetails: any,
                            // memberDataEdit:any
                          ) => {
                            this.handleClickEdit(
                                anchorEl,
                               this.state.verifyEditTable                               ,
                                verifyPageType,
                                'terminatedMembers',
                                'Terminated',
                                true,
                                row,
                                row,
                                // this.state.verifyEditTable['terminatedMembers']['rows'][row[0]],
                                memberFileUploadDetails,
                                // memberDataEdit //just now
                            )
                           }
                          }
                          handleClickUpload={(
                            isUpload: boolean,
                            row: number[],
                          ) => {
                            this.handleClickUploadNew(
                              verifyPageType,
                              'activeMembers',
                              'Active',
                              isUpload,
                              row[0],
                              verifyEditTable['activeMembers']['rows'][row[0]],
                            );
                          }}
                          selectedTab={tab}
                          allowedVerifyActions={{
                            approve: GlobalFunction.checkUserRbacByPolicy(
                              'MS41',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            disapprove: GlobalFunction.checkUserRbacByPolicy(
                              'MS42',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                            forConfirm: GlobalFunction.checkUserRbacByPolicy(
                              'MS43',
                              this.state.verifierData,
                              this.state.loading_state,
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                  //   ) : verifyPageType === 'vd' && tab === 3 ? (
                  //     <Grid container style={{ paddingTop: '30px', backgroundColor: '#F5F7FB' }}>
                  //       <Grid item xs={12} style={{ marginBottom: '30px' }}>
                  //         {/* <div style={{ marginRight: '10px', display: 'inline-block' }}>
                  //           <Avatar style={{
                  //             backgroundColor: '#3AB77D',
                  //             width: '30px', height: '30px'
                  //           }}>
                  //             3
                  //           </Avatar>
                  //         </div>
                  //         <Typography className={clsx('sub-title')}
                  //           style={{ display: 'inline-block' }}
                  //           color="textPrimary">
                  //           Verify Active Members ({verifyEditTable['activeMembers']
                  //             && verifyEditTable['activeMembers']['rows']
                  //             ? verifyEditTable['activeMembers']['rows'].length : 0})
                  //         </Typography> */}
                  //       </Grid>
                  //       <Grid item xs={12}>
                  //         <TableComponent
                  //           id="tab-supportings"
                  //           data-cy='tab-supportings'
                  //           rows={
                  //             verifyEditTable['supportings'] ?
                  //               Object.keys(verifyEditTable['supportings']).length > 0
                  //                 ? verifyEditTable['supportings']['rows']
                  //                 : [] : []
                  //           }
                  //           columns={
                  //             verifyEditTable['supportings'] ?
                  //               Object.keys(verifyEditTable['supportings']).length > 0
                  //                 ? verifyEditTable['supportings']['columns']
                  //                 : [] : []
                  //           }
                  //           intgFltrColumnExtensions={
                  //             verifyEditTable['supportings'] ?
                  //               get(
                  //                 verifyEditTable['supportings'],
                  //                 'intgFltrColumnExtensions',
                  //                 [],
                  //               ) : []
                  //           }
                  //           message="No available data"
                  //           onClickRow={(row: number[]) =>
                  //             this.handleClickRow(row[0], 'supportings', verifyEditTable['supportings']['rows'][row[0]])}
                  //           onSelectedRadio={(row: number, value: string) =>
                  //             this.handleSelectedRadio(row, value, 'supportings')
                  //           }
                  //           disableSelect
                  //           disableSearch
                  //           formattedColumns={
                  //             verifyEditTable['supportings'] ?
                  //               Object.keys(verifyEditTable['supportings']).length > 0
                  //                 ? verifyEditTable['supportings']['formattedColumns']
                  //                 : {} : {}
                  //           }
                  //           columnExtensions={
                  //             verifyEditTable['supportings'] ?
                  //               Object.keys(verifyEditTable['supportings']).length > 0
                  //                 ? verifyEditTable['supportings']['columnExtensions']
                  //                 : [] : []
                  //           }
                  //           filterExtensions={
                  //             verifyEditTable['supportings'] ?
                  //               Object.keys(verifyEditTable['supportings']).length > 0
                  //                 ? verifyEditTable['supportings']['filterExtensions']
                  //                 : [] : []
                  //           }
                  //           defaultFilter={verifyEditFilters['supportings']}
                  //           onFilterChange={this.handleFilterChange('supportings')}
                  //           onSortingChange={this.handleSortingChange('supportings')}
                  //           currentPage={this.state.currentTablePage}
                  //           setCurrentPage={(page: number) => {
                  //             this.setState({ currentTablePage: page });
                  //           }}
                  //           pageSize={verifyEditPageSize['supportings']}
                  //           onPageSizeChange={(pageSize: number) => {
                  //             this.handlePageSizeChange('supportings', pageSize);
                  //           }}
                  //           handleClickEdit={this.handleClickEdit}
                  //           handleClickUpload={this.handleClickUpload(verifyPageType, 'supportings', 'For Validation')}
                  //           selectedTab={tab}
                  //           allowedVerifyActions={{
                  //             approve: GlobalFunction.checkUserRbacByPolicy('MS41', this.state.verifierData, this.state.loading_state),
                  //             disapprove: GlobalFunction.checkUserRbacByPolicy('MS42', this.state.verifierData, this.state.loading_state),
                  //             forConfirm: GlobalFunction.checkUserRbacByPolicy('MS43', this.state.verifierData, this.state.loading_state),
                  //           }}
                  //         />
                  //       </Grid>
                  //     </Grid>
                  ) : null}
                                 {/* //~ TABLES FOR VERIFY EDIT ENDS HERE */}
                </Grid>
                <Grid item xs={12}>
                  <Grid container alignItems="stretch">
                    <Grid item xs={12}>
                      <MyFloatingButtons
                        verifyPageType={verifyPageType}
                        floatingButtonActions={this.handleFloatingButtonActions}
                        currentUserData={this.state.verifierData}
                        isLoading={this.state.loading_state}
                      />
                    </Grid>
                  </Grid>
                  <TmpDataModal
                    onClose={this.handleTmpModalClose}
                    loadData={this.setTmpValue}
                    isModalOpen={tmpDataModal}
                    id="tmp-data-modal"
                  />
                  {partialtable !== undefined &&
                  partialtable.rows !== undefined &&
                  partialtable.rows.length > 0 ? (
                    <>
                    {/* {console.log('FINAL 2 partialmatchmodal', partialRow)} */}
                    <PartialMatchModal
                      isModalOpen={partialModalOpen}
                      id="partial-match-modal"
                      onClose={this.closePartialModal}
                      data={
                        partialtable !== undefined &&
                        partialtable.rows !== undefined
                          ? partialtable.rows
                          : []
                      }
                      row={partialRow}
                      onSave={this.savePartialModal}
                      onUpdateRow={(row: number) => {
                        this.setState({ partialRow: row });
                      }}
                    />
                    </>
                  ) : null}
                  {validationtable !== undefined &&
                  validationtable.rows !== undefined &&
                  validationtable.rows.length > 0 ? (
                    <MemberInfoModal
                      isModalOpen={matchPrincipalModalOpen}
                      id={'match-principal-modal'}
                      onClose={this.closeMatchPrincipalModal}
                      data={
                        validationtable !== undefined &&
                        validationtable.rows !== undefined
                          ? validationtable.rows
                          : []
                      }
                      row={validationRow}
                      memberDataFields={this.state.memberDataFields}
                      tableData={this.state.infoData}
                      principalData={this.state.selectedPrincipal}
                    />
                  ) : null}

                  <MemberFilesModal
                    isModalOpen={memberFilesModalOpen}
                    id="disapproved-member-files-modal"
                    row={row}
                    // data={this.state.memberData}
                    uploadDetails={this.state.memberFileUploadDetails}
                    pageType={this.state.verifyPageType}
                    onClose={this.closeMemberFilesModal}
                    onSubmit={(
                      row: number,
                      remarks: string,
                      user_remarks?: string,
                      details?: any,
                      rowData?: any,
                      data?:any,
                      openFileModal?:any,
                      uploadedDocuments?:any
                    ) => {
                      this.saveMemberFilesModal(row, remarks, user_remarks, details, rowData, data, openFileModal, uploadedDocuments)

                      }
                    }
                    onDelete={(row: number, name: string, details: any) => {
                      this.deleteMemberFiles(row, name, details);
                    }}
                  />
                  <ApproveMemberModal
                    isModalOpen={approveModalOpen}
                    id="approve-member-modal"
                    uploadDetails={this.state.memberFileUploadDetails}
                    row={row}
                    onClose={this.closeApproveModal}
                    // onClose={this.closeMemberFilesModal}
                    onApprove={(
                      row: number, 
                      data: any, 
                      remarks: any, 
                      user_remarks?:any, 
                      details?: any,
                      uploadedDocuments?:any
                    ) => {
                      // this.saveApproveModal(row, data, remarks)
                      // row: number, files: any, remarks: string, user_remarks?: any, details?:any
                      // this.saveApproveModal(row, data, remarks, user_remarks, details, )
                      this.saveApproveModal(row, data, remarks, user_remarks, details, data, uploadedDocuments )
                      // console.log('saveapprove', uploadedDocuments)

                      }
                    }
                    currentData={documentData}
                  />
                  <DisapproveMemberModal
                    isModalOpen={disapproveModalOpen}
                    id="disapprove-member-modal"
                    uploadDetails={this.state.memberFileUploadDetails}
                    row={row}
                    onClose={this.closeDisapproveModal}
                    onDisapprove={(row: number, remarks: string, userRemarks:string) =>
                      this.saveDisapproveModal(row, remarks, userRemarks)
                    }
                    currentRemarks={documentData}
                  />
                  <ForConfirmationModal
                    isModalOpen={forConfirmationModalOpen}
                    id="for-confirmation-modal"
                    row={row}
                    onClose={this.closeForConfirmationModal}
                    onConfirm={(row: number, remarks: string, userRemarks:string) => 
                      // {
                      this.saveForConfirmationModal(row, remarks, userRemarks)
                    
                    }
                    currentRemarks={documentData}
                  />
                  <AddActionRemarksModal
                    isModalOpen={isActionRemarksModalOpen}
                    row={actionRemarksModalProps.row}
                    tab={actionRemarksModalProps.tab}
                    action={actionRemarksModalProps.action}
                    actionValue={actionRemarksModalProps.actionValue}
                    onActionSubmit={this.onAddActionRemarksModalSubmit}
                    onClose={this.closeActionRemarksModal}
                    uploadDetails={this.state.memberFileUploadDetails}
                  />
                  <SnackbarNotification
                    isOpen={snackbarNotifProps.isOpen}
                    message={snackbarNotifProps.message}
                    variant={snackbarNotifProps.variant}
                    onSnackbarClose={() => {
                      this.closeSnackbarNotif();
                    }}
                  />
                  {/*
                const data = this.state.validationtable;
                data['rows'][row]
                */}
                  {this.state.validationtable['rows'] &&
                  this.state.validationtable['rows'][
                    approveDependent.row ? approveDependent.row : 0
                  ] &&
                  approveDependent.action ? (
                    <ApproveDependentModal
                      isModalOpen={approveDependent.isModalOpen === true}
                      action={approveDependent.action}
                      memberType={
                        this.state.validationtable['rows'][
                          approveDependent.row ? approveDependent.row : 0
                        ]['member_type']
                      }
                      memberName={
                        this.state.validationtable['rows'][
                          approveDependent.row ? approveDependent.row : 0
                        ]['member_name']
                      }
                      remarks={
                        this.state.validationtable['rows'][
                          approveDependent.row ? approveDependent.row : 0
                        ]['remarks']
                      }
                      selectedPrincipal={
                        this.state.validationtable['rows'][
                          approveDependent.row ? approveDependent.row : 0
                        ]['selected_principal']
                      }
                      possiblePrincipals={
                        approveDependent.possiblePrincipals
                          ? approveDependent.possiblePrincipals
                          : []
                      }
                      id="approve-dependent-modal"
                      row={approveDependent.row ? approveDependent.row : 0}
                      onClose={this.closeApproveDependentModal}
                      onAction={this.saveApproveDependentModal}
                    />
                  ) : null}
                </Grid>
              </Grid>
            </>
          ) : generateFlag && verifyPageType === 'default' ? (
            <>
              {/* {console.log(
                console.log(
                  'VerifyMemberPage 8888888',
                  JSON.parse(JSON.stringify(allData)),
                ),
              )} */}
              <ExceptionReport
                id={member_upload_data._id}
                pmaker_task={pmaker_task}
                case_id={this.props.match.params.ticket_id}
                client_id={clientId}
                client_name={corporateAccountName}
                client_accNo={corporateAccountNo}
                data={JSON.parse(JSON.stringify(allData))}
                memberUploadData={member_upload_data}
                email={member_upload_data.sender_email}
                handleBack={this.handleBack}
                handleCloseLostInternetConnectionModal={
                  this.handleCloseLostInternetConnectionModal
                }
                handleOpenLostInternetConnectionModal={
                  this.handleOpenLostInternetConnectionModal
                }
                handlePublishToOcp={this.handlePublishToOcp}
                ocpOrigin={ocpPath}
                initApprovedMembers={this.state.initApprovedMembers}
                batchDetails = {
                  { data: member_upload_data,
                    corporateAccountNo: corporateAccountNo,
                    corporateAccountName:corporateAccountName,
                    contractName: contractName,
                    contractId: contractId, //Ann
                    pageType: verifyPageType,
                    ocpOrigin: ocpPath,
                    member_id: member_id,
                    generateFlag: generateFlag,
                    benefitPlanTree: this.state.benefitPlanTree,
                    handleRedirect: this.handleRedirect,
                    batch_ticket_id: localStorage.getItem('TICKET_ID')
                  }
                }
                ticket_closed={ticket_closed}
                allValidated={this.state.allValidated}
              />
            </>
           
          ) : generateFlag && verifyPageType === 'edit' ? (
            
            <ViewCorrections
              renderClientInfo={renderClientDetails}
              verifyTabs={verifyTabs}
              handleBack={this.handleBack}
              handleOnMarkAsDone={this.handleViewCorrectionsMarkAsDone}
              handlePublishToOcp={this.handlePublishToOcp}
              memberData={member_edit_data}
              ocpOrigin={ocpPath}
              client_name={corporateAccountName}
              client_accNo={corporateAccountNo}
              client_id={clientId}
              clientData={this.state.clientData}
              ticket_id={batch_ticket_id ?? ''}
            />
          ) : null}
          
          <ModalComponent
            id="member-list-modal"
            isModalOpen={isErrorModalOpen}
            title={errorTitle}
            message={errorMessage}
            onClose={() => {
              this.setState({ isErrorModalOpen: false });
            }}
          />
          <ModalComponent
            id="member-list-modal"
            isModalOpen={isModalOpen}
            title={modalTitle}
            message={modalMessage}
            onClose={this.closeModal}
          />
          <ConfirmationModalComponent
            id="upload-member-confirm-modal"
            isModalOpen={isConfirmModalOpen}
            modalTitle={modalTitle}
            modalMessage={modalMessage}
            closeText={modalCloseText}
            confirmText={modalConfirmText}
            noCancelButton={noCancelButton}
            onClose={() => {
              this.setState({
                isConfirmModalOpen: false,
              });
            }}
            onConfirm={() => {
              const { next } = this.state;
              if (next) {
                this.setState(
                  {
                    isConfirmModalOpen: false,
                  },
                  () => {
                    next();
                  },
                );
              }
            }}
            customMessage={customModalMessage}
          />
          <LostInternetModal
            id="Lost Internet Modal"
            isModalOpen={this.state.check_internet_flag}
            onClose={this.handleCloseLostInternetConnectionModal}
          />

          <EditMemberModal 
            isModalOpen={memberFilesModalOpen}
            pageType={this.state.verifyPageType}
            verifyEditTable={this.state.verifyEditTable}
            memberData={this.state.memberData}
            anchorElement={this.state.anchorEl}
            handlePopoverClose={this.handlePopoverClose}
            planTypes={this.state.plan_types}
            handleChangeMemberData={this.handleChangeMemberData}
            handlePatchMemberData={this.handlePatchMemberData}
            memberDataFields={this.state.memberDataFields}
            // id="disapproved-member-files-modal"
            otherTableRow={this.state.otherTableRow}
            id={this.state.temp_id}
            row={this.state.row}
            tableName={this.state.tableName}
            details={this.state.details}
            rowData={this.state.rowData}
            // memberEditSelected={Object.assign({}, this.state.data)}
            tempId={this.state.temp_id}
            prefix={this.state.prefix}
            tab={this.state.tab}
            remarks={this.state.remarks}
            data={this.state.memberFileUploadDetails}
            rowDataForEdit={this.state.rowDataForEdit}
            dataForEdit={this.state.dataForEdit}
            // isUpload={this.isUpload}
            // member_details={this.state.member_details}
            uploadDetails={this.state.memberFileUploadDetails}
            // onSubmit={(row: number,  remarks: string, details: any, files?: any,) =>
            //   this.saveMemberFilesModal(row, files, remarks, details)
            // }
            onSubmit={(
              row: number,
              remarks: string,
              user_remarks?: string,
              details?: any,
              rowData?: any,
              data?:any,
              openFileModal?:any,
              uploadedDocuments?:any
            ) => {
              this.saveMemberFilesModal(row, remarks, user_remarks, details, rowData, data, openFileModal, uploadedDocuments)
              }
            }
            onSubmit2={(
              row: number,
              data: any,
              remarks?:string,
              details?: any,
            ) => this.saveMemberFilesModal(row, data, remarks, details)}
          
            onDelete={(row: number, name: string, details: any) => {
              this.deleteMemberFiles(row, name, details);
            }}
            onCloseMemberFilesModal={this.closeMemberFilesModal}
            onClose={() => {
              this.setState({ 
                memberFilesModalOpen: false,
              });
              setTimeout(() => {
                this.setState({
                  rowData : [],
                memberData:null
                });
              }, 500);
            }}
            handleClickUpload={this.handleClickUpload}
            handleClickUploadNewNew={(pageType: string, tableName: string, prefix: string) => (isUpload: boolean) => {
              // isUpload: true,
              this.handleClickUpload(pageType, tableName, prefix)
              console.log('isUpload?2', isUpload)}}
            saveMemberFilesModal={this.saveMemberFilesModal}
            supportingDocumentChecker={this.supportingDocumentChecker}
            isEdit={this.state.isEdit}
            // onClose2={this.handlePopoverClose}
          />
          <MemberInformationModal
            id={
              verifyPageType === 'default'
                ? 'verify_member_information_modal'
                : 'verify_edit_information_modal'
            }
            isModalOpen={this.state.isMemberInfoModalOpen}
            data={this.state.memberData}
            clientData={this.state.clientData}
            memberCardNo={memberCardNo}
            user_remarks={this.state.user_remarks}
            tableData={this.state.infoData}
            onClose={() => {
              this.setState({ isMemberInfoModalOpen: false });
            }}
            memberDataFields={this.state.memberDataFields}
            displayUserRemarks={this.state.displayUserRemarks}
          />
          <RemarksModal
            id={'verify-member-ocp-remarks-modal'}
            isModalOpen={this.state.isRemarksModal}
            data={this.state.remarksData}
            onClose={() => {
              this.setState({ isRemarksModal: false });
            }}
          />
        </div>
      );
    }
  }
}

const mapStateToProps = (state: Store) => state.home;

const mapDispatchToProps = (dispatch: Dispatch) => ({
  Map: bindActionCreators(VerifyMemberActions.Map, dispatch),
});

export { mapStateToProps, mapDispatchToProps };

