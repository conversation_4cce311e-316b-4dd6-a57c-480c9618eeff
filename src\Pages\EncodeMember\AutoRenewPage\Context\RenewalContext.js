import React, { createContext, useContext, useState } from 'react';

const RenewalContext = createContext();

export const useRenewalContext = () => useContext(RenewalContext);

export const RenewalProvider = ({ children, ...props }) => {
  
  const [isConfirmRenewal, setIsConfirmRenewal] = useState(false);
  const [isOpenFinishRenewalModal, setIsOpenFinishRenewalModal] = useState(false);

  const modalStateProps = {
    isConfirmRenewal, setIsConfirmRenewal, isOpenFinishRenewalModal, setIsOpenFinishRenewalModal,
  };
 

  return (
    <RenewalContext.Provider value={{ props,modalStateProps }}>
      {children}
    </RenewalContext.Provider>
  );
};