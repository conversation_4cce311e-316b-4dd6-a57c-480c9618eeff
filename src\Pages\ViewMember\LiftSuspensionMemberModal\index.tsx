import * as React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Button,
  IconButton,
  Typography,
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Checkbox,
  InputLabel,
  TextField,
} from '@material-ui/core/';
import { makeStyles, withStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import { DatePicker } from './DatePicker';
import moment from 'moment';
import { get, isNil } from 'lodash';
import {API} from '../../API'

const dependentCheckboxStyle = {
  size: 12,
  padding: 0,
  color: '#272E4C',
  '&$checked': {
      color: '#272E4C',
  },
};

const useStyles = makeStyles(theme => ({
  root: {
    flexWrap: 'wrap',
  },
  dialog: {
    align: 'center',
    justify: 'center',
    width: '95%',
    fontFamily: 'usual',
  },
  dialogContent: {
    paddingTop: '0px',
    paddingBottom: theme.spacing(3),
    minWidth: '250px',
    width: '55vw',
  },
  dialogContentText: {
    fontSize: '0.875rem',
  },
  container: {
    paddingRight: '15px',
    paddingLeft: '15px',
    minWidth: '680px',
    width: 'auto',
  },
  dialogTitle: {
    paddingTop: theme.spacing(5),
    minWidth: 400,
  },
  dialogFirstTitle: {
    fontSize: '16px',
    color: '#272E4C',
    fontWeight: 600,
    marginBottom: '5px',
  },
  dialogSecondTitle: {
    fontSize: '14px',
    color: '#272E4C',
  },
  dialogAction: {
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(6),
  },
  boldText: {
    fontWeight: 'bold',
  },
  contentText: {
    fontWeight: 700,
  },
  cancelButton: {
    minWidth: 129,
    marginRight: 25,
    border: '1px solid #3AB77D',
    color: '#3AB77D',
    backgroundColor: '#FFFFFF',
  },
  submitButton: {
    minWidth: 140,
    backgroundColor: '#FD5451',
  },
  previousButton: {
    minWidth: 120,
    fontWeight: 600,
  },
  nextButton: {
    minWidth: 130,
    fontWeight: 600,
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  tab: {
    textAlign: 'left',
    alignItems: 'left',
  },
  error: {
    marginTop: 4,
    fontSize: '10px',
    color: '#FF0000',
  },
  textValue: {
    fontWeight: 700,
    fontSize: 14,
  },
  textLabel: {
    color: '#a7abb7',
  },
  gridItem: {},
  tblTitle: {
    fontSize: 14,
    fontWeight: 600,
    color: 'rgba(39, 49, 76, 1)',
},
tblSubtitle: {
    fontSize: 12,
    fontWeight: 400,
    color: 'rgba(39, 49, 76, 1)',
},
tblPaperWithDeps: {
    minHeight: 175,
    borderRadius: 0,
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
},
tblPaperNoDeps: {
    minHeight: 'auto',
    borderRadius: 0,
    boxShadow: '0px 3px 6px #272E4C19',
    padding: 0,
},
}));

const SubmitButton = withStyles({
  root: {
    color: '#FFFFFF',
    backgroundColor: '#3ab77d',
    '&:hover': {
      backgroundColor: '#2c9263',
    },
  },
})(Button);

const CancelButton = withStyles({
  root: {
    '&:hover': {
      backgroundColor: '#3AB77D',
      color: '#FFFFFF',
    },
  },
})(Button);

interface LiftSuspensionMemberModalProps {
  isModalOpen: boolean;
  id: string;
  memberData: any;
  maxDate?: Date;
  onClose: () => void;
  dependents?: any[];
  onLiftSuspension: (data: any, displayData: any[]) => void;
  isPrincipal?: boolean;
}

export const LiftSuspensionMemberModal: React.FC<
  LiftSuspensionMemberModalProps
> = (props: LiftSuspensionMemberModalProps): JSX.Element => {
  const classes = useStyles();
  const { onLiftSuspension, isModalOpen, memberData, id } = props;
  const [effectiveLiftingDate, setEffectiveLiftingDate] = React.useState<Date | null>(null);
  const [liftDateError, setLiftDateError] = React.useState<boolean>(false);
  const [liftDateErrorMessage, setLiftDateErrorMessage] = React.useState<string>('');
  const [maxDate, setMaxDate] = React.useState<Date | undefined>(undefined);
  const [fetchedDependents, setFetchedDependents] = React.useState<any>([]);
  // @ts-ignore
  const [user, setUser] = React.useState<any>({});
  // @ts-ignore
  const [isLoaded, setIsLoaded] = React.useState<boolean>(false);
  const [selectedDependents, setSelectedDependents] = React.useState<any[]>([]);
  const [remarks, setRemarks] = React.useState('');

  React.useEffect(() => {
    if (isModalOpen === true) {
      if (!isNil(props.maxDate)) {
        const maxDateMoment = moment(props.maxDate);

        if (!isNil(maxDateMoment) && maxDateMoment.isValid()) {
          const dayBefore = maxDateMoment.add(-1, 'days').toDate();
          setMaxDate(dayBefore);
        }
      }
    }
  }, [props.maxDate])

  React.useEffect(() => {
    async function getMemberDependents() {
      try {
        const userData = await API.getUserDataFromDb();
        setUser(userData);

        let dependentsArray: any[] = [];
        await API.getMemberStatusActionDependents(memberData._id)
          .then(response => {
            console.log(response)
            if (response && response.error === undefined) {
              if (response.length > 0) {
                response.forEach(dependent => {
                  dependent['principal_name'] = `${memberData.first_name} ${memberData.middle_name} ${memberData.last_name}`;
                  dependentsArray.push(dependent);
                });
              } else {
                setIsLoaded(true);
              }
            } else {
              setIsLoaded(true);
            }
          })
          .catch(error => {
            console.log(error)
            setIsLoaded(true);
          })

        setFetchedDependents(dependentsArray);
        setIsLoaded(true);
      } catch (error) {
        console.log(error)
        setIsLoaded(true);
      }
    }

    if (isModalOpen === true) {
      console.log('LIFT member data', memberData)
      getMemberDependents();
    }
  }, [isModalOpen])

  React.useEffect(() => { }, [selectedDependents])

  const handleLiftSuspensionMember = () => {
    if (effectiveLiftingDate === null) {
      setLiftDateError(true);
      setLiftDateErrorMessage('Lift date is required');
    } else {
      let memberObjectIds: any[] = selectedDependents;
      memberObjectIds.unshift(props.memberData['_id']
        ? props.memberData['_id'] : '')

      const data = {
        ref_id: memberData.member_id,
        memberIds: memberObjectIds,
        suspension_start_date: memberData.member_suspension.suspension_start_date,
        suspension_lift_date: effectiveLiftingDate,
        suspension_started_by: user.userName,
        user_id: user.userId,
        user_name: user.userName,
        remarks: remarks,
        apply_to_all: false,
      }
      const displayData = {
        member_id: memberData.member_id ? memberData.member_id : 'None',
        member_name: `${memberData.first_name} 
        ${memberData.middle_name ? memberData.middle_name : ''} 
        ${memberData.last_name}${memberData['suffix'] && memberData['suffix'] !== null && memberData['suffix'].trim() !== ''
            ? ', ' + memberData['suffix'] : ''}`,
        corporation: memberData.client_object.registered_name ? memberData.client_object.registered_name : 'None',
        type: memberData.member_type ? memberData.member_type : 'None',
        previous_status: 'Suspended'
      }
      onLiftSuspension(data, [displayData]);
      resetData();
    }
  };

  const resetData = () => {
    setIsLoaded(false);
    setUser({});
    setFetchedDependents([]);
    setSelectedDependents([]);
    setRemarks('');
    setLiftDateError(false);
    setLiftDateErrorMessage('');
    setEffectiveLiftingDate(null);
}

  const getSuspensionDate = () => {
    const suspensionDate = get(
      memberData,
      'member_suspension.suspension_start_date',
      null,
    );

    if (!isNil(suspensionDate)) {
      const suspensionDateMoment: moment.Moment = moment(suspensionDate);
      if (!isNil(suspensionDateMoment) && suspensionDateMoment.isValid()) {
        return suspensionDateMoment.format('MMM DD, YYYY');
      }
    }

    return '-';
  };

  const onClose = () => {
    setEffectiveLiftingDate(null);
    resetData();
    props.onClose();
  }

  function handleDependentsCheckboxChange(id: string) {
    if (selectedDependents.includes(id)) {
      setSelectedDependents(selectedDependents.filter(item => item !== id));
    } else {
      const newSelectedDependents = [...selectedDependents];
      newSelectedDependents.push(id);
      setSelectedDependents(newSelectedDependents);
    }
  }

const DependentsRow = (dependents: any[]) => {
  return dependents.map(
    (row, index) => {
      return (
        <TableRow key={index}>
          <TableCell style={{ width: '5%' }}>
            <Checkbox
              id={'client_member_status_check'}
              data-cy={'client_member_status_check'}
              style={dependentCheckboxStyle}
              disabled={
                row['member_status'] === 'Suspended' ?
                  false : true
              }
              indeterminate={
                row['member_status'] === 'Suspended' ?
                  false : true
              }
              checked={selectedDependents.includes(
                row['_id']
              )}
              onChange={() => {
                handleDependentsCheckboxChange(row['_id']);
              }}
            />
          </TableCell>
          <TableCell style={{ width: '35%' }}>
            {`${row['first_name']} 
            ${row['middle_name'] ? row['middle_name'] : ''} 
            ${row['last_name']}${row['suffix'] && row['suffix'] !== null && row['suffix'].trim() !== ''
                ? ', ' + row['suffix'] : ''}`}
          </TableCell>
          <TableCell style={{ width: '30%' }}>
            {row['relationship_to_principal']}
          </TableCell>
          <TableCell style={{ width: '50%' }}>
            {row['member_status']}
          </TableCell>
        </TableRow>
      );
    }
  )
}

const NoDependentsRow = (message: string) => {
  return (
      <TableRow>
          <TableCell
              component="th"
              scope="row"
              style={{ textAlign: 'center' }}
              colSpan={5}
          >
              {message}
          </TableCell>
      </TableRow>
  )
}

  return (
    <Dialog
      id={id}
      maxWidth={false}
      open={isModalOpen}
      scroll={'paper'}
      aria-labelledby="modal-title"
      className={classes.dialog}
      style={{ zIndex: 1200 }}
    >
      <DialogTitle
        className={classes.dialogTitle}
        disableTypography={true}
        id="modal-title"
      >
        <div className={classes.dialogFirstTitle}>Lift Suspension Details</div>
        <IconButton
          data-cy={'lift_suspension_modal_close_btn'}
          id={'lift_suspension_modal_close_btn'}
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={3} style={{ padding: '1.5em 0' }}>
          <Grid item xs={12}>
            <Typography className={classes.textLabel}>
              EFFECTIVE DATE OF SUSPENSION
            </Typography>
            <Typography className={classes.textValue}>
              {getSuspensionDate()}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <DatePicker
              data-cy={'lift_suspension_modal_lift_picker'}
              id={'lift_suspension_modal_lift_picker'}
              placeholder="Effective Date of Lifting"
              value={effectiveLiftingDate}
              error={liftDateError}
              error_message={liftDateErrorMessage}
              minDate={new Date()}
              maxDate={maxDate}
              minDateMessage="Date of lifting should not be before start of suspension."
              onChange={(date: Date) => {
                setEffectiveLiftingDate(date);
                setLiftDateError(false);
                setLiftDateErrorMessage('');
              }}
              isRequired={true}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography className={classes.tblTitle}>Dependents</Typography>
                  <Typography className={classes.tblSubtitle}>
                    Choose the dependents to Lift Suspension.
                  </Typography>
          </Grid> 
          <Grid item xs={12}>
          <Paper
                    className={fetchedDependents.length > 0 ?
                      classes.tblPaperWithDeps : classes.tblPaperNoDeps
                    }
                  >
                    <Table
                      data-cy={'client_member_reactivate_dependents_table'}
                      id={'client_member_reactivate_dependents_table'}
                      size="small"
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell style={{ width: '5%' }}>
                            {/* Checkbox Column */}
                          </TableCell>
                          <TableCell style={{ width: '35%' }}>
                            Member Name
                          </TableCell>
                          <TableCell style={{ width: '30%' }}>
                            Relationship
                          </TableCell>
                          <TableCell style={{ width: '50%' }}>
                            Status
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {fetchedDependents.length > 0 ?
                          DependentsRow(fetchedDependents) : NoDependentsRow('No dependents')
                        }
                      </TableBody>
                    </Table>
                  </Paper>
          </Grid>
          <Grid item xs={12}>
            <InputLabel className="label" htmlFor="lift_suspend_member_modal_remarks_field">
              Remarks
            </InputLabel>
            <TextField
              data-cy={'lift_suspend_member_modal_remarks_field'}
              id={'lift_suspend_member_modal_remarks_field'}
              style={{ marginTop: '8px', width: '100%' }}
              margin="normal"
              variant="outlined"
              multiline={true}
              rows={10}
              value={remarks}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { value } = e.target;
                setRemarks(value);
              }}
              inputProps={{ 'aria-label': 'bare' }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className={classes.dialogAction}>
        <Grid
          container
          direction="row"
          justify="space-evenly"
          alignItems="stretch"
        >
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <CancelButton
              data-cy={'lift_suspension_modal_cancel_btn'}
              id={'lift_suspension_modal_cancel_btn'}
              className={classes.cancelButton}
              variant="contained"
              color="primary"
              onClick={onClose}
            >
              Cancel
            </CancelButton>
          </Grid>
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <SubmitButton
              data-cy={'lift_suspension_modal_btn'}
              id={'lift_suspension_modal_btn'}
              className={classes.submitButton}
              variant="contained"
              color="primary"
              onClick={handleLiftSuspensionMember}
            >
              Lift Suspension
            </SubmitButton>
          </Grid>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};
