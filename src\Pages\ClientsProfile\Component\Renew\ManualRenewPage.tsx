//#region Global Imports
import * as React from 'react'
import clsx from 'clsx';
import { Loader } from 'Components/UI/LoadingIndicator';
import { PageHeaderComponent } from 'Components/UI/PageHeader';
import { FloatingButtons } from 'Components/UI/FloatingButtons';
import { RenewalTable } from './Components/RenewalTable';
import { API } from '../../../API';
import { Processmaker } from '../../../Processmaker';
import {
    every,
    filter,
    get,
    includes,
    isNil,
    toLower,
} from 'lodash';
import {
    Grid,
    Button,
    Typography
} from '@material-ui/core/';
import {
    ArrowForward,
    ArrowBack,
} from '@material-ui/icons'
import { makeStyles } from '@material-ui/core/styles';
import { Components } from '@hims/core';
import {
    pageSizes,
    MEMBERS_TO_RENEW_COLUMNS,
    MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMNS,
    MEMBERS_TO_RENEW_COLUMN_EXTENSIONS,
    MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMN_EXTENSIONS,
    MEMBERS_TO_RENEW_FILTER_EXTENSIONS,
    MEMBERS_TO_EXCLUDE_FROM_RENEWAL_FILTER_EXTENSIONS,
    MEMBERS_TO_RENEW_INTEGRATED_FILTERS,
    MEMBERS_TO_EXCLUDE_INTEGRATED_FILTERS,
    // EXCLUDE_STATIC_DATA
} from './ManualRenew.const'

import { ModalComponent } from 'Components/UI/ModalComponent';
import { ConfirmationModalComponent } from 'Components/UI/ConfirmationModalComponent';
import { ExceptionReport } from './ExceptionReportPage';

import { RouteComponentProps } from 'react-router';
import { Redirect } from 'react-router-dom';
import { useLocation } from 'react-router-dom';


const useStyles = makeStyles({
    actionButton: {
        height: '40px',
        minWidth: '150px',
        margin: '0 5px'
    },
});

export const ManualRenewPage: React.FC<RouteComponentProps> = (
    props: RouteComponentProps
): JSX.Element => {
    const classes = useStyles();
    const location = useLocation();

    const [memberUpload, setMemberUpload] = React.useState<any>({});
    let [activeStep, setActiveStep] = React.useState<number>(0);
    const [user, setUser] = React.useState<any>({});
    const [loadingState, setLoadingState] = React.useState<boolean>(false);
    const [breadCrumbs, setBreadCrumbs] = React.useState<any>([]);
    const [uploadSaveForNowData, setUploadSaveForNowData] = React.useState<any>([]);

    const [pageData, setPageData] = React.useState<any>({
        clientId: '',
        pageType: 'selection',
        ticketId: '',
        ticketIdFromPath: '',
        userId: 'renew-members',
        ticketType: 'Manual Renewal',
    });
    const [saveForNowId, setSaveForNowId] = React.useState<string>('')

    const [clientData, setClientData] = React.useState<any>({});

    const [redirectState, setRedirectState] = React.useState<any>({
        redirectFlag: false,
        redirectInfo: {
            pathname: '/membership/',
        },
    })

    const [tableSort, setTableSort] = React.useState<any>({
        renew: [],
        exclude: [],
    });
    const [tableFilter, setTableFilter] = React.useState<any>({
        renew: [],
        exclude: [],
    });
    const [excludeTableStates, setExcludeTableStates] = React.useState<any>({
        rowsCopy: [],
        rows: [],
    });
    const [renewTableStates, setRenewTableStates] = React.useState<any>({
        rowsCopy: [],
        rows: [],
    });
    const [renewSelectionState, setRenewSelectionState] = React.useState<any>({
        renewMembers: [],
        renewSelection: [],
        renewReset: false,
    });
    const [excludeSelectionState, setExcludeSelectionState] = React.useState<any>({
        excludeMembers: [],
        excludeSelection: [],
        excludeReset: false,
    });

    const [pageHeaders] = React.useState<any>([
        'Select Members to Renew',
        'Finished Manual Renew of Members',
    ]);

    const [dialogModalState, setDialogModalState] = React.useState<any>({
        isOpen: false,
        title: '',
        message: '',
    });

    const [exceptionReportData, setExceptionReportData] = React.useState<any>({});

    const [processModalState, setProcessModalState] = React.useState<any>({
        isOpen: false,
        modalTitle: '',
        modalMessage: '',
        closeText: 'Cancel',
        confirmText: 'Confirm',
        onClose: null,
        onConfirm: null,
        noCancelButton: false,
    });

    const [successModalState, setSuccessModalState] = React.useState<any>({
        isOpen: false,
        title: '',
        message: '',
    });

    React.useEffect(() => {
        async function getUser() {
            try {
                const userData = await API.getUserDataFromDb();
                setUser(userData);
            } catch (error) {
                console.log('get user err', error)
            }
        }

        console.log('renewal page props', props)
        if (props['match'] && props['match']['params']) {
            let params = props['match']['params'];
            
            const ticketIdFromUrl = location.pathname.match(/\/([^/]+)$/)?.[1] || '';
            const ticketIdFromPath = params['ticket_id'] || ticketIdFromUrl;
            
            setPageData({
                ...pageData,
                clientId: params['client_id'] ? params['client_id'] : '',
                pageType: params['type'] ? params['type'] : '',
                ticketIdFromPath: ticketIdFromPath,
            })
            setLoadingState(true);
            getUser();
            getBreadCrumbs(params['client_id'], ticketIdFromPath);
        }
    }, [])

    React.useEffect(() => {
        console.log('page data', pageData)
    }, [pageData])

    React.useEffect(() => {
        console.log('client data', clientData)
    }, [clientData])

    React.useEffect(() => {
        console.log('save for now id', saveForNowId)
    }, [saveForNowId])

    React.useEffect(() => {
    }, [loadingState, tableSort, tableFilter])

    React.useEffect(() => {
        // console.log('excludeTableStates rows', excludeTableStates['rows'])
        // console.log('excludeSelection state', excludeSelectionState)
    }, [excludeTableStates, excludeSelectionState])

    React.useEffect(() => {
        // console.log('renewTableStates rows', renewTableStates['rows'])
        // console.log('renewSelection state', renewSelectionState)
    }, [renewTableStates, renewSelectionState])

    React.useEffect(() => {
        if (Object.keys(exceptionReportData).length > 0) {
            setLoadingState(false);
            setActiveStep(++activeStep);
        }
    }, [exceptionReportData])

    const getBreadCrumbs = (client_id: string, ticketIdFromPath: string) => {
        API.getClient(client_id)
            .then(response => {
                if (response && response.error === undefined) {
                    setClientData(response);
                    
                    if (client_id) {
                        const data_item = response;
                        if (data_item) {
                            const breadcrumbs_items = [
                                {
                                    label: 'CLIENTS',
                                    url: '#/membership/clients',
                                },
                                {
                                    label: data_item['registered_name'],
                                    url: '#/membership/clients-profile/' + client_id,
                                },
                            ];
                            setBreadCrumbs([...breadcrumbs_items]);

                            if (ticketIdFromPath === '') {
                                getMembersData(client_id).catch(error => {
                                    console.error('Error loading members data:', error);
                                });
                            } else {
                                getSaveForNowMembersData(ticketIdFromPath, client_id)
                            }
                        }
                    }
                }
            });

    }

    const getMembersData = (client_id: string): Promise<any> => {

        
        if (!client_id) {
            const error = new Error('Client ID is required but was not provided');
            setLoadingState(false);
            return Promise.reject(error);
        }
        
        return API.searchManualRenewalEligibleMembers(client_id)
          .then((response) => {
            
            if (response && response.error === undefined) {
              // Handle case where response.data might be null/undefined
              const memberData = response.data || [];

              
              if (memberData.length > 0) {
                
                // Debug: Count members by status
                const statusCounts = memberData.reduce((acc: any, member: any) => {
                  const status = member.member_status || 'Unknown';
                  acc[status] = (acc[status] || 0) + 1;
                  return acc;
                }, {});
                
                // Debug: Check for suspended members specifically
                const suspendedMembers = memberData.filter((m: any) => m.member_status === 'Suspended');
   
                if (suspendedMembers.length > 0) {
                  console.log('Suspended member sample:', suspendedMembers[0]);
                }
              }
      
              const processedData = arrayDataSetter(memberData);
              
              setRenewTableStates((prevStates) => ({
                ...prevStates,
                rowsCopy: processedData,
                rows: processedData,
                pagination: {
                  ...prevStates.pagination,
                  totalCount: response.totalCount || 0,
                },
              }));
              return Promise.resolve(response);
            } else {
              console.log('API returned error:', response?.error);
              const errorMessage =
                response?.error?.message || 'An unknown error occurred.';
              const error = new Error(errorMessage);
              (error as any).apiResponse = response;
              return Promise.reject(error); 
            }
          })
          .catch((err: any) => {
            const unexpectedError = new Error(
              'Unexpected error during data fetch (catch block).'
            );
            (unexpectedError as any).originalError = err;
            return Promise.reject(unexpectedError);
          })
          .finally(() => {
            setLoadingState(false);
          });
    };
      
    const getSaveForNowMembersData = (ticketIdFromPath: string, client_id: string) => {
        API.getSaveForNowData(pageData['userId'], ticketIdFromPath).then(resp => {
            if (resp && resp.error) {
                console.log('get save for now err', resp.error)
                setLoadingState(false); // Set loading to false on error
            } else {
                if (resp && resp.length > 0 && resp[0]) {
 
                    let newObj = resp[0];
                    setSaveForNowId(newObj._id ? newObj._id : '');
                    setUploadSaveForNowData(newObj);

                    if (
                        newObj.data.hasOwnProperty('renew_selected') &&
                        newObj.data.hasOwnProperty('renew_selection') &&
                        newObj.data.hasOwnProperty('exclude_selected') &&
                        newObj.data.hasOwnProperty('exclude_selection') &&
                        newObj.data.hasOwnProperty('exclude_members') &&
                        newObj.data.hasOwnProperty('renew_members')
                    ) {
                        
                        newObj.data.renew_members.forEach((member, index) => {
                            if (member.member_status === 'For Validation' || 
                                member.member_status_before_expiration === 'For Validation' ||
                                member.formatted_member_status === 'For Validation') {
                                console.log(`🚨 FOR VALIDATION in SAVED DATA - Member ${index + 1}:`, {
                                    member_id: member.member_id,
                                    member_status: member.member_status,
                                    member_status_before_expiration: member.member_status_before_expiration,
                                    formatted_member_status: member.formatted_member_status
                                });
                            }
                        });

                        setRenewTableStates({
                            ...renewTableStates,
                            rowsCopy: arrayDataSetter(newObj.data.renew_members),
                            rows: arrayDataSetter(newObj.data.renew_members),
                        })
                        setExcludeTableStates({
                            ...excludeTableStates,
                            rowsCopy: arrayDataSetter(newObj.data.exclude_members),
                            rows: arrayDataSetter(newObj.data.exclude_members),
                        })
                        setRenewSelectionState({
                            ...renewSelectionState,
                            renewMembers: newObj.data.renew_selected,
                            renewSelection: newObj.data.renew_selection,
                        })
                        setExcludeSelectionState({
                            ...excludeSelectionState,
                            excludeMembers: newObj.data.exclude_selected,
                            excludeSelection: newObj.data.exclude_selection,
                        })
                        setLoadingState(false);
                    } else if (newObj.data.hasOwnProperty('uploaded_data') && newObj.data.uploaded_data.hasOwnProperty('rows')) {
                        // This is EncodeMember save-for-now data with uploaded member data, process it for renew
                        setUploadedMembers(newObj.data.uploaded_data.rows)
                    } else {
                        // This is save-for-now data from a different process that doesn't have the expected structure
                        // Fall back to loading normal member data

                        const clientIdToUse = client_id || clientData._id || pageData.clientId;
                        if (!clientIdToUse) {
                            setLoadingState(false);
                            return;
                        }
                        
                        getMembersData(clientIdToUse).catch(error => {
                            console.error('Error loading members data:', error);
                        });
                    }
                } else {
                    // No save-for-now data found, proceed with getMembersData
                   
                    const clientIdToUse = client_id || clientData._id || pageData.clientId;
                    if (!clientIdToUse) {
                        setLoadingState(false);
                        return;
                    }
                    
                    getMembersData(clientIdToUse).catch(error => {
                        console.error('Error loading members data:', error);
                    });
                }
            }
        }).catch(error => {
            console.log('get save for now err', error)
            setLoadingState(false); // Set loading to false on catch error
        })
    }

    const setUploadedMembers = (obj: any[]) => {
        let payload: any[] = []
        obj.map(a => { 
            payload.push({
                member_id: a['member_id'] ? a['member_id'] : null,
                first_name: a['first_name'] ? a['first_name'] : '',
                middle_name: a['middle_name'] ? a['middle_name'] : '',
                last_name: a['last_name'] ? a['last_name'] : '',
            }) 
        })

        console.log('postUploadedDataForRenewal payload', payload)
        API.postUploadedDataForRenewal(payload).then(resp => {
            console.log('postUploadedDataForRenewal res', resp)
            setRenewTableStates({
                ...renewTableStates,
                rowsCopy: arrayDataSetter(resp),
                rows: arrayDataSetter(resp),
            })
        })
        setLoadingState(false);
    }

    const arrayDataSetter = (array: any[]) => {
        let newArray: any[] = [];
        if (array && Array.isArray(array) && array.length > 0) {
            try {
                newArray = array.map(a => {
                    // Safely handle potentially undefined/null values
                    const firstName = (a.first_name || '').toString().trim();
                    const middleName = (a.middle_name || '').toString().trim();
                    const lastName = (a.last_name || '').toString().trim();
                    const suffix = (a.suffix || '').toString().trim();
                    
                    let nameData = `${firstName} ${middleName ? middleName : ''} ${lastName}${suffix && suffix !== 'null' && suffix !== ''
                        ? ', ' + suffix : ''}`;
                    
                    // Use the backend's formatted_member_status if available, otherwise use member_status
                    const formattedStatus = a.formatted_member_status || a.member_status || 'Active';
                    
                    let dataObj = {
                        ...a,
                        member_name: nameData.trim(),
                        formatted_member_status: formattedStatus,
                        member_status_before_expiration: a.member_status_before_expiration || formattedStatus
                    }

                    return dataObj;
                })
            } catch (error) {
                console.error('Error processing member data in arrayDataSetter:', error);
                return [];
            }
        }

        return newArray;
    }


    const handleSortingChange = (sorting: any[], tableName: string) => {
        // console.log(`${tableName} table sorting`, sorting)
 
        // Create a copy of the data to avoid mutating state
        let data = tableName === 'renew' 
            ? [...renewTableStates['rows']]
            : [...excludeTableStates['rows']];

        if (sorting && sorting.length > 0) {
            // Extract sort parameters
            const { direction, columnName } = sorting[0];
            
            // Sort the copied data
            data.sort((a, b) => {
                const aValue = a[columnName] || '';
                const bValue = b[columnName] || '';
                
                if (direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue > bValue ? -1 : 1;
                }
            });
        }
        // Note: Removed the else block that was reconstructing from rowsCopy
        // This was causing duplication issues. The current rows should remain as is.

        // Update the appropriate table state
        if (tableName === 'renew') {
            setRenewTableStates({
                ...renewTableStates,
                rows: data
            });
        } else {
            setExcludeTableStates({
                ...excludeTableStates,
                rows: data
            });
        }

        setTableSort({
            ...tableSort,
            [tableName]: sorting,
        });
    }

    const handleFilterChange = (filters: any[], tableName: string) => {
        // console.log(`${tableName} table filters`, filters)

        const getFilteredData = (tableRows, filters) => {
            if (
                !isNil(tableRows) &&
                tableRows.length > 0 &&
                !isNil(filters) &&
                filters.length > 0
            ) {
                return filter(tableRows, tableRow => {
                    return every(filters, filter => {
                        const tableRowValue = get(tableRow, filter.columnName, '');

                        return includes(toLower(tableRowValue), toLower(filter.value));
                    });
                });
            }

            return tableRows;
        };

        let filteredData: any[] = [];

        if (tableName === 'renew') {
            if (filters && filters.length > 0) {
                // Apply filters to the original rowsCopy data to ensure we have all data available
                filteredData = getFilteredData(renewTableStates['rowsCopy'], filters);
            } else {
                // When clearing filters, restore from rowsCopy to get all original data back
                filteredData = renewTableStates['rowsCopy'];
            }
            
            setRenewTableStates({
                ...renewTableStates,
                rows: filteredData
            });
        } else {
            if (filters && filters.length > 0) {
                // Apply filters to the original rowsCopy data to ensure we have all data available
                filteredData = getFilteredData(excludeTableStates['rowsCopy'], filters);
            } else {
                // When clearing filters, restore from rowsCopy to get all original data back
                filteredData = excludeTableStates['rowsCopy'];
            }
            
            setExcludeTableStates({
                ...excludeTableStates,
                rows: filteredData
            });
        }

        setTableFilter({
            ...tableFilter,
            [tableName]: filters,
        });
    }

    const handleRowSelection = (selection: any[], origin: string) => {
        let selectedData: any[] = [];

        if (selection && selection.length > 0) {
            selection.forEach(rowId => {
                if (origin === 'renew') { //if 'renew'
                    if (renewTableStates['rows'][rowId]) {
                        selectedData.push(renewTableStates['rows'][rowId])
                    }
                } else {  //if 'exclude'
                    if (excludeTableStates['rows'][rowId]) {
                        selectedData.push(excludeTableStates['rows'][rowId])
                    }
                }
            })
        }

        if (origin === 'renew') { //if 'renew'
            setRenewSelectionState({
                ...renewSelectionState,
                renewMembers: selectedData,
                renewSelection: selection,
            })
        } else {  //if 'exclude'
            setExcludeSelectionState({
                ...excludeSelectionState,
                excludeMembers: selectedData,
                excludeSelection: selection,
            })
        }
    }

    const handleMemberAction = (data: any) => {
        if (data === 'toExclude') { //if 'toExclude'
            // check selected members if already transferred to Exclude table
            if (renewSelectionState['renewMembers'] && renewSelectionState['renewMembers'].length > 0) {
                // Use array for ID lookup
                const existingExcludeIds = excludeTableStates['rows'].map((eData: any) => eData._id);
                
                // Filter out members that are already in the exclude table
                const membersToAdd = renewSelectionState['renewMembers'].filter((rmData: any) => 
                    rmData._id && existingExcludeIds.indexOf(rmData._id) === -1
                );
                
                // Combine existing exclude members with new ones
                const newExcludeMembers = [...excludeTableStates['rows'], ...membersToAdd];
                const newExcludeMembersCopy = [...excludeTableStates['rowsCopy'], ...membersToAdd];
                
                // Remove selected members from renew table
                const selectedIds = renewSelectionState['renewMembers'].map((m: any) => m._id);
                const remainingRenewMembers = renewTableStates['rows'].filter((rData: any) => 
                    rData._id && selectedIds.indexOf(rData._id) === -1
                );
                const remainingRenewMembersCopy = renewTableStates['rowsCopy'].filter((rData: any) => 
                    rData._id && selectedIds.indexOf(rData._id) === -1
                );
                
                // Update renew table state (both rows and rowsCopy)
                setRenewTableStates({
                    ...renewTableStates,
                    rows: remainingRenewMembers,
                    rowsCopy: remainingRenewMembersCopy,
                });
                
                // Update exclude table state (both rows and rowsCopy)
                setExcludeTableStates({
                    ...excludeTableStates,
                    rows: newExcludeMembers,
                    rowsCopy: newExcludeMembersCopy,
                });
                
                // Reset selection
                setRenewSelectionState({
                    ...renewSelectionState,
                    renewMembers: [],
                    renewSelection: [],
                    renewReset: true,
                });
            }
        } else { //if 'toRenew'
            // check selected members if already transferred to Renew table
            if (excludeSelectionState['excludeMembers'] && excludeSelectionState['excludeMembers'].length > 0) {
                // Use array for ID lookup
                const existingRenewIds = renewTableStates['rows'].map((rData: any) => rData._id);
                
                // Filter out members that are already in the renew table
                const membersToAdd = excludeSelectionState['excludeMembers'].filter((emData: any) => 
                    emData._id && existingRenewIds.indexOf(emData._id) === -1
                );
                
                // Combine existing renew members with new ones
                const newRenewMembers = [...renewTableStates['rows'], ...membersToAdd];
                const newRenewMembersCopy = [...renewTableStates['rowsCopy'], ...membersToAdd];
                
                // Remove selected members from exclude table
                const selectedIds = excludeSelectionState['excludeMembers'].map((m: any) => m._id);
                const remainingExcludeMembers = excludeTableStates['rows'].filter((eData: any) => 
                    eData._id && selectedIds.indexOf(eData._id) === -1
                );
                const remainingExcludeMembersCopy = excludeTableStates['rowsCopy'].filter((eData: any) => 
                    eData._id && selectedIds.indexOf(eData._id) === -1
                );
                
                // Update exclude table state (both rows and rowsCopy)
                setExcludeTableStates({
                    ...excludeTableStates,
                    rows: remainingExcludeMembers,
                    rowsCopy: remainingExcludeMembersCopy,
                });
                
                // Update renew table state (both rows and rowsCopy)
                setRenewTableStates({
                    ...renewTableStates,
                    rows: newRenewMembers,
                    rowsCopy: newRenewMembersCopy,
                });
                
                // Reset selection
                setExcludeSelectionState({
                    ...excludeSelectionState,
                    excludeMembers: [],
                    excludeSelection: [],
                    excludeReset: true,
                });
            }
        }
    }

    const handleUploadSaveForNow = () => {
        const save_for_now_id = uploadSaveForNowData._id
        let data = uploadSaveForNowData.data
        data['renew_members'] = renewTableStates['rows']
        data['renew_selected'] = renewSelectionState['renewMembers']
        data['renew_selection'] = renewSelectionState['renewSelection']
        data['exclude_members'] = excludeTableStates['rows']
        data['exclude_selected'] = excludeSelectionState['excludeMembers']
        data['exclude_selection'] = excludeSelectionState['excludeSelection']
        API.patchSaveForNow(save_for_now_id, data)
            .then(response => {
                if (response && response.error) {
                    console.log('save for now err', response.error)
                    setLoadingState(false);
                    showMessageModal(
                        'Error saving information',
                        response.error
                    );
                } else {
                    console.log('save for now res', response)
                    setLoadingState(false);
                    showProcessModal(
                        'Save for now',
                        'Details are saved successfully.',
                        '',
                        'Okay',
                        () => {
                            onBackToDashboard();
                        },
                        () => {
                            onBackToDashboard();
                        },
                        true,
                    );
                }
            })
            .catch(error => {
                console.log('save for now catch err', error)
                setLoadingState(false);
                showMessageModal(
                    'Error saving information',
                    error
                );
            })
        // onBackToDashboard();
        // setLoadingState(false);
    }

    const createTicket = (isSaveForNow?: boolean, payload?: any) => {
        Processmaker.post('cases/', {
            pro_uid: '1647530415ef69a7c7f2a84054629541',
            tas_uid: '8908991705ef69a7c88f834084664369',
        })
            .then(response => {
                console.log('manual renewal ticket res', response);
                if (response && response.app_uid) {
                    const ticket_id = response.app_uid;
                    setPageData({
                        ...pageData,
                        ticketId: ticket_id,
                    })
                    executeTicketTrigger(ticket_id);
                    setTicketVariable(ticket_id, response);
                    if (isSaveForNow && payload) {
                        processSaveForNow(ticket_id, payload, pageData['ticketType']);
                    } else {
                        processRenewMembers();
                    }
                }
            })
            .catch(error => {
                console.log('manual renewal ticket err', error);
                setLoadingState(false);
                if (error.message === 'USER_DATA_NOT_FOUND') {
                    showProcessModal(
                        'User Data Error',
                        'User data not found. Please try logging in again.',
                        '',
                        'Okay',
                        () => {
                            // window.location.replace('../index.html#/');
                        },
                        () => {
                            // window.location.replace('../index.html#/');
                        },
                        true,
                    );
                } else {
                    setLoadingState(false);
                }
            });
    }

    const executeTicketTrigger = (appUid: string, success?) => {
        Processmaker.put(
            'cases/' + appUid + '/execute-trigger/6066708415df623339fa4f4056468668',
            {},
        )
            .then(response => {
                console.log('execute ticket trigger res', response);
                if (success) {
                    success();
                }
            })
            .catch(error => {
                console.log('execute ticket trigger err', error);
            })
    }

    const setTicketVariable = (appUid: string, pmaker_response?: any) => {
        Processmaker.put('cases/' + appUid + '/variable', {
            client_id: clientData['_id']
                ? clientData['_id'] : '',
            client_name: clientData['registered_name']
                ? clientData['registered_name'] : '',
            from_membership: true,
        }, true)
            .then(response => {
                console.log('set ticket variable res', response);
                if (pmaker_response) {
                    let payload = {
                        status: 'OPEN',
                        ticket_id: pmaker_response['app_number']
                            ? pmaker_response['app_number'].toString() : '',
                        pmaker_case_uid: appUid,
                        ticket_type: pageData['ticketType'],
                        client: clientData['registered_name']
                            ? clientData['registered_name'] : '',
                        client_id: clientData['_id']
                            ? clientData['_id'] : '',
                    };

                    API.postTicket(payload, true)
                        .then((postTicketRes) => {
                            console.log('set ticket postTicket res', postTicketRes);
                        })
                        .catch(error => {
                            console.log('set ticket postTicket err', error)
                        });
                }
            })
            .catch(error => {
                console.log('save ticker variable err', error);
            });
    }
    
    const handleSaveForNowAction = () => {

        const locationState = location.state || {};
        const detailsData = locationState.details_data || {};
        
        
        const {
            save_for_now_id,
            ticketId,
            batchnames,
            uploadMemberData,
            raw_uploaded_data,
            filtered_raw_data,
            selected_data_map,
            rows_start_at,
            uploaded_data,
            columns_data,
            gender_data,
            civil_status_data,
            plan_type_data,
            site_data,
            type_data,
            relationship_data,
            vip_data,
            ph_rider_data,
            member_consent_data,
            principals_options,
            trueTicketId,
            encodePageType,
            isSaveForNowMemberList,
            renewalType
          } = locationState;
          
          const {
              sender_name,
              sender_email,
              date_sent,
              sent_through_channel,
              ticket_id,
              file_url,
              isOcp,
              batch_name,
              number_of_members,
              default_data_map_save_for_now,
              data_map_save_for_now,
          } = detailsData;
      
          const data = {
            sender_name,
            sender_email,
            date_sent,
            sent_through_channel,
            batchnames,
            batch_name,
            ticket_id,
            file_url,
            isOcp,
            number_of_members,
            uploadMemberData,
            raw_uploaded_data,
            filtered_raw_data,
            selected_data_map,
            rows_start_at,
            uploaded_data,
            columns_data,
            gender_data,
            civil_status_data,
            plan_type_data,
            site_data,
            type_data,
            relationship_data,
            vip_data,
            ph_rider_data,
            member_consent_data,
            principals_options,
            default_data_map_save_for_now,
            data_map_save_for_now,
            renewalType: "manualRenewal"
          };


        if (activeStep === 0) { //this will run if "Save for Now" Button
            setLoadingState(true);
            let payload = {
                ...data,
                renew_members: renewTableStates['rows'],
                renew_selected: renewSelectionState['renewMembers'],
                renew_selection: renewSelectionState['renewSelection'],
                exclude_members: excludeTableStates['rows'],
                exclude_selected: excludeSelectionState['excludeMembers'],
                exclude_selection: excludeSelectionState['excludeSelection'],
                page_type: pageData['pageType'],
            }
            
            setLoadingState(true);
            processSaveForNow(pageData['ticketIdFromPath'], payload);
        } else if (activeStep === 1) {  //this will run if "step 1" and they click "Cancel" Button
            setActiveStep(0);
            setDialogModalState({ isOpen: false, title: '', message: '' });
            setProcessModalState({
                isOpen: false,
                modalTitle: '',
                modalMessage: '',
                closeText: 'Cancel',
                confirmText: 'Confirm',
                onClose: null,
                onConfirm: null,
                noCancelButton: false,
            });
            setSuccessModalState({ isOpen: false, title: '', message: '' });
        }
    }

    const processSaveForNow = (ticketId: string, data: any, ticketType?: any) => {
            setLoadingState(true);

            // If we have an existing saveForNowId, update the existing record
            if (saveForNowId && saveForNowId !== '') {

                API.patchSaveForNow(saveForNowId, data)
                    .then(response => {
                        if (response && response.error) {
                            setLoadingState(false);
                            showMessageModal(
                                'Error saving information',
                                response.error
                            );
                        } else {
                            setLoadingState(false);
                            showProcessModal(
                                'Save for now',
                                'Details are saved successfully.',
                                '',
                                'Okay',
                                null,
                                null,
                                true,
                            );
                        }
                        return response;
                    })
                    .then(() => {
                        return Processmaker.get('cases/' + ticketId);
                    })
                    .then(response => {

                        if (response && response.current_task && response.current_task[0] && 
                            response.current_task[0].tas_title === "Claimed Renew Memberlist") {
                            console.log('Task title is "Claimed Renew Memberlist 1", routing case...');
                            routeCase(ticketId, () => {
                                console.log('Case routed successfully to Manual Renewal - succeeding save');
                            });
                        } else {
                            console.log('this is the case if its already at Manual Renewal - no more route case needed');
                            console.log('Expected: "Manual Renewal"');
                            console.log('Actual:', response?.current_task?.[0]?.tas_title);
                        }
                    })
                    .catch(error => {
                        if (error.message && error.message.includes('cases/')) {
                        } else {
                            console.log('patch save for now catch err', error)
                            setLoadingState(false);
                            showMessageModal(
                                'Error saving information',
                                error
                            );
                        }
                    })
            } else {
                // Create new save record
                API.saveForNow(pageData['userId'], ticketId, ticketType, data)
                    .then(response => {
                        if (response && response.error) {
                            console.log('save for now err', response.error)
                            setLoadingState(false);
                            showMessageModal(
                                'Error saving information',
                                response.error
                            );
                        } else {
                            console.log('save for now res', response)
                            // Update saveForNowId for future saves
                            if (response._id) {
                                setSaveForNowId(response._id);
                            }
                            setLoadingState(false);
                            showProcessModal(
                                'Save for now',
                                'Details are saved successfully.',
                                '',
                                'Okay',
                                null,
                                null,
                                true,
                            );
                        }
                        return response;
                    })
                    .then(() => {
                        return Processmaker.get('cases/' + ticketId);
                    })
                    .then(response => {
                        console.log('Processmaker response:', response);
                        if (response && response.current_task && response.current_task[0] && 
                            response.current_task[0].tas_title === "Claimed Renew Memberlist") {
                            routeCase(ticketId, () => {
                                console.log('Case routed successfully - first save');
                            });
                        }
                    })
                    .catch(error => {
                        if (error.message && error.message.includes('cases/')) {
                            console.log('get case err', error);
                        } else {
                            console.log('save for now catch err', error)
                            setLoadingState(false);
                            showMessageModal(
                                'Error saving information',
                                error
                            );
                        }
                    })
            }
    }

    //saving of data to the new endpoint - collection /saveMembersForRenewal
    const handleSaveForRenewal = () => {
        setLoadingState(true);
        return Processmaker.get('cases/' + pageData['ticketIdFromPath'])
            .then(response => {
               

                // Extract app_number for batch name
                const appNumber = response?.app_number;
                const batchName = appNumber
                
                if (response && response.current_task && response.current_task[0]) {
                    const currentTaskTitle = response.current_task[0].tas_title;
                    
                    if (currentTaskTitle === "Finished Manual Renew of Members") {
                        console.log('Moving to verify renewal memberlist');
                        console.log('Using batch name:', batchName, 'from app_number:', appNumber);
    
                        return handleSubmitManualRenewalMembersForVerification(batchName)
                        .then(() => {
                            console.log('Members submitted successfully for verification');
                            return Promise.resolve(response);
                        });
                        
                    } else if (currentTaskTitle === "Verify Renewal Memberlist") {
                        console.log('Task is already at Verify Renewal Memberlist, we cannot do another route case, proceeding to success');
                        // Task is already at the correct state, proceed directly to success
    
                        return Promise.resolve(response);
                    } else {
                        console.log('Unexpected task title:', currentTaskTitle);
                        setLoadingState(false);
                    }
                }
            })
        .catch(error => {
            console.log('Error in verification ticket routing flow:', error);
            setLoadingState(false);
            return null;
    
        });
    }
        

    const handleSubmitManualRenewalMembersForVerification = async (batchName: string): Promise<any> => {
        try {
            // Extract full member objects from the table states instead of just IDs
            const includedMembers = renewTableStates['rows'].filter(member => member._id);
            // Get location state data
            const locationState = location.state || {};
            const detailsData = locationState.details_data || {};
            
            // Extract the channel and sender email from location state
            const channel = detailsData.sent_through_channel || 'Fax'; // fallback to 'Fax'
            const senderEmail = detailsData.sender_email || '';
            const senderName = detailsData.sender_name || '';

            const excludedMemberIds = excludeTableStates['rows']
                .map(member => member._id)
                .filter(id => id); // Filter out any null/undefined IDs
            
            // Get contract ID - use the actual BenefitPlan ObjectId from contracts array
            const contractId = (clientData.contracts && clientData.contracts[0]) || '';
            
            // Validate required fields
            if (!pageData['clientId']) {
                throw new Error('Client ID is required');
            }
            if (!contractId) {
                throw new Error('Contract ID is required');
            }
            if (!pageData['ticketIdFromPath']) {
                throw new Error('Case ID is required');
            }
            if (!includedMembers || includedMembers.length === 0) {
                throw new Error('At least one member must be included for renewal');
            }
            
            
            const response = await API.postManualRenewalMembersForVerification(
                pageData['clientId'],        
                contractId,                   
                pageData['ticketIdFromPath'], 
                includedMembers,
                batchName,         
                'Manual Renewal',
                excludedMemberIds,
                channel,    
                senderEmail,  
                senderName
            )
            
            if (response && response.error) {
                console.log('Submission error:', response.error);
                throw new Error(response.error);
            }
            
            setMemberUpload(response)
            console.log('Manual renewal members submitted successfully:', response);
            return response;
            
        } catch (error) {
            console.log('Error submitting manual renewal members:', error);
            setLoadingState(false);
            throw error;
        }
    };
        

    const handleRenewAction = () => {
        if (activeStep === 0) {
            const totalMembersToRenew = renewTableStates['rows'].length;
            const corporateName = clientData['registered_name'];

            showProcessModal(
                'Confirm Renewal',
                `Submitting this list will send it to the Account Processing Verifier for validation. 
                  Are you sure you want to renew ${totalMembersToRenew} members to the corporate account ${corporateName}?`,
                'Cancel',
                'Confirm',
                null,
                () => {
                    setLoadingState(true);

                    Promise.resolve()
                        .then(() => handleSaveForRenewal())
                        .then(() => processRenewMembers())
                }
            );
        }  else if (activeStep === 1) {
                    
                    setLoadingState(true);

                     const memberUploadId = memberUpload._id
                     const caseId = pageData['ticketIdFromPath'];
                        
                    if (!memberUploadId) {
                     throw new Error('Failed to get member upload ID from response');
                    }
                    

                    API.confirmMemberUpload(memberUploadId, caseId)
                    
                    setLoadingState(false);
                        
                    showProcessModal(
                        'Submission Successful',
                        'Member file was successfully processed',
                        '',
                        'Okay',
                        () => {
                            onBackToDashboard();
                        },
                        () => {
                            onBackToDashboard();
                        },
                        true,
                    );
        }
}

    const processRenewMembers = () => {
        let membersToRenew = renewTableStates['rows'];
        if (membersToRenew.length > 0) {
            let membersToRenewIds: any[] = membersToRenew.map(mtrData => {
                if (mtrData['_id']) {
                    return mtrData['_id'];
                } else {
                    return null;
                }
            }).filter(idData => idData !== null);

            let payload = [
                {
                    memberIds: membersToRenewIds,
                    user_id: user['userId'] ? user['userId'] : '',
                    user_name: user['userName'] ? user['userName'] : '',
                }
            ];
            console.log('renew members payload', payload)

            setLoadingState(true);

            API.putRenewMembers(payload, pageData['clientId'])
                .then(response => {
                    console.log('renew members res', response)
                    if (response && response.error) { //error
                        setLoadingState(false);
                        showMessageModal(
                            'Renew Members Failed',
                            'An error occurred while trying to renew the members.'
                        )
                    } else { //success
                        setExceptionReportData({
                            renewed: renewTableStates.rows,
                            excluded: excludeTableStates.rows,
                            other_corps: response['members_from_other_client']
                              ? response['members_from_other_client'] : [],
                            member_ids_not_found: response['member_ids_not_found']
                              ? response['member_ids_not_found'] : [],
                          });
                    }
                })
                .then(() => {
                    return Processmaker.get('cases/' + pageData['ticketIdFromPath']);
                })
                .then(response => {
                    console.log('Processmaker response:', response);
                    console.log('Current task:', response?.current_task);
                    console.log('Task title:', response?.current_task?.[0]?.tas_title);

                    //FOCUS MUNA SA SAVING OF DATA UNG API NI JACOB - DO NOT TOUCH CODE BELOW THO -  5855 next ticket
                    
                    //from "Claimed Renew Memberlist" to "Manual Renewal" to "Finished Manual Renew of Members"
                    //ito ung case na direct nag copy paste ng url 
                    if (response && response.current_task && response.current_task[0] && 
                        response.current_task[0].tas_title === "Claimed Renew Memberlist") {
                        console.log('CASE1: Task title is "Claimed Renew Memberlist 1", routing case...');
                        routeCase(pageData['ticketIdFromPath'], () => {
                            console.log('Case routed successfully - on CONFIRM button click route 1');//  if claimed renew memberlist, becomes "Manual Renewal"
                            routeCase(pageData['ticketIdFromPath'], () => {
                                console.log('Case routed successfully - on CONFIRM button click route 2'); //then it becomes "Finished Manual Renew of Members"
                            });
                        });
                     //from "Manual Renewal" to "Finished Manual Renew of Members"
                     //ito yung case na from encode member page to manual renewal page = w/c triggers route case-  itll become Manual Renewal tas title -> finished manual renew of members
                    } else if (response && response.current_task && response.current_task[0] && 
                        response.current_task[0].tas_title === "Manual Renewal") {
                        console.log('CASE2: FROM manual renewal to finished manual renew of members');
                        routeCase(pageData['ticketIdFromPath'], () => {
                            console.log('Case routed successfully - on CONFIRM button click 1 case 2');
                        }); 
                    } else {
                        console.log('Routing is already at Finished Manual Renew of Members');
                    }
                })
                .catch(error => {
                    if (error.message && error.message.includes('cases/')) {
                        console.log('get case err', error);
                    } else {
                        console.log('patch save for now catch err', error)
                        setLoadingState(false);
                        showMessageModal(
                            'Error saving information',
                            error
                        );
                    }
                })
        } else {
            setLoadingState(false);
            showMessageModal(
                'Renew Members Failed',
                'No member data to process for renewal.'
            )
        }
    }


    const routeCase = (ticketId, success) => {
        Processmaker.put('cases/' + ticketId + '/route-case', {})
            .then(() => {
                success();
            }).catch(error => {
                console.log('route case err', error);
                if (error.message === 'USER_DATA_NOT_FOUND') {
                    setLoadingState(false);
                    showProcessModal(
                        'User Data Error',
                        'User data not found. Please try logging in again.',
                        '',
                        'Okay',
                        () => {
                            // window.location.replace('../index.html#/');
                        },
                        () => {
                            // window.location.replace('../index.html#/');
                        },
                        true,
                    );
                } else {
                    setLoadingState(false);
                }
            })
    }

    const showProcessModal = (
        title: string,
        message: string,
        closeLabel: string,
        confirmLabel: string,
        onCloseCallback?: any,
        onConfirmCallback?: any,
        isNoCancel?: boolean,
    ) => {
        setProcessModalState({
            ...processModalState,
            isOpen: true,
            modalTitle: title,
            modalMessage: message,
            closeText: closeLabel,
            confirmText: confirmLabel,
            onClose: () => {
                if (onCloseCallback) {
                    onCloseCallback();
                }
                resetProcessModalState();
            },
            onConfirm: () => {
                if (onConfirmCallback) {
                    onConfirmCallback();
                }
                resetProcessModalState();
            },
            noCancelButton: isNoCancel ? isNoCancel : false,
        })
    }

    const resetProcessModalState = () => {
        setProcessModalState({
            isOpen: false,
            modalTitle: '',
            modalMessage: '',
            closeText: 'Cancel',
            confirmText: 'Confirm',
            onClose: null,
            onConfirm: null,
            noCancelButton: false,
        });
    }

    const showMessageModal = (modalTitle: string, modalMessage: string) => {
        setDialogModalState({
            isOpen: true,
            title: modalTitle,
            message: modalMessage,
        })
    }

    const handleSuccessModalClose = () => {
        setSuccessModalState({
            isOpen: false,
            title: '',
            message: '',
        });
        onBackToDashboard();
    };

    const onBackToDashboard = () => {
        setRedirectState({
            redirectFlag: true,
            redirectInfo: {
                pathname: '/membership/',
            }
        })
    }

    const rowDataFixer = (array: any) => {
        let newEntry: any = [];
        if (array.length > 0) {
            array.forEach(data => {
                let newRow = {
                    ...data,
                    member_status_before_expiration: data.member_status_before_expiration || data.formatted_member_status || data.member_status
                }
                newEntry.push(newRow)
            })

            return newEntry
        }
        return []
    }

    if (redirectState['redirectFlag'] === true) {
        return <Redirect to={redirectState['redirectInfo']} />
    } else {
        return (
            <div className={clsx('RenewPage')} style={{ paddingLeft: '25px', paddingTop: '20px', paddingRight: '25px', }}>
                {loadingState ? <Loader /> : null}
                <Grid container className={clsx('member-header')}>
                    <Grid item xs={12}>
                        <Components.UI.BreadcrumbsComponent items={breadCrumbs} />
                    </Grid>
                    <Grid item xs={12}>
                        <PageHeaderComponent
                            id={'upload_member_stepper'}
                            label={pageHeaders[activeStep]}
                        />
                    </Grid>
                    <Grid item container xs={12} style={{ paddingTop: 40, paddingBottom: 140 }}>
                        {activeStep === 0 ? (
                            <>
                                <Grid item xs={5}>
                                    <Typography
                                        style={{ fontSize: '14px', color: '#272E4C', marginBottom: '5px' }}>
                                        Members to Renew
                            </Typography>
                                    <RenewalTable
                                        id={'members-to-renew-table'}
                                        rows={rowDataFixer(renewTableStates.rows)}
                                        columns={MEMBERS_TO_RENEW_COLUMNS}
                                        columnExtensions={MEMBERS_TO_RENEW_COLUMN_EXTENSIONS}
                                        intgFltrColumnExtensions={MEMBERS_TO_RENEW_INTEGRATED_FILTERS}
                                        filterExtensions={MEMBERS_TO_RENEW_FILTER_EXTENSIONS}
                                        message={'No data to display'}
                                        selection={renewSelectionState['renewSelection']}
                                        onRowSelection={(selection: any[]) => {
                                            handleRowSelection(selection, 'renew')
                                        }}
                                        isResetSelection={renewSelectionState['renewReset']}
                                        onResetSelectionFlag={() => {
                                            setRenewSelectionState({
                                                renewMembers: [],
                                                renewSelection: [],
                                                renewReset: false,
                                            })
                                        }}

                                        defaultPageSizes={pageSizes}
                                        onSortingChange={(sorting: any) => {
                                            handleSortingChange(sorting, 'renew');
                                        }}
                                        defaultSort={tableSort['renew']}
                                        onFilterChange={(filters: any[]) => {
                                            handleFilterChange(filters, 'renew');
                                        }}
                                        defaultFilter={tableFilter['renew']}
                                    />
                                </Grid>
                                <Grid item xs={2} style={{ alignItems: 'center', justifyContent: 'center' }}>
                                    <Grid item xs={12} style={{ paddingLeft: '25%', paddingRight: '25%', marginTop: '60%', marginBottom: '20px' }}>
                                        <Button
                                            variant="contained"
                                            color="primary"
                                            style={{ minWidth: 130 }}
                                            onClick={() => {
                                                handleMemberAction('toExclude')
                                            }}
                                        >
                                            Exclude
                                        <ArrowForward />
                                        </Button>
                                    </Grid>
                                    <Grid item xs={12} style={{ paddingLeft: '25%' }}>
                                        <Button
                                            variant="contained"
                                            color="secondary"
                                            style={{ minWidth: 130 }}
                                            onClick={() => {
                                                handleMemberAction('toRenew')
                                            }}
                                        >
                                            <ArrowBack />
                                        Renew
                                </Button>
                                    </Grid>
                                </Grid>
                                <Grid item xs={5}>
                                <Typography
                                style={{ fontSize: '14px', color: '#272E4C', marginBottom: '5px' }}
                                >
                                Members to Exclude from Renewal
                                </Typography>
                                    <RenewalTable
                                        id={'members-to-exclude-table'}
                                        rows={rowDataFixer(excludeTableStates.rows)}
                                        columns={MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMNS}
                                        columnExtensions={MEMBERS_TO_EXCLUDE_FROM_RENEWAL_COLUMN_EXTENSIONS}
                                        intgFltrColumnExtensions={MEMBERS_TO_EXCLUDE_INTEGRATED_FILTERS}
                                        filterExtensions={MEMBERS_TO_EXCLUDE_FROM_RENEWAL_FILTER_EXTENSIONS}
                                        message={'No data to display'}
                                        selection={excludeSelectionState['excludeSelection']}
                                        onRowSelection={(selection: any[]) => {
                                            handleRowSelection(selection, 'exclude')
                                        }}
                                        isResetSelection={excludeSelectionState['excludeReset']}
                                        onResetSelectionFlag={() => {
                                            setExcludeSelectionState({
                                                excludeMembers: [],
                                                excludeSelection: [],
                                                excludeReset: false,
                                            })
                                        }}

                                        defaultPageSizes={pageSizes}
                                        onSortingChange={(sorting: any) => {
                                            handleSortingChange(sorting, 'exclude');
                                        }}
                                        defaultSort={tableSort['exclude']}
                                        onFilterChange={(filters: any[]) => {
                                            handleFilterChange(filters, 'exclude');
                                        }}
                                        defaultFilter={tableFilter['exclude']}
                                    />
                                </Grid>
                            </>
                        ) : activeStep === 1 ? (
                            <ExceptionReport
                                reportData={exceptionReportData}
                                memberDataFields={clientData.member_data_fields}
                            />
                        ) : null}
                    </Grid>
                    <Grid item xs={12}>
                        <FloatingButtons
                            leftButtons={
                                <div>
                                    <Button
                                        id='button-first-action'
                                        data-cy='button-first-action'
                                        className={classes.actionButton}
                                        variant="contained"
                                        color="primary"
                                        size="small"
                                        onClick={ handleSaveForNowAction}
                                    >
                                        {activeStep === 0 ? 'Save for Now' : 'Cancel'}
                                    </Button>
                                    <Button
                                        id='button-second-action'
                                        data-cy='button-second-action'
                                        className={classes.actionButton}
                                        variant="contained"
                                        color="primary"
                                        size="small"
                                        onClick={ handleRenewAction}>
                                        {activeStep === 0 ? 'Renew' : 'Confirm Renewal'}
                                    </Button>
                                </div>
                            }
                            rightButtons={null}
                        />
                    </Grid>
                </Grid>

                <ModalComponent
                    id="dialog-renew-modal"
                    isModalOpen={dialogModalState['isOpen']}
                    title={dialogModalState['title']}
                    message={dialogModalState['message']}
                    onClose={() => {
                        setDialogModalState({
                            isOpen: false,
                            title: '',
                            message: '',
                        })
                    }}
                />

                <ConfirmationModalComponent
                    id="confirm-renew-modal"
                    isModalOpen={processModalState['isOpen']}
                    modalTitle={processModalState['modalTitle']}
                    modalMessage={processModalState['modalMessage']}
                    closeText={processModalState['closeText']}
                    confirmText={processModalState['confirmText']}
                    onClose={processModalState['onClose']}
                    onConfirm={processModalState['onConfirm']}
                    noCancelButton={processModalState['noCancelButton']}
                />

                <ModalComponent
                    id="renewal-success-modal"
                    isModalOpen={successModalState.isOpen}
                    title={successModalState.title}
                    message={successModalState.message}
                    onClose={handleSuccessModalClose}
                />
            </div>
        )
    }
}