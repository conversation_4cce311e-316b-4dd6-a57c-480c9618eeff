import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Button,
  IconButton,
  TextField,
  InputLabel,
} from '@material-ui/core/';
import CloseIcon from '@material-ui/icons/Close';

const useStyles = makeStyles(theme => ({
  root: {
    flexWrap: 'wrap',
  },
  dialog: {
    align: 'center',
    justifyContent : 'center',
    width: '95%',
    fontFamily: 'usual',
  },
  dialogContent: {
    paddingTop: '0px',
    paddingLeft: '24px',
    paddingRight: '24px',
  },
  dialogContentText: {
    fontSize: '0.875rem',
  },
  container: {
    paddingRight: '15px',
    paddingLeft: '15px',
    minWidth: '680px',
    width: 'auto',
  },
  dialogTitle: {
    paddingTop: theme.spacing(5),
    marginRight: '64px',
  },
  dialogFirstTitle: {
    fontSize: '16px',
    color: '#272E4C',
    fontWeight: 600,
    marginBottom: '5px',
  },
  dialogSecondTitle: {
    fontSize: '14px',
    color: '#272E4C',
    paddingTop: theme.spacing(2),
  },
  dialogAction: {
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(2),
  },
  boldText: {
    fontWeight: 'bold',
  },
  cancelButton: {
    width: 115,
    marginRight: 25,
    border: '1px solid #3AB77D',
    color: '#3AB77D',
    backgroundColor: '#FFFFFF',
  },
  disapproveButton: {
    textTransform: 'none',
    minWidth: 129,
    marginRight: 25,
    color: '#FFFFFF',
    backgroundColor: '#FF5555',
    '&:hover': {
      background: '#FF7777',
    },
  },
  approveButton: {
    textTransform: 'none',
    minWidth: 129,
    marginRight: 25,
    color: '#FFFFFF',
    backgroundColor: '#3AB77D',
  },
  forConfirmation: {
    textTransform: 'none',
    minWidth: 129,
    marginRight: 25,
    color: '#FFFFFF',
    backgroundColor: '#3AB77D',
  },
  label: {
    fontSize: '14px',
    color: '#272E4C',
    fontWeight: 600,
    paddingTop: theme.spacing(2),
  },
  previousButton: {
    minWidth: 120,
    fontWeight: 600,
  },
  nextButton: {
    minWidth: 130,
    fontWeight: 600,
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  tab: {
    textAlign: 'left',
    alignItems: 'left',
  },
}));

interface AddActionRemarksModalProps {
  id?: string;
  isModalOpen: boolean;
  row: number;
  tab: string; // 'Partial' | 'Incomplete' | 'Conflict' | 'Validation' | 'Disapproved';
  action: string; // 'Approve' | 'Disapprove';
  actionValue: any;
  onActionSubmit: (
    row: number,
    tab: string,
    action: any,
    remarks: string,
    userRemarks?:any,
    newValidation?:any
  ) => void;
  onClose: (row: number) => void;
  uploadDetails?: any
}

export const AddActionRemarksModal: React.FC<AddActionRemarksModalProps> = (
  props: AddActionRemarksModalProps,
): JSX.Element => {
  const [isModalOpen, setIsModalOpen] = useState(props.isModalOpen);
  const [modalProperties, setModalProperties] = useState({
    title: 'Approve',
    subTitle1: 'approved',
    subTitle2: 'approval',
    button: 'approveButton',
  });
  const [remarks, setRemarks] = useState('');
  const classes = useStyles();
  const [userRemarks, setUserRemarks] = useState('');
  const [userRemarksDisplay, setUserRemarksDisplay] = useState('');

  useEffect(() => {
    setIsModalOpen(props.isModalOpen);
    if (props.isModalOpen) {
      // setRemarks('');
      const userRemarks: string = props.uploadDetails.rowData.user_remarks || ''
      setRemarks(userRemarks)
      setUserRemarks(userRemarks)
    }
  }, [props.isModalOpen]);

  useEffect(() => {
    switch (props.action) {
      case 'Approve':
        setModalProperties({
          title: 'Approve',
          subTitle1: 'approved',
          subTitle2: 'approval',
          button: 'approveButton',
        });
        break;

      case 'Disapprove':
        setModalProperties({
          title: 'Disapprove',
          subTitle1: 'disapproved',
          subTitle2: 'disapproval',
          button: 'disapproveButton',
        });
        break;

      case 'For Confirmation':
        setModalProperties({
          title: 'For Confirmation',
          subTitle1: 'for confirmation',
          subTitle2: 'confirmation',
          button: 'forConfirmation',
        });
        break;
    }
  }, [props.action]);

  const updateDisplayRemarks = (userRemarks) => {
    if(props.uploadDetails && props.uploadDetails['rowData']) {
      let userRemsDisplay = props.uploadDetails.rowData.user_remarks
      setUserRemarksDisplay(userRemsDisplay)
      setRemarks(userRemsDisplay)
    } else {
      console.log('err props', props)
    }
  };

  const onActionSubmit = () => {
    if (props.onActionSubmit) {
      // delete props.uploadDetails.rowData['user_validation'] 

      if(props && props.uploadDetails.rowData.member_details['validation_status'] !== undefined) {
        console.log('update valdation status if approved action', props.uploadDetails.rowData.member_details['validation_status'])
        let memberValid = props.uploadDetails.rowData.member_details['validation_status']

        if(props.action === 'Approve' || props.actionValue === 'approve') {
          memberValid.action = 'Approve'
          memberValid.status = 'APPROVED'
        }
      }
      console.log('add action1',  props.actionValue);
      props.onActionSubmit(props.row, props.tab, props.actionValue, remarks, userRemarks);
      updateDisplayRemarks(userRemarks)
    }
  };

  const onActionClose = () => {
    if (props.onClose) {
      props.onClose(props.row)
    }
  }

  return (
    <Dialog
      id={props.id}
      maxWidth={false}
      open={isModalOpen}
      scroll={'paper'}
      aria-labelledby="modal-title"
      className={classes.dialog}
      style={{ zIndex: 9999 }}
    >
      <DialogTitle
        className={classes.dialogTitle}
        disableTypography={true}
        id="modal-title"
      >
        <div className={classes.dialogFirstTitle}>
          {modalProperties.title} Member
        </div>
        <div className={classes.dialogSecondTitle}>
          This entry will be {modalProperties.subTitle1}. You may add your
          reason for {modalProperties.subTitle2} below.
        </div>
        <IconButton
          id='close_button'
          data-cy='close_button'
          aria-label="close"
          className={classes.closeButton}
          onClick={onActionClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container>
          <Grid item xs={12}>
            <InputLabel
              className={classes.label}
              htmlFor="action_modal_remarks_tf"
            >
              Remarks
            </InputLabel>
            <TextField
              style={{ marginTop: '8px', width: '100%' }}
              id="action_modal_remarks_tf"
              data-cy='action_modal_remarks_tf'
              margin="normal"
              variant="outlined"
              multiline={true}
              minRows={10}
              value={remarks}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { value } = e.target;
                setRemarks(value);

                if(value && value !== undefined) {
                  setUserRemarks(value)
                } else {
                  // setUserRemarks('')

                }
              }}
              inputProps={{ 'aria-label': 'bare' }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className={classes.dialogAction}>
        <Grid
          container
          direction="row"
          justifyContent ="space-evenly"
          alignItems="stretch"
        >
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <Button
              id="cancel_button"
              data-cy='cancel_button'
              className={classes.cancelButton}
              variant="contained"
              color="secondary"
              onClick={onActionClose}
            >
              Cancel
            </Button>
          </Grid>
          <Grid item xs={4} style={{ textAlign: 'center' }}>
            <Button
              id="submit_button"
              data-cy='submit_button'
              className={classes[modalProperties.button]}
              variant="contained"
              color="default"
              onClick={onActionSubmit}
            >
              {modalProperties.title}
            </Button>
          </Grid>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

AddActionRemarksModal.defaultProps = {
  id: 'add_action_remarks_modal_id',
  isModalOpen: false,
  action: 'Approve',
};
