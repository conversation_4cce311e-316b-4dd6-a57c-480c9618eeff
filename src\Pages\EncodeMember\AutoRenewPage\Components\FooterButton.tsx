import { Button, makeStyles } from "@material-ui/core";
import { FloatingButtons } from "Components/UI/FloatingButtons";
import { memo } from "react";
import { useRenewalContext } from "../Context/RenewalContext";
import { ConfirmationModal } from "Components/UI/ConfirmationModal";
import { API } from "Pages/API";
import { useLocation, useParams } from 'react-router-dom';
import { isValidAutoRenewalPayload } from "../Validation/checkAutoRenewParams";


const ButtonStyle = makeStyles({
  root: {
    height: '40px',
    minWidth: '150px',
    '&.Mui-disabled': {
      backgroundColor: '#E4E4E4',
      color: 'rgba(0, 0, 0, 0.61)',
    },
  },
  rightButton: {
    height: '40px',
    minWidth: '99px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
  },
  leftButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    border: '2px solid #3AB77D',
  },
  backButton: {
    padding: '0px 8px',
    height: '40px',
    width: '150px',
    backgroundColor: '#3C394A',
    color: '#FFFFFF',
    textTransform: 'none',
    '&:hover': {
      background: '#000000',
    },
  },
  leftText1: {
    fontSize: '13px',
  },
  leftText2: {
    fontSize: '7px',
  },
  buttonTitle: {
    fontWeight: 700,
    fontSize: 13,
  },
  buttonSubTitle: {
    fontWeight: 400,
    fontSize: 7,
  },
});

interface RouteParams {
  client_id?: string;
  ticket_id: string;
}

function RenewalFooterButton(props) {
const buttonclasses = ButtonStyle();

const location  = useLocation();
const searchParams = new URLSearchParams(location.search);

const caseId = searchParams.get("ticketId"); 
const contractId = searchParams.get("contractId"); 

const params = useParams<RouteParams>();
const { client_id, ticket_id} = params || {};


const renewalsStateProps = useRenewalContext();
const { handleBackButton, membersForRenewal, clientName } = renewalsStateProps?.props
const { isConfirmRenewal, setIsConfirmRenewal, isOpenFinishRenewalModal, setIsOpenFinishRenewalModal } = renewalsStateProps?.modalStateProps || {}


const submitMembersForRenewal = async () => {

  const saveAutoRenewParams = {
      client_id,
      contractId,
      case_id:ticket_id,
      memberObjIds: membersForRenewal.map((member) => member._id),
      batch_name: caseId,
      type: "Auto Renewal"
  }

  if (!isValidAutoRenewalPayload(saveAutoRenewParams)) {
  console.error("Validation failed: payload structure is incorrect");
  return;
  }
 
  API.postAutoRenewalMembersForVerification(saveAutoRenewParams).then((res) => {
    if (res.status === 200) {
      setIsOpenFinishRenewalModal(true);
      setIsConfirmRenewal(false);
    }
  })
}
const leftButtons = () =>{
  return(
    <>
      <Button
        data-cy={'upload_member_next_btn'}
        id={'upload_member_next_btn'}
        className={buttonclasses.root}
        variant="contained"
        color="primary"
        size="small"
        disabled={props.disabled}
        onClick={()=> setIsConfirmRenewal(true)}
      >
        Next
      </Button>
    </>
  )
}

const backButton = ()=> {
  return (
    <div>
      <Button
        id={'encode_member_back_btn'}
        className={buttonclasses.backButton}
        variant="contained"
        size="small"
        onClick={handleBackButton}
      >
        Back
      </Button>
    </div>
  );
}
  
  return (
    <>
     <FloatingButtons
        leftButtons={ leftButtons() }
        rightButtons={ backButton() }
      />
      <ConfirmationModal
                title={'Confirm Renewal'}
                id={'encode-member-modal'}
                isModalOpen={isConfirmRenewal}
                company={
                  clientName || 'N/A'
                } //label
                member_count={
                  membersForRenewal?.length || 0
                }
                onSubmit={()=>{
                  submitMembersForRenewal()
                  setIsOpenFinishRenewalModal(true)
                  setIsConfirmRenewal(false)
                }}
                onClose={()=> setIsConfirmRenewal(false)}
              />
    </>
    
  );
}



export default memo(RenewalFooterButton);