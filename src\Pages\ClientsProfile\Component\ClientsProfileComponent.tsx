import React, { useState, useEffect } from 'react';
import { Redirect } from 'react-router-dom';
import { Grid } from '@material-ui/core/';
import Avatar from '@material-ui/core/Avatar';
import { makeStyles } from '@material-ui/core/styles';
import { GlobalFunction } from 'Components/UI/GlobalFunction';
import { SubHeader } from 'Components/UI/SubHeader';
import { SideMenu } from 'Components/UI/SideMenu';
import { BasicInformation } from './BasicInformation';
import { MembersComponent } from './Members';
import { TransmittalListHistory } from './TransmittalListHistory';
import { ContactsBranches } from './ContactsBranches';
import { HMOInformation } from './HMOInformation';
import { MemberFields } from './MemberFields' 
import { ServicingInstructions } from './ServicingInstructions'
import { ClientUtilization } from './ClientUtilization'
import { ClientHistory } from './ClientHistory';
import { ClientCommissions } from './ClientCommissions';
import { Documents } from './Documents';
import { AuditLogsPage } from './AuditLogs/AuditLogsPage';
import { ClientTickets } from './ClientTickets';
import _ from 'lodash';
import { API } from '../../API';
import { cloneDeep } from 'lodash';
import Link from '@material-ui/core/Link'; 
import moment from 'moment';
import { Loader } from './../../../Components/UI/LoadingIndicator';
import { ActionMemoRecipients } from './ActionMemoRecipients'
import { ActionMemoHistory } from './ActionMemoHistory';
import { INITIAL_STATE } from './HMOInformationBenefits/ProposalPage/ProposalReducer';
const membershipModuleManagerGroup = ['APD_MANAGER GROUP', 'APD_SUPERVISOR GROUP', 'APD_TL GROUP', 'APD_MANAGER', 'APD_SUPERVISOR', 'APD_TL'];
const membershipPrintingGroup = ['DEPT_APD_PRINTING GROUP', 'APD_PRINTING_STAFF GROUP', 'APD_PRINTING_STAFF'];

let API_URL = localStorage.getItem('CLIENT_URL')
  ? localStorage.getItem('CLIENT_URL')
  : localStorage.getItem('XDEV_CLIENT_URL');


const viewClientStyles = makeStyles({
    gridItem: {
      // marginTop: '10px',
      // marginBottom: '20px',
    },
    editContainer: {
      paddingRight: '20px',
    },
    gridContainer: {
      paddingTop: '30px',
      paddingBottom: '30px',
      borderBottom: '1px solid #ccc',
    },
    UploadFileContainer: {
      position: 'fixed',
      right: 0,
      bottom: 0,
      zIndex: 200,
      backgroundColor: '#f5f7fb',
      padding: '30px',
      borderRadius: '10px',
      boxShadow:
        '0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)',
    },
    UploadFileLink: {
      padding: '15px',
      textAlign: 'center',
    },
    dropzone: {
      width: '460px',
      height: '70px',
      marginTop: '10px',
      marginBottom: '20px',
      border: '2px dashed rgba(30, 32, 113, 0.3)',
      borderRadius: '5px',
      backgroundColor: '#e6e8ee',
      color: '#7b8094',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
    },
    ProposalRadio: {
      paddingLeft: '40px',
    },
    containerColor: {
      backgroundColor: '#E5E5E5',
    },
    linkStyleNotSelected: {
      fontSize: 14,
      fontWeight: 700,
      color: 'rgba(58, 183, 125, 0.3)',
      cursor: 'pointer',
    },
    linkStyleSelected: {
      fontSize: 14,
      fontWeight: 700,
      color: 'rgba(58, 183, 125, 1)',
      cursor: 'pointer',
    },
    linkStyleDisabled: {
      fontSize: 14,
      fontWeight: 700,
      color: '#9EA3AC',
      cursor: 'pointer',
    },
    documentTopNavNotSelected: {
      color: 'rgba(39, 46, 76, 0.5)',
      fontSize: '0.875rem',
      fontWeight: 400,
      cursor: 'pointer',
      paddingRight: '20px',
    },
    documentTopNavSelected: {
      textDecoration: 'underline',
      color: '#3AB77D',
      fontSize: '0.875rem',
      fontWeight: 600,
      cursor: 'pointer',
      paddingRight: '20px',
    },
    HistoryTopNavNotSelected: {
      color: 'rgba(39, 46, 76, 0.5)',
      paddingLeft: 24,
      paddingRight: 24,
      fontSize: '0.875rem',
      fontWeight: 400,
      cursor: 'pointer',
      marginRight: '20px',
    },
    HistoryTopNavSelected: {
      textDecoration: 'underline',
      color: '#3AB77D',
      paddingLeft: 24,
      paddingRight: 24,
      fontSize: '0.875rem',
      fontWeight: 600,
      cursor: 'pointer',
      marginRight: '20px',
    },
    searchField: {
      backgroundColor: 'white',
    },
    fieldContainer: {
      padding: '5px',
    },
    fieldLabel: {
      color: 'rgba(39, 46, 76, 0.6)',
      fontSize: '12px',
      marginBottom: '5px',
    },
    fieldStyle: {},
    searchInputAdornment: {
      color: '#00000061',
      marginRight: '8px',
    },
    generateReport: {
      paddingRight: '15px',
      marginTop: 15,
      textAlign: 'center',
    },
    downloadIcon: {
      color: 'rgba(58, 183, 125, 1)',
      fontSize: 14,
      marginRight: '6px',
    },
    downloadDisabled: {
      color: '#9EA3AC',
      fontSize: 14,
      marginRight: '6px',
    },
    contactheader: {
      fontSize: 14,
    },
    chipdes: {
      marginTop: 10,
      marginRight: '6px',
    },
    inputLabel: {
      color: '#272e4c',
    },
    outlinedInput: {
      marginTop: '10px',
      marginBottom: '20px',
      width: '100%',
      textOverflow: 'scroll',
    },
    keyboardDatePicker: {
      marginTop: '10px',
      marginBottom: '20px',
      width: '212px',
    },
    radio: {
      '&.Mui-checked': {
        color: '#1e2071',
      },
    },
    typooverflow: {
      overflow: 'scroll',
      maxWidth: '80%',
    },
    AddPropHeader: {
      fontSize: 14,
    },
    AddPropInst: {
      fontSize: 12,
    },
    CheckDesc: {
      paddingTop: '15px',
    },
    outlineinputadornment: {
      maxWidth: '100%',
    },
    editinputadornment: {
      backgroundColor: '#757575',
      borderRadius: '14px',
      padding: '5px',
      fontSize: 12,
      marginRight: '5px',
    },
    contactHeader: {
      fontWeight: 'bold',
    },
    uploadFile: {
      border: '1.5px dashed',
      color: '#7b8099',
      backgroundColor: '#E6E8EE',
      borderRadius: 5,
      width: 290,
      fontSize: 10,
    },
    button: {
      color: 'rgb(58, 183, 125)',
    },
    uploadParagraph: {
      textAlign: 'center',
      marginLeft: '25px',
      marginTop: '-20px',
    }
  });
const useStyles = makeStyles(theme => ({
  mainContainer: {
    padding: '32px 24px 25px',
  },
  main: {
    backgroundColor: '#F5F7FB',
    padding: '32px 24px',
  },
  headMain: {
    // backgroundColor: '#F5F7FB',
  },
  gridContainer: {
    padding: theme.spacing(1),
    paddingBottom: '75px',
  },
  gridContainerNoPadding: {
    padding: theme.spacing(1),
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 700,
    color: '#272e4c',
  },
  subSectionTitle: {
    fontSize: 14,
    fontWeight: 400,
    color: '#272e4c',
  },
  editBtn: {
    width: '135px',
    fontSize: '14px',
    color: '#fff',
  },
  alignRight: {
    textAlign: 'right',
  },
  sideBarContainer: {
    borderRight: '1px solid #ccc',
    paddingRight: '25px',
  },
  contentContainer: {
    paddingLeft: '25px',
  },
  sideBarLists: {
    padding: 0,
    margin: 0,
    listStyle: 'none',
  },
  sideBarLink: {
    color: '#272e4c',
    margin: '0 0 10px',
    display: 'block',
  },
  fieldGroupTitle: {
    fontSize: 13,
    fontWeight: 700,
    color: '#272e4c',
  },
  listItemTextRoot: {
    flex: 'none',
  },
  listItemTextPrimarySelected: {
    fontSize: 14,
    fontWeight: 700,
    color: 'rgba(39, 46, 76, 1)',
    cursor: 'pointer',
  },
  listItemTextPrimaryNotSelected: {
    fontSize: 14,
    fontWeight: 700,
    color: 'rgba(39, 46, 76, 0.3)',
    cursor: 'pointer',
  },
  dropzone: {
    width: '460px',
    height: '88px',
    marginTop: '10px',
    marginBottom: '20px',
    border: '2px dashed rgba(30, 32, 113, 0.3)',
    borderRadius: '5px',
    backgroundColor: '#e6e8ee',
    color: '#7b8094',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  inputLabel: {
    color: '#adadad',
    fontSize: '13px',
    marginBottom: '5px',
  },
  viewText: {
    fontSize: '15px',
    fontWeight: 600,
  },
  keyboardDatePicker: {
    marginTop: '10px',
    marginBottom: '20px',
    width: '212px',
  },
  checkbox: {
    '&.Mui-checked': {
      color: '#1e2071',
    },
  },
  radio: {
    '&.Mui-checked': {
      color: '#1e2071',
    },
  },
  appBar: {
    top: 'auto',
    bottom: 0,
    border: '1px solid #707070',
    backgroundColor: 'rgba(54, 54, 54, 0.64)',
    [theme.breakpoints.up('sm')]: {
      width: 'calc(100% - 72px)',
    },
  },
  grow: {
    flexGrow: 1,
  },
  button: {
    margin: '0 10px',
    minHeight: '42px',
    minWidth: '150px',
  },
  buttonTitle: {
    fontWeight: 700,
    fontSize: 13,
  },
  buttonSubTitle: {
    fontWeight: 400,
    fontSize: 7,
  },
  menuList: {
    display: 'inline-block',
    padding: '10px 45px 10px 0',
  },
  menuLink: {
    color: '#929292',
    display: 'block',
    fontSize: '1.10em',
    '&:hover': {
      color: '#3ab77d',
    },
  },
  LinkActive: {
    color: '#3ab77d',
    fontWeight: 'bold',
    textDecoration: 'underline',
  },
}));

interface IClientProfileComponentProps {
  data: any | any[];
  page: any;
  match?: any;
  client_id: any; 
  classes?: any;
  // client?: any;
  rows?: [] | null;
  clientInfo?: any; 
  proposalInfo?: any; 
  SetClientInfo?:any;
  SetProposalInfo?:any;
  client_all_info?: any;
  contractType?: any;
  benefit_plan_tree?: any;
  plan_types?: any;
  planTypeNode?:any;
  clientData?:any;
  callback?:any;
}

export const ClientsProfileComponent: React.FC<IClientProfileComponentProps> = (
  props: IClientProfileComponentProps,
) => {
  //rows, client_all_info, benefit_plan_tree, plan_types, contractType
  const { data, page, client_id,  callback, clientData,  } = props;
  const classes = useStyles();
  // const [viewOnly, setViewOnly] = useState(true);
  const [loading, setLoading] = useState(false);
  const [activeItem, setActiveItem] = useState(page === `/membership/clients-profile/${client_id}/member` ? 'Members' : 'Basic Information');
  const [redirectToClientsList, setRedirectToClientsList] = useState(false);
  const [isTL, setIsTL] = useState<boolean>(false);
  const [isPrintingStaff, setIsPrintingStaff] = useState<boolean>(false);
  const [currentUserData, setCurrentUserData] = useState<any>({});
  const [hasMFChanges, setHasMFChanges] = useState<boolean>(false);
  const [clientInfo, setClientInfo] = useState<boolean>(false);
  const [proposalnfo, setProposalInfo] = useState<boolean>(false);
  const [siCardSortList, setSICardSortList] = useState<any[]>([]);
  const [logoArr, setLogoArr] = useState<string>('');
  const [logoArr2, setLogoArr2] = useState<any[]>(); 
  const [imageLogo2, setImageLogo2] = useState<any>();

console.log('ClientsProfileComponent', props);
const [clientHMOData2, setClientHMOData2]  = useState<any>();
const [clientHMOID, setClientHMOID]  = useState<any>();
const [clientDataInitial, setClientDataInitial] = useState({});
const [contractsInfo, setContractsInfo]  = useState<any[]>([]);
const [clientId, setClientID] = useState('');
// const [imglogo, setImageLogo] = useState<any[]>([]);
const [contractId, setContractId] = useState<string>('');
const [contractName, setContractName] = useState<string>('');
const [planTypes, setPlanTypes] = useState<any[]>([]);
const [planTypesNew, setPlanTypesNew] = useState<any[]>([]);
const [contractStatus, setContractStatus] = useState('');
const [clientContractType, setClientContractType] = useState('');
const [clientReqFields, setClientReqFields] = useState<any>({});
const [mctNode, setMctNode] = useState<any>(null);
const [loader, setLoader] = useState(false);
const [actionMemo, setActionMemo] = useState(data.action_memo_recipient);
const [servicingList, setServicingList] = useState({});
const [sortingFinalList, setSortingFinalList] = useState<any[]>([]) 
const [sortingInitialList, setSortingInitialList] = useState<any[]>([])
 
  const getGroup = async () => {
    await API.getUserDataFromDb().then(userData => {
      console.log(userData)
      setCurrentUserData(userData);

      if (membershipModuleManagerGroup.includes(userData.groupName)) {
        setIsTL(true);
      }
      if (membershipPrintingGroup.includes(userData.groupName)) {
        setIsPrintingStaff(true);
      }
    });
  };
 
  useEffect(() => {
    getGroup();
    getClientInfo(callback, data, clientId);
    console.log('API_URL', API_URL)
  }, []);

  useEffect(() => {
  }, [currentUserData])

  useEffect(() => {
    if(data !== null ){
    }
},[data])

console.log('CPcomp list', props.data.member_data_fields);

  useEffect(() => {
    setActionMemo(data.action_memo_recipient)
  }, [data.action_memo_recipient])


  const sideBarItems = [
    { //index: 0
      title: 'CLIENT INFORMATION',
      lists: [
        {
          name: GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
            ? 'Basic Information' : null,
        },
        {
         // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS67', currentUserData, loading)
            ? 'Contacts/Branches' : null,
        },
        {
         // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
          ?  'Member Fields' : null,
        },
        {
         // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
            ?  'Servicing Instructions' : null,
        },
        {
          // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
            ? 'Action Memo Recipients' : null,
        },
        {
          // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
            ? 'Action Memo History' : null,
        },
      ],
      
    },
    { //index: 1
      title: 'ACCOUNT',
      lists: [
        {
          //disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading) ? 'HMO Information' : null,
        },
        {
          name: GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
            ? 'Members' : null,
        },
        // {
        //  // disabled: true,
        //   name: GlobalFunction.checkUserRbacByPolicy('MS69', currentUserData, loading)
        //     ? 'History' : null,
        // },
        {
          name: isTL || isPrintingStaff || 
          GlobalFunction.checkUserRbacByPolicy('MS86', currentUserData, loading) ? 'Transmittal List History' : null,
        },
        // {
        //   // disabled: true,
        //   name: GlobalFunction.checkUserRbacByPolicy('MS70', currentUserData, loading)
        //     ? 'Utilization' : null,
        // },
        // {
        //   // disabled: true,
        //   name: GlobalFunction.checkUserRbacByPolicy('MS70', currentUserData, loading)
        //     ? 'Commissions' : null,
        // },
          {
          // disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS70', currentUserData, loading)
            ? 'Documents' : null,
        },
       
      ],
    },
    { //index: 2
      title: 'ACTIVITIES',
      lists: [
        {
          //disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS70', currentUserData, loading)
            ? 'Tickets' : null,
        },
        {
          //disabled: true,
          name: GlobalFunction.checkUserRbacByPolicy('MS70', currentUserData, loading)
            ? 'Audit Logs' : null,
        },
      ],
    },
  ];

  const breadCrumbs = [
    'CLIENTS',
    data !== undefined ? data.registered_name : '',
  ];

  const activeSideBarItem = item => {
    setActiveItem(item);
    // setViewOnly(true);
    APICalls();
  };

  const APICalls = () => {
    switch (activeItem) {
      case 'Members':
        break;
      default:
        break;
    }
  };

  const setTempMFData = (data: any) => {
    let cData = Object.assign({}, props.clientInfo);

    cData['member_data_fields'] = data;

    console.log('DATAAA setTemp', data)
    let cardSortList: any[] = [];
    if (cData.member_data_fields && cData.member_data_fields.length > 0) {
      let fcsl = cData.member_data_fields.filter(item => {
        return item.is_required;
      });
      setServicingList(fcsl) 
      for(const item of fcsl){
        cardSortList.push({
          code: item.system_name,
          label: item.system_name,
        })
      }
    }
    setSICardSortList(cardSortList);
  };

  const classes2 = viewClientStyles();
  const handleClientBreadcrumbRedirect = () => {
    setRedirectToClientsList(true);
  }
  // const getInvalidFields = (cData: any, proposalInfo: any): any => {
  //   let fields: string[] = [];
  //   let wrongFormatNo: string[] = [];
  //   let wrontFormat: string[] = [];
  //   let fieldAlreadyExist: string[] = [];

  //   // Registered Corporate Name
  //   if (cData.registered_name === '') {
  //     fields.push('Registered Corporate Name');
  //   }

  //   // Trade / Brand Name
  //   if (cData.brand_name === '') {
  //     fields.push('Trade / Brand Name');
  //   }

  //   // Registered Corporate Account No.
  //   if (cData.corporate_account_no === '') {
  //     fields.push('Corporate Account No.');
  //   } else {
  //     const corp_account_no = cData.corporate_account_no;
  //     if (!corp_account_no.match(/\d{2}-\d{2}-\d{5}$/)) {
  //       if (!wrongFormatNo.includes('Corporate Account No.')) {
  //         wrongFormatNo.push('Corporate Account No.');
  //       }
  //     }
  //   }

  //   if (cData.isCorpAccountValid === false) {
  //     fieldAlreadyExist.push('Corporate Account Number Already Exist.');
  //   }

  //   // SEC Registration No.
  //   if (cData.sec_reg_no === '') {
  //     fields.push('SEC Registration No.');
  //   }

  //   // Corporate TIN
  //   if (cData.tin_no === '') {
  //     fields.push('Corporate TIN');
  //   } else {
  //     //checking tin format
  //     const corp_tin_no = cData.tin_no;
  //     if (!corp_tin_no.match(/\d{3}-\d{3}-\d{3}-\d{3}$/)) {
  //       if (!wrongFormatNo.includes('Corporate TIN')) {
  //         wrongFormatNo.push('Corporate TIN');
  //       }
  //     }
  //   }

  //   // VAT Type
  //   if (cData.vat_type === '') {
  //     fields.push('VAT Type');
  //   }

  //   if (cData.name_on_card === '') {
  //     fields.push('Name Displayed In Card');
  //   }

  //   // Industry
  //   if (cData.industry === '') {
  //     fields.push('Industry');
  //   }

  //   // Ownership
  //   if (cData.ownership === '') {
  //     fields.push('Ownership');
  //   }

  //   // Contact Details
  //   // Floor
  //   if (cData.main_office_address.floor === '') {
  //     fields.push('Main Office Address - Floor');
  //   }

  //   // Building Number
  //   if (cData.main_office_address.bldg_no === '') {
  //     fields.push('Main Office Address - Building Number');
  //   }

  //   // Building Name
  //   if (cData.main_office_address.bldg_name === '') {
  //     fields.push('Main Office Address - Building Name');
  //   }

  //   // Street
  //   if (cData.main_office_address.street === '') {
  //     fields.push('Main Office Address - Street');
  //   }

  //   // Barangay
  //   if (cData.main_office_address.brgy === '') {
  //     fields.push('Main Office Address - Barangay');
  //   }

  //   // Region
  //   if (cData.main_office_address.region === '') {
  //     fields.push('Main Office Address - Region');
  //   }

  //   // City
  //   if (cData.main_office_address.city === '') {
  //     fields.push('Main Office Address - City');
  //   }

  //   // Zip Code
  //   if (cData.main_office_address.zip_code === '') {
  //     fields.push('Main Office Address - Zip Code');
  //   }

  //   //Proposal Coverage
  //   if (
  //     proposalInfo.coverage_start_date === '' ||
  //     proposalInfo.coverage_start_date === null ||
  //     (proposalInfo.coverage_end_date === '' ||
  //       proposalInfo.coverage_end_date === null)
  //   ) {
  //     fields.push('Proposal Coverage Period');
  //   }

  //   // Telephone Number/Trunk Line
  //   if (
  //     cData.main_office_address.contact_numbers !== undefined &&
  //     cData.main_office_address.contact_numbers.length > 0
  //   ) {
  //     cData.main_office_address.contact_numbers.map(tel => {
  //       if (tel['no'] === '') {
  //         if (!fields.includes('Telephone Number/Trunk Line')) {
  //           fields.push('Telephone Number/Trunk Line');
  //         }
  //       }
  //     });
  //   } else {
  //     if (!fields.includes('Telephone Number/Trunk Line')) {
  //       fields.push('Telephone Number/Trunk Line');
  //     }
  //   }
  //   // Authorized Signatory
  //   if (cData.authorized_signatory === '') {
  //     fields.push('Authorized Signatory');
  //   }

  //   // Authorized Email
  //   if (
  //     cData.authorized_signatory_email_address !== undefined &&
  //     cData.authorized_signatory_email_address !== ''
  //   ) {
  //     const email_sign = cData.authorized_signatory_email_address;
  //     if (
  //       !email_sign.match(/\S+@\S+\.\S+/) ||
  //       email_sign.indexOf(' ') !== -1 ||
  //       email_sign.indexOf('..') !== -1
  //     ) {
  //       if (!wrontFormat.includes('Authorized Signatory Email')) {
  //         wrontFormat.push('Authorized Signatory Email');
  //       }
  //       // if(!wrongFormatIds.includes("authorizedSignatoryEmail")){
  //       //     wrongFormatIds.push("authorizedSignatoryEmail");
  //       // }
  //     }
  //   }
  //   // Contact Persons
  //   if (
  //     cData.main_office_address.contact_persons !== undefined &&
  //     cData.main_office_address.contact_persons.length > 0
  //   ) {
  //     cData.main_office_address.contact_persons.map((person: any) => {
  //       if (person['name'] === '') {
  //         if (!fields.includes('Contact Person - Name')) {
  //           fields.push('Contact Person - Name');
  //         }
  //       }
  //       if (person['contact_no'] === '') {
  //         if (!fields.includes('Contact Person - Contact Number')) {
  //           fields.push('Contact Person - Contact Number');
  //         }
  //       }
  //       if (person['email_address'] === '') {
  //         if (!fields.includes('Contact Person - Email')) {
  //           fields.push('Contact Person - Email');
  //         }
  //       } else if (
  //         !person['email_address'].match(/\S+@\S+\.\S+/) ||
  //         person['email_address'].indexOf(' ') !== -1 ||
  //         person['email_address'].indexOf('..') !== -1
  //       ) {
  //         if (!wrontFormat.includes('Contact Person - Email')) {
  //           wrontFormat.push('Contact Person - Email');
  //         }
  //         // if (!wrongFormatNo.includes("email-"+idx)) {
  //         //     wrongFormatNo.push("email-"+idx)
  //         // }
  //       }
  //       if (person['designation'] === '') {
  //         if (!fields.includes('Contact Person - Designation')) {
  //           fields.push('Contact Person - Designation');
  //         }
  //       }
  //     });
  //   } else {
  //     fields.push('Contact Persons');
  //   }

  //   // Branches
  //   cData.branches.map((branch: any) => {
  //     if (branch['branch_name'] === '') {
  //       if (!fields.includes('Branch Office Address - Branch Name')) {
  //         fields.push('Branch Office Address - Branch Name');
  //       }
  //     }
  //     if (branch['floor'] === '') {
  //       if (!fields.includes('Branch Office Address - Floor')) {
  //         fields.push('Branch Office Address - Floor');
  //       }
  //     }
  //     // if (branch['unit'] === '') {
  //     // 	if (!fields.includes("Branch Office Address - Unit")) {
  //     // 		fields.push("Branch Office Address - Unit");
  //     // 	}
  //     // }
  //     if (branch['bldg_no'] === '') {
  //       if (!fields.includes('Branch Office Address - Building Number')) {
  //         fields.push('Branch Office Address - Building Number');
  //       }
  //     }
  //     if (branch['bldg_name'] === '') {
  //       if (!fields.includes('Branch Office Address - Building Name')) {
  //         fields.push('Branch Office Address - Building Name');
  //       }
  //     }
  //     if (branch['street'] === '') {
  //       if (!fields.includes('Branch Office Address - Street')) {
  //         fields.push('Branch Office Address - Street');
  //       }
  //     }
  //     if (branch['brgy'] === '') {
  //       if (!fields.includes('Branch Office Address - Barangay')) {
  //         fields.push('Branch Office Address - Barangay');
  //       }
  //     }
  //     if (branch['region'] === '') {
  //       if (!fields.includes('Branch Office Address - Region')) {
  //         fields.push('Branch Office Address - Region');
  //       }
  //     }
  //     if (branch['city'] === '') {
  //       if (!fields.includes('Branch Office Address - City')) {
  //         fields.push('Branch Office Address - City');
  //       }
  //     }
  //     if (branch['zip_code'] === '') {
  //       if (!fields.includes('Branch Office Address - Zip Code')) {
  //         fields.push('Branch Office Address - Zip Code');
  //       }
  //     }
  //     if (
  //       branch['contact_numbers'] !== undefined &&
  //       branch['contact_numbers'].length > 0
  //     ) {
  //       branch['contact_numbers'].map(tel => {
  //         if (tel['no'] === '') {
  //           if (!fields.includes('Branch Telephone Number/Trunk Line')) {
  //             fields.push('Branch Telephone Number/Trunk Line');
  //           }
  //         }
  //       });
  //     } else {
  //       if (!fields.includes('Branch Telephone Number/Trunk Line')) {
  //         fields.push('Branch Telephone Number/Trunk Line');
  //       }
  //     }
  //     if (
  //       branch['contact_persons'] !== undefined &&
  //       branch['contact_persons'].length > 0
  //     ) {
  //       branch['contact_persons'].map((person: any) => {
  //         if (person['name'] === '') {
  //           if (!fields.includes('Branch Contact Person - Name')) {
  //             fields.push('Branch Contact Person - Name');
  //           }
  //         }
  //         if (person['contact_no'] === '') {
  //           if (!fields.includes('Branch Contact Person - Contact Number')) {
  //             fields.push('Branch Contact Person - Contact Number');
  //           }
  //         }
  //         if (person['email_address'] === '') {
  //           if (!fields.includes('Branch Contact Person - Email')) {
  //             fields.push('Branch Contact Person - Email');
  //           }
  //         } else if (
  //           !person['email_address'].match(/\S+@\S+\.\S+/) ||
  //           person['email_address'].indexOf(' ') !== -1 ||
  //           person['email_address'].indexOf('..') !== -1
  //         ) {
  //           if (!wrontFormat.includes('Branch Contact Person - Email')) {
  //             wrontFormat.push('Branch Contact Person - Email');
  //           }
  //           // if (!wrongFormatNo.includes("addrEmail-"+parentIndex+"-"+idx)) {
  //           //     wrongFormatNo.push("addrEmail-"+parentIndex+"-"+idx)
  //           // }
  //         }
  //         if (person['designation'] === '') {
  //           if (!fields.includes('Branch Contact Person - Designation')) {
  //             fields.push('Branch Contact Person - Designation');
  //           }
  //         }
  //       });
  //     }
  //   });

  //   // Previous HMO Provider
  //   if (cData.prev_hmo_provider === '') {
  //     fields.push('Previous HMO Provider');
  //   }

  //   // Agent / Broker

  //   if (
  //     cData.sales_channel &&
  //     Object.keys(cData.sales_channel).length > 0 &&
  //     cData.sales_channel.type === 'agent'
  //   ) {
  //     if (cData.sales_channel.agent_name === '') {
  //       fields.push('Agent Name');
  //     }
  //   }

  //   if (
  //     cData.sales_channel &&
  //     Object.keys(cData.sales_channel).length > 0 &&
  //     cData.sales_channel.type === 'broker'
  //   ) {
  //     if (cData.sales_channel.company_name === '') {
  //       fields.push('Broker - Name of Company');
  //     }
  //     if (cData.sales_channel.contact_person === '') {
  //       fields.push('Broker - Contact Person');
  //     }
  //     if (cData.sales_channel.position === '') {
  //       fields.push('Broker - Position');
  //     }
  //     if (cData.sales_channel.contact_no === '') {
  //       fields.push('Broker - Contact Number');
  //     }
  //     if (cData.sales_channel.email_address === '') {
  //       fields.push('Broker - Email Address');
  //     } else {
  //       const broker_email = cData.sales_channel.email_address;
  //       if (!broker_email.match(/\S+@\S+\.\S+/)) {
  //         if (!wrontFormat.includes('Broker Email')) {
  //           wrontFormat.push('Broker Email');
  //         }
  //       }
  //     }
  //   }

  //   return {
  //     fields: fields,
  //     wrong_format_no: wrongFormatNo,
  //     wrong_format: wrontFormat,
  //     field_already_exist: fieldAlreadyExist,
  //   };
  // };

  const getClientInfoFromAPI = () => {
    setLoading(true);

    API.getClientHMOData(client_id)
      .then(response => {
        console.log('getclienthmodata res', response)
      
        if(response && response.error === undefined) {
          console.log('yes')
          setClientHMOData2(response)
          let data2 : any[] = cloneDeep(INITIAL_STATE.clientInfo);
          data2['servicing_instructions'] = [];
      

          let clientData: any = response.client ? response.client : null;
          let memberFields: any = clientData.member_data_fields
          let sortingCardsList: any[] = [];

          setClientReqFields(memberFields)
          if(memberFields) {
            setClientHMOID(clientData._id)
            setClientDataInitial(
              cloneDeep(response.client ? response.client : null),
            )
              let fcsl = clientData.member_data_fields.filter(item => {
                return item.is_required;
              })
              for(const item of fcsl){
                sortingCardsList.push({
                  code: item.system_name,
                  label: item.system_name,
                })
              }
              setSortingFinalList(sortingCardsList)
              setSortingInitialList(sortingCardsList)
          }

        } else {
          console.log('res err')
        }

        if(clientData !== undefined) {
          console.log('memberdatafields > 0', clientData)

          if(clientData) {
            console.log('clientdata', clientData)
          } else {
            console.log('empty')
          }
        } else {
          console.log('memberdatafields empty', clientData)
        }

        // setDisplayList(displaySortingList)
      })
  }

  console.log('out clientReqFields', clientReqFields)
  const getClientInfo  = async (callback: any, client: any, clientId: string) => {
    console.log('getClientInfo', client)
    console.log(callback)
    console.log(clientId)


    API.getClientHMOInfo(props.client_id)
      .then(response => {
        setPlanTypesNew(response.plan_types)
        if(response && response.error === undefined) {
          const {client, benefit_plan_tree, plan_types } = response;
          setClientInfo(response);
          // let data: any[] = cloneDeep(INITIAL_STATE.clientInfo);
          // let data: any[] = clientInfo['servicing_instructions'] = []
          // console.log('getClientInfo  clientInfo servicing_instructions', data)
         
          let planTypeOptions: any[] = []; //ISelectItem[] = [];
          // let clientData: any = response.client ? response.client : null;
          let memberPlanType = client.plan_type ? client.plan_type : '';
          let contract_name = '';
          let abl = '';
          let mbl = '';
          let pec = '';
          let planTypeNode: any = null;
        console.log('abl', abl)
        console.log('mbl', mbl)
        console.log('pec', pec)
        // console.log('clientData', clientData)
        console.log('contract_name', contract_name)
        // console.log('planTypeNode', planTypeNode)
        // console.log('setClientInfo - response', clientInfo)
        
        if(response.client) {
            console.log('response client', response)
            setClientDataInitial(cloneDeep(response.client ? response.client : null),
            );

        if (plan_types && plan_types.length > 0) {
          console.log('inside plan_types', plan_types)
          for(const item of plan_types){
            planTypeOptions.push({
              id: item.plan_type_code,
              value: item.plan_type_name,
              label: item.plan_type_name,
            })
          }
          console.log('planTypeOptions', planTypeOptions)
        }
        

        let mctNode: any = null;
        // console.log('ben plan tree out', benefit_plan_tree)
        if (benefit_plan_tree && benefit_plan_tree.length > 0) {
          // console.log('Benefit plan tree in', benefit_plan_tree)
          mctNode = _.find(benefit_plan_tree, item => item.tree_id === 'MCT');
          setMctNode(mctNode); // Set the state
          let cplanType = mctNode.custom_metadata['contract_plan_type']
          if (!_.isNil(mctNode)) {
            // contract_name = mctNode.name
            //   ? 'Version ' + mctNode.version
            //   : '';
            contract_name = mctNode.name ? 'Contract ' + mctNode.version : '';
          }
          planTypeNode = _.find(
            benefit_plan_tree,
            item =>
              memberPlanType &&
              item.name === memberPlanType &&
              item.type === 'PlanType',
          );
          console.log('mctNode', mctNode)
          console.log('cplanType', cplanType)
          console.log('planTypeNode', planTypeNode)
          setClientContractType(cplanType)
          if (!_.isNil(planTypeNode)) {
            console.log('planTypeNode in', planTypeNode)
            const rnbNode = _.find(
              benefit_plan_tree,
              item => item.tree_id === 'MCT-CT-' + planTypeNode.code + '-RB',
            );

            if (!_.isNil(rnbNode)) {
              console.log('rbnode in', rnbNode)
              const rnbLimits = _.get(rnbNode, 'limits', []);
              _.forEach(rnbLimits, rnbLimit => {
                switch (rnbLimit.type) {
                  case 'ABL':
                    abl = `${
                      rnbLimit.limit_unit
                    } ${GlobalFunction.formatNumber(rnbLimit.limit)}`;
                    break;
                  case 'MBL':
                    mbl = `${
                      rnbLimit.limit_unit
                    } ${GlobalFunction.formatNumber(rnbLimit.limit)}`;
                    break;
                  case 'PEC':
                    const limitUnit = _.get(rnbLimit, 'limit_unit', '');
                    switch (limitUnit) {
                      case 'PHP':
                        pec = `${
                          rnbLimit.limit_unit
                        } ${GlobalFunction.formatNumber(rnbLimit.limit)}`;
                        break;

                      case 'Percentage':
                        pec = `${GlobalFunction.formatNumber(
                          rnbLimit.limit,
                        )} %`;
                        break;

                      case 'Months':
                        const timeBasedLimit: any[] = _.get(
                          rnbLimit,
                          'time_based_limit',
                          [],
                        );
                        if (timeBasedLimit.length > 0) {
                          const tblLimits: any[] = [];
                          _.forEach(timeBasedLimit, tblLimit => {
                            tblLimits.push(
                              `${GlobalFunction.formatNumber(
                                _.get(tblLimit, 'number_month', 0),
                              )} ${_.get(tblLimit, 'day_based', '')}`,
                            );
                          });
                          pec = tblLimits.join(', ');
                        }
                        break;
                      default:
                        pec = '';
                    }
                    break;
                  default:
                    break;
                }
              });
            }
          }
        }
       }

          let ncontractId = '';
          let benefitTree: any[] = response.benefit_plan_tree
            ? response.benefit_plan_tree
            : [];

          let nabl: any = {};

          console.log('nabl', nabl)
          console.log('benefitTree', benefitTree)
          if (benefitTree.length > 0) {
            benefitTree.forEach(item => {
              if (item.level === 1 && item.code === 'MCT') {
                ncontractId = item._id ? item._id : '';
                 // item.version
                  //   ? client.client_id + ' ' + item.version
                  let contractNameDisplay = client && client.contract_id !== undefined ? client.contract_id + ' ' + moment(client.coverage_start_date).format('DD/MM/YYYY') + ' - ' + moment(client.coverage_end_date).format('DD/MM/YYYY') : 'Contract'
                setContractName(contractNameDisplay);
                setContractId(ncontractId);
                console.log('Contract ID', contractId)
                console.log('Contract Name display', contractName)
              }
              if (item.level === 2 && item.code === 'CT') {
                if (item.limits && item.limits.length > 0) {
                  item.limits.forEach(limit => {
                    if (limit.type === 'ABL') {
                      nabl = limit;
                    }
                  });
                }
              }
            });
          }
          console.log('response1', response)
          // let initpropinfo = prosalInfo;
          let MCTNode: any[] = response.benefit_plan_tree.filter(
            node => node['code'] && node['code'] === 'MCT',
          );

          const adddtls: any = [];
          setContractStatus(MCTNode[0].status);
          let PInfo: any = {};
          // let initpropinfo = INITIAL_STATE.proposalInfo;
          if (MCTNode[0]) {
            console.log(MCTNode);
            if (MCTNode[0].custom_metadata.additional_info) {
              if (MCTNode[0].custom_metadata.additional_info.contact_person) {
                adddtls.push('Contact Person');
              }
              if (MCTNode[0].custom_metadata.additional_info.company_name) {
                adddtls.push('Company Name');
              }
              if (MCTNode[0].custom_metadata.additional_info.address) {
                adddtls.push('Address');
              }
              // console.log('startdate',MCTNode[0].custom_metadata.coverage_start_date)
              let tmpstartdate: string =
                MCTNode[0].custom_metadata.coverage_start_date;
              tmpstartdate = tmpstartdate.substring(
                0,
                tmpstartdate.indexOf('T') + 1,
              );
              tmpstartdate = `${tmpstartdate}00:00:00.000Z`;
              let tmpenddate: string =
                MCTNode[0].custom_metadata.coverage_end_date;
              tmpenddate = tmpenddate.substring(0, tmpenddate.indexOf('T') + 1);
              tmpenddate = `${tmpenddate}00:00:00.000Z`;
              PInfo = {
                status: response.client.status,
                coverage_start_date: tmpstartdate,
                coverage_end_date: tmpenddate,
                additional_details: adddtls,
                CompanyNameSelected: MCTNode[0].custom_metadata.additional_info
                  .company_name
                  ? MCTNode[0].custom_metadata.additional_info.company_name
                  : '',
                ContactPersonSelected: MCTNode[0].custom_metadata
                  .additional_info.contact_person
                  ? MCTNode[0].custom_metadata.additional_info.contact_person
                  : '',
                AddressSelected:
                  MCTNode[0].custom_metadata.additional_info.address &&
                  MCTNode[0].custom_metadata.additional_info.address ===
                    'branch'
                    ? 'branch'
                    : 'Head Office',
                SelectedBranch:
                  MCTNode[0].custom_metadata.additional_info.address &&
                  MCTNode[0].custom_metadata.additional_info.address ===
                    'branch'
                    ? MCTNode[0].custom_metadata.additional_info.address
                    : '',
              };
            } else {
              let tmpstartdate: string =
                MCTNode[0].custom_metadata.coverage_start_date;
              tmpstartdate = tmpstartdate.substring(
                0,
                tmpstartdate.indexOf('T') + 1,
              );
              tmpstartdate = `${tmpstartdate}00:00:00.000Z`;
              let tmpenddate: string =
                MCTNode[0].custom_metadata.coverage_end_date;
              tmpenddate = tmpenddate.substring(0, tmpenddate.indexOf('T') + 1);
              tmpenddate = `${tmpenddate}23:59:59.999Z`;
              PInfo = {
                status: response.client.status,
                coverage_start_date: tmpstartdate,
                coverage_end_date: tmpenddate,
              };
            }
          }

          console.log('MCT: ', MCTNode);
          console.log('proposal info: ', PInfo);
          const tmpContracts: any[] = [
            {
              name: MCTNode[0].version,
              value: MCTNode[0]._id,
            },
          ];
          console.log('Contracts', tmpContracts);
          setContractsInfo(tmpContracts);
          console.log('contracstinfo', contractsInfo)
          // props.SetProposalInfo(Object.assign( PInfo)); //initpropinfo,
          console.log('after setup proposalInfo', setProposalInfo)

          if (response.plan_types && response.plan_types.length > 0) {
             console.log('response plantypes', response.plan_types)
             const nplanTypes:any[] = [];
             for(const item of response.plan_types){
              let link = item.plan_type_code //underwriting
                ? '/membership/client-benefits/' +
                  props.client_id +
                  '/' +
                  ncontractId +
                  '?plantype=' +
                  item.plan_type_code
                : '#';
              item['link'] = link;
              nplanTypes.push(item)
             }
            setPlanTypes(nplanTypes);
          }

          console.log('plantypes+link', planTypes)
          setLoading(false);
        } else {
          console.log('getclient hmo api error: ', response);
          setLoading(false);
        }
      })
      .catch(error => {
        console.log('getclient hmo api error: ', error);
        setLoading(false);
      });

      console.log('endd', props)
  };
  useEffect(() => {
    getClientInfoFromAPI()
  }, [])
  useEffect(() => {
    
    if(data.files !== undefined && data.files[0] !== undefined && data.files !== null) {
      let cLogoChannel = data.files;
      let cLogoArr = [''];
      let cLogoProp;
      let count = 0;
      console.log('inside', data.files)
      let cLogoName =  data.files[0]['path']
      //to fix img path hardcode for testing only, once path is provided change this
      let imgPath = 'https://api-dev.hims.veridata.com.ph/api/uploads/tmp/1657159931057-Image_20220707_092507.jpeg'
      let imgPath2 = 'https://api-dev.hims.veridata.com.ph/api/uploads/tmp/' + data.files[0]['filename']

      console.log('IMAGE LINK', imgPath)
      console.log('IMAGE LINK2', imgPath2)
        cLogoArr.shift();
        for(cLogoProp in cLogoChannel) {
          if(count > 0){ 
          cLogoArr.push(cLogoChannel[cLogoProp][0])
          console.log('inside loop', cLogoChannel)
          console.log('inside loop', cLogoArr)
        }
        count++;
      }
    setLogoArr(imgPath2); //cLogoArr
    setImageLogo2(imgPath2)
    setLogoArr2(cLogoName);
    } else {
      console.log('no data files for client logo')
    }
  },[data])
  console.log('logoArr', logoArr)
  console.log('logoArr2', logoArr2)

  const setTempSIData = (data: any) => {
    let cData = Object.assign({}, props.clientInfo);

    cData['servicing_instructions'] = data;

    console.log('setclientinfo(cData)', cData)
  };

  const ClientAuditLogs = () => {
  console.log('THIS AUDITLOGS!')
    return (
      <div>
        <Grid container>
        <AuditLogsPage  classes={classes2} />
        </Grid>
      </div>
    );
  };


  if (redirectToClientsList === true) {
    return (
      <Redirect to={{ pathname: '/membership/clients/list' }} />
    )
  }

  const handleSaveActionMemoRecipients = (direct, cc, closeEvent) => {
    const directEmails:any[] = [];
    const dir = direct.filter((val) => val.email !== "" && val.type == "direct");
    for(const obj of dir){
      directEmails.push(obj.email)
    }
    const ccEmails:any[] = [];
    const ccTemp = cc.filter((val) => val.email !== "" && val.type == "cc");
    for(const obj of ccTemp){
      ccEmails.push(obj.email)
    }

    let payload = {
      direct: directEmails,
      cc: ccEmails
    }

    setLoader(true);

    API.saveActionMemoRecipients(payload, client_id).then(response => {
      console.log('active memo saved', response)
      setActionMemo(payload);
      setLoader(false);
      closeEvent(false);
    });
  }

  return (
    <>
      <SubHeader
        headingTitle={data !== undefined ? data.registered_name : ''}
        breadCrumbsArray={breadCrumbs !== undefined ? breadCrumbs?.map((breadcrumb, i) => {
          if (i === 0) {
            return (
              <Link onClick={handleClientBreadcrumbRedirect}>{breadcrumb}</Link>
            )
          } else {
            return (
              breadcrumb
            )
          }
        }) : []}
        layoutType={'layout_1'}
        enableButton={false}
        enableStatus={true}
        setStatus={data && data.status ? data.status : ''}
      />
      <div className={classes.main}>
        <Grid container xs={12}>
        {/* src={`data:image/jpeg;base64, ${logoArr}`} */}
        {/* <img style={{height:'20px'}}  src={logoArr} /> */}
          <Grid item xs={12} md={2} className={classes.sideBarContainer}>
             {data && data.files !== undefined ?
          <Avatar
            // src={logoArr2}
            src={imageLogo2}
            alt="avatar"
            />
            : ''} 
            <SideMenu items={
              sideBarItems !== undefined ? sideBarItems.map((item) => {
                if (item['lists']) {
                  item['lists'] = item['lists'].filter(listItem => listItem.name !== null)
                }

                return item;
              }) : []
              
            } activeItems={activeSideBarItem} currentActive={activeItem} />
          </Grid>
          <Grid item xs={12} md={10} className={classes.contentContainer}>
            {/* Basic Information */}
            {activeItem === 'Basic Information' && GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
              ? (
                <BasicInformation classes={classes} data={data} />
              ) : null}
              {/* Contacts/Branches */}
            {activeItem === 'Contacts/Branches' && GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
              ? (
                <ContactsBranches 
                    classes={classes} 
                    data={data} 
                /> ) : null}
              {/* Member Fields MemberFields */}
            {activeItem === 'Member Fields' && GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
              ? (
                <MemberFields
                  data={data}
                  clientData={props.clientInfo}
                  hasChanges={hasMFChanges}
                  // openMFModal={openMFModal}
                  setHasChanges={setHasMFChanges}
                  setTempMFData={setTempMFData}
                  openConfirmationModal={() => {
                    //setMFData(mfData);
                    // handleConfirmSubmitModalOpen('memberFields');
                  }}
                  onClientDataUpdated={clientData => {
                    console.log('onClientDataUpdated', clientData);
              }}
            />
              ) : null}
              { console.log('props.clientInfo ',  props.clientInfo)}
              {/* Servicing Instructions ServicingInstructions */}
              {activeItem === 'Servicing Instructions' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ServicingInstructions  
                    memberFields={sortingInitialList}
                    data={data && data.servicing_instructions ? data.servicing_instructions : {}}
                    status={props.clientInfo ? props.clientInfo.status : ''}
                    setSIData={setTempSIData}
                    // saveSIData={handleSaveSIData}
                    // client={data}
                    classes={classes}
                /> : null}

            {loader === true ? <Loader /> : null}
            {/* Action Memo Recipients */}
            {activeItem === 'Action Memo Recipients' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ActionMemoRecipients
                handleSave={handleSaveActionMemoRecipients}
                action_memo_recipients={actionMemo}
              /> : null}
             
              {/* Action Memo Recipients */}
              {activeItem === 'Action Memo History' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ActionMemoHistory
                clientData={data}
              /> : null}

              {/* HMO Information */}
              {activeItem === 'HMO Information' && GlobalFunction.checkUserRbacByPolicy('MS66', currentUserData, loading)
              ? (
                <HMOInformation 
                  classes={classes} 
                  clientData={data} 
                  planTypesNew={planTypesNew}
                  clientId={props.client_id}
                  contractId={contractId}
                  planTypes={planTypes}
                  setClientInfo={setClientInfo}
                  setProposalInfo={setProposalInfo}
                  client_all_info={props.client_all_info}
                  viewOnly
                  proposalInfo={props.proposalInfo}
                  contractType={clientContractType}
                  abl
                  MCTNode={mctNode}
                  />
              ) : null}

              {/* Members  */}
          {activeItem === 'Members' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <MembersComponent 
                    data={data} 
                    memberFields={clientReqFields}
                    // memberId={props.match.member_id}
                    /> : null
            }
          {/*Transmittal List History */}
          {activeItem === 'Transmittal List History' ? <TransmittalListHistory clientId={client_id} clientData={data} /> : null}

          
          {/* History */}
          {activeItem === 'History' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ClientHistory 
                    data={data} 
                    clientId={clientId}
                    /> : null}

          {/* Utilization */}
          {activeItem === 'Utilization' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ClientUtilization 
                      data={data} 
                      clientId={clientId} 
                      contractId={contractId} 
                  /> : null}

          {/* Utilization */}
          {activeItem === 'Documents' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
            ? <Documents 
                    data={data} 
                    clientId={clientId} 
                    contractId={contractId} 
                /> : null}


          {/* Commissions */}
          {activeItem === 'Commissions' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ClientCommissions 
                    data={data} 
                    clientInfo={clientInfo}
                  /> : null}

           {/* Tickets */}
           {activeItem === 'Audit Logs' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ClientAuditLogs 
                    // data={data} 
                    // clientInfo={clientInfo}
                  /> : null}

          {/* TicketsPage */}
          {activeItem === 'Tickets' && GlobalFunction.checkUserRbacByPolicy('MS68', currentUserData, loading)
              ? <ClientTickets 
                    data={data} 
                    clientInfo={clientInfo}
                  /> : null}
          </Grid>
        </Grid>
      </div>
    </>
  );
};
