import React, { useState, useEffect } from 'react';
import {
  Grid,
  // InputAdornment,
  List,
  ListItem,
  ListItemText,
  // TextField,
} from '@material-ui/core/';
import { makeStyles } from '@material-ui/core/styles';
// import { Search } from '@material-ui/icons/';
import {
  // get,
  filter,
  find,
  forEach,
  // includes,
  map,
  reduce,
  replace,
  sortBy,
  startsWith,
  toLower,
  toUpper,
} from 'lodash';
import clsx from 'clsx';
//import TextField from '@material-ui/core/TextField';

import { CategoryForm } from '../Components/CategoryForm';
//import { TableComponent } from 'Components/UI/TableComponent';

import '@hims/core/dist/index.css';
import './style.scss';

import { API } from 'Pages/API';


const benefitsTableStyles = makeStyles({
  container: {},
  leftSection: {
    borderRight: '#3C394A22 solid 1px',
  },
});

// const columns: any = [
//   { name: 'benefitType', title: 'Category' },
//   { name: 'benefit', title: 'Benefit' },
//   { name: 'limitType', title: 'Limit Type' },
//   { name: 'limit', title: 'Limit' },
// ];

// const columnExtensions: any = [
//   { columnName: 'benefitType', width: 300, wordWrapEnabled: true },
//   { columnName: 'benefit', wordWrapEnabled: true },
//   { columnName: 'limitType', width: 200, wordWrapEnabled: true },
//   { columnName: 'limit', width: 200, wordWrapEnabled: true },
// ];

interface BenefitsTableProps {
  memberBenefits: any;
  contractId?: any;
  memberCardNo?: string;
  sharedNodeObject: any;
}

export const BenefitsTable: React.FC<BenefitsTableProps> = (
  props: BenefitsTableProps,
): JSX.Element => {
  const ROOM_AND_BOARD_NAME = 'Room and board';
  const BASE_PLAN_CODE = 'BASE';

  const { sharedNodeObject } = props;

  const styles = benefitsTableStyles();
  const [roomAndBoardNode, setRoomAndBoardNode] = useState<any>(null);
  const [memberPlanTypeNode, setMemberPlanTypeNode] = useState<any>(null);
  const [planPackages, setPlanPackages] = useState<any>([]);
  const [leftPanelItems, setLeftPanelItems] = useState<any>([]);
  const [selectedLeftPanelItem, setSelectedLeftPanelItem] = useState<any>(null);
  const [benefits, setBenefits] = useState<any>([]);
  const [filteredBenefits, setFilteredBenefits] = useState<any>([]);
  // const [columnFilters, setColumnFilters] = useState<any>([]);
  // const [globalFilter, setGlobalFilter] = useState<string>('');

  useEffect(() => {
    if (props.memberBenefits && props.memberBenefits.length > 0) {
      getPlanPackages();
    }
  }, [props.memberBenefits]);

  console.log('BENEFITS USEEFFECT', props.memberBenefits)
  // console.log('BENEFITS USEEFFECT LENGTH', props.memberBenefits.length)
  useEffect(() => {
    getLeftPanelItems();
  }, [planPackages, selectedLeftPanelItem]);

  useEffect(() => {
    getBenefits();
    getMemberBenefits();
  }, [selectedLeftPanelItem]);

  useEffect(() => {
    getFilteredBenefits();
  }, [benefits]);
  // }, [benefits, globalFilter]);

  const textFieldStyles = makeStyles({
    root: {
      '& div.MuiInputBase-root': {
        backgroundColor: 'transparent',
      },
    },
  });

  let sharedNodeCWB = filter(props.memberBenefits, cwbNode => {
    return cwbNode.tree_id === 'MCT-CT-BASE-CWB' && cwbNode.name === 'Company Wide Benefits';
 });

  let sharedNodeTC = filter(props.sharedNodeObject, tcNode => {
    return tcNode.node.tree_id === 'MCT-CT-BASE-TC' && tcNode.node.name === 'Terms and Conditions';
  });
  console.log('bcsTC', sharedNodeTC)

console.log('textFieldStyles', textFieldStyles)
  const getSortOrder = code => {
    switch (toUpper(code)) {
      case 'RB':
        return 1;
      case 'CV':
        return 2;
      case 'RI':
        return 3;
        case 'OR':
          return 4;
          case 'ID':
            return 5;
      default:
        return 999;
    }
  };

  const getPlanPackages = () => {
    let rnbNode: any = null;
    let planTypeNode: any = null;
    const benefitPlanPackages = filter(props.memberBenefits, memberBenefit => {
      if (toLower(memberBenefit.name) === toLower(ROOM_AND_BOARD_NAME)) {
        rnbNode = memberBenefit;
      }

      if (memberBenefit.level === 3 && memberBenefit.type === 'PlanType') {
        planTypeNode = memberBenefit;
      }

      return (memberBenefit.level === 4 || memberBenefit.code === 'DP');
    });

    const sortedPlanPackages = sortBy(benefitPlanPackages, planPackage =>
      getSortOrder(planPackage.code),
    );

    setPlanPackages(sortedPlanPackages);
    setRoomAndBoardNode(rnbNode);
    setMemberPlanTypeNode(planTypeNode);
    if (!selectedLeftPanelItem) {
      setSelectedLeftPanelItem(rnbNode);
    }

    console.log('sortedPlanPackages', sortedPlanPackages)
    console.log('rnbNode', rnbNode)
    console.log('planTypeNode', planTypeNode)
    console.log('planTypeNode', props.memberBenefits)
    console.log('benefitPlanPackages', benefitPlanPackages)
    console.log('PlanType', props.memberBenefits)
  };

  const getBenefitCategories = planPackage => {
    if (
      roomAndBoardNode &&
      toLower(roomAndBoardNode.name) === toLower(planPackage.name)
    ) {
      return [];
    }

    const benefitCategories = filter(props.memberBenefits, memberBenefit => {
      let benefitTreeId = memberBenefit.tree_id;
      let planPackageTreeId = planPackage.tree_id;
      if (memberPlanTypeNode !== null) {
        benefitTreeId = replace(
          memberBenefit.tree_id,
          BASE_PLAN_CODE,
          memberPlanTypeNode.code,
        );
        planPackageTreeId = replace(
          planPackage.tree_id,
          BASE_PLAN_CODE,
          memberPlanTypeNode.code,
        );
      }

      return (
        memberBenefit.level === 5 &&
        startsWith(benefitTreeId, planPackageTreeId)
      );
    });

    return benefitCategories;
  };

  const getLeftPanelItem = (planPackage: any, children: any) => {
    const isRoomAndBoardNode =
      roomAndBoardNode &&
      toLower(roomAndBoardNode.name) === toLower(planPackage.name);

    const isSelected =
      selectedLeftPanelItem &&
      selectedLeftPanelItem.tree_id === planPackage.tree_id;
    
    // if (['OR', 'ID', 'TC', 'ILS', 'CWB', 'DP'].includes(planPackage.code)) {
    //   return (
    //     <div key={`left-item-${planPackage.tree_id}`} className="sub-section">
    //       <List className={clsx('side-menu-list')}>
    //         <ListItem key={`left-list-item-${planPackage.tree_id}`}>
    //           <ListItemText
    //             id={'benefit_categories_' + planPackage.tree_id}
    //             data-cy={'benefit_categories_' + planPackage.tree_id}
    //             className={clsx(
    //               isSelected
    //                 ? 'side-menu-list-item-selected'
    //                 : 'side-menu-list-item',
    //             )}
    //             onClick={() => {
    //               if (!isSelected) {
    //                 setSelectedLeftPanelItem(planPackage);
    //               }
    //             }}
    //           >
    //             {planPackage.name}
    //           </ListItemText>
    //         </ListItem>
    //       </List>
    //     </div>
    //   );
    // } else {
      if (!['OR', 'ID', 'TC', 'ILS', 'CWB', 'DP', 'CS', 'EL', 'COSH'].includes(planPackage.code)) {
      return (
        <div key={`left-item-${planPackage.tree_id}`} className="sub-section">
          <div
            id={'room_and_board_' + planPackage.name}
            data-cy={'room_and_board_' + planPackage.tree_id}
            className={clsx('light-text', 'header-1', {
              'plan-package-selected': isSelected,
              'plan-package-clickable': isRoomAndBoardNode,
            })}
            onClick={() => {
              if (isRoomAndBoardNode) {
                setSelectedLeftPanelItem(planPackage);
              }
            }}
          >
            {planPackage.name}
          </div>
          <List className={clsx('side-menu-list')}>{children}</List>
        </div>
      );
     }
return null;
  };

  const getLeftPanelItems = () => {
    const panelItems = map(planPackages, planPackage => {
      const benefitCategories = getBenefitCategories(planPackage);
      const panelItemChildren = map(benefitCategories, benefitCategory => {
        const isSelected =
          selectedLeftPanelItem &&
          selectedLeftPanelItem.tree_id === benefitCategory.tree_id;
        return (
          <ListItem key={`left-list-item-${benefitCategory.tree_id}`}>
            <ListItemText
              id={'benefit_categories_' + benefitCategory.tree_id}
              data-cy={'benefit_categories_' + benefitCategory.tree_id}
              className={clsx(
                isSelected
                  ? 'side-menu-list-item-selected'
                  : 'side-menu-list-item',
              )}
              onClick={() => {
                if (!isSelected) {
                  setSelectedLeftPanelItem(benefitCategory);
                }
              }}
            >
              {benefitCategory.name}
            </ListItemText>
          </ListItem>
        );
      });
      return getLeftPanelItem(planPackage, panelItemChildren);
    });

    setLeftPanelItems(panelItems);
  };

  const getFilteredBenefits = () => {
    // if (globalFilter && globalFilter !== '') {
    //   const containsGlobalFilter = (target: any) => {
    //     return (
    //       target &&
    //       target !== undefined &&
    //       target !== null &&
    //       includes(toLower(target), toLower(globalFilter))
    //     );
    //   };

    //   const filtered = filter(benefits, benefit => {
    //     return (
    //       containsGlobalFilter(benefit.benefitType) ||
    //       containsGlobalFilter(benefit.benefit) ||
    //       containsGlobalFilter(benefit.limitType) ||
    //       containsGlobalFilter(benefit.limit)
    //     );
    //   });

    //   console.log("filtered", filtered);
    //   setFilteredBenefits(filtered);
    // } else {
    setFilteredBenefits(benefits);
    // }
  };

  const getMemberBenefits = () => {
    console.log('getMemberBenefits', props.contractId)
    //props.memberCardNo
    if(!props.contractId){
      return;
    }
    API.getMemberBenefits(props.contractId).then(response => {
      console.log('BENEFITS RESPONSE', response)
      //console.log('memberCardNo', props.memberCardNo)
    })
  }

  const getBenefits = () => {
    if (props.memberBenefits) {
      // console.log('memberBenefits val', props.memberBenefits)
      if (
        roomAndBoardNode &&
        selectedLeftPanelItem &&
        startsWith(roomAndBoardNode.tree_id, selectedLeftPanelItem.tree_id)
      ) {
        // console.log('calling getRoomAndBoardBenefits()')
        getRoomAndBoardBenefits();
        return;
      }

      // console.log('calling getBenefits()')
      const parentNodes = filter(props.memberBenefits, benefit => {
        let benefitTreeId = benefit.tree_id;
        let selectedLeftPanelItemTreeId = selectedLeftPanelItem.tree_id;
        if (memberPlanTypeNode !== null) {
          benefitTreeId = replace(
            benefit.tree_id,
            BASE_PLAN_CODE,
            memberPlanTypeNode.code,
          );
          selectedLeftPanelItemTreeId = replace(
            selectedLeftPanelItem.tree_id,
            BASE_PLAN_CODE,
            memberPlanTypeNode.code,
          );
        }

        return (
          ((benefit.level === 5 && !['EC', 'DNT', 'CC', 'ADD', 'EM','PV', 'SD'].includes(benefit.code) )|| benefit.level === 6 ) &&
          selectedLeftPanelItem &&
          startsWith(benefitTreeId, selectedLeftPanelItemTreeId)
        );
      });

      const childNodes = filter(props.memberBenefits, benefit => {
        let benefitTreeId = benefit.tree_id;
        let selectedLeftPanelItemTreeId = selectedLeftPanelItem.tree_id;
        if (memberPlanTypeNode !== null) {
          benefitTreeId = replace(
            benefit.tree_id,
            BASE_PLAN_CODE,
            memberPlanTypeNode.code,
          );
          selectedLeftPanelItemTreeId = replace(
            selectedLeftPanelItem.tree_id,
            BASE_PLAN_CODE,
            memberPlanTypeNode.code,
          );
        }

        return (
          benefit.level === 7 &&
          selectedLeftPanelItem &&
          startsWith(benefitTreeId, selectedLeftPanelItemTreeId)
        );
      });

      // add EL children to CS node 
      if (selectedLeftPanelItem && ['EL', 'CS'].includes(selectedLeftPanelItem.code)) {
        let elNode = props.memberBenefits.find(benefit => {
          return benefit.code === 'EL' && benefit.tree_id === `MCT-CT-${memberPlanTypeNode.code}-TC-EL`;
        });
        let csNode = props.memberBenefits.find(benefit  => {
          return benefit.code === 'CS' && benefit.tree_id === `MCT-CT-${memberPlanTypeNode.code}-CS`;
        });
        
        console.log("sdfsdf", csNode);
        let csChildNodes: any = [elNode];

        let elChildren = filter(props.memberBenefits, benefit => {
          return benefit.level === 7 && benefit.tree_id.startsWith(`MCT-CT-${memberPlanTypeNode.code}-TC-EL-`);
        });

        // Add each child node of the eligibility node to be used as rows on Eligibility Form
        let children: any[] = [];
        if (elChildren) {
          elChildren.forEach(child => {
            let dpChildren = child.children;
            dpChildren.forEach(dpChild => {
              const foundChild = find(
                props.memberBenefits,
                childNode => dpChild === childNode._id,
              );
              if (foundChild && startsWith(foundChild.tree_id, child.tree_id) && foundChild.level === 8) {
                  children.push(foundChild);
              }
              child.child_nodes = children;
              csChildNodes.push(foundChild);
            })
            children = [];
          });
        }
        
        // Map through the child nodes of eligibility to be used as rows on Co-Sharing form
        if (
          (csChildNodes) &&
          (selectedLeftPanelItem && selectedLeftPanelItem.code === 'CS')
        ) {
          let csRows: any[] = [];
          csChildNodes.map((csItem: any) => {
            if (csItem && csItem.code === 'EL') {
              const tmpRowIndex = csRows.findIndex(tmp=>tmp.code==='PRINCIPAL')
              if(tmpRowIndex===-1){
                let child = Object.assign({}, csItem);
                csRows.push({
                  id:'PRINCIPAL',
                  code:'PRINCIPAL',
                  custom_metadata:child.custom_metadata,
                  title:'Principal',
                  layers:child.layers,
                  nodes:[child],
                })
              }
            } else {
              const tmpRowIndex = csRows.findIndex(tmp=>tmp.code===csItem.code)
              if(tmpRowIndex===-1){
                let child = Object.assign({}, csItem);
                csRows.push({
                  id:child.code,
                  code:child.code,
                  custom_metadata:child.custom_metadata,
                  title:child.custom_metadata.title ? child.custom_metadata.title : '',
                  layers:child.layers,
                  nodes:[child],
                })
              } else {
                let child = Object.assign({}, csItem);
                if(child.custom_metadata && csRows[tmpRowIndex].custom_metadata===undefined){
                  csRows[tmpRowIndex].custom_metadata=child.custom_metadata
                }
                csRows[tmpRowIndex].nodes.push(child)
              }
            }
          });
          setBenefits(csRows);
        }
      }

      if (selectedLeftPanelItem && selectedLeftPanelItem.code === 'ID') {
        let idNode: any = props.memberBenefits.find(benefit => {
          return benefit.code === 'ID' && benefit.tree_id === `MCT-CT-${memberPlanTypeNode.code}-ID`;
        });
        let idChildNodes: any = idNode && idNode.children ? idNode.children : []
        let idRows: any = []

        console.log("sdasd idNode", idNode, idChildNodes)
        if (idChildNodes) {
          idChildNodes.forEach(childID => {
            console.log("childID?", childID)
            let child: any = props.memberBenefits.filter(benefit => {
              return benefit._id === childID
            });
            if (child) {
              console.log("fsadfg_child", child)
              idRows.push(child[0]);
            }
          });
        }
        console.log("idRows?", idRows)
        setBenefits(idRows)
      }

      if (selectedLeftPanelItem && selectedLeftPanelItem.code !== 'CS' && selectedLeftPanelItem.code !== 'ID') {
        // console.log('getBenefits parentNodes val', parentNodes)
        // console.log('getBenefits childNodes val', childNodes)
        let combinedNodes = combineNodes(parentNodes, childNodes);
        console.log('getBenefits benefits val', combinedNodes)
        setBenefits(combinedNodes);
      }
    }
  };

  const getRoomAndBoardBenefits = () => {
    console.log("Check Node: ",roomAndBoardNode,' - ',props.memberBenefits.filter(node=>node.level===4));
    const roomAndBoardBenefits = filter(props.memberBenefits, benefit => {
      let benefitTreeId = benefit.tree_id;
      let roomAndBoardNodeTreeId = roomAndBoardNode.tree_id;
      if (memberPlanTypeNode !== null) {
        benefitTreeId = replace(
          benefit.tree_id,
          BASE_PLAN_CODE,
          memberPlanTypeNode.code,
        );
        roomAndBoardNodeTreeId = replace(
          roomAndBoardNode.tree_id,
          BASE_PLAN_CODE,
          memberPlanTypeNode.code,
        );
      }

      return (
        benefit.level === 5 && startsWith(benefitTreeId, roomAndBoardNodeTreeId)
      );
    });

    console.log("Check Room And Board: ",props.memberBenefits);

    // console.log('roomAndBoardBenefits val', roomAndBoardBenefits)
    if (roomAndBoardBenefits) {
      const combinedNodes: any[] = [];
      forEach(roomAndBoardBenefits, benefit => {
        const parsedBenefits = getParsedBenefits(benefit, null);
        combinedNodes.push(...parsedBenefits);
      });

      console.log('getRoomAndBoardBenefits benefits val', combinedNodes)
      setBenefits(combinedNodes);
    }
  };

  const combineNodes = (parentNodes, childNodes) => {
    const combinedNodes = reduce(
      parentNodes,
      (nodes: any[], node) => {
        if (node.children && node.children.length > 0) {
          forEach(node.children, child => {
            const foundChild = find(
              childNodes,
              childNode => child === childNode._id,
            );

            if (foundChild) {
              const parsedBenefits = getParsedBenefits(
                node,
                foundChild,
              );
              // console.log('if node.children foundChild parsedBenefits', parsedBenefits)
              nodes.push(...parsedBenefits);
            } else {
              const parsedBenefits = getParsedBenefits(node, null);
              // console.log('if node.children NOT foundChild parsedBenefits', parsedBenefits)
              nodes.push(...parsedBenefits);
            }
          });
        } else {
          const parsedBenefits = getParsedBenefits(node, null);
          // console.log('else node.children parsedBenefits', parsedBenefits)
          nodes.push(...parsedBenefits);
        }

        // console.log('returned nodes', nodes)
        return nodes;
      },
      [],
    );

    return combinedNodes;
  };

  const getParsedBenefits = (parent, child) => {
    const parsedBenefits: any[] = [];

    let code = '';
    let parentContractPlanType: string = '';
    let childContractPlanType: string = '';

    if (selectedLeftPanelItem) {

      if(selectedLeftPanelItem['code']) {
        code = selectedLeftPanelItem.code;
      }

      if(!parent['contract_plan_type'] && selectedLeftPanelItem['contract_plan_type']) {
        parentContractPlanType = selectedLeftPanelItem.contract_plan_type
      } else {
        parentContractPlanType = parent['contract_plan_type']
      }
    }

    console.log('getParsedBenefits parent', parent)
    console.log('getParsedBenefits child', child)
    console.log('getParsedBenefits code', code)
    console.log('getParsedBenefits selectedLeftPanelItem', selectedLeftPanelItem)
    console.log('getParsedBenefits props', props)
    console.log('getParsedBenefits memberPlanTypeNode', memberPlanTypeNode)

    if (child) {
      let childLimits: any[] = child['limits'] ? child['limits'] : [];
      let child_nodes: any[] = [];
      if (child.child_nodes) {
        child_nodes = child.child_nodes;
      }
      // console.log('getParsedBenefits child limits', childLimits)

      if(child['contract_plan_type']) {
        childContractPlanType =  child['contract_plan_type'];
      } else {
        if(memberPlanTypeNode['custom_metadata']) {
          if(memberPlanTypeNode['custom_metadata']['contract_plan_type']) {
            childContractPlanType = memberPlanTypeNode['custom_metadata']['contract_plan_type']
          } else {
            childContractPlanType = '——';
          }
        } else {
          childContractPlanType = '——';
        }
      }

      parsedBenefits.push({
        parent_name: parent.name,
        name: child ? child.name : '----',
        custom_metadata: child['custom_metadata'] ? child['custom_metadata'] : [],
        limits: childLimits,
        contract_plan_type: childContractPlanType !== 'Hybrid' ? childContractPlanType : '——',
        child_nodes: child_nodes,
      });
    } else if (['RB', 'EM', 'DNT', 'CC', 'EC', 'PV', 'SH', 'ADD', 'SD', 'OM', 'OR', 'ID', 'TC','ILS','CWB', 'DP', 'CS', 'EL', 'COSH'].includes(code)) {
      let parentLimits: any[] = parent['limits'] ? parent['limits'] : [];

      // console.log('getParsedBenefits parent limits', parentLimits)

      parsedBenefits.push({
        name: parent.name,
        custom_metadata: parent['custom_metadata'] ? parent['custom_metadata'] : [],
        limits: parentLimits,
        contract_plan_type: parentContractPlanType !== 'Hybrid' ? parentContractPlanType : '——',
      });
    }

    // const benefitName = child ? child.name : '----';
    // if (limits && limits.length > 0) {
    //   forEach(limits, limit => {
    //     let limitDisplay = '----';
    //     if (get(limit, 'limit', false) && get(limit, 'limit_unit', false)) {
    //       limitDisplay = `${limit.limit} ${limit.limit_unit}`;
    //     }

    //     parsedBenefits.push({
    //       benefitType: parent.name,
    //       benefit: benefitName,
    //       limitType: limit.type,
    //       limit: limitDisplay,
    //     });
    //   });
    // } else {
    //   parsedBenefits.push({
    //     benefitType: parent.name,
    //     benefit: benefitName,
    //     limitType: 'None',
    //     limit: '----',
    //   });
    // }

    return parsedBenefits;
  };

  // const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const { value } = e.target;
  //   setGlobalFilter(value);
  // };

  // const handleFilterChange = (filters: any) => {
  //   setColumnFilters(filters);
  // };

  // const handleSorting = (sorting: any) => {
  //   console.log(sorting);
  // };

  console.log('selectedLeftPanelItem val', selectedLeftPanelItem)

const setSelectedLeftPanelItemByCode = (code, level?, planType?)=> {
  // check if there is a plantype
  let selectedBenefitCategory: any = {};
  if (planType) {
    console.log("plan type check", planType)
    selectedBenefitCategory = props.memberBenefits.find(e => {
      return e.code === code && e.level === level && e.tree_path[2] === planType;
    });

    if (!selectedBenefitCategory) {
      let tmpBenefitCategory: any = Object.assign({}, props.memberBenefits.find(e => {
        return e.code === code && e.level === level && e.tree_path[2] === 'BASE';
      }));
      tmpBenefitCategory.custom_metadata = Object.assign({})
      tmpBenefitCategory.remarks = 'N/A'

      selectedBenefitCategory = tmpBenefitCategory;
    }
  } else {
    selectedBenefitCategory = props.memberBenefits.find(e => {
      return e.code === code && e.level === level;
    });
  }
  console.log('bcsSelectBenefit', selectedBenefitCategory, planType)
  setSelectedLeftPanelItem(selectedBenefitCategory)
}


  return (
    <div className={clsx('BenefitsTable')}>
      <Grid
        container
        className={styles.container}
        spacing={4}
        direction="row"
        justify="flex-start"
        alignItems="stretch"
      >
        <Grid item xs={2} className={clsx(styles.leftSection)}>
          {leftPanelItems}    
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -15 , paddingLeft: 0}}>
                <ListItemText
                  className={clsx(
                  selectedLeftPanelItem && selectedLeftPanelItem.code === 'OR'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                  setSelectedLeftPanelItemByCode('OR', 4)
                  }}
               >
                  Other Riders
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'DP'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('DP', 6)
                    }}
               >
                  Dependents
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'COSH'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('COSH', 4, memberPlanTypeNode.code)
                    }}
               >
                  Co-Sharing
                </ListItemText>
                </ListItem>
            </List>
            </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'ID'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('ID', 4, memberPlanTypeNode.code)
                    }}
               >
                  Identifiers
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'TC'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('TC', 4)
                    }}
               >
                  Terms And Conditions
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'ILS'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('ILS', 4, memberPlanTypeNode.code)
                    }}
               >
                  Illnesses
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25 , paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'CWB'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('CWB', 4)
                    }}
               >
                  Company Wide Benefits
                </ListItemText>
                </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25, paddingLeft: 0 }} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'CS'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    console.log('memberplantypenode cost-sharing', memberPlanTypeNode.code)
                    setSelectedLeftPanelItemByCode('CS', 5, memberPlanTypeNode.code)
                    //setSelectedBenefitCategory(`CS`)
                    }}
               >
                  Cost-Sharing
                </ListItemText>
              </ListItem>
            </List>
          </div>
          <div className="sub-section">
            <List>
              <ListItem style={{ marginTop: -25, paddingLeft: 0}} >
                <ListItemText
                  className={clsx(
                    selectedLeftPanelItem && selectedLeftPanelItem.code === 'EL'
                    ? 'side-menu-list-item-selected'
                    : 'side-menu-list-item',
                  )}
                  onClick={() => {
                    setSelectedLeftPanelItemByCode('EL', 5, memberPlanTypeNode?.code)
                    }}
               >
                  Eligibility
                </ListItemText>
                </ListItem>
            </List>
          </div>
        </Grid>
        <Grid item xs={10}>
          <CategoryForm
            subFormNode={selectedLeftPanelItem}
            sharedNodes={sharedNodeObject}
            rows={filteredBenefits}
            viewOnly={true}
            sharedNodeTC={sharedNodeTC[0]}
            sharedNodeCWB={sharedNodeCWB[0]}
            planTypeNode={memberPlanTypeNode}
          />

          {/* <TextField
            style={{ width: '100%' }}
            id="benefits-search"
            placeholder="Search a benefit or benefit category"
            label=""
            margin="normal"
            variant="outlined"
            //value={globalFilter}
            //onChange={handleSearch}
            // InputProps={{
            //   startAdornment: (
            //     <InputAdornment position="start">
            //       <Search style={{ color: 'rgba(54, 54, 54, 0.4)' }} />
            //     </InputAdornment>
            //   ),
            // }}
          /> */}
          {/* <TableComponent
            id="benefits-table"
            rows={filteredBenefits}
            //columns={columns}
            message="No member benefits data"
            //columnExtensions={columnExtensions}
            formattedColumns={{}}
            onClickRow={() => {}}
            // defaultFilter={columnFilters}
            // onFilterChange={handleFilterChange}
            // onSortingChange={handleSorting}
            disableSelect
            disableSearch
          /> */}
        </Grid>
      </Grid>
    </div>
  );
};